/********************************************************************************
 * @file    hal_def.h
 * <AUTHOR>
 * @version V1.0
 * @date    2024-08-05
 * @brief   硬件抽象层定义. 包含设备、中断向量头文件。定义复位系统及看门狗宏，定义临界开关中断宏等。
 * @note
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
 *
 ******************************************************************************/
#ifdef __cplusplus
extern "C"
{
#endif

    /** @addtogroup HAL-HC32L196
     * @{
     */

    /** @addtogroup Hardware-Abstract-define
     * @{
     */

#ifndef __HAL_DEF_H
#define __HAL_DEF_H

/* Includes ------------------------------------------------------------------*/
// mcu库头文件
#include "typedef.h"
#include "boot_api.h"
#include "intvec_tab.h"

#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_gpio.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_flash.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_rtc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_simptc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sipeeprom.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_spi.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sysc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sysclk.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sysoption.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_systickcortexm0.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_tc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_uart.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_utils.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_wdt.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_clktrim.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_common.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_d2f.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_def.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_devices.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_dsp.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_eeprom.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_emu.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_emu_lib.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_gpadc_lib.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_iic.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_intc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_iocnt.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_iso7816.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_kbi.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_lcd.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_m2m.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_madc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_novoltagem.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_rtc_lib.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_common.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_def.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_ecc.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_hash.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_rsa.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_sea_trng.h"

/* ---------------------- IAR Compiler ---------------------- */
#ifdef __IAR_SYSTEMS_ICC__
#include <intrinsics.h>
#define HAL_COMPILER_IAR

/* ---------------------- ARM Compiler ---------------------- */
#elif defined(__CC_ARM)
    typedef unsigned long __istate_t;
#define __enable_interrupt() __enable_irq()
#define __disable_interrupt() __disable_irq()
#define __get_interrupt_state() __get_PRIMASK()
#define __set_interrupt_state(x) __set_PRIMASK(x)
#define __no_operation() __nop()
#define __root
#define __INLINE __attribute__((always_inline)) static __inline
#define __no_init __attribute__((zero_init))
/* ------------------ Unrecognized Compiler ------------------ */
#else
#pragma message("ERROR: Unrecognized compiler.")
#endif

//========================================================

/* 定义看门狗溢出时间周期 */
#define HAL_WDG_TIMEOUT_MS 5000 /* watch dog periods, unit:ms */

/* 定义开关中断以及临界代码宏 */
#define HAL_ENABLE_INTERRUPTS() __enable_interrupt()
#define HAL_DISABLE_INTERRUPTS() st(__disable_interrupt(); __no_operation();)
#define HAL_INTERRUPTS_ARE_ENABLED() !__get_interrupt_state()
#define HAL_ENTER_CRITICAL_SECTION(x) st(x = __get_interrupt_state(); HAL_DISABLE_INTERRUPTS();)
#define HAL_EXIT_CRITICAL_SECTION(x) st(__set_interrupt_state(x);)
#define HAL_CRITICAL_STATEMENT(x) st(__istate_t s; HAL_ENTER_CRITICAL_SECTION(s); x; HAL_EXIT_CRITICAL_SECTION(s);)

/* 定义软件复位指令 */
#define HAL_SYSTEM_RESET() (NVIC_SystemReset())    /// 强制系统复位

    /* 定义看门狗使能及喂狗函数接口 */
    extern __weak void hal_dwt_enable(void);
    extern __weak void hal_dwt_reset(void);
#define HAL_WDG_START()   \
    do {                  \
        hal_dwt_enable(); \
    } while(0)
#define HAL_WDG_RESET()  \
    do {                 \
        hal_dwt_reset(); \
    } while(0) /* clear watch dog counter */

/* 定义寄存器写保护设置(如果有的话) */
// #define HAL_SAFETY_WR(x) st(M0P_FLASH->BYPASS = 0x5A5A; M0P_FLASH->BYPASS = 0xA5A5; x)

// @bug
#define HAL_SAFETY_WR(x) st(x)
#define HAL_VRTC_ACCESS(x) st(x)

#define EnableNvic(irq, lev, en)                         \
    do {                                                 \
        NVIC_ClearPendingIRQ((IRQn_Type)irq);                       \
        NVIC_SetPriority((IRQn_Type)irq, (uint32_t)lev); \
        if(en)                                           \
            NVIC_EnableIRQ((IRQn_Type)irq);              \
        else                                             \
            NVIC_DisableIRQ((IRQn_Type)irq);             \
    } while(0)

#define DisableNvic(irq)                      \
    do {                                      \
        NVIC_ClearPendingIRQ((IRQn_Type)irq); \
        NVIC_DisableIRQ((IRQn_Type)irq);      \
    } while(0)
    /* Exported gloable variable ------------------------------------------------ */

    /* @brief  系统内核时钟频率 */
    extern uint32_t SystemCoreClock;

#endif /* __HAL_DEF_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif
