#ifndef EEPROM_DRIVER_H
#define EEPROM_DRIVER_H

#include "typedef.h"


#define BL24C256A

#define I2C_WR	0		
#define I2C_RD	1		

#define EXTEE_TYPE 512

#ifdef AT24C64
	#define EE_MODEL_NAME		"AT24C64"
	#define EE_DEV_ADDR			0xA0		
	#define EE_PAGE_SIZE		32			
	#define EE_SIZE				(8*1024)	
	#define EE_ADDR_BYTES		2	
#endif

#ifdef AT24C256
    #define EE_MODEL_NAME		"AT24C256"
    #define EE_DEV_ADDR 		0xA0		
    #define EE_PAGE_SIZE		64			
    #define EE_SIZE 			(32*1024)	
    #define EE_ADDR_BYTES		2	

#endif

#ifdef AT24C512
    #define EE_MODEL_NAME		"AT24C512"
    #define EE_DEV_ADDR 		0xA0		
    #define EE_PAGE_SIZE		128			
    #define EE_SIZE 			(64*1024)	
    #define EE_ADDR_BYTES		2	

#endif

#ifdef BL24C256A
    #define EE_MODEL_NAME		"BL24C256A"
    #define EE_DEV_ADDR 		0xA0		
    #define EE_PAGE_SIZE		64			
    #define EE_SIZE 			(32*1024)	
    #define EE_ADDR_BYTES		2	

#endif

/* Exported functions -------------------------------------------------------*/
struct eeprom_s
{
    /** EERPOM读操作 */
    uint8_t (*read)(uint32_t _usAddress, void *_pReadBuf, uint16_t _usSize);
    /** EEPROM写操作，pdat指针为空时，表示擦除动作（写成0）*/
    uint8_t (*write)(uint32_t addr, const void* pdat, uint16_t len);
};
extern const struct eeprom_s eeprom;
#endif
