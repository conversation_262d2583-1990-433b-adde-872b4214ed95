
#include "typedef.h"
#include "hal_timer.h"
#include "hal_mcu.h"


#define TMRx_cnt            HT_TMR1    ///定义毫秒计数器定时器通道
#define TMRx_cnt_timern     1          ///定义毫秒计数器定时器通道号
#define PWM_TIMER_NO        0          ///定义PWM定时器通道号


static void (*hal_systick_proc)(void);
static CallChain_s* systick_call = NULL; // 系统节拍中断调用函数指针链表
volatile static uint32_t systick_cnt;

#if PWM_TIMER_EN
/* PWM定时器实例 */
static HT_TMR_TypeDef  * const PWM_TMRx[PWM_TIMER_NUM] =
{
#if PWM_TIMER0_EN
    HT_TMR0,
#endif
#if PWM_TIMER1_EN
    HT_TMR1,
#endif
#if PWM_TIMER2_EN
    HT_TMR2,
#endif
#if PWM_TIMER3_EN
    HT_TMR3,
#endif
};
#endif

/// @brief 系统节拍定时器服务函数，1ms
/// @param  
void SysTick_Handler(void)
{
    systick_cnt++;

    if(hal_systick_proc != NULL) (hal_systick_proc)();
    /* 遍询系统节拍调用函数链 */
    for(CallChain_s* c = systick_call; c != NULL; c = c->next)
    {
        c->func();
    }
}


/// @brief 定时器计数功能，不用开中断
__INLINE static void mcu_timer_cnt_start(uint8_t restart)
{
    /* 查询是否启动了定时器 */
    if((TMRx_cnt->TMRCON & TMR_TMRCON_CNTEN) == 0)
    {
        HAL_SAFETY_WR( HT_CMU->CLKCTRL1 |= ( 1 << TMRx_cnt_timern); );     ///打开定时器时钟
        TMRx_cnt->TMRDIV = (WORK_APB_HZ/1000 - 1);           /// 设置分频，使1ms为1分频
        TMRx_cnt->TMRPRD = 0xFFFF;                           /// 设置计数周期
        TMRx_cnt->TMRCON = TMR_TMRCON_MODE_TIMING;           /// 定时模式
    }
    else if(restart) 
    {
        TMRx_cnt->TMRCON &= (~TMR_TMRCON_CNTEN);             /// 关闭计数器
        TMRx_cnt->TMRCNT  = 0;                               /// 清空定时器计数器
    }
    TMRx_cnt->TMRCON  |= TMR_TMRCON_CNTEN;
}

/// @brief 返回ms数
__INLINE static uint16_t mcu_timer_cnt_get(void)
{
    return (uint16_t)TMRx_cnt->TMRCNT;
}


/// @brief 启动系统节拍定时器
/// @param func 
void hal_systick_start(void func(void))
{
	SysTick_Config(HT_CMU_SysClkGet() / 1000);
    hal_systick_proc = func;
    mcu_timer_cnt_start(1);
}

/// @brief 插入系统节拍回调函数
/// @param call  要插入的回调函数结构体指针
void hal_systick_call_insert(CallChain_s* call)
{
    /* 遍询系统节拍调用函数链 */
    for(CallChain_s* c = systick_call; c != NULL; c = c->next)
    {
        if(c == call) return; // call已经在链表中了
    }

    /* 添加到链首位置 */
    call->next = systick_call;
    systick_call = call;
}

/// @brief 删除某个后台系统节拍回调函数
/// @param call 要删除的回调函数结构体指针
void hal_systick_call_remove(CallChain_s* call)
{
    if(call == systick_call)
    {
        systick_call = systick_call->next; // 如果在链首，则直接去掉
    }
    else
    {
        for(CallChain_s* c = systick_call; c != NULL; c = c->next)
        {
            if(c->next == call)
            {
                c->next = call->next;      // 下一个链就是call, 则改指向再下一个
                break;
            }
        }
    }
    call->next = NULL;
}
/// @brief 获取计数值
/// @return 
uint32_t systick_cnt_get()
{
    return systick_cnt;
}
/// @brief 计算从tickOld到当前时间的间隔
/// @param tickOld 
/// @return 
uint32_t systick_interval_get(uint32_t tickOld)
{
    uint32_t tick = systick_cnt_get();

    //if(tick < tickOld) { tick = UINT32_MAX - tick + tickOld; }
    if(tick < tickOld) { tick = (UINT32_MAX - tickOld) + tick + 1; }
    else { tick = tick - tickOld; }

    return tick;
}

/// @brief 软定时器定时间隔设置
/// @param [in]  t-软定时器
/// @param [in]  interval-定时间隔，单位MS
void hal_timer_interval(SwTimer_s* t, uint32_t interval)
{
    t->interval = interval;
    t->start = systick_cnt;
}

/// @brief 软定时器定时重新开始
/// @param [in]  t-软定时器
void hal_timer_restart(SwTimer_s* t)
{
    t->start = systick_cnt;
}

/// @brief 查询软定时器是否定时到期
/// @param [in]  t-软定时器
/// @return 0-定时未到期 1-定时已到期
bool hal_timer_expired(const SwTimer_s* t)
{
    volatile uint32_t diff;
    volatile uint32_t cnt_tmp = systick_cnt;

    if(cnt_tmp < t->start) { diff = (UINT32_MAX - t->start) + cnt_tmp + 1; }
    else { diff = cnt_tmp - t->start; }

    return boolof(t->interval < diff);
	// uint32_t diff = (systick_cnt - t->start) + 1;
	// return boolof(t->interval < diff);
}


/// 以下为定时器相关函数
/// @brief  开启PWM定时器
/// @param  [in]  t-定时器类型 HAL_PWM_TIMER_TYPE_0 ~ HAL_PWM_TIMER_TYPE_3
/// @param  [in]  hz-频率 100 - 1000000 Hz
/// @param  [in]  div-分频系数
/// @retval None
//__INLINE static void hal_pwm_start(HAL_PWM_TIMER_TYPE t, uint32_t hz)
static void hal_pwm_start(HAL_PWM_TIMER_TYPE t, uint32_t hz)
{
#if PWM_TIMER_EN
    uint32_t tempreg;
    if(hz < 100) hz = 100;
    hz %= 100000;
    uint32_t tmp = 1000000 / hz; // 计算周期,单位us

    HAL_SAFETY_WR( HT_CMU->CLKCTRL1 |= ( 1 << PWM_TIMER_NO); ); ///打开定时器时钟

    PWM_TMRx[t]->TMRCON &= (~TMR_TMRCON_CNTEN);                    /// 关闭计数器
    PWM_TMRx[t]->TMRDIV = (WORK_APB_HZ/1000000 - 1);               /// 设置分频，使1us为1分频
    PWM_TMRx[t]->TMRPRD = tmp;                                     /// 设置计数周期
    PWM_TMRx[t]->TMRCMP = tmp / 2;                                 /// 设置比较值
    PWM_TMRx[t]->TMRCNT = 0;                                       /// 清空定时器计数器
    PWM_TMRx[t]->TMRCAP = 0;                                       /// 清空捕捉寄存器
    tempreg = TMR_TMRCON_CNTEN;                                    /// 打开计数器
    tempreg |= (TMR_TMRCON_PWMHL_HIGH | TMR_TMRCON_PWMC_UP | TMR_TMRCON_MODE_PWM);  /// PWM向上计数方式，初始电平高
    PWM_TMRx[t]->TMRCON = tempreg;                                 /// 启动定时器
#endif
}
/// @brief   关闭PWM定时器
/// @param t t-定时器类型 HAL_PWM_TIMER_TYPE_0 ~ HAL_PWM_TIMER_TYPE_3
/// @return  None
//__INLINE static void hal_pwm_stop(HAL_PWM_TIMER_TYPE t)
static void hal_pwm_stop(HAL_PWM_TIMER_TYPE t)
{
#if PWM_TIMER_EN
    PWM_TMRx[t]->TMRCON &= (~TMR_TMRCON_CNTEN);                    /// 关闭计数器
    HAL_SAFETY_WR( HT_CMU->CLKCTRL1 &= ~( 1 << ((uint8_t)(PWM_TMRx[t] - HT_TMR0))); ); ///关闭定时器时钟
#endif
}

void hal_pwm_out(HAL_PWM_TIMER_TYPE ch, uint32_t hz)
{
    hal_pwm_stop(ch);
    if(hz != 0)
    {
        hal_pwm_start(ch, hz);
    }
}

///

/**
 * @brief  系统精确毫秒延时，才用定时器，无需开中断也可用
 * @param  [in]  ms-延时毫秒数
 * @retval None
 */
void hal_delay_ms(uint32_t ms)
{
    uint32_t cnt_tmp;
    ms++;
    mcu_timer_cnt_start(0);
    while(ms--)
    {
        cnt_tmp = mcu_timer_cnt_get();
        while(mcu_timer_cnt_get() == cnt_tmp){}
        HAL_WDG_RESET();
    }
}

/// @brief pll  22MHz，开启指令预取，延时1us
/// @param count 
void hal_delay_xus(uint16_t count)    
{
    while(count--)
    {
        __NOP();__NOP();__NOP();__NOP();
        __NOP();__NOP();__NOP();__NOP();
        __NOP();__NOP();__NOP();__NOP(); 
    }
}
/// @brief // pll  22MHz，开启指令预取，延时1ms
/// @param count 
void hal_delay_xms(uint16_t count)
{
    s32 Fcpu = HT_CMU_SysClkGet() / 2000;  
    u8 d;

    if(Fcpu == 16) { d = 18; } 
    else { d = 3; }

    Fcpu = (s32)(Fcpu * 0.855);
    while(count--)
    {
        s32 step = Fcpu;
        while(1){step -= d; if(step < 0) break;}
    }
 }

const struct hal_delay_s hal_timer = 
{
    .systick_start  = hal_systick_start,
    .systick_insert = hal_systick_call_insert,
    .systick_remove = hal_systick_call_remove,
    .systick_cnt    = systick_cnt_get,
    .msdly          = hal_delay_ms,
    .interval       = hal_timer_interval,
    .restart        = hal_timer_restart,
    .expired        = hal_timer_expired,
    .xms            = hal_delay_xms,
    .pwm_out        = hal_pwm_out,
};