/**
  ******************************************************************************
  * @file    hal_mcu.h
  * <AUTHOR> @date    2024
  * @brief   MCU内核驱动头文件
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  ******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __HAL_MCU_H
#define __HAL_MCU_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"


/* ------------------------------------------------------------------------------------------------
 *                                        Target Defines
 * ------------------------------------------------------------------------------------------------
 */
#define HAL_MCU_LITTLE_ENDIAN     TRUE

/* ------------------------------------------------------------------------------------------------
 *                                     MCU system clock
 * ------------------------------------------------------------------------------------------------
 */
/* 正常电表应用场景只需低速、内部RC振荡、高速三种应用场景。 频率大小依据MCU实际情况配置。 */
typedef uint32_t  MCU_CLOCK_MODE;
#define WORK_LC_32768              32768        ///定义32768工作频率
#define WORK_RC_HZ                 (11010000)   ///定义MCU内部RC振荡器工作频率，也即MCU复位后的频率, 芯片默认2分频
#define WORK_RUN_HZ                22020096     ///定义MCU使用的运行工作频率，通常来自于PLL、内部或者外部高速
#define WORK_APB_HZ                22020096     ///定义MCU使用的外设工作频率

/* ------------------------------------------------------------------------------------------------
 *                                     Initialization status
 * ------------------------------------------------------------------------------------------------
 */
/* MCU内核初始化返回状态定义 */
typedef union
{
    struct
    {
        char power_on_rst   : 1; ///@ 上电的复位,热启动
        char clk_src_extosc : 1; ///@ 外部晶振异常
        char abnormal_rst   : 1; ///@ 异常发生的复位
        char por_bor_rst    : 1; ///@ 掉电复位,冷启动
        char rly_act        : 1; ///@ 继电器动作中，可读写
    };
    uint8_t value;
} McuCoreStus_s;

/* Exported functions ------------------------------------------------------- */
struct hal_mcu_t
{
    /// @brief MCU内核状态
    McuCoreStus_s* stus;
    /// @brief MCU复位后运行模式前的MCU初始化，并返回MCU状态
    McuCoreStus_s (*init)(void);
    /// @brief MCU进入低功耗模式设置，用于功耗管理
    void (*sleep)(void);
    /// @brief 延时us,只能在hal层使用，不建议在应用层使用
    void (*wait_us)(uint16_t us);
    /// @brief 掉电确认检测. true-表示已掉电
    bool (*pwrdn_query)(void);
    /// @brief 上电确认检测. true-表示已上电
    bool (*pwron_query)(void);
    /// @brief 系统主频模式切换。主频模式参考MCU_CLOCK_MODE定义
    uint8_t (*sysclk_set)(MCU_CLOCK_MODE mode);
    /// @brief 喂看门狗操作
    void (*wdg_clr)(void);
    /// @brief soc芯片emu单元内部sag判断
    void (*is_sag_callback)(bool func(void));
    /// @brief 获取LVD中断状态
    bool (*lvd_intrpt_get)(void);
};
extern const struct hal_mcu_t hal_mcu; /// hal_mcu子模块

#endif /* __HAL_MCU_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

