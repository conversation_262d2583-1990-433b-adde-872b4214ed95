/**
  ******************************************************************************
  * @file    hal_mcu.c
  * <AUTHOR> @date    2024
  * @brief   本模块主要包括MCU的内核、时钟总线、复位源、电源功耗管理等芯片级驱动。
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_gpio.h"
#include "hal_def.h"
#include "hal_timer.h"
#include "debug.h"


/* Private typedef -----------------------------------------------------------*/
/* @brief 供电检测模式定义 */
typedef enum
{
    PWR_ON,  // 上电电压检测
    PWR_OFF, // 掉电电压检测
} PVD_MODE;

/* Private define ------------------------------------------------------------*/
#define TEST_POR_TIMES       180             // 检测系统上电电压次数
#define TEST_PDR_TIMES       100             // 检测系统下电电压次数

/* Private macro -------------------------------------------------------------*/
#define HRC_ADJ          ((*(uint32_t*)0x00040140) & 0x3F)  ///HRC载入校准值

/* Private variables ---------------------------------------------------------*/
static McuCoreStus_s mcu_stus;
__weak uint32_t SystemCoreClock = WORK_RC_HZ; // 系统复位后默认时钟
#if USE_SAG_TYPE != SAG_NULL
static bool (*is_sag)(void);
#endif
static uint8_t InstructionsPerUs = WORK_RC_HZ / 2000000;
static uint8_t lvd_intrpt = FALSE;

/* Private functions ---------------------------------------------------------*/
void hal_mcu_wait_us(uint16_t nUs);

//PMU中断处理函数
//void PMU_IRQHandler(void)
void irq_handler_lvd(void)
{
    if(HT_PMU->PMUIF & PMU_PMUIF_LVD0IF) 
    {
        lvd_intrpt = boolof(HT_PMU->PMUSTA & PMU_PMUSTA_LVD0FLG); // lvd中断标志位
    }
    HT_PMU->PMUIF = 0; // 清除所有中断标志位
}

/**
  * @brief  配置供电检测模式
  * @retval mode=PWR_ON 表示检测上电
  */
static void mcu_lvd_mode_set(PVD_MODE mode)
{   
    hal_gpio.monitor(0);
    if(mode == PWR_ON)
    {
        HAL_SAFETY_WR
        (
            HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN | PMU_PMUCON_HoldLDO ); // 使能LVD检测，检测电压为1.21V,不使能BOR检测和开启BOR复位，保持LDO状态
                                //PMU_PMUCON_BORDETEN  | PMU_PMUCON_BORRST); // 使能LVD检测，检测电压为1.21V,不使能BOR检测和开启BOR复位，保持LDO状态
            //HT_PMU->VDETCFG  = (PMU_VDETCFG_VCCLVL_2V8 | PMU_VDETCFG_BORLVL_2V2);   ///设置VCC检测电压为2.8V，BOR检测电压为2.2V
            //HT_PMU->VDETPCFG = (0x0020 | PMU_VDETPCFG_VDETTIME_300uS | PMU_VDETPCFG_VDETPRD_16P5mS);   ///设置VCC检测时间为300us，检测周期为16.5ms
        );    
    }
    else
    {
        HAL_SAFETY_WR
        (
            HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN | PMU_PMUCON_HoldLDO); // 使能LVD检测，检测电压为1.21V
                                //PMU_PMUCON_BORDETEN  | PMU_PMUCON_BORRST); // 使能LVD检测，检测电压为1.21V
            //HT_PMU->VDETCFG  = (PMU_VDETCFG_VCCLVL_2V8 | PMU_VDETCFG_BORLVL_2V2); ///设置VCC检测电压为2.8V，BOR检测电压为2.2V
            //HT_PMU->VDETPCFG = (0x0020 | PMU_VDETPCFG_VDETTIME_300uS | PMU_VDETPCFG_VDETPRD_16P5mS); ///设置VCC检测时间为300us，检测周期为16.5ms
        );  
    }
	hal_mcu_wait_us(100);//LVD开启后到输出稳定建立大约需要100us时间
}

/**
  * @brief  获取LVD检测状态
  * @retval 0-供电正常
  * @retval 1-供电异常
  */
static bool mcu_lvd_state_get(PVD_MODE mode)
{
    return boolof(!(HT_PMU->PMUSTA & PMU_PMUSTA_LVD0FLG));
}

/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
  * @{
  */

/**
  * @brief  MCU指令等待若干uS时间.
  * @param  [in]  nUs-等待时长, 单位:微秒 
  * @note   该等待采用的是指令摸拟, 并不精确, 适用于硬件驱动不需要严格的时序等待
  */
void hal_mcu_wait_us(uint16_t nUs)
{
    int32_t clkcnt = nUs * InstructionsPerUs - 8;
    while(1){ clkcnt -= 3; if(clkcnt < 0) break; }
}

/**
  * @brief  上电确认检测
  * @param  none
  * @retval TRUE-power on  FALSE-not power on
  */
bool hal_mcu_pwron_query(void)
{
    mcu_lvd_mode_set(PWR_ON); 
    for(int i = TEST_POR_TIMES; i > 0; i--)
    {
        /**为了防止电源抖动，如果检测到上电，先进行1ms间隔的连续检测 */
        if(mcu_lvd_state_get(PWR_ON)) return false;
        HAL_WDG_RESET();
        hal_mcu_wait_us(1000);
    }
    return true;
}

/**
  * @brief  掉电确认检测
  * @param  none
  * @retval TRUE-power down  FALSE-not power down
  */
bool hal_mcu_pwrdn_query(void)
{
    if(mcu_stus.rly_act) return false;

    mcu_lvd_mode_set(PWR_OFF);
    for(int i = TEST_PDR_TIMES; i > 0; i--)
    {
    #if USE_SAG_TYPE == SAG_EMU
        if(is_sag != NULL && (is_sag)()) return true;
    #elif USE_SAG_TYPE == SAG_PIN
        if(is_sag != NULL && (is_sag)()) continue;
    #endif
        if(!mcu_lvd_state_get(PWR_OFF)) 
        {
            if(lvd_intrpt){HAL_CRITICAL_STATEMENT( lvd_intrpt = FALSE;);}
            return false;
        }
    }
    return true;
}

bool hal_lvd_intrpt_get(void)
{
    return boolof(lvd_intrpt);
}

void hal_dwt_enable(void)
{
    HT_WDT->WDTCLR = 0xAA7F;        /// 64ms*(0x7F+1),8秒
    HT_RTC->CTRLBYFLASH |= 0x02;    //使能DWT时钟LRC
}

void hal_dwt_reset(void)
{
    HT_WDT->WDTCLR = 0xAA7F;
}

void hal_wdt_close(void)
{
    HT_WDT->WDTCLR = 0xAA7F; //clear WDT
    HT_RTC->CTRLBYFLASH &= 0xFFFD;
    HT_RTC->LRCCOMAND = 0x5555;
    HT_RTC->LRCCOMAND = 0xAAAA;
}

/// @brief 系统时钟切换
/// @param mode
/// @return 0-外部晶振异常
uint8_t hal_sysclk_set(MCU_CLOCK_MODE mode)
{
    CMU_InitTypeDef cmu;
    switch(mode)
    {
        case WORK_LC_32768:
            /**********  配置CMU 模块  **********/
            cmu.SysClkSel = SysLF;    // SysLF;  // SysHRCDiv1;   //SysLRC   CMU_SYSCLKCFG_CLKSEL_HRC;
            cmu.CPUDiv    = CPUDiv1;

            SystemCoreClock   = WORK_LC_32768;
            InstructionsPerUs = 0;
            break;

        case WORK_RC_HZ:
            cmu.SysClkSel = SysHRCDiv1;    // SysHRCDiv1
            cmu.CPUDiv    = CPUDiv1;       // CMU_CPUCLKDIV_1; //  19.66M

            SystemCoreClock   = WORK_RC_HZ;
            InstructionsPerUs = WORK_RC_HZ / 2000000;
            break;

        case WORK_RUN_HZ:
            cmu.SysClkSel = SysPLL;     // SysHRCDiv1;   //SysPLL;
            cmu.CPUDiv    = CPUDiv1;    // CMU_CPUCLKDIV_1; //  22M

            SystemCoreClock   = WORK_RUN_HZ;
            InstructionsPerUs = WORK_RUN_HZ / 2000000;
            break;
    }
    HT_CMU_Init(&cmu);
    return 1;
}

/**
  * @brief  MCU复位后运行模式前的MCU初始化.
  * @param  none
  * @retval MCU core 初始化状态, 参考tMcuCoreStus定义
  */
McuCoreStus_s hal_mcu_core_init(void)
{
    /* 初始化RCMF */
    hal_dwt_enable();
    HT_CMU->HRCADJ = HRC_ADJ; ///RCMF频率调校

    hal_sysclk_set(WORK_RC_HZ);  //默认使用RC时钟

    mcu_stus.power_on_rst = hal_mcu_pwron_query();
    if(mcu_stus.power_on_rst)
    {
        {
            ///获取复位源信息
            uint32_t tmp = HT_PMU->RSTSTA;
            if(tmp & PMU_RSTSTA_WDTRST){ mcu_stus.abnormal_rst = true; }
            mcu_stus.abnormal_rst = boolof(tmp & PMU_RSTSTA_WDTRST); ///查询是否看门狗复位
            mcu_stus.por_bor_rst  = boolof(tmp & (PMU_RSTSTA_PORRST | PMU_RSTSTA_LBORRST)); ///判断是否冷启动
            HT_PMU->RSTSTA &= ~tmp;
        }        
        /// HRC到PLL固定延时800ms hal_mcu_pwron_query 有300ms
        hal_dwt_reset();
        for(uint8_t i = 10; i > 0; i--) hal_mcu_wait_us(40000); // 不使用hal_timer
		hal_dwt_reset();
        /* Configurate system clock */
        if(hal_sysclk_set(WORK_RUN_HZ))
        {
            mcu_stus.clk_src_extosc = 0;
        }
        else
        {
            mcu_stus.clk_src_extosc = 1;
        }

        HAL_SAFETY_WR
        (
            /*系统外设时钟配置*/
            //外设总线时钟控制寄存器 0
            // 先清0，再设置需要使能的外设时钟，屏蔽位或者没有体现的数据位不可在此操作。
            HT_CMU->CLKCTRL0 &= ~(0
                            + CMU_CLKCTRL0_LCDEN        * 1
                            + CMU_CLKCTRL0_SPI0EN       * 1
                            + CMU_CLKCTRL0_I2CEN        * 1
                            // + CMU_CLKCTRL0_PLLEN        * 1
                            + CMU_CLKCTRL0_HRCEN        * 1
                            + CMU_CLKCTRL0_PLLLOCKEN    * 1
                            // + CMU_CLKCTRL0_LFDETEN      * 1
                            // + CMU_CLKCTRL0_PLLDETEN     * 1
                            // + CMU_CLKCTRL0_HRCDETEN     * 0
                            // + CMU_CLKCTRL0_OSC_SLP      * 1
                            + CMU_CLKCTRL0_CLKOUTEN     * 1
                            // + CMU_CLKCTRL0_1P5LBOREN    * 1 
                            + CMU_CLKCTRL0_ARGEN        * 1
                            + CMU_CLKCTRL0_CRCEN        * 1
                            );
            HT_CMU->CLKCTRL0 |= (0
                            + CMU_CLKCTRL0_LCDEN        * 0
                            + CMU_CLKCTRL0_SPI0EN       * 0
                            + CMU_CLKCTRL0_I2CEN        * 0
                            // + CMU_CLKCTRL0_PLLEN        * 1
                            + CMU_CLKCTRL0_HRCEN        * 0
                            + CMU_CLKCTRL0_PLLLOCKEN    * 0
                            // + CMU_CLKCTRL0_LFDETEN      * 1
                            // + CMU_CLKCTRL0_PLLDETEN     * 1
                            // + CMU_CLKCTRL0_HRCDETEN     * 0
                            // + CMU_CLKCTRL0_OSC_SLP      * 1
                            + CMU_CLKCTRL0_CLKOUTEN     * 0
                            // + CMU_CLKCTRL0_1P5LBOREN    * 1 
                            + CMU_CLKCTRL0_ARGEN        * 0
                            + CMU_CLKCTRL0_CRCEN        * 0
                            );
            //外设总线时钟控制寄存器 1
            HT_CMU->CLKCTRL1 = (0
                            + CMU_CLKCTRL1_TMR0EN           * 0
                            + CMU_CLKCTRL1_TMR1EN           * 0
                            + CMU_CLKCTRL1_TMR2EN           * 0
                            + CMU_CLKCTRL1_TMR3EN           * 0
                            + CMU_CLKCTRL1_UART0EN          * 1
                            + CMU_CLKCTRL1_UART1EN          * 1
                            + CMU_CLKCTRL1_UART2EN          * 1
                            + CMU_CLKCTRL1_UART3_7816_1EN   * 1
                            + CMU_CLKCTRL1_UART4_7816_0EN   * 0
                            + CMU_CLKCTRL1_UART5EN          * 0
                            + CMU_CLKCTRL1_TMR4EN           * 0
                            + CMU_CLKCTRL1_TMR5EN           * 0
                            + CMU_CLKCTRL1_UART6EN          * 0
                            + CMU_CLKCTRL1_SPI1EN           * 0
                            + CMU_CLKCTRL1_SOFTWDTEN        * 0
                            + CMU_CLKCTRL1_LRCRTC2EN        * 0
                            );
        );
        /// 关闭BOR，LBOR1.9V可满足
        HAL_SAFETY_WR
        (
            HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN | PMU_PMUCON_HoldLDO); // 使能LVD检测，检测电压为1.21V,不使能BOR检测和开启BOR复位，保持LDO状态
                                //PMU_PMUCON_BORDETEN  | PMU_PMUCON_BORRST);  // 使能LVD检测，检测电压为1.21V,使能BOR检测且开启BOR复位，保持LDO状态
            HT_PMU->VDETCFG  = (PMU_VDETCFG_VCCLVL_2V8 | PMU_VDETCFG_BORLVL_2V2);  ///设置VCC检测电压为2.8V，BOR检测电压为2.2V
            HT_PMU->VDETPCFG = (0x0020 | PMU_VDETPCFG_VDETTIME_300uS | PMU_VDETPCFG_VDETPRD_16P5mS);  ///设置VCC检测时间为300us，检测周期为16.5ms
        ); 
    }
    else
    {
        /* 低功耗运行模式前的MCU初始化 */
        hal_sysclk_set(WORK_RC_HZ);  //默认使用RC时钟
        HAL_SAFETY_WR
        (
            /*系统外设时钟配置*/
            //外设总线时钟控制寄存器 0
            // 先清0，再设置需要使能的外设时钟，屏蔽位或者没有体现的数据位不可在此操作。
            HT_CMU->CLKCTRL0 &= ~(0
                            + CMU_CLKCTRL0_LCDEN        * 1
                            + CMU_CLKCTRL0_SPI0EN       * 1
                            + CMU_CLKCTRL0_I2CEN        * 1
                            + CMU_CLKCTRL0_PLLEN        * 1
                            + CMU_CLKCTRL0_HRCEN        * 1
                            + CMU_CLKCTRL0_PLLLOCKEN    * 1
                            // + CMU_CLKCTRL0_LFDETEN      * 1
                            // + CMU_CLKCTRL0_PLLDETEN     * 1
                            // + CMU_CLKCTRL0_HRCDETEN     * 0
                            // + CMU_CLKCTRL0_OSC_SLP      * 1
                            + CMU_CLKCTRL0_CLKOUTEN     * 1
                            // + CMU_CLKCTRL0_1P5LBOREN    * 1 
                            + CMU_CLKCTRL0_ARGEN        * 1
                            + CMU_CLKCTRL0_CRCEN        * 1
                            );
            HT_CMU->CLKCTRL0 |= (0
                            + CMU_CLKCTRL0_LCDEN        * 0
                            + CMU_CLKCTRL0_SPI0EN       * 0
                            + CMU_CLKCTRL0_I2CEN        * 0
                            // + CMU_CLKCTRL0_PLLEN        * 1
                            + CMU_CLKCTRL0_HRCEN        * 0
                            + CMU_CLKCTRL0_PLLLOCKEN    * 0
                            // + CMU_CLKCTRL0_LFDETEN      * 1
                            // + CMU_CLKCTRL0_PLLDETEN     * 1
                            // + CMU_CLKCTRL0_HRCDETEN     * 0
                            // + CMU_CLKCTRL0_OSC_SLP      * 1
                            + CMU_CLKCTRL0_CLKOUTEN     * 0
                            // + CMU_CLKCTRL0_1P5LBOREN    * 1 
                            + CMU_CLKCTRL0_ARGEN        * 0
                            + CMU_CLKCTRL0_CRCEN        * 0
                            );
            //外设总线时钟控制寄存器 1
            HT_CMU->CLKCTRL1 = 0;//(0
                            // + CMU_CLKCTRL1_TMR0EN           * 0
                            // + CMU_CLKCTRL1_TMR1EN           * 0
                            // + CMU_CLKCTRL1_TMR2EN           * 0
                            // + CMU_CLKCTRL1_TMR3EN           * 0
                            // + CMU_CLKCTRL1_UART0EN          * 0
                            // + CMU_CLKCTRL1_UART1EN          * 0
                            // + CMU_CLKCTRL1_UART2EN          * 0
                            // + CMU_CLKCTRL1_UART3_7816_1EN   * 0
                            // + CMU_CLKCTRL1_UART4_7816_0EN   * 0
                            // + CMU_CLKCTRL1_UART5EN          * 0
                            // + CMU_CLKCTRL1_TMR4EN           * 0
                            // + CMU_CLKCTRL1_TMR5EN           * 0
                            // + CMU_CLKCTRL1_UART6EN          * 0
                            // + CMU_CLKCTRL1_SPI1EN           * 0
                            // + CMU_CLKCTRL1_SOFTWDTEN        * 0
                            // + CMU_CLKCTRL1_LRCRTC2EN        * 0
                            // ); 
        );
        /* 关闭 BOR*/
        HAL_SAFETY_WR
        (
            HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN);  // 使能LVD检测，检测电压为1.21V,关闭BOR检测，关闭大功耗LDO
                                //PMU_PMUCON_BORDETEN  | PMU_PMUCON_BORRST);  // 使能LVD检测，检测电压为1.21V,使能BOR检测且开启BOR复位，保持LDO状态
            HT_PMU->VDETCFG  = (PMU_VDETCFG_VCCLVL_2V8 | PMU_VDETCFG_BORLVL_2V2);  ///设置VCC检测电压为2.8V，BOR检测电压为2.2V
            HT_PMU->VDETPCFG = (0x0020 | PMU_VDETPCFG_VDETTIME_300uS | PMU_VDETPCFG_VDETPRD_16P5mS);  ///设置VCC检测时间为300us，检测周期为16.5ms
        );  
    }

    /// 使能PMU中断  LVD0        
    HT_PMU->PMUIE = (0 +
                    + PMU_PMUIE_VCCIE  * 0
                    + PMU_PMUIE_BORIE  * 0 
                    + PMU_PMUIE_LVD0IE * 1 
                    + PMU_PMUIE_LVD1IE * 0 
                    + PMU_PMUIE_POWIE  * 0             
                    );
    irq_vector_set(PMU_IRQn, irq_handler_lvd);  //设置中断向量
    /* 使能PMU中断 */
    NVIC_ClearPendingIRQ(PMU_IRQn);
    NVIC_SetPriority(PMU_IRQn, 3);
    NVIC_EnableIRQ(PMU_IRQn);

    /* Configurate watchdog */
    hal_dwt_enable();

#ifndef NDEBUG
    uint32_t tmp = HT_CMU_SysClkGet();
    //DBG_PRINTF(D,"MCU clock: %d Hz\n", tmp);
#endif
    return mcu_stus;
}

/**
  * @brief  MCU睡眠模式设置
  * @param  none
  * @retval none
  */
void hal_mcu_sleep(void)
{
    HAL_ENABLE_INTERRUPTS(); ///睡眠前必须始终打开中断!!!

    ///进入deepsleep模式，关闭基准电压源
    ///MCU唤醒后会自动切换到HRC，频率默认为8M
    HAL_SAFETY_WR
                (
                    HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN);  // 使能LVD检测，检测电压为1.21V,关闭BOR检测，关闭大功耗LDO
                ); 
    SCB->SCR = 0x0000;    // hold(0x0000)   sleep(0x0004)
    __NOP();__WFI();
    ///开启BOR
    HAL_SAFETY_WR
    (
        HT_PMU->PMUCON   = (PMU_PMUCON_LVD0DETEN | \
                            PMU_PMUCON_BORDETEN  | PMU_PMUCON_BORRST);  // 使能LVD检测，检测电压为1.21V,使能BOR检测且开启BOR复位，保持LDO状态
    );   
    HAL_WDG_RESET(); ///唤醒后清看门狗

    /* 唤醒后检查上电运行情况 */
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();
}

void hal_is_sag_callback(bool func(void))
{
#if USE_SAG_TYPE != SAG_NULL
    is_sag = func;
#endif
}

/// @brief 声明hal_uart子模块对象
const struct hal_mcu_t hal_mcu =
{
    .stus             = &mcu_stus,
    .init             = hal_mcu_core_init,
    .sleep            = hal_mcu_sleep,
    .wait_us          = hal_mcu_wait_us,
    .pwrdn_query      = hal_mcu_pwrdn_query,
    .pwron_query      = hal_mcu_pwron_query,
    .sysclk_set       = hal_sysclk_set,
    .wdg_clr          = hal_dwt_reset,
    .is_sag_callback  = hal_is_sag_callback,
    .lvd_intrpt_get   = hal_lvd_intrpt_get,
};

/** @} */
/** @} */
/** @} */
