/********************************************************************************
  * @file    event.c
  * <AUTHOR> @date    2024
  * @brief   事件记录处理
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "app.h"
#include "event.h"
#include "..\config\profile_capture_obj.h"
#include "..\config\profile_table.h"


/* Private constants ---------------------------------------------------------*/


/* Private variables ---------------------------------------------------------*/
static EVT_STUS evt_out_status;                 // 事件状态输出
static evt_data_s evt_data[EVENT_TYPE_NUM];     // 事件数据

/* Private function prototypes -----------------------------------------------*/
void event_state_clr(void);

/* Private functions ---------------------------------------------------------*/

static void event_clean_capture(uint8_t *buf, NVM_LOG_t idx)
{
    uint8_t *p = buf;
    uint16_t len;
    for(uint16_t i = 0; i < EVENT_TYPE_NUM; i++)
    {   
        len = 0;
        if(evt_data[i].time != 0)//事件发生时间不为0，代表事件发生
        {
            mclock.gseconds_to645(p, evt_data[i].time, CLOCK_YMDhms), p += 6;
            memcpy(p, &evt_data[i].id, 4), p += 4;
            memcpy(p, &evt_data[i].opt_code, 4), p += 4;
            len = p - buf;
            mlog.roll_add(log_addr(idx), buf, len);
            evt_data[i].time = 0;
        }
    }
}

/// @brief 事件记录处理
/// @param ptr 事件表项指针
/// @param typ 事件类型1：发生时刻，0:整条记录数据
/// @return 
static void event_capture(const profile_tab_s* ptr, uint8_t typ)
{
    uint16_t len;
    uint8_t  buf[MAX_CAPTURE_DATA_LEN];

    if(ptr->id == C73_EVENT_CLEAN_RECORD) //事件清零单独处理
    {
        event_clean_capture(buf, ptr->idx);
        return;
    }

    if(ptr->capture != NULL) 
    {
        if(typ) //发生时刻
        {
            len = (*(ptr->capture))(buf, ptr->idx, 1);
            mlog.roll_add(log_addr(ptr->idx), buf, len);
        }
        else if(ptr->e_state != 0)
        {
            len = (*(ptr->capture))(buf, ptr->idx, 0);
            mlog.append(log_addr(ptr->idx), buf, 0, len); /// 整条记录数据，所以偏移为0
        }
    }
}

/* Public functions ----------------------------------------------------------*/

/// @brief 事件模块初始化
/// @param  
void event_init(void)
{
    /* 校验事件参数和数据 */

    memset(&evt_out_status, 0, sizeof(evt_out_status));
}

/// @brief 事件模块秒任务
/// @param  
void event_second_run(void)
{
    clock_s clock;
    uint32_t alarm_state1 = 0, alarm_state2 = 0;

    /* 1.校验事件参数和数据 */


    /* 2.扫描事件记录 */
    for(uint16_t i = 0; i < eleof(evt_tab); i++)
    {
        const profile_tab_s* ptr = &evt_tab[i];
        if(ptr->state_query != NULL)
        {
            /* 事件记录 */
            if((*(ptr->state_query))(ptr->s_state)) {event_capture(ptr, 1);}
            if((ptr->e_state != 0) && ((*(ptr->state_query))(ptr->e_state))) { event_capture(ptr, 0); } //掉电记录开始和结束同时记录
            /* 事件上报在此压栈*/
            
        }
    }

    /* 3. 清事件状态 ,所有事件状态在此清除，所以事件模块任务需放在最后*/
    demand.state_clr();
    power_event.state_clr();
    mclock.state_clr();
    tariff.state_clr();
    sysinit.state_clr();
#if CONTROL_SUPPORT_ENABLE
    control.state_clr();
#endif

#if USE_LCD
    display.state_clr();
#endif
    mstatus.state_clr();

    event_state_clr();
}

/// @brief 清除某一类事件，或全清事件
/// @param et 
void event_group_clr(evt_type_t et)
{
    if(et >= EVENT_TYPE_NUM)
    {   
        // 全清事件
        evt_out_status |= EVT_CLEAR_STATUS;
        for(uint16_t i = 0; i < eleof(evt_tab); i++)
        {
            mlog.empty(log_addr(evt_tab[i].idx)); // 清除事件记录
            /* 清除事件记录累计次数 */
            // evt_cum_counter[i] = 0x00;
        }
    }
    else
    {   
        // 清某一类事件
        for(uint16_t i = 0; i < eleof(evt_tab); i++)
        {
            if(evt_tab[i].et == et)
            {
                mlog.empty(log_addr(evt_tab[i].idx)); // 清除事件记录
                /* 清除事件记录累计次数 */
                // evt_cum_counter[i] = 0x00;
                break;
            }
        }
    }
}

/// @brief 645协议清除某一类事件
/// @param id 事件ID
/// @param opt_code 操作码
void event_group_clr_645(uint32_t id, uint32_t opt_code)
{
    for(uint16_t i = 0; i < eleof(evt_tab); i++)
    {
        if(evt_tab[i].id == id)
        {
            mlog.empty(log_addr(evt_tab[i].idx)); // 清除事件记录
            /* 清除事件记录累计次数 */
            evt_data[i].id       = id;
            evt_data[i].opt_code = opt_code;
            evt_data[i].time     = mclock.datetime->u32datetime;
            break;
        }
    }
}

/// @brief 事件模块数据清除
/// @param type 
void event_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {

    }

    if(type & SYS_DATA_RESET)
    {
        /* 清事件记录 */
        if(type != SYS_GLOBAL_RESET)
        {
            event_group_clr(EVENT_TYPE_NUM);
        }
        memset(&evt_out_status, 0, sizeof(evt_out_status));
    }
}

/// @brief 获取事件总数
/// @param et 事件类型
/// @return 
uint32_t event_rcd_cnt_get(uint32_t id)
{
    for(uint16_t i = 0; i < eleof(evt_tab); i++)
    {
        const profile_tab_s* ptr = &evt_tab[i];
        if(ptr->id == id)
        {
            return mlog.entries_cnt_get(log_addr(ptr->idx));
        }
    }
    return 0;
}

/// @brief 获取事件模块输出状态
/// @param state 
/// @return 
bool event_state_query(uint16_t state)
{
    return boolof(evt_out_status & state);
}

/// @brief 清除事件模块输出状态
/// @param  
void event_state_clr(void)
{
    memset(&evt_out_status, 0, sizeof(evt_out_status));
}


/// @brief 声明事件模块对象
const struct event_s event =
{
    .reset                  = event_reset,
    .state_query            = event_state_query,
    .state_clr              = event_state_clr,
    .group_clr              = event_group_clr, 
    .group_clr_645          = event_group_clr_645,
    .rcd_cnt_get            = event_rcd_cnt_get,
};

/// @brief 声明事件模块任务接口
const struct app_task_t event_task =
{
    .init                   = event_init,
    .second_run             = event_second_run,
};



