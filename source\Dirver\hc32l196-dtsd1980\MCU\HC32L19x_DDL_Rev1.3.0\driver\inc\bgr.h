/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   bgr.h
 *
 * @brief  Header file for GBR functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

#ifndef __CRC_H__
#define __CRC_H__

/******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C"
{
#endif

/**
 ******************************************************************************
 ** \defgroup BGR
  **
 ******************************************************************************/
//@{

/******************************************************************************
 * Global type definitions
 ******************************************************************************/


/******************************************************************************
 * Global definitions
 ******************************************************************************/

/******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/******************************************************************************
 * Global function prototypes (definition in C source)                        
 ******************************************************************************/
///<内部温度传感器使能/关闭
void Bgr_TempSensorEnable(void);
void Bgr_TempSensorDisable(void);
///<BGR使能/关闭
void Bgr_BgrEnable(void);
void Bgr_BgrDisable(void);

//@} // BGR Group

#ifdef __cplusplus
}
#endif

#endif /* __BGR_H__ */
/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/

