@REM 父目录（项目根目录）
@REM ├─scripts/
@REM │   └─deploy.bat    ← 本脚本位置
@REM ├─output/           ← 编译输出
@REM │   ├─ModuleA/
@REM │   │   └─app(v1.2.3-20231001).hex
@REM │   └─ModuleB/
@REM │       └─driver(v2.5.0-b1234).hex
@REM └─doc/              ← 提测文档
@REM     ├─用户手册.pdf
@REM     └─改动说明.docx

@echo off
setlocal enabledelayedexpansion
:: 设置脚本编码为UTF-8
chcp 65001 >nul

echo [FILE] file:jenkins_file_copy.bat
:: 配置参数
@set "git_tag=%1"
REM 配置的 Git 提交者名称
@set "GIT_COMMITTER_NAME=%2"
REM 正在检出的提交哈希。
@set "GIT_COMMIT=%3"
REM git链接
@set "GIT_URL=%4"

set "des_dir=F:\Project"
set "script_dir=%~dp0"
set "parent_dir=%script_dir%..\"

:: 计算父目录绝对路径
pushd "%parent_dir%"
set "parent_dir=%cd%"
popd

:: 验证目录结构
set "output_root=%parent_dir%\output"
set "source_dir=%parent_dir%\doc"

echo [INFO] parent_dir: %parent_dir%
echo [INFO] output_root: %output_root%
echo [INFO] git_tag: %git_tag%

if not exist "%output_root%" (
    echo [ERROR] 父目录下缺少output文件夹: %output_root%
    exit /b 1
)
if not exist "%source_dir%" (
    echo [ERROR] 父目录下缺少doc文件夹: %source_dir%
)

:: 获取项目名称（取父目录名称）
for %%i in ("%parent_dir%") do set "projectName=%%~nxi"
echo [INFO] 项目名称: %projectName%
echo [INFO] 输出目录: %output_root%
:: 初始化版本信息
set "versionPart="
:: 遍历output下所有一级子目录
for /d %%d in ("%output_root%\*") do (
    pushd "%%d"
    
    :: 查找当前子目录中的hex文件
    set "hex_file="
    for /f "delims=" %%f in ('dir /b /a-d *.hex 2^>nul') do (
        set "hex_file=%%f"
        echo [INFO] 处理目录: %%~nxd
        echo [DEBUG] 发现HEX文件: %%f
        
        :: 版本解析示例格式：Project(v1.0-20231001).hex
        for /f "tokens=2 delims=()" %%a in ("%%f") do (
            for /f "tokens=1-2 delims=-" %%b in ("%%a") do (
                set "versionPart=%%b-%%c"
            )
        )
    )
    popd
    
    :: 找到第一个有效版本即退出循环
    if defined versionPart goto :create_target
)

:: 版本验证
if not defined versionPart (
    echo [ERROR] 未找到有效版本信息，请检查hex文件命名格式
    echo [INFO]  期望格式：文件名(版本号-日期).hex
    exit /b 1
)

:create_target
:: 创建带版本号的目标目录
set "target_dir=%des_dir%\%projectName%\%versionPart%"
echo 正在创建版本目录: %target_dir%
if not exist "%target_dir%" mkdir "%target_dir%" 2>nul || (
    echo [ERROR] 目录创建失败: %target_dir%
    echo [INFO]  可能原因：权限不足或路径无效
    
    exit /b 1
)
echo [INFO] 版本目录创建成功: %target_dir%
:: 复制所有编译输出（保留子目录结构）
echo [INFO] 正在复制编译输出...
for /d %%d in ("%output_root%\*") do (
    xcopy "%%d" "%target_dir%\%%~nxd\" /E /H /C /I /Y /Q
    if errorlevel 1 (
        echo [ERROR] 复制失败: %%~nxd
        exit /b 1
    )
)

:: 整理提测文档（使用中文路径）
set "test_docs=%target_dir%\提测文档"
echo 正在组织提测文档到: %test_docs%
robocopy "%source_dir%" "%test_docs%" ^
    *改动说明* ^
    *手册* ^
    *自测报告* ^
    *参数配置表* ^
    *操作指引* ^
    /s /njh /njs /nc /ns /r:3 /w:5

if %errorlevel% gtr 3 (
    echo [ERROR] 文档整理失败，错误码: %errorlevel%
    exit /b 1
)

:: 生成版本信息文件
(
    echo 项目名称: %projectName%
    echo 版本标识: %versionPart%
    echo 构建时间: %date% %time%
    echo Git链接:  %GIT_URL%
    echo 分支名称: %GIT_LOCAL_BRANCH%
    echo Git TAG: %git_tag%
    echo 提交人：  %GIT_COMMITTER_NAME%
    echo 提交哈希: %GIT_COMMIT%
) > "%target_dir%\版本信息.txt"

:: 清理空目录（可选）
@REM for /d %%d in ("%target_dir%\*") do (
@REM     dir /b "%%d" | findstr . >nul || rmdir "%%d"
@REM )

:: 压缩输出目录
echo 正在压缩输出目录: "%target_dir%"
echo 压缩文件名: %projectName%-%git_tag%.tar.gz
cd /d %des_dir%/%projectName%
tar -czvf "%projectName%-%git_tag%.tar.gz" "%versionPart%"
if errorlevel 1 (
    echo [ERROR] 压缩失败，错误码: %errorlevel%
    exit /b 1
)
set dist_dir="%projectName%-%git_tag%.tar.gz"
echo [INFO] 压缩成功: %dist_dir%
echo ================== 执行结果 ==================
echo 项目名称:   %projectName%
echo 版本号:     %versionPart%
echo 构建时间:   %date% %time%
echo Git链接:    %GIT_URL%
echo 分支名称:   %GIT_LOCAL_BRANCH%
echo Git TAG:   %git_tag%
echo 提交人：    %GIT_COMMITTER_NAME%
echo 提交哈希:   %GIT_COMMIT%
echo ==============================================
exit /b 0