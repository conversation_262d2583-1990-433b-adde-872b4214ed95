/*
 * @Author:
 * @Date: 2024-12-17 08:58:06
 * @LastEditTime: 2025-01-17 15:45:11
 * @Description:
 */

#include "utils.h"
#include "typedef.h"
#include "stdio.h"

static const char hex_tab[] = "0123456789ABCDEF";

/// @brief 计算8位校验和, 累加所有字节
/// @param dat
/// @param size
/// @return
uint8_t cal_checksum8(const void *dat, uint16_t size)
{
    uint8_t        ret = 0;
    const uint8_t *p   = dat;
    while(size != 0)
    {
        ret += *p;
        p++, size--;
    }
    return ret;
}

/// @brief 从小端获取一个uint16数据
/// @param p
/// @return
uint16_t get_lsbdata16(const uint8_t *p)
{
    return ((uint16_t)p[1] << 8) + (uint16_t)p[0];
}

/// @brief 从大端获取一个uint16数据
/// @param p
/// @return
uint16_t get_msbdata16(const uint8_t *p)
{
    return ((uint16_t)p[0] << 8) + (uint16_t)p[1];
}

/// @brief 填充一个uint16数据到大端缓冲
/// @param p
/// @param dat
void set_msbdata16(uint8_t *p, uint16_t dat)
{
    p[0] = (uint8_t)(dat >> 8);
    p[1] = (uint8_t)dat;
}

/// @brief 填充一个uint16数据到大端缓冲
/// @param p
/// @param dat
void set_lsbdata16(uint8_t *p, uint16_t dat)
{
    uint8_t i;
    for(i = 0; i < 2; i++)
    {
        p[i] = (uint8_t)dat;
        dat >>= 8;
    }
}

/// @brief 获取一个uint32数据，从小端缓冲中
uint32_t get_lsbdata32(const uint8_t *p)
{
    int8_t   i;
    uint32_t dat = 0;
    for(i = 3; i >= 0; i--)
    {
        dat <<= 8;
        dat += p[i];
    }
    return dat;
}

/// @brief 填充一个uint32数据到小端缓冲
void set_lsbdata32(uint8_t *p, uint32_t dat)
{
    uint8_t i;
    for(i = 0; i < 4; i++)
    {
        p[i] = (uint8_t)dat;
        dat >>= 8;
    }
}

/// @brief 填充一个uint32数据到大端缓冲
/// @param p-大端缓冲地址，dat-待填数据
/// @param dat
void set_msbdata32(uint8_t *p, uint32_t dat)
{
    int8_t i;
    for(i = 3; i >= 0; i--)
    {
        p[i] = (uint8_t)dat;
        dat >>= 8;
    }
}

/// @brief 从大端缓冲获取一个uint32数据
/// @param p 缓冲区指针
/// @return
uint32_t get_msbdata32(const uint8_t *p)
{
    uint8_t  i;
    uint32_t dat = 0;
    for(i = 0; i < 4; i++)
    {
        dat <<= 8;
        dat += p[i];
    }
    return dat;
}

/// @brief 从小端缓冲获取一个float数据
float get_lsbfloat32(const uint8_t *p)
{
    uint32_t dat = get_lsbdata32(p);
    return *(float *)&dat;
}

/// @brief 填充一个float数据到小端缓冲
void set_lsbfloat32(uint8_t *p, float dat)
{
    set_lsbdata32(p, *(uint32_t *)&dat);
}

/// @brief 从大端缓冲获取一个float数据
float get_msbfloat32(const uint8_t *p)
{
    uint32_t dat = get_msbdata32(p);
    return *(float *)&dat;
}
/// @brief 填充一个float数据到大端缓冲
void set_msbfloat32(uint8_t *p, float dat)
{
    set_msbdata32(p, *(uint32_t *)&dat);
}

/// @brief 缓存区的bcd数据按字节转换成十六进制数据 lsb
/// @param buf 缓存区地址
/// @param bcd bcd数据地址
/// @param len 数据长度
void bcd_to_lsbhex(uint8_t *buf, const uint8_t *bcd, uint16_t len)
{
    uint16_t i;
    for(i = 0; i < len; i++) { buf[i] = bcdtob(bcd[i]); }
}

/// @brief 缓存区的bcd数据按字节转换成十六进制数据 msb
/// @param buf 缓存区地址
/// @param bcd bcd数据地址
/// @param len 数据长度
void bcd_to_msbhex(uint8_t *buf, const uint8_t *bcd, uint16_t len)
{
    uint16_t i;
    uint8_t *buf_end = buf + len - 1;

    for(i = 0; i < len; i++) { *buf_end-- = bcdtob(bcd[i]); }
}

/// @brief 缓存区的hex数据按字节转换成bcd数据，MSB
/// @param buf 缓存区地址
/// @param bcd bcd数据地址
/// @param len 数据长度
void hex_to_msbbcd(uint8_t *bcd, const uint8_t *buff, uint16_t len)
{
    uint16_t i;
    uint8_t *bcd_end = bcd + len - 1;

    for(i = 0; i < len; i++) { *bcd_end-- = btobcd(buff[i]); }
}

/// @brief 缓存区的hex数据按字节转换成bcd数据，LSB
/// @param buf 缓存区地址
/// @param bcd bcd数据地址
/// @param len 数据长度
void hex_to_lsbbcd(uint8_t *bcd, const uint8_t *buff, uint16_t len)
{
    uint16_t i;
    uint8_t *bcd_end = bcd;

    for(i = 0; i < len; i++) { *bcd_end++ = btobcd(buff[i]); }
}

/// @brief 转换数据为BCD编码到小端缓冲中
/// @param bcd   BCD缓冲
/// @param hex32 待转换数据
/// @param num   转换成BCD字节数
void uint32_to_lsbbcd(uint8_t *bcd, uint32_t hex32, uint8_t num)
{
    while(num != 0)
    {
        *bcd  = btobcd((uint8_t)(hex32 % 100));
        hex32 = hex32 / 100;
        bcd++, num--;
    }
}

/// @brief 转换数据为BCD编码到小端缓冲中,最高位符号位
/// @param bcd   BCD缓冲
/// @param hex32 待转换数据
/// @param num   转换成BCD字节数
void int32_to_lsbbcd(uint8_t *bcd, int32_t hex32, uint8_t num)
{
    uint8_t sign = 0;
    if(hex32 < 0)
    {
        sign  = 0x80;
        hex32 = -hex32;
    }
    while(num != 0)
    {
        *bcd  = btobcd((uint8_t)(hex32 % 100));
        hex32 = hex32 / 100;
        bcd++, num--;
    }
    if(sign != 0) { *(bcd - 1) |= sign; }
}

/// @brief 转换数据为BCD编码到大端缓冲中
/// @param bcd   BCD缓冲
/// @param hex32 待转换数据
/// @param num   转换成BCD字节数
void uint32_to_msbbcd(uint8_t *bcd, uint32_t hex32, uint8_t num)
{
    while(num != 0)
    {
        num--;
        bcd[num] = btobcd((uint8_t)(hex32 % 100));
        hex32    = hex32 / 100;
    }
}

/// @brief 转换小端缓冲中BCD编码为长整型
/// @param bcd   BCD缓冲
/// @param num   转换成BCD字节数
/// @return      长整型
uint32_t lsbbcd_to_hex32(const uint8_t *bcd, uint8_t num)
{
    uint32_t hex32 = 0;
    while(num != 0)
    {
        num--;
        hex32 = (hex32 * 100) + bcdtob(bcd[num]);
    }
    return hex32;
}

/// @brief  转换大端缓冲中BCD编码为长整型
/// @param bcd   BCD缓冲
/// @param num   转换成BCD字节数
/// @return      长整型
uint32_t msbbcd_to_hex32(const uint8_t *bcd, uint8_t num)
{
    uint32_t hex32 = 0;
    while(num != 0)
    {
        hex32 = (hex32 * 100) + bcdtob(*bcd);
        bcd++, num--;
    }
    return hex32;
}

/// @brief 转换数据为字符数据(不足部分前面补零)
/// @param str   字符缓冲
/// @param val   原始数据
/// @param size  字符串期望长度
void uint32_to_charstr(char *str, uint32_t val, uint8_t size)
{
    char temp[11];
    
    // 将数值转成字符串
    int actual_len = snprintf(temp, sizeof(temp), "%lu", (unsigned long)val);
    
    if(actual_len >= size)
    {
        memcpy(str, temp, size);
    }
    else
    {
        // 需要补0
        uint8_t zeros = size - actual_len;
        memset(str, 0x30, size);
        memcpy(&str[zeros], temp, actual_len);
    }
}

/// @brief 转换字符数据为整数
/// @param str   字符缓冲
/// @param size  字符串长度
uint32_t charstr_to_uint32(char *str, uint8_t size)
{
    uint32_t val = 0;

    for (uint8_t i = 0; i < size - 1; i++)
    {
        val = (val + (str[i] - 0x30)) * 10;
    }
    val += (str[size - 1] - 0x30);

    return val;
}

/// @brief 转换字符数据为大端BCD数组
/// @param str   字符缓冲
/// @param len   字符串长度
/// @param bcd   缓存区地址
/// @return      缓存区长度
uint8_t charstr_to_msbbcd(char *str, uint8_t len ,uint8_t *bcd)
{
    uint8_t j = 0;
    for (uint8_t i = 0; i < len; i += 2)
    {
        bcd[j] = ((str[i] - 0x30) << 4) | (str[i + 1] - 0x30);
        j++;
    }
    return j;
}

/// @brief 计算10的x次方
/// @param x
/// @return
double pow10f(int8_t x)
{
    double  result = 1.0;
    uint8_t tmp    = abs(x);
    if(x > 0)
    {
        for(uint8_t i = 0; i < tmp; i++) { result = result * 10.0; }
    }
    else
    {
        for(uint8_t i = 0; i < tmp; i++) { result = result / 10.0; }
    }

    return result;
}

/// @brief 把一个十六进制字节转换成两个可视字符
/// @param str  字符缓冲
/// @param hex  hex数据
static void hex_2char(char *str, uint8_t hex)
{
    str[0] = hex_tab[hex >> 4];
    str[1] = hex_tab[hex & 0x0F];
}

/// @brief hex数据转换为字符数据
/// @param str   字符缓冲
/// @param hex   hex数据
/// @param size  数据长度
/// @return     转换后数据长度
uint16_t hexstr_to_charstr(char *str, const uint8_t *hex, uint32_t size)
{
    uint32_t i = 0;
    while(size != 0)
    {
        hex_2char(str, hex[i++]);
        str += 2, size--;
    }
    return i * 2;
}

/// @brief 将字符串转换为十六进制数组
/// @param hex  十六进制数组缓冲
/// @param str  字符串
static uint8_t char_2hex(uint8_t *hex, const char *str)
{
    uint8_t i, ret, tmp = 0;
    for(i = 0, ret = 2; i < 2; i++)
    {
        uint8_t ch = str[i];
        tmp <<= 4;
        if(ch >= '0' && ch <= '9') { tmp += (ch - '0'); }
        else if(ch >= 'A' && ch <= 'F') { tmp += (ch - 'A' + 10); }
        else if(ch >= 'a' && ch <= 'f') { tmp += (ch - 'a' + 10); }
        else { return ret; }
        ret--;
    }
    *hex = tmp;
    return 0;
}

/// @brief 将字符串转换为十六进制数组
/// @param hex  十六进制数组缓冲
/// @param str  字符串
/// @param size 十六进制数组缓冲大小
/// @return 实际转换的字节数
uint16_t string_to_hexarray(uint8_t *hex, const char *str, uint16_t size)
{
    uint16_t i = 0;
    while(size > 1)
    {
        char_2hex(&hex[i++], str);
        str += 2, size -= 2;
    }
    return i;
}

/// @brief 字符串反序
/// @param str  字符串缓冲
/// @param size 字符串长度
void string_reverse(void *str, uint32_t size)
{
    char  tmp;
    char *hdr = (char *)str;
    char *ptr = hdr + size - 1;    ///< 指向未尾

    for(size >>= 1; size != 0; size--)
    {
        tmp  = *hdr;
        *hdr = *ptr;
        *ptr = tmp;
        hdr++, ptr--;
    }
}

/// @brief 字符串反序拷贝
/// @param des  目的缓冲
/// @param src  源缓冲
/// @param size 长度
void strcpy_reverse(void *des, const void *src, uint32_t size)
{
    char *d = (char *)des;
    char *s = (char *)src + size - 1;    // 指向未尾
    while(size-- != 0) { *d++ = *s--; }
}

/// @brief 字节反序
/// @param c 字节数据
/// @return 反序后字节数据
uint8_t byte_reverse(uint8_t c)
{
    c = (c & 0xaa) >> 1 | (c & 0x55) << 1;
    c = (c & 0xcc) >> 2 | (c & 0x33) << 2;
    c = (c & 0xf0) >> 4 | (c & 0x0f) << 4;
    return c;
}

uint32_t json_get_value(const char *json, const char *key, char *value, bool need_hex)
{
    char *ptr_start;
    char *ptr_end;
    char  number_buf[16];
    char  key_buf[64];

    snprintf(key_buf, sizeof(key_buf), "\"%s\":\"", key);
    ptr_start = strstr(json, key_buf);
    if(!ptr_start) return 0;

    ptr_start += strlen(key_buf);
    ptr_end = strchr(ptr_start, '"');
    if(!ptr_end) return 0;

    size_t len = ptr_end - ptr_start;
    if(need_hex)
    {
        memcpy(value, ptr_start, len);
        value[len] = '\0';
        return 0;
    }

    if(len >= sizeof(number_buf)) return 0;

    memcpy(number_buf, ptr_start, len);
    number_buf[len] = '\0';
    return (uint32_t)atoi(number_buf);
}

// 判断闰年
static uint8_t is_leap_year(uint32_t year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

#define YEAR 0
#define MONTH 1
#define DAY 2
#define HOUR 3
#define MINUTE 4
#define SECOND 5

uint32_t calculate_timestamp(uint8_t *dt)
{
    uint8_t  days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    uint32_t total_seconds   = 0;

    if(dt[YEAR] > 99 || dt[MONTH] < 1 || dt[MONTH] > 12 || dt[DAY] < 1 || dt[DAY] > 31 || dt[HOUR] > 23 || dt[MINUTE] > 59 || dt[SECOND] > 59)
    {
        return 0;    // 返回 0 表示输入错误
    }

    uint32_t year = dt[YEAR] + 2000;

    for(uint32_t y = 1970; y < year; y++) { total_seconds += (is_leap_year(y) ? 366 : 365) * 24 * 3600; }

    if(is_leap_year(year)) { days_in_month[1] = 29; }

    for(uint32_t m = 0; m < dt[MONTH] - 1; m++) { total_seconds += days_in_month[m] * 24 * 3600; }

    total_seconds += (dt[DAY] - 1) * 24 * 3600;

    total_seconds += dt[HOUR] * 3600 + dt[MINUTE] * 60 + dt[SECOND];

    total_seconds -= 8 * 60 * 60;

    return total_seconds;
}
