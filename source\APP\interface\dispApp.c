/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      disApp.c
 *    Describe:      显示服务模块
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#include "app.h"
#include "dispApp.h"
#include "bsp_cfg.h"
#include "utils.h"
#include "math.h"
#include "api.h"
#include "relayApp.h"
#include "status.h"
#include "display_item.h"
#include "display_table.h"
#include "datastore.h"
#include "crc.h"
#if SW_PAYMENT_EN
#include "tariff.h"
#include "step_tariff.h"
#endif
#include "mic.h"
#include "A_XDR.h"
#include "comm_phy.h"
#include "key.h"

/* Private define ------------------------------------------------------------*/
#if PAY_CURRENCY_IS_ENERGY
#define TYPE_ICON_MONEYTYPE TYPE_ICON_KWH
#else
#define TYPE_ICON_MONEYTYPE TYPE_ICON_YUAN
#endif

/* LCD显示数值相关的属性定义 */
typedef uint8_t DP_ATTR;

#define DP_LZ ((DP_ATTR)(1 << 0))    // 显示值支持前导0

/* Private macro ------------------------------------------------------------------------ */
#define DISPLAY_CRC16 0    /// 只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CAL_CS16(x, y) crc16(DISPLAY_CRC16, (uint8_t *)(x) + 4, y - 4)
#define CRC16_CHK(struct, len) STRUCT_CRC16_CHK(DISPLAY_CRC16, struct, len)
#define CRC16_CAL(struct, len) STRUCT_CRC16_GET(DISPLAY_CRC16, struct, len)

#define DISP_PARA_ADDR nvm_addr(NVM_DISPLAY_PARA)
#define DISP_LIST1_ADDR (DISP_PARA_ADDR + sizeof(disp_para_s))
#define DISP_LIST2_ADDR (DISP_LIST1_ADDR + sizeof(disp_list_s))
#define DISP_LIST3_ADDR (DISP_LIST2_ADDR + sizeof(disp_list_s))

#define disp_date(year, month, day) lcd.disp_date(year, month, day)
#define disp_time(hour, minute, second) lcd.disp_time(hour, minute, second)
/* Private typedef -----------------------------------------------------------*/
#if USE_LCD
typedef enum
{
    DISP_INS_NULL = 0,    // 数据不存在
    DISP_INS_OK,          //
    DISP_INS_VRR,         //
    DISP_INS_ERR_01,      //
    DISP_INS_RESET,       //
    DISP_INS_TEST,        //
    DISP_INS_NUM,         // 数量
} DISP_INS_TYPE;

typedef struct
{
    uint32_t bit_mask;    // bit_mask需要与tDispReqStatus位对应
    uint8_t  buf[LCD_MS_DIGITS];
} disp_message_s;

typedef struct
{
    uint8_t  idex;
    uint16_t state;                         /// 事件掩码
    bool (*const *func)(uint16_t state);    /// 对应模块事件状态获取
} disp_insmeg_s;

typedef enum
{
    DISP_FIRST_IDX = 0,    // 第一个显示项索引
    DISP_NEXT_IDX,         // 下一个显示项索引
    DISP_PRVS_IDX,         // 前一个显示项索引
    DISP_LAST_IDX,         // 最后一个显示项
    DISP_NULL_IDX,
} disp_idx_t;

typedef union
{
    struct
    {
        uint8_t refresh : 1;     // 显示刷新标志位
        uint8_t fault : 1;       // 电表故障标志
        uint8_t off_flag : 1;    // 息屏延时标识
    };
    uint8_t flag;
} disp_flag_s;

typedef struct
{
    disp_obj_s obj;
#if LCD_MULTI_LINE_DISPLAY
    disp_obj_s obj_p1;    // 副屏1显示对象
    disp_obj_s obj_p2;    // 副屏2显示对象
    disp_obj_s obj_p3;    // 副屏3显示对象
#endif
    uint8_t idx;    // 在显示列表(用户配置)中的索引} tDispIdx;
} disp_info_s;

typedef struct    /// 当前显示清单定义
{
    DISP_IDX_TYPE        num;    /// 索引个数
    const DISP_IDX_TYPE *ptr;    /// 索引指针
#if LCD_MULTI_LINE_DISPLAY
    const DISP_IDX_TYPE *ptr_p1;    // 副屏1索引指针
    const DISP_IDX_TYPE *ptr_p2;    // 副屏2索引指针
    const DISP_IDX_TYPE *ptr_p3;    // 副屏3索引指针
#endif
} cur_diap_list_s;

typedef struct
{
    uint16_t cnt;    // 计数
} auto_disp_cnt_s, manual_disp_cnt_s;

/*键盘输入结构体*/
typedef struct
{
    uint16_t cnt;              // 时间计数
    uint8_t  len;              // 按键输入字符长度
    uint8_t  key_buf[24];      // 按键缓存
    uint8_t  token_buf[20];    // TOKEN缓存值
} keyin_disp_ctrl_s;

/*键盘输出结构体*/
typedef struct
{
    uint16_t      cnt;           /// 时间计数
    uint16_t      short_code;    /// 短码
    DISP_IDX_TYPE buf[4];        /// 短码显示总表索引缓存
} keyout_disp_ctrl_s;

typedef struct
{
    uint8_t page : 7;
    uint8_t req_flag : 1;
} multi_disp_ctrl_s;

typedef struct
{
    mess_mode_s mode;
    uint8_t     cnt;
    uint8_t     pos;
    uint8_t     idx_screen;
    uint8_t     max_screen;
    uint8_t     duration;
    uint8_t     txt[DISP_MSG_MAX_LEN];
    uint8_t     len;
} msg_disp_ctrl_s;

typedef struct    /// 锁屏模式定义
{
    disp_obj_s obj;    /// 当前锁屏显示对象
} lock_disp_ctrl_s;

typedef struct    /// 显示控制模式定义
{
    auto_disp_cnt_s    autos;     /// 自动轮显模式
    manual_disp_cnt_s  manual;    /// 按键轮显模式
    keyin_disp_ctrl_s  keyin;     /// 键盘输入模式
    keyout_disp_ctrl_s keyout;    /// 键盘输出模式
    multi_disp_ctrl_s  mutil;     /// 多屏显示模式
    msg_disp_ctrl_s    msg;       /// 消息模式
    lock_disp_ctrl_s   lock;      /// 锁定模式
} disp_ctrl_s;

typedef struct    /// 翻页定义
{
    uint8_t screen;       /// 当前屏数
    uint8_t turn_flag;    /// 翻屏标志
} disp_screen_s;

typedef struct    /// 显示上下文定义
{
    disp_mode_t mode;         /// 当前显示模式
    disp_mode_t last_mode;    /// 上一次显示模式
    disp_flag_s stus;         /// 显示状态标志
    disp_info_s info;         /// 当前有显示对象信息
    disp_idx_t  idxt;         /// 当前显示索引方向
} disp_context_s;

/* Private macro ------------------------------------------------------------------------ */

/* Private constants ---------------------------------------------------------*/
/// @brief  顺序必须与 DISP_INS_TYPE 一致 dis_ins_meg_list
const uint8_t dis_insmsg_tab[DISP_INS_NUM][LCD_MS_DIGITS] = {
    {
        "  NULL  ",
    },
    {"   OK   "},
    {"  VER   "},
    {" ERR_01 "},
    {" RESET  "},
    {"  TEST  "},
};

/// @brief 事件报警消息表 顺序必须与 disp_req_status_s 一致
const disp_message_s dis_evt_tab[] = {
    {lbitmask(0), "  OPEN  "},
    {lbitmask(2), "OVER  PO"},
};
/// @brief 消息模式显示消息表
static const disp_insmeg_s dis_ins_meg_list[] = {
    {.idex = DISP_INS_OK, .state = 0, .func = NULL},
};

static const uint8_t disp_data_ov[] = {' ', ' ', ' ', 'O', 'V', ' ', ' ', ' '};    // 数据溢出显示
#endif

static const ICON_TYPE_t icon_trans_w[] = {TYPE_ICON_W, TYPE_ICON_KW, TYPE_ICON_MW};

static const ICON_TYPE_t icon_trans_wh[] = {TYPE_ICON_WH, TYPE_ICON_KWH, TYPE_ICON_MWH};

static const ICON_TYPE_t icon_trans_var[] = {TYPE_ICON_VAR, TYPE_ICON_KVAR, TYPE_ICON_MVAR};

static const ICON_TYPE_t icon_trans_varh[] = {TYPE_ICON_VARH, TYPE_ICON_KVARH, TYPE_ICON_MVARH};

static const ICON_TYPE_t icon_trans_va[] = {TYPE_ICON_VA, TYPE_ICON_KVA, TYPE_ICON_MVA};

static const ICON_TYPE_t icon_trans_vah[] = {TYPE_ICON_VAH, TYPE_ICON_KVAH, TYPE_ICON_MVAH};

static const ICON_TYPE_t icon_trans_i[] = {TYPE_ICON_A, TYPE_ICON_KA};

static const ICON_TYPE_t icon_trans_v[] = {TYPE_ICON_V, TYPE_ICON_KV};

extern const disp_para_s  disp_default_para;         /// 默认参数
static const disp_para_s *disp_running_para;         /// 运行参数，指向codeflash
static uint8_t            pwron_all_disp_cnt = 5;    /// 上电全显计时器 默认5秒，5-30可设置
/*Private Variable-------------------------------------------------*/
#if USE_LCD
static cur_diap_list_s cur_disp_list;    /// 当前显示清单
static disp_ctrl_s     disp_control;
static disp_context_s  disp_context;
static uint8_t         disp_token_timeout = 0;
static DISP_STUS       disp_out_stus;
diap_pwrdwn_state_t    disp_pwdn_stus = PD_DISP_CLOSE;
#endif

/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/// @brief 参数计算检验后存入NVM中
/// @param ofst
/// @param val
/// @param len
/// @return
static bool disp_para_store(uint16_t ofst, const void *val, uint16_t len)
{
    disp_para_s para;
    if(ofst != 0) memcpy(&para, disp_running_para, sizeof(para));
    memcpy((uint8_t *)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(disp_para_s));
    disp_running_para = (const disp_para_s *)DISP_PARA_ADDR;
    return nvm.write((uint32_t)disp_running_para, &para, sizeof(para));
}

/// @brief 参数指针初始化并检验参数
/// @param
static void disp_para_load(void)
{
    disp_running_para = (const disp_para_s *)DISP_PARA_ADDR;
    if(CRC16_CHK(disp_running_para, sizeof(disp_para_s)) == false) { disp_running_para = &disp_default_para; }
}

/// @brief 捕获对象索引清单计算检验后存入NVM中
/// @param list 空指针将清零
/// @return
static bool disp_list_store(TYPE_DISP_LIST type, disp_list_s *list)
{
    uint32_t addr;
    switch(type)
    {
        case DISP_LIST1:
            addr = DISP_LIST1_ADDR;
            break;
        case DISP_LIST2:
            addr = DISP_LIST2_ADDR;
            break;
        case DISP_LIST3:
            addr = DISP_LIST3_ADDR;
            break;
        default:
            return false;
    }
    if(list != NULL) CRC16_CAL(list, sizeof(disp_list_s));
    return nvm.write(addr, list, sizeof(disp_list_s));
}

/// @brief 捕获对象索引清单指针初始化
/// @param
static const disp_list_s *disp_list_load(TYPE_DISP_LIST type)
{
    const disp_list_s *list;
    const uint32_t    *default_id;
    uint16_t           default_id_num;

    switch(type)
    {
        default:    // 默认自动list1
        case DISP_LIST1:
            list           = (const disp_list_s *)DISP_LIST1_ADDR;
            default_id     = disp_list1_id;
            default_id_num = disp_list1_id_num;
            break;
        case DISP_LIST2:
            list           = (const disp_list_s *)DISP_LIST2_ADDR;
            default_id     = disp_list2_id;
            default_id_num = disp_list2_id_num;
            break;
        case DISP_LIST3:
            list           = (const disp_list_s *)DISP_LIST3_ADDR;
            default_id     = disp_list3_id;
            default_id_num = disp_list3_id_num;
    }

    assert(default_id_num < DISP_ID_MAX_NUM);
    if(CRC16_CHK(list, sizeof(disp_list_s)) == FALSE)
    {
        disp_list_s tmp;
        tmp.num = 0;
        for(uint16_t i = 0; i < default_id_num; i++)
        {
            for(uint16_t j = 0; j < disp_total_table_num; j++)
            {
                if(default_id[i] == disp_total_table[j].id)
                {
                    tmp.buf[i] = j;    /// 从显示总表中找到索引
                    tmp.num++;
                    break;
                }
            }
        }
        assert(tmp.num == default_id_num);
        // tmp.num = default_id_num;
        disp_list_store(type, &tmp);
    }
    return list;
}

#if SW_PAYMENT_EN
/// @brief  获取总表中索引对应数据
/// @param id  - 数据项id
/// @return
static const disp_obj_s *disp_item_get(uint32_t id)
{
    uint16_t i;
    for(i = 0; i < disp_total_table_num; i++)
    {
        if(disp_total_table[i].id == id) return disp_total_table + i;
    }
    return NULL;
}
#endif

#if USE_LCD && USE_KEYBOARD
/// @brief 根据按键输入短码查找在短码显示总表中的索引
/// @param keyin 按键输入内容
/// @param buf   显示对象索引缓存
/// @return
static uint8_t disp_code_scan(const keyin_disp_ctrl_s *keyin, DISP_IDX_TYPE *buf)
{
    uint8_t num = 0;
    if(keyin->len != 3) return 0;
    for(DISP_IDX_TYPE i = 0; i < disp_total_table_num; i++)
    {
        if(!memcmp(keyin->key_buf, disp_total_table[i].code, 3))
        {
            if(++num > 4) break;
            *buf++ = i;
        }
    }
    return num;
}
#endif

#if USE_LCD
/// @brief  获取相应显示模式的显示列表
/// @param mode  - 显示模式
/// @return true - 获取显示列表成功，false - 获取显示列表失败
static bool disp_list_get(disp_mode_t mode)
{
    const disp_list_s *list;

    switch(mode)
    {
        case DISP_AUTO:
            list = disp_list_load(DISP_LIST1);
            break;

        case DISP_MANUAL:
            list = disp_list_load(DISP_LIST2);
            break;

        case DISP_PWDN:
            list = disp_list_load(DISP_LIST3);
            break;
        default:
            return false;
    }
    cur_disp_list.num = list->num;
    cur_disp_list.ptr = list->buf;

    if(cur_disp_list.num > DISP_ID_MAX_NUM)
    {
        cur_disp_list.num = DISP_ID_MAX_NUM;    // 防止越界
    }
    return true;
}

/// @brief  短码显示
/// @param code - 短码
#if USE_KEYBOARD
void disp_short_code(uint16_t code)
{
    uint8_t str[4];

    if(code == 0xFFFF) return;

    sprintf((char *)str, "%03d", code);
    lcd.disp_string(LCD_PS, LCD_PS_CODE_ADDR, str, 3);
}
#endif
/// @brief  判断翻屏是否完成
/// @param
/// @return
static bool disp_is_subscreen_turn(void)
{
    if(disp_control.mutil.req_flag == true)
    {
        disp_control.mutil.page++;
        return true;
    }
    disp_control.mutil.page = 0;
    return false;
}

/// @brief 获取当前屏数
/// @param num
/// @return
static uint8_t disp_screen_num_get(uint8_t num)
{
    if(num == 0) disp_control.mutil.page = 0;
    if(disp_control.mutil.page == num) { disp_control.mutil.req_flag = false; }
    else { disp_control.mutil.req_flag = true; }
    return disp_control.mutil.page;
}

/// @brief 判断短码显示是否完成
/// @param index
/// @param max_index
/// @return
static bool disp_is_screen_turn(uint8_t *index, uint8_t max_index)
{
    if(*index < (max_index - 1)) return true;
    return false;
}

/// @brief 判断消息显示是否完成
/// @param index
/// @param max_index
/// @return
static bool disp_is_msgscreen_turn(uint8_t *index, uint8_t max_index)
{
    if(*index < max_index)
    {
        (*index)++;
        return true;
    }
    return false;
}

/// @brief  获取显示列表中的对应索引的显示项
/// @param type   - 显示项索引类型
static void disp_obj_info_get(disp_idx_t type)
{
    disp_info_s *p      = &disp_context.info;
    uint16_t     id_num = cur_disp_list.num;

    switch(type)
    {
        case DISP_FIRST_IDX:
            p->idx = 0;
            break;
        case DISP_LAST_IDX:
            p->idx = id_num - 1;
            break;
        case DISP_NEXT_IDX:
            p->idx = (p->idx + 1) % id_num;
            break;
        case DISP_PRVS_IDX:
            p->idx = (p->idx + id_num - 1) % id_num;
            break;
        default:
            return;
    }
#if USE_KEYBOARD
    if(disp_context.mode == DISP_KEYOUT)
    {
        p->obj.id     = disp_total_table[cur_disp_list.ptr[p->idx]].id;
        p->obj.format = disp_total_table[cur_disp_list.ptr[p->idx]].format;
        return;
    }
#endif
    p->obj = disp_total_table[cur_disp_list.ptr[p->idx]];
}

/// @brief  12字节时间显示 ，主要用于需量等显示
/// @param year
/// @param month
/// @param day
/// @param hour
/// @param minute
/// @param second
static void disp_date_time(uint8_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second)
{
    uint8_t screen = disp_screen_num_get(1);

    if(screen == 1) { disp_time(hour, minute, second); }
    else { disp_date(year, month, day); }
}

/// @brief 无效时间显示，主要用于需量等显示
/// @param
static void disp_invalid_date_time(void)
{
    disp_date_time(0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF);
}

/// @brief 有符号数显示, 溢出时，累积量取余，瞬时量取高位
/// @param val    数据
/// @param scaler 换算因子
/// @param dot    小数位数
/// @param icon   单位
/// @param attr   显示数值在LCD上显示体现的相关属性
static void disp_integer(int64_t val, int8_t scaler, uint8_t dot, ICON_TYPE_t icon, DP_ATTR attr)
{
    uint64_t max_val  = DISP_MAX_VAL;
    uint8_t  leadzero = (attr & DP_LZ) ? 1 : 0;
#if DISP_AUTO_UNIT_EN
    uint8_t dot_bak;    // 备份小数位数
#endif
    bool flg_unit;    // 是否单位转换标志

    /* 数值量纲转换 */
    scaler += dot;
    if(scaler > 0) { val *= pow10f(scaler); }
    else if(scaler < 0) { val /= pow10f(-scaler); }

    switch(icon)    // 切换显示单位
    {
#if !DISP_AUTO_UNIT_EN
        case TYPE_ICON_KWH:
        case TYPE_ICON_KVARH:
        case TYPE_ICON_KVAH: {
            val = val % (DISP_MAX_VAL + 1);
        }
        break;
#endif
        default: {
            /* 数值大小溢出最大显示值 */
#if !LCD_MS_MINUS_SIGN
            if(val < 0) { max_val = max_val / 10; }
#endif
#if DISP_AUTO_UNIT_EN
            dot_bak = dot;    // 备份小数位数
#endif
            while(labs(val) > max_val)
            {
                if(dot > 0)
                {
                    val /= 10;
                    dot--;
                }
#if DISP_AUTO_UNIT_EN
                else
                {
                    dot      = dot_bak;
                    flg_unit = true;
                    switch(icon)    // 切换显示单位
                    {
                        case TYPE_ICON_WH:
                            icon = TYPE_ICON_KWH;
                            break;
                        case TYPE_ICON_KWH:
                            icon = TYPE_ICON_MWH;
                            break;

                        case TYPE_ICON_VARH:
                            icon = TYPE_ICON_KVARH;
                            break;
                        case TYPE_ICON_KVARH:
                            icon = TYPE_ICON_MVARH;
                            break;

                        case TYPE_ICON_VAH:
                            icon = TYPE_ICON_KVAH;
                            break;
                        case TYPE_ICON_KVAH:
                            icon = TYPE_ICON_MVAH;
                            break;

                        case TYPE_ICON_W:
                            icon = TYPE_ICON_KW;
                            break;
                        case TYPE_ICON_KW:
                            icon = TYPE_ICON_MW;
                            break;

                        case TYPE_ICON_VAR:
                            icon = TYPE_ICON_KVAR;
                            break;
                        case TYPE_ICON_KVAR:
                            icon = TYPE_ICON_MVAR;
                            break;

                        case TYPE_ICON_VA:
                            icon = TYPE_ICON_KVA;
                            break;
                        case TYPE_ICON_KVA:
                            icon = TYPE_ICON_MVA;
                            break;

                        case TYPE_ICON_V:
                            icon = TYPE_ICON_KV;
                            break;
#if LCD_MULTI_UNIT_DISPLAY
                        case TYPE_ICON_V1:
                            icon = TYPE_ICON_KV1;
                            break;
                        case TYPE_ICON_V2:
                            icon = TYPE_ICON_KV2;
                            break;
                        case TYPE_ICON_V3:
                            icon = TYPE_ICON_KV3;
                            break;

                        case TYPE_ICON_A:
                            icon = TYPE_ICON_KA;
                            break;
                        case TYPE_ICON_A1:
                            icon = TYPE_ICON_KA1;
                            break;
                        case TYPE_ICON_A2:
                            icon = TYPE_ICON_KA2;
                            break;
                        case TYPE_ICON_A3:
                            icon = TYPE_ICON_KA3;
                            break;

                        case TYPE_ICON_MWH:
                        case TYPE_ICON_MVAH:
                        case TYPE_ICON_MVARH:    // 此时溢出则取余显示，即归零显示
                            val      = val % (max_val + 1);
                            flg_unit = false;
                            break;
#endif
                        case TYPE_ICON_KV:
#if LCD_MULTI_UNIT_DISPLAY
                        case TYPE_ICON_KV1:
                        case TYPE_ICON_KV2:
                        case TYPE_ICON_KV3:
#endif
                        case TYPE_ICON_KA:
#if LCD_MULTI_UNIT_DISPLAY
                        case TYPE_ICON_KA1:
                        case TYPE_ICON_KA2:
                        case TYPE_ICON_KA3:
#endif
                        case TYPE_ICON_MW:
                        case TYPE_ICON_MVA:
                        case TYPE_ICON_MVAR:    // 瞬时量
                        default:
                            val /= 10;    // 小数位数为0时，直接除10
                            dot      = 0;
                            flg_unit = false;
                            break;
                    }
                    if(flg_unit)
                    {
                        if(dot > 3) { val *= pow10f(dot - 3); }
                        else if(dot < 3) { val /= pow10f(3 - dot); }
                    }
                }
#else
                else { val /= 10; }    // 小数位数为0时，直接除10
#endif
            }
        }
    }

    lcd.disp_digit(LCD_MS, (int32)val, LCD_MS_DIGITS, dot, leadzero);
    lcd.icon_light(icon, 1);
}

/// @brief 浮点数显示
/// @param val    数据
/// @param scaler 换算因子
/// @param dot    小数位数
/// @param icon   单位
/// @param attr   显示数值在LCD上显示体现的相关属性
static void disp_float(float val, int8_t scaler, uint8_t dot, ICON_TYPE_t icon, DP_ATTR attr)
{
    /* 转换scaler+dot=0，再调用dispInteger接口 */
    int64_t tmp;
    int8_t  ofst_sclaler = scaler + dot;
    if(ofst_sclaler > 0) { tmp = (int64_t)(val * pow10f(ofst_sclaler)); }
    else if(ofst_sclaler < 0) { tmp = (int64_t)(val / pow10f(-ofst_sclaler)); }
    else { tmp = (int64_t)val; }

    disp_integer(tmp, -dot, dot, icon, attr);
}

/// @brief  HEX 16进制字符串数据显示
/// @param pos  - 显示位置
/// @param pdat - 数据
/// @param len  - 数据长度
static void disp_char_string(uint8_t pos, uint8_t *pdat, uint8_t len)
{
    if(len > LCD_MS_DIGITS)
    {
        uint8_t *ptr, page;

        if(len % LCD_MS_DIGITS) { page = disp_screen_num_get(len / LCD_MS_DIGITS); }
        else { page = disp_screen_num_get((len - 1) / LCD_MS_DIGITS); }
        ptr = (uint8_t *)pdat + page * LCD_MS_DIGITS;
        if(disp_control.mutil.req_flag) { lcd.disp_string(LCD_MS, pos, ptr, LCD_MS_DIGITS); }
        else
        {
            len %= LCD_MS_DIGITS;
            if(len == 0) { lcd.disp_string(LCD_MS, pos, ptr, LCD_MS_DIGITS); }
            else { lcd.disp_string(LCD_MS, LCD_MS_DIGITS - len, ptr, len); }
        }
    }
    else { lcd.disp_string(LCD_MS, LCD_MS_DIGITS - len, (uint8_t *)pdat, len); }
}

/// @brief 字符串数据显示 ascii
/// @param pos  - 显示位置
/// @param pdat - 数据
/// @param len  - 数据长度
static void disp_hex_string(uint8_t pos, uint8_t *pdat, uint8_t len)
{
    uint8_t buf[100];
    len = hexstr_to_charstr((char *)buf, pdat, len);
    disp_char_string(pos, buf, len);
}

/// @brief 消息显示
/// @param mode - 显示模式
/// @param pos  - 显示位置
/// @param txt  - 数据
/// @param len  - 数据长度
/// @param duration - 持续时间
static void disp_message(mess_mode_s mode, uint8_t pos, const uint8_t *txt, uint8_t len, uint16_t duration)
{
    disp_context.last_mode      = disp_context.mode;
    disp_context.mode           = DISP_MESSAGE;
    disp_control.msg.mode       = mode;
    disp_control.msg.pos        = pos;
    len                         = min(DISP_MSG_MAX_LEN, len);
    disp_control.msg.idx_screen = 0;
    disp_control.msg.max_screen = len / LCD_MS_DIGITS - 1;
    disp_control.msg.len        = len;
    memcpy(disp_control.msg.txt, txt, len);
    disp_control.msg.duration = duration;
    disp_control.msg.cnt      = 0;
    disp_screen_num_get(0);
}
#if (LCD_PS_DIGITS >= 8)
/// @brief 副屏显示OAD及屏数
/// @param oad
/// @param idx
static void oad_display(uint32_t oad, uint8_t idx)
{
    uint8_t str[LCD_PS_DIGITS];
    uint8_t len = 0;
#if LCD_PS_DIGITS >= 8
    len += sprintf((char *)str, "%08x", oad);
#endif
#if LCD_PS_DIGITS >= 10
    len += sprintf((char *)str + 8, "%02d", idx);
#endif
#if LCD_PS_DIGITS >= 8
    lcd.disp_string(LCD_PS, LCD_PS_CODE_ADDR, str, len);
#endif
}

/// @brief 副屏滚动显示
/// @param oad
static void oad_scrolling_display(char *oad)
{
    static char *start_pos     = NULL;    // 开始显示的位置
    static char *last_obiscode = NULL;

    uint8_t cur_digit_num = 0;    // 当次要显示的8的个数
    uint8_t cur_len       = 0;    // 当次要显示的所需的长度
    char   *ptr;
    char    text[2 * LCD_PS_DIGITS - 1];
    bool    not_first_page;

    if(oad != last_obiscode) start_pos = oad;    // obis切换则从起始位置开始

    not_first_page = ((start_pos != oad) ? 1 : 0);

    ptr = start_pos;
    while(*ptr != '\0' && cur_digit_num < LCD_PS_DIGITS)    // 根据副屏8个数确定当次要显示的长度
    {
        if(*ptr != '.') cur_digit_num++;
        ptr++;
    }
    cur_len = ptr - start_pos;

    if(start_pos + cur_len >= oad + strlen(oad))    // 剩余内容一屏可以显示完成
    {
        if(not_first_page)
        {
            sprintf(text, "%s", start_pos);
            start_pos = oad;
        }
        else { memmove(text, oad, cur_len); }
    }
    else
    {
        memmove(text, start_pos, cur_len - 1);
        text[cur_len - 1] = '\0';
        start_pos += (cur_len - 1);
    }
    lcd.disp_string(LCD_PS, LCD_PS_CODE_ADDR, (uint8_t *)(text), cur_len);    // 显示OBIS
    last_obiscode = oad;
}
#endif

static ICON_TYPE_t disp_unit_transfer(class_unit_t unit)
{
    switch(unit)
    {
        case UNIT_YEAR:
        case UNIT_MONTH:
        case UNIT_WEEK:
        case UNIT_DAY:
        case UNIT_HOUR:
        case UNIT_MINUTE:
        case UNIT_SECOND:
            return TYPE_ICON_NULL;
        case UNIT_ANGLE:
            return TYPE_ICON_NULL;
        case UNIT_TEMP:
            return TYPE_ICON_TEMP;
        case UNIT_CURRENCY:
            return TYPE_ICON_MONEYTYPE;
        case UNIT_m3:
            return TYPE_ICON_NULL;
        case UNIT_W:
            return TYPE_ICON_W;
        case UNIT_kW:
            return TYPE_ICON_KW;
        case UNIT_VA:
            return TYPE_ICON_VA;
        case UNIT_kVA:
            return TYPE_ICON_KVA;
        case UNIT_var:
            return TYPE_ICON_VAR;
        case UNIT_kvar:
            return TYPE_ICON_KVAR;
        case UNIT_kWh:
            return TYPE_ICON_KWH;
        case UNIT_kVAh:
            return TYPE_ICON_KVAH;
        case UNIT_kvarh:
            return TYPE_ICON_KVARH;
        case UNIT_A:
            return TYPE_ICON_A;
        case UNIT_V:
            return TYPE_ICON_V;
        case UNIT_Hz:
            return TYPE_ICON_Hz;
        case UNIT_Pf:
            return TYPE_ICON_PF;
        case UNIT_100:
            return TYPE_ICON_PERCENT;
        default:    // 其他单位
            return TYPE_ICON_NULL;
            break;
    }
}

/// @brief  数据显示
/// @param obj - 显示对象
static void disp_obj_show(const disp_obj_s *obj)
{
    disp_obj_api_s obj_api;
    uint8_t       *buf;
    ICON_TYPE_t    unit_icon;
#if DISP_PRIMARY_SIDE
    pt_ct_type_t pt_ct = PT_CT_TYPE_NONE;
#endif
    DP_ATTR attr = 0;
    uint8_t dot  = DF_DECIMAL_0;    // 小数点位置
    uint8_t len, data_type;
    uint8_t df;

    if(obj == NULL) goto Data_Err;
    if(obj->id == ID_LCD_ALL) goto LCD_Check;
    df = obj->df;
    if(df == DF_UNDEFINED) goto Data_Err;

#if LCD_MULTI_LINE_DISPLAY
    if(obj->info != NULL)    // 显示字符
    {
        char   *p = (char *)obj->info;
        uint8_t len;
        len = strlen(p);
        if(len > LCD_MS_DIGITS) { len = LCD_MS_DIGITS; }    //
        lcd.disp_string(LCD_MS, 0, (uint8_t *)p, len);      // 显示字符串
        return;
    }
#endif

    // 根据ID从接口类获取数据
    obj_api.sign = 0;
    if(!api.data_get(obj->id, &obj_api)) goto Data_Err;
    data_type = obj_api.data[0];
    len       = a_xdr_de_data(obj_api.data, obj_api.data_len);    // 解XDR编码
    if(len == 0) goto Data_Err;
    buf = obj_api.data;

    unit_icon = disp_unit_transfer((class_unit_t)obj_api.unit);    // 转化单位
    switch(df)
    {
        case DF_DECIMAL_0:    // 整数显示
        case DF_DECIMAL_1:
        case DF_DECIMAL_2:
        case DF_DECIMAL_3:
        case DF_DECIMAL_4:
            dot = df;
            break;
        case DF_ENERGY:    // 能量显示
            if(obj_api.unit == UNIT_kVAh)
            {
                unit_icon = icon_trans_vah[disp_running_para->spe.spe_format.energy_dimen];    //
            }
            else if(obj_api.unit == UNIT_kvarh)
            {
                unit_icon = icon_trans_varh[disp_running_para->spe.spe_format.energy_dimen];    //
            }
            else
            {
                unit_icon = icon_trans_wh[disp_running_para->spe.spe_format.energy_dimen];    //
            }
            obj_api.scaler -= 3 * ((int8_t)disp_running_para->spe.spe_format.energy_dimen - 1);
            attr |= disp_running_para->spe.spe_format.energy_lz ? DP_LZ : 0;
            if(disp_running_para->spe.spe_format.energy_decimal <= 4) dot = DF_DECIMAL_0 + (disp_running_para->spe.spe_format.energy_decimal);
#if DISP_PRIMARY_SIDE
            pt_ct = PT_CT_TYPE_PT_CT;
#endif
            break;

        case DF_POWER:    // 功率显示
            if(obj_api.unit == UNIT_VA)
            {
                unit_icon = icon_trans_va[disp_running_para->spe.spe_format.power_dimen];    // 转化单位
            }
            else if(obj_api.unit == UNIT_var)
            {
                unit_icon = icon_trans_var[disp_running_para->spe.spe_format.power_dimen];    // 转化单位
            }
            else
            {
                unit_icon = icon_trans_w[disp_running_para->spe.spe_format.power_dimen];    // 转化单位
            }
            obj_api.scaler -= 3 * disp_running_para->spe.spe_format.power_dimen;                                                                // 移动小数点
            attr |= disp_running_para->spe.spe_format.power_lz ? DP_LZ : 0;                                                                     // 前导零显示
            if(disp_running_para->spe.spe_format.power_decimal <= 4) dot = DF_DECIMAL_0 + (disp_running_para->spe.spe_format.power_decimal);    // 覆盖原对象的小数点显示属性
#if DISP_PRIMARY_SIDE
            pt_ct = PT_CT_TYPE_PT_CT;
#endif
            break;
        case DF_CURRENT:    // 电流显示
            unit_icon = icon_trans_i[disp_running_para->spe.spe_format.current_dimen];
            obj_api.scaler -= 3 * disp_running_para->spe.spe_format.current_dimen;
            attr |= disp_running_para->spe.spe_format.current_lz ? DP_LZ : 0;
            if(disp_running_para->spe.spe_format.current_decimal <= 4) dot = DF_DECIMAL_0 + (disp_running_para->spe.spe_format.current_decimal);
#if DISP_PRIMARY_SIDE
            pt_ct = PT_CT_TYPE_CT;
#endif
            break;
        case DF_VOLTAGE:    // 电压显示
            unit_icon = icon_trans_v[disp_running_para->spe.spe_format.voltage_dimen];
            obj_api.scaler -= 3 * disp_running_para->spe.spe_format.voltage_dimen;
            attr |= disp_running_para->spe.spe_format.voltage_lz ? DP_LZ : 0;
            if(disp_running_para->spe.spe_format.voltage_decimal <= 4) dot = DF_DECIMAL_0 + (disp_running_para->spe.spe_format.voltage_decimal);
#if DISP_PRIMARY_SIDE
            pt_ct = PT_CT_TYPE_PT;
#endif
            break;
        case DF_PF:
            unit_icon = TYPE_ICON_PF;
            dot       = DF_DECIMAL_4;    // 功率因数显示
            break;

        case DF_HEX_STRING:
            disp_hex_string(0, buf, len);
            return;

        case DF_CHR_STRING:
            disp_char_string(0, buf, len);
            return;

        case DF_DATE:                             // date
            disp_date(buf[0], buf[1], buf[2]);    // xx-xx-xx
            return;

        case DF_TIME:    // time
            disp_time(buf[0], buf[1], buf[2]);
            return;

        case DF_DATE_TIME:    // clock 主要用于需量显示的时钟显示
        {
            disp_date_time((uint8_t)(get_msbdata16(buf) % 100), buf[2], buf[3], buf[5], buf[6], buf[7]);
        }
            return;
        default:
            return;    // 不支持的显示格式
    }

    {
        float   f_tmp    = 0.0f;    // 浮点数显示
        int64_t tmp      = 0;
        bool    is_float = false;
        switch(data_type)
        {
            case DT_INTEGER_UNSIGNED:
            case DT_LONG_UNSIGNED:
            case DT_DOUBLE_LONG_UNSIGNED: {
                /* 根据数据长度获取具体数值大小 */
                if(len == 1)
                    tmp = *buf;
                else if(len == 2)
                    tmp = get_msbdata16(buf);
                else if(len == 4)
                    tmp = get_msbdata32(buf);
                else
                    goto Data_Err;
                // f_tmp = tmp * 1.0f;
                break;
            }

            case DT_INTEGER:
            case DT_LONG:
            case DT_DOUBLE_LONG: {
                /* 根据数据长度获取具体数值大小 */
                if(len == 1)
                    tmp = (int8_t)*buf;
                else if(len == 2)
                    tmp = (int16)get_msbdata16(buf);
                else if(len == 4)
                    tmp = (int32)get_msbdata32(buf);
                else
                    goto Data_Err;
                // f_tmp = tmp * 1.0f;
                break;
            }
            case DT_OCTET_STRING: {
                bool sign = false;
                if(obj_api.sign && (buf[len - 1] & 0x80))
                {
                    buf[len - 1] &= 0x7F;    ///< 去掉负号
                    sign = true;
                }
                tmp = (int64_t)lsbbcd_to_hex32(buf, len);
                tmp = (sign ? -tmp : tmp);    // 负数转换
                break;
            }
            case DT_FLOAT32: {
                f_tmp    = get_msbfloat32(buf);
                is_float = true;
                break;
            }

            default:
                goto Data_Err;
        }
#if DISP_PRIMARY_SIDE
        if(disp_running_para->spe.spe_format.primary_side)    // 1 次侧显示
        {
            if(!is_float) { f_tmp = tmp * 1.0f; }
            is_float = true;

            if(pt_ct == PT_CT_TYPE_PT) { f_tmp *= mic.ins->pt_ratio; }
            else if(pt_ct == PT_CT_TYPE_CT) { f_tmp *= mic.ins->ct_ratio; }
            else if(pt_ct == PT_CT_TYPE_PT_CT) { f_tmp *= mic.ins->pt_ratio * mic.ins->ct_ratio; }
        }
#endif
        if(is_float)    // 浮点数显示
        {
            disp_float(f_tmp, obj_api.scaler, dot - DF_DECIMAL_0, unit_icon, attr);    // 以最大显示小数位数显示数据
        }
        else
        {
            disp_integer(tmp, obj_api.scaler, dot - DF_DECIMAL_0, unit_icon, attr);    // 以最大显示小数位数显示数据
        }
        return;
    }

Data_Err:
    lcd.all_light(0);
    lcd.disp_string(LCD_MS, 0, dis_insmsg_tab[DISP_INS_NULL], LCD_MS_DIGITS);
    return;

LCD_Check:
    lcd.all_light(1);
    return;
}

/// @brief  显示按键信息
/// @param
#if USE_KEYBOARD
static void disp_key_info(void)
{
    uint16_t temp;

    if(disp_control.keyin.len < 8) { lcd.disp_string(LCD_MS, 8 - disp_control.keyin.len, disp_control.keyin.key_buf, disp_control.keyin.len); }
    else { lcd.disp_string(LCD_MS, 0, disp_control.keyin.key_buf + disp_control.keyin.len - LCD_MS_DIGITS, LCD_MS_DIGITS); }
    if(disp_control.keyin.len < 25) { temp = disp_control.keyin.len - disp_control.keyin.len / 5; }
    else { temp = 20; }
    disp_short_code(temp);
}
#endif

/// @brief  显示通讯符号
static void disp_comm_sign(uint8_t on_off)
{
#if defined(COM_IR)
    {
        static uint8_t ir_comm = 0;
        if(comm_phy.state_query(COMM_STATUS_IR_RECV)) { ir_comm = 5; }
        else
        {
            if(ir_comm > 0) ir_comm--;
        }
        if(ir_comm)
        {
            lcd.icon_light(TYPE_ICON_IR_COMM, on_off);    // 点亮
        }
        else
        {
            lcd.icon_light(TYPE_ICON_IR_COMM, 0);    // 关闭显示
        }
    }
#endif

#if defined(COM_MODULE)
    {
        static uint8_t module_comm = 0;
        if(comm_phy.state_query(COMM_STATUS_MODULE_RECV)) { module_comm = 5; }
        else
        {
            if(module_comm > 0) module_comm--;
        }
        if(module_comm)
        {
            lcd.icon_light(TYPE_ICON_COMM, on_off);    //
        }
        else
        {
            lcd.icon_light(TYPE_ICON_COMM, 0);    // 关闭显示
        }
    }
#endif

#if defined(COM_RS4851)
    {
        static uint8_t rs4851_comm = 0;
        if(comm_phy.state_query(COMM_STATUS_RS4851_RECV)) { rs4851_comm = 5; }
        else
        {
            if(rs4851_comm > 0) rs4851_comm--;
        }
        if(rs4851_comm)
        {
            lcd.icon_light(TYPE_ICON_4851_COMM, on_off);    //
        }
        else
        {
            lcd.icon_light(TYPE_ICON_4851_COMM, 0);    // 关闭显示
        }
    }
#endif

#if defined(COM_RS4852)
    {
        static uint8_t rs4852_comm = 0;
        if(comm_phy.state_query(COMM_STATUS_RS4852_RECV)) { rs4852_comm = 5; }
        else
        {
            if(rs4852_comm > 0) rs4852_comm--;
        }
        if(rs4852_comm)
        {
            lcd.icon_light(TYPE_ICON_4852_COMM, on_off);    //
        }
        else
        {
            lcd.icon_light(TYPE_ICON_4852_COMM, 0);    // 关闭显示
        }
    }
#endif
}

/// @brief  显示即时信息
/// @param type
static void disp_instant_msg(DISP_INS_TYPE type)
{
    uint8_t  buf[80];
    uint8_t *in = buf;

    memcpy(in, dis_insmsg_tab[(uint8_t)type], 8);
    in += 8;
    disp_message(MESS_MODE_INS, 0, buf, (in - buf), 3);
}

/// @brief  显示提示信息
/// @param
static void disp_tips_msg(void)
{
    switch(disp_control.msg.mode)
    {
        case MESS_MODE_EVE: {
            uint8_t *ptr = disp_control.msg.txt;

            uint8_t page = disp_control.msg.idx_screen;
            ptr += page * LCD_MS_DIGITS;
            lcd.disp_string(LCD_MS, disp_control.msg.pos, ptr, LCD_MS_DIGITS);
            break;
        }

        default:
            lcd.disp_string(LCD_MS, disp_control.msg.pos, (uint8_t *)disp_control.msg.txt, LCD_MS_DIGITS);
            break;
    }
}
#endif

/// @brief  显示符号
/// @param
static void disp_icon_ind(void)
{
    static bool flicker = true;    // 闪烁标志
    flicker             = !flicker;

#if RLY_INSIDE_ENABLE || RLY_OUTSIDE_ENABLE
    /*------继电器符号处理------*/
    lcd.icon_light(TYPE_ICON_RLY_OFF, 1);
#endif
    /*------内部或外部电池电压符号处理------*/
#if USE_ADC_INBAT
    lcd.icon_light(TYPE_ICON_BAT1, mstatus.reg->running.ext_bat_low);
#endif
#if USE_ADC_EXBAT
    lcd.icon_light(TYPE_ICON_BAT2, mstatus.reg->running.ext_bat_low);
#endif

    /*-----测试密钥指示-----------*/
    // lcd.icon_light(TYPE_ICON_KEY, );

    /*-----时钟错误处理-----------*/
    lcd.icon_light(TYPE_ICON_ALARM, mstatus.reg->self_chk.rtc);

    /*------潜动/象限/逆相序处理-------*/
#if defined(POLYPHASE_METER)
    if(mic.ins->stus.pa_start || mic.ins->stus.pb_start || mic.ins->stus.pc_start) { lcd.icon_light((ICON_TYPE_t)(TYPE_ICON_Q1 + mic.ins->quadrant[0] - 1), 1); }
    else { lcd.icon_light(TYPE_ICON_P_START, 1); }
#else
    if(mic.ins->stus.pa_start) { lcd.icon_light((ICON_TYPE_t)(TYPE_ICON_Q1 + mic.ins->quadrant[0] - 1), 1); }
    else { lcd.icon_light(TYPE_ICON_P_START, 1); }
#endif

    /*-------费率指示-----------*/
    lcd.icon_light((ICON_TYPE_t)(TYPE_ICON_CUR_TF_1 + tariff.cur_rate_get() - 1), 1);

    /*------电流反向/失压/过压/欠压-----*/
#if defined(POLYPHASE_METER)

#else

#endif
    /*------信号强度指示------*/

#if DISP_SIGN_STRENGTH_EN

#endif

#if DISP_ICON_COMM_ENABLE    // 默认不打开通讯符号显示
    /*------通讯符号指示------*/
    // disp_comm_sign(flicker); //闪烁显示5秒
    disp_comm_sign(1);    // 常显5秒
#endif
}

/// @brief  自动轮显中插入事件报警
/// @param refresh 是否更新显示模式
/// @return true - 有事件插入显示，false - 无事件插入显示
static bool disp_event_scan_refresh(bool refresh)
{
    disp_req_status_s status;

    status.open_cov = false;
    status.p_over   = false;    // boolof(mstatus.reg->load.over_load | mstatus.reg->load.lmt_exceed);

#if USE_LCD == 0
    disp_para_load();
#endif
    status.lword &= disp_running_para->req_filter.lword;

#if DISP_DISP_AUTO_EVENT
    if(status.lword != 0)
    {
        uint8_t  buf[DISP_MSG_MAX_LEN];
        uint8_t *in = buf;

        if(!refresh) return true;
        for(uint32_t i = 0; i < eleof(dis_evt_tab); i++)
        {
            if((in + LCD_MS_DIGITS - buf) >= DISP_MSG_MAX_LEN) break;    // 防止message buf溢出
            if(status.lword & dis_evt_tab[i].bit_mask)
            {
                memcpy(in, dis_evt_tab[i].buf, LCD_MS_DIGITS);
                in += LCD_MS_DIGITS;
            }
        }
        disp_message(MESS_MODE_EVE, 0, buf, (in - buf), disp_running_para->time.auto_Period);
        return true;
    }
#endif

    return false;
}

#if USE_LCD

/// @brief 显示实时消息提示
/// @param
static void disp_instant_msg_scan(void)
{
    for(uint16_t i = 0; i < eleof(dis_ins_meg_list); i++)
    {
        const disp_insmeg_s *ptr = dis_ins_meg_list + i;

        if(ptr->func != NULL && (*(ptr->func))(ptr->state))
        {
            disp_instant_msg((DISP_INS_TYPE)ptr->idex);
            break;
        }
    }
}

#define dispEventScan() disp_event_scan_refresh(true)

/// @brief 上翻/下翻键触发显示
/// @param
static void disp_key_enter_trig(void)
{
    switch(disp_context.mode)
    {
        case DISP_AUTO:
            disp_control.autos.cnt = 0;
            disp_context.last_mode = disp_context.mode;
            disp_context.mode      = DISP_MANUAL;
            disp_context.idxt      = DISP_FIRST_IDX;
            break;

        case DISP_MANUAL:
            disp_control.manual.cnt = 0;
            if(disp_is_subscreen_turn()) return;
            disp_context.idxt = DISP_NEXT_IDX;
            break;

        case DISP_MESSAGE:
            disp_control.msg.cnt   = 0;
            disp_control.autos.cnt = 0;
            disp_context.last_mode = disp_context.mode;
            disp_context.mode      = DISP_MANUAL;
            disp_context.idxt      = DISP_FIRST_IDX;
            break;

        case DISP_LOCKING:
            disp_context.last_mode = disp_context.mode;
            disp_context.mode      = DISP_MANUAL;
            disp_context.idxt      = DISP_FIRST_IDX;
            break;

        default:
            break;
    }
}

/* 退回键触发显示 */
static void disp_key_backspace_trig(void)
{
    switch(disp_context.mode)
    {
        case DISP_AUTO:
            disp_control.autos.cnt = 0;
            disp_context.last_mode = disp_context.mode;
            disp_context.mode      = DISP_MANUAL;
            disp_context.idxt      = DISP_LAST_IDX;
            break;

        case DISP_MANUAL:
            disp_control.manual.cnt = 0;
            if(disp_is_subscreen_turn()) return;
            disp_context.idxt = DISP_PRVS_IDX;
            break;

#if USE_KEYBOARD
        case DISP_KEYIN:
            disp_control.keyin.cnt = 0;
            if(--disp_control.keyin.len > 0)
            {
                if(disp_control.keyin.key_buf[disp_control.keyin.len - 1] == '-') { disp_control.keyin.len--; }
            }
            else
            {
                disp_context.last_mode = disp_context.mode;
                disp_context.mode      = DISP_MANUAL;
                disp_context.idxt      = DISP_LAST_IDX;
            }
            break;

        case DISP_KEYOUT:
            disp_control.keyout.cnt = 0;
            disp_context.last_mode  = disp_context.mode;
            disp_context.mode       = DISP_MANUAL;
            disp_context.idxt       = DISP_LAST_IDX;
            break;
#endif

        case DISP_MESSAGE:
            disp_control.msg.cnt   = 0;
            disp_context.last_mode = disp_context.mode;
            disp_context.mode      = DISP_MANUAL;
            disp_context.idxt      = DISP_FIRST_IDX;
            break;

        // case DISP_LOCKING:  // 空格键长按进入锁屏, 锁屏模式下短按无效
        // disp_context.last_mode = disp_context.mode;
        // disp_context.mode = DISP_MANUAL;
        // disp_context.idxt = DISP_PRVS_IDX;
        // break;
        default:
            break;
    }
}

#if USE_KEYBOARD
/* 字符键触发显示 */
static void disp_key_character_trig(char ch)
{
    //  if(control_manual(TYPE_MANUAL_ON)) return;  // 必须执行 000 + enter合闸

    // buzz.start(100, 0, 0, 0, 12);
    switch(disp_context.mode)
    {
        case DISP_AUTO:
            disp_control.autos.cnt = 0;
            break;

        case DISP_MANUAL:
            disp_control.manual.cnt = 0;
            break;

        case DISP_KEYIN:
            disp_control.keyin.cnt = 0;
            if(disp_control.keyin.len < 24)
            {
                uint8_t num = disp_control.keyin.len % 10;
                if((num == 4) || (num == 9)) disp_control.keyin.key_buf[disp_control.keyin.len++] = '-';
                disp_control.keyin.key_buf[disp_control.keyin.len++] = ch;
            }
            return;

        case DISP_KEYOUT:
            disp_control.keyout.cnt = 0;
            break;

        case DISP_MESSAGE:
            disp_control.msg.cnt = 0;
            break;
    }

    disp_context.last_mode                               = disp_context.mode;
    disp_context.mode                                    = DISP_KEYIN;
    disp_control.keyin.len                               = 0;
    disp_control.keyin.key_buf[disp_control.keyin.len++] = ch;
}
#endif

/// @brief 超时任务处理
/// @param ptr
static void disp_counter_trig(disp_context_s *ptr)
{
    ptr->idxt         = DISP_NULL_IDX;
    ptr->stus.refresh = true;

    if(ptr->mode == DISP_AUTO)
    {
        if(++disp_control.autos.cnt >= disp_running_para->time.auto_Period)
        {
            disp_control.autos.cnt = 0;
            if(disp_is_subscreen_turn()) return;
            ptr->idxt = DISP_NEXT_IDX;
#if SUPPORT_DISP_AUTO_ENENT
            if(dispEventScan()) ptr->idxt = DISP_NULL_IDX;
#endif
        }
    }
    else if(ptr->mode == DISP_MANUAL)
    {
        if(++disp_control.manual.cnt >= disp_running_para->time.mode_outtime)
        {
            disp_control.manual.cnt = 0;
            if(disp_is_subscreen_turn()) return;
            goto Atuo_Mode;
        }
    }
#if USE_KEYBOARD
    else if(ptr->mode == DISP_KEYIN)
    {
        uint8_t  i;
        eStsDisp rtn;

        if(++disp_control.keyin.cnt >= DISP_KEYIN_MODE_OUTTIME)
        {
            disp_control.keyin.cnt = 0;
            if(disp_control.keyin.len != 24) goto Atuo_Mode;
            for(i = 0; i < 20; i++) { disp_control.keyin.token_buf[i] = disp_control.keyin.key_buf[i + i / 4]; }
            rtn            = sts_app.process(&disp_control.keyin.token_buf[0], MANUAL_ENTRY);
            ptr->last_mode = ptr->mode;
            display_sts_msg(rtn, MANUAL_ENTRY);
        }
    }
    else if(ptr->mode == DISP_KEYOUT)
    {
        if(++disp_control.keyout.cnt >= disp_running_para->time.mode_outtime)
        {
            disp_control.keyout.cnt = 0;
            if(disp_is_subscreen_turn()) return;
            if(!disp_is_screen_turn(&disp_context.info.idx, cur_disp_list.num)) goto Atuo_Mode;
            ptr->idxt = DISP_NEXT_IDX;
        }
    }
#endif
    else if(ptr->mode == DISP_MESSAGE)
    {
        if(++disp_control.msg.cnt >= disp_control.msg.duration)
        {
            disp_control.msg.cnt = 0;
            switch(disp_control.msg.mode)
            {
                case MESS_MODE_EVE:
                    if(disp_is_subscreen_turn()) return;
                    if(disp_is_msgscreen_turn(&disp_control.msg.idx_screen, disp_control.msg.max_screen)) return;
                    break;
            }

            switch(ptr->last_mode)
            {
#if USE_KEYBOARD
                case DISP_KEYIN:
                case DISP_KEYOUT:
#endif
                case DISP_MESSAGE:
                    goto Atuo_Mode;

                case DISP_LOCKING:
                    ptr->mode = ptr->last_mode;
                    ptr->idxt = DISP_NULL_IDX;
                    break;

                default:
                    ptr->mode = ptr->last_mode;
                    ptr->idxt = DISP_NEXT_IDX;
                    break;
            }
        }
    }
    return;

Atuo_Mode:
    ptr->last_mode = ptr->mode;
    ptr->mode      = DISP_AUTO;
    ptr->idxt      = DISP_FIRST_IDX;
    disp_list_get(DISP_AUTO);
}

/// @brief LCD背光控制
/// @param  typ = 1 点亮， = 0 放在秒任务。
static void disp_backlight(uint8_t typ, uint8_t timer)
{
    static uint8_t disp_backlight_on_time = 0;

    if(typ)
    {
        led.ctrl(BLACKIT_LED, FREQ_ON);
        disp_backlight_on_time = timer;
        return;
    }

    if(disp_context.stus.fault && mstatus.reg->running.factory)
    {
        led.ctrl(BLACKIT_LED, FREQ_1HZ);
        disp_backlight_on_time = 0;
    }
    else
    {
        if(disp_running_para->time.backlight_time == 0xFF)    // LCD背光永久常亮
        {
            led.ctrl(BLACKIT_LED, FREQ_ON);
            disp_backlight_on_time = 0;
        }
        else
        {
            if(disp_backlight_on_time != 0)
            {
                led.ctrl(BLACKIT_LED, FREQ_ON);
                if(disp_context.mode != DISP_LOCKING) disp_backlight_on_time--;
            }
            else { led.ctrl(BLACKIT_LED, FREQ_OFF); }
        }
    }
}

/* Public functions ----------------------------------------------------------*/
/// @brief 秒任务
/// @param
void display_second_run(void)
{
    disp_para_load();
    // disp_list_get(disp_context.mode);

    if(pwron_all_disp_cnt > 0)
    {
        lcd.all_light(1);    // 上电常显
        lcd.refresh();
        pwron_all_disp_cnt--;
        return;
    }
    disp_counter_trig(&disp_context);
    if(disp_context.idxt != DISP_NULL_IDX) { disp_obj_info_get(disp_context.idxt); }
    disp_instant_msg_scan();    // 瞬时消息处理
    disp_backlight(0, 0);       // 背光控制处理
    // dprintf("\r\n RTC: 20%02d-%02d-%02d, %02d:%02d:%02d", rtc.YY, rtc.MM, rtc.DD, rtc.hh, rtc.mm, rtc.ss);
}

/// @brief 循环任务
/// @param
/// @brief 循环任务
/// @param 无
/// @return 无
/// @details
/// 该函数是显示模块的空闲任务，在系统空闲时循环执行。
/// 主要负责处理按键输入、更新显示模式、刷新LCD屏幕等。
void display_idle_run(void)
{
    disp_context.idxt = DISP_NULL_IDX;    // 初始化显示索引方向为NULL

#if USE_BTN_DISP_DN                         // 如果使能了按键显示下翻功能
    keyboard_type_s pkey = key.indent();    // 获取按键识别结果
    if(pkey.value != TYPE_KEY_NONE)         // 如果有按键按下
    {
        disp_context.stus.refresh = true;                             // 设置显示刷新标志
        disp_backlight(1, disp_running_para->time.backlight_time);    // 点亮背光

        /* 根据当前按键类型进行显示处理 */
        switch(pkey.value)
        {
            case TYPE_KEY_ENTER:                  // 回车键
#if !USE_KEYBOARD && RLY_INSIDE_ENABLE            // 如果没有使用键盘且使能了内部继电器
                if(pkey.mode == TYPE_KEY_LONG)    // 长按回车键
                {                                 // 长按enter键，手动合闸
#if RLY_INSIDE_ENABLE
                    if(!control.manual(TYPE_MANUAL_ON, TYPE_RLY_1))    // 手动合闸
#endif
                        beep.start(100, 0, 0, 0, 12);    // 触发按键音
                    return;                              // 返回
                }
#endif
                // beep.start(100, 0, 0, 0, 12);    // 触发按键音
                disp_key_enter_trig();    // 触发回车键显示处理
                break;

            case TYPE_KEY_BACKSPACE:              // 退格键
                if(pkey.mode == TYPE_KEY_LONG)    // 长按退格键
                {
                    disp_context.last_mode = disp_context.mode;    // 保存当前模式
                    disp_context.mode      = DISP_LOCKING;         // 进入锁定模式
                    break;                                         // 跳出switch
                }
                // beep.start(100, 0, 0, 0, 11);    // 触发按键音
                disp_key_backspace_trig();    // 触发退格键显示处理
                break;

#if USE_KEYBOARD                                                   // 如果使能了键盘
            default:                                               // 其他字符键
                if(pkey.mode == TYPE_KEY_LONG) return;             // 长按则返回
                buzz.start(100, 0, 0, 0, pkey.value - '0' + 1);    // 触发按键音
                disp_key_character_trig(pkey.value);               // 触发字符键显示处理
                break;                                             // 跳出switch
#endif
        }

        if(disp_context.mode != disp_context.last_mode) { disp_list_get(disp_context.mode); }    // 如果显示模式改变，则获取新的显示列表
    }
#endif
    // 如果当前模式与上次模式不同且不是消息模式
    if(disp_context.mode != disp_context.last_mode && disp_context.mode != DISP_MESSAGE)
    {
        disp_context.last_mode = disp_context.mode;    // 更新上次模式
        disp_screen_num_get(0);                        // 获取当前屏数
    }

    if(disp_context.idxt != DISP_NULL_IDX) { disp_obj_info_get(disp_context.idxt); }    // 如果显示索引方向不为NULL，则获取显示对象信息

    if(disp_context.stus.refresh)    // 如果显示刷新标志为true
    {
        disp_context.stus.refresh = false;    // 清除显示刷新标志
        lcd.all_light(0);                     // 关闭所有LCD图标
        disp_icon_ind();                      // 显示图标指示

        switch(disp_context.mode)    // 根据当前显示模式进行处理
        {
#if USE_KEYBOARD                    // 如果使能了键盘
            case DISP_KEYIN:        // 键盘输入模式
                disp_key_info();    // 显示按键信息
                break;              // 跳出switch
#endif

            case DISP_MESSAGE:      // 消息模式
                disp_tips_msg();    // 显示提示消息
                break;              // 跳出switch

            default:                                      // 其他模式
                disp_obj_show(&disp_context.info.obj);    // 显示当前显示对象
                break;                                    // 跳出switch
        }

        lcd.refresh();    // 刷新LCD屏幕
    }
}

/// @brief 上电初始化
/// @param
void display_init(void)
{
    // 初始化参数
    disp_para_load();
    disp_list_get(DISP_AUTO);
    memset(&disp_control, 0, sizeof(disp_control));
    memset(&disp_context, 0, sizeof(disp_context));
    // DBG_PRINTF(P_ANY, D, "\r\n>>>display_init...");
    disp_obj_info_get(DISP_FIRST_IDX);
    // 全显
    pwron_all_disp_cnt = disp_running_para->time.pwon_all_disp;
    lcd.all_light(1);
    lcd.refresh();
#if USE_LED_BACK
    led.ctrl(BLACKIT_LED, FREQ_ON);
#endif
}

/// @brief 低功耗运行初始化
/// @param
void display_init_nopower(void)
{
    disp_para_load();
    memset(&disp_control, 0, sizeof(disp_control));
    memset(&disp_context, 0, sizeof(disp_context));
    disp_list_get(DISP_PWDN);
}

/// @brief 低功耗显示运行
/// @param
#if DISP_POWER_DOWN_DISPLAY
void display_run_nopower(void)
{
    static disp_mode_t mode     = DISP_AUTO;
    static uint16_t    index    = 0;
    static uint16_t    auto_cnt = 0;
    static uint16_t    butt_cnt = 0;
    static uint8_t     wakeup   = false;

    if(bsp.wakeup_state(KEY_DISP_DN_WAKEUP | KEY_DISP_UP_WAKEUP))
    {
        bsp.wakeup_close(KEY_DISP_DN_WAKEUP | KEY_DISP_UP_WAKEUP);
        key.disp_btn_pwrdn_scan();
        keyboard_type_s pkey = key.indent();
        if(pkey.value == TYPE_KEY_NONE)
        {
            // bsp.wakeup_init(DISP_WAKEUP);
            return;
        }
        if(disp_pwdn_stus != PD_DISP_KEY_IN)
        {
            disp_pwdn_stus              = PD_DISP_KEY_IN;
            mode                        = DISP_AUTO;
            auto_cnt                    = 0;
            index                       = 0;
            disp_control.mutil.req_flag = false;
            disp_control.mutil.page     = 0;
            lcd.ctrl(LCD_PWROFF_OPEN);
        }
        else
        {
            mode     = DISP_MANUAL;
            butt_cnt = 0;
            if(pkey.value != TYPE_KEY_ENTER && pkey.value != TYPE_KEY_BACKSPACE)
            {
                // bsp.wakeup_init(DISP_WAKEUP);
                return;
            }

            if(pkey.value == TYPE_KEY_BACKSPACE)
            {
                if(!disp_is_subscreen_turn())
                {
                    if(index == 0) index = cur_disp_list.num;
                    index--;
                }
            }
            else
            {
                if(!disp_is_subscreen_turn())
                {
                    if(++index >= cur_disp_list.num) index = 0;
                }
            }
        }
    }

    if(bsp.wakeup_state(DISP_WAKEUP) && (disp_pwdn_stus != PD_DISP_CLOSE))
    {
#if !DISP_POWER_DOWN_BTN_TIMEOUT
        if(mode == DISP_AUTO)
        {
            if(++auto_cnt >= disp_running_para->time.pwdn_auto_Period)
            {
                auto_cnt = 0;
                if(!disp_is_subscreen_turn())
                {
                    if(++index >= cur_disp_list.num)    // 自动轮显一圈数据项后关闭显示
                    {
                        mode           = DISP_AUTO;
                        index          = 0;
                        disp_pwdn_stus = PD_DISP_CLOSE;
                        auto_cnt       = 0;
                        butt_cnt       = 0;
                        bsp.wakeup_close(DISP_WAKEUP);
                        lcd.ctrl(LCD_CLOSE);
                    }
                }
            }
        }
        else if(mode == DISP_MANUAL)
        {
            if(++butt_cnt >= disp_running_para->time.mode_outtime)
#endif
#if DISP_POWER_DOWN_BTN_TIMEOUT
                if(++butt_cnt >= disp_running_para->time.pwdn_auto_Period)
#endif
                {
                    butt_cnt = 0;
                    if(!disp_is_subscreen_turn())
                    {
                        mode = DISP_AUTO;
#if DISP_POWER_DOWN_BTN_TIMEOUT    // 按键查询显示超时后关闭显示
                        index    = 0;
                        auto_cnt = 0;
                        butt_cnt = 0;
                        bsp.wakeup_close(DISP_WAKEUP);
                        lcd.ctrl(LCD_CLOSE);
                        disp_pwdn_stus = PD_DISP_CLOSE;
#endif
                    }
                }
        }
    }

    if(disp_pwdn_stus == PD_DISP_KEY_IN || wakeup)
    {
        if(disp_pwdn_stus == PD_DISP_DELAY && mode == DISP_LOCKING) wakeup = false;
        lcd.all_light(0);
        disp_context.info.idx = index;
        disp_context.info.obj = disp_total_table[cur_disp_list.ptr[index]];
        disp_obj_show(&disp_context.info.obj);
        lcd.refresh();
        bsp.wakeup_init(DISP_WAKEUP);
    }
}
#endif
/// @brief 读取显示参数
/// @param
/// @return
const disp_para_s *display_para_get(void)
{
    return disp_running_para;
}

/// @brief 设置显示时间参数
/// @param ofst
/// @param val
/// @param len
/// @return
bool display_para_set(uint16_t ofst, const void *val, uint16_t len)
{
    if(ofst >= sizeof(disp_para_s)) return false;
    if(ofst == member_offset(disp_para_s, time.pwon_all_disp))
    {
        volatile uint8_t tmp = *(uint8_t *)val;
        if((tmp < 5) || (tmp > 30)) return false;    // 上电全显设置范围5-30秒
    }
    return disp_para_store(ofst, val, len);
}

#endif

#if USE_LCD
/// @brief 设置显示列表
/// @param type     显示列表类型
/// @param id_list  显示id列表
/// @param num      显示id个数
/// @return         true - 设置成功，false - 设置失败
bool display_id_list_set(TYPE_DISP_LIST type, const uint32_t *id_list, uint16_t num)
{
    uint16_t    i, j;
    disp_list_s tmp;

    if((num == 0) || (num > DISP_ID_MAX_NUM)) return false;

    /* 判断是否设置显示总表内的显示数据项 */
    for(i = 0; i < num; i++)
    {
        for(j = 0; j < disp_total_table_num; j++)
        {
            if(id_list[i] != disp_total_table[j].id) continue;
            tmp.buf[i] = j;
            break;
        }
        if(j >= disp_total_table_num) return false;    /// 显示总表中未找到, 返回失败
    }
    tmp.num = num;
    if(disp_list_store(type, &tmp) == false) return false;
    disp_list_get(disp_context.mode);
    return true;
}

/// @brief 设置某一屏的ID
/// @param type     显示列表类型
/// @param id_list  显示id列表
/// @param num      显示id个数
/// @return         true - 设置成功，false - 设置失败
bool display_id_set(TYPE_DISP_LIST type, uint16_t index, uint32_t id)
{
    uint16_t    j;
    disp_list_s tmp;

    if((index == 0) || (index > DISP_ID_MAX_NUM)) return false;
    tmp = *disp_list_load(type);

    /* 判断是否设置显示总表内的显示数据项 */
    for(j = 0; j < disp_total_table_num; j++)
    {
        if(id == disp_total_table[j].id) break;
    }
    if(j >= disp_total_table_num) return false;    /// 显示总表中未找到, 返回失败
    tmp.buf[index - 1] = j;
    if(tmp.num < index) tmp.num = index;
    if(disp_list_store(type, &tmp) == false) return false;
    disp_list_get(disp_context.mode);
    return true;
}

/// @brief 获取显示列表的id列表
/// @param type     显示列表类型
/// @param id_list  显示列表的id列表
/// @return
uint16_t display_id_list_get(TYPE_DISP_LIST type, uint32_t *id_list)
{
    const disp_list_s *list = disp_list_load(type);
    for(uint8_t i = 0; i < list->num; i++) { id_list[i] = disp_total_table[list->buf[i]].id; }
    return list->num;
}

/// @brief 获取某一屏的ID 和屏数
/// @param type     显示列表类型
/// @param index    第几屏
/// @param id_list  获取到的显示列表的id列表
/// @return
uint8_t display_id_get(TYPE_DISP_LIST type, uint16_t index, uint32_t *id)
{
    const disp_list_s *list = disp_list_load(type);
    uint8_t            num;
    if(index < list->num)
    {
        *id = disp_total_table[list->buf[index]].id;
        num = disp_total_table[list->buf[index]].num;
    }
    else
    {
        *id = 0;
        num = 0;
    }
    return num;
}

uint16_t display_id_list_get_num(TYPE_DISP_LIST type)
{
    const disp_list_s *list = disp_list_load(type);
    return list->num;
}

/// @brief 显示模块复位，恢复默认参数
/// @
void display_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        disp_para_store(0, &disp_default_para, sizeof(disp_para_s));

        if(type != SYS_GLOBAL_RESET)
        {    // 显示列表恢复默认值
            nvm.write(DISP_LIST1_ADDR, NULL, sizeof(disp_list_s));
            nvm.write(DISP_LIST2_ADDR, NULL, sizeof(disp_list_s));
            nvm.write(DISP_LIST3_ADDR, NULL, sizeof(disp_list_s));
        }
    }

    disp_para_load();
    disp_list_get(DISP_AUTO);
    memset(&disp_control, 0, sizeof(disp_control));
    memset(&disp_context, 0, sizeof(disp_context));
    disp_obj_info_get(DISP_FIRST_IDX);
}
/// @brief 显示模块事件确认
/// @param state
/// @return
bool display_state_query(DISP_STUS state)
{
    return boolof(disp_out_stus & state);
}

/// @brief 显示模块事件设置
/// @param state
void display_state_set(DISP_STUS state)
{
    disp_out_stus |= state;
}

/// @brief 显示模块事件清除
/// @param
void display_state_clr(void)
{
    disp_out_stus = 0;
}
#endif

/// @brief 声明显示模块对象
const struct display_s display = {
#if USE_LCD
    .reset           = display_reset,
    .para_get        = display_para_get,
    .para_set        = display_para_set,
    .state_query     = display_state_query,
    .state_set       = display_state_set,
    .state_clr       = display_state_clr,
    .id_list_get_num = display_id_list_get_num,
    .id_list_get     = display_id_list_get,
    .id_get          = display_id_get,
    .id_list_set     = display_id_list_set,
    .id_set          = display_id_set,
#endif
};

/// @brief 继电器控制模块任务接口
const struct app_task_t display_task = {
#if USE_LCD
    .init       = display_init,
    .second_run = display_second_run,
    .idle_run   = display_idle_run,
#if DISP_POWER_DOWN_DISPLAY
    .power_off_init = display_init_nopower,
    .power_off_run  = display_run_nopower,
#else
    .power_off_init = NULL,
    .power_off_run  = NULL,
#endif
#else
    .init           = NULL,
    .second_run     = NULL,
    .idle_run       = NULL,
    .power_off_init = NULL,
    .power_off_run  = NULL,
#endif
};
