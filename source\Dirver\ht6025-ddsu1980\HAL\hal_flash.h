/**
 ******************************************************************************
* @file    hal_flash.h
* <AUTHOR> @date    2024
* @brief   mcu flash driver
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __HAL_FLASH_H
#define __HAL_FLASH_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"




/* Exported types ------------------------------------------------------------*/
typedef uint32_t      HAL_FLASH_ADDR_t;



struct hal_flash_s
{
    ///@读取flash
    bool (*read)(HAL_FLASH_ADDR_t ofst, void* pdat, uint16_t num);

    ///@写入flash，用于app
    bool (*write)(HAL_FLASH_ADDR_t ofst, const void* pdat, uint16_t num);

    ///@编程flash，用于bootload
    bool (*program)(HAL_FLASH_ADDR_t ofst, const void* pdat, uint16_t num);
};
extern const struct hal_flash_s hal_flash;  ///hal_flash 子模块


#endif /* __HAL_FLASH_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

