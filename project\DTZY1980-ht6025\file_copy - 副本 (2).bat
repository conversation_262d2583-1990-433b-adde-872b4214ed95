@echo off
:: chcp 65001 > nul
@setlocal ENABLEDELAYEDEXPANSION

:: 获取当前项目目录
pushd %~dp0
for %%i in (.) do set "target=%%~nxi"
cd ..\
for %%i in (.) do set "proj=%%~nxi"
cd %~dp0


:: 参数处理（示例：file_copy.bat DTZY1980-6）
set "MODE=%~1"
set "ver=%~2"

::获取日期 
@set year=%date:~0,4%
@set month=%date:~5,2%
@set day=%date:~8,2%

if "%time:~0,1%"==" " (set hour=0"%time:~1,1%") else (set hour=%time:~0,2%)
@set minute=%time:~3,2%
@set second=%time:~6,2%
 

:: 提取固件版本号
for /f "tokens=3" %%a in ('findstr /C:"#define METER_SOFT_VER" ..\..\source\ver.h') do set "soft_ver_tmp=%%a"
set "soft_ver=%soft_ver_tmp:~1,5%"

:: 创建输出目录
set "DIR_OUT=..\..\output\%target%"
set "hex_path=%DIR_OUT%"
if not exist "%hex_path%" mkdir "%hex_path%"

:: 清理旧文件
del /Q "%hex_path%\*.*" >nul 2>&1
pause
:: 生成BIN文件
set "out_file=.\Release\Exe"
echo Generating BIN file: "%hex_path%\%MODE%-app(%soft_ver%-%year%%month%%day%).bin"
ielftool.exe --verbose --bin=__ICFEDIT_APP_start__-__ICFEDIT_APP_end__ "%out_file%\app.out" "%hex_path%\%MODE%-app(%soft_ver%-%year%%month%%day%).bin"
if %errorlevel% neq 0 (
    echo [ERROR] ielftool.exe failed with code %errorlevel%. Check app.out and linker settings.
    exit /b %errorlevel%
)
pause
:: 复制HEX文件
set "name=%MODE%-(%soft_ver%-%year%%month%%day%)"
echo Copying HEX file to "%hex_path%\%name%.hex"
copy "%out_file%\app.hex" "%hex_path%\%name%.hex" >nul
if %errorlevel% neq 0 (
    echo [ERROR] Failed to copy HEX file. Ensure "%out_file%\app.hex" exists.
    exit /b %errorlevel%
)

echo Build artifacts copied successfully.
exit /b 0