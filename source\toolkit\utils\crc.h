/*
 * @Author: 
 * @Date: 2024-12-17 08:58:06
 * @LastEditTime: 2025-01-06 08:55:09
 * @Description: 
 */
/**
 ******************************************************************************
* @file    crc.h
* <AUTHOR> @date    2024
* @brief   crc算法头文件
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __CRC_H
#define __CRC_H

/* Includes ------------------------------------------------------------------*/
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

/* Exported types ------------------------------------------------------------*/
/* Exported defines ----------------------------------------------------------*/


/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
extern uint8_t  crc8_update_bit(uint8_t crc, const void* cp, uint8_t size);
extern uint16_t crc16_update_revtab(uint16_t crc, const void* cp, uint16_t size);
extern uint16_t crc16_update_revbit(uint16_t crc, const void* cp, uint16_t size);
extern uint16_t crc16_update_tab(uint16_t crc, const void* cp, uint16_t size);
extern uint16_t crc16_update_bit(uint16_t crc, const void* cp, uint16_t size);
extern uint32_t crc32_update_revtab(uint32_t crc, const void* cp, uint32_t size);
extern uint32_t crc32_update_revbit(uint32_t crc, const void* cp, uint32_t size);
extern uint32_t crc32_update_tab(uint32_t crc, const void* cp, uint32_t size);
extern uint32_t crc32_update_bit(uint32_t crc, const void* cp, uint32_t size);

/* Exported variables -*/

#define crc8(s, x, y)    crc8_update_bit(s, x, y)
#define crc16(s, x, y)   crc16_update_revtab(s, x, y)
#define crc32(s, x, y)   crc32_update_revtab(s, x, y)

////注意，以下定义只用于规定格式的结构体！！！！！
// struct 
// {
//     uint16_t chk;
//     uint16_t crc;  //crc16校验码，变量名不能改变，否则不能使用此宏
//     ...
//     ...
// }
// s-初值，x-结构体指针，l-结构体大小
#define STRUCT_CRC16_CHK(s,x,l)      ((x)->crc == crc16_update_revtab(s, (uint8_t *)(x) + 4, l - 4) ? TRUE : FALSE)
#define STRUCT_CRC16_GET(s,x,l)      ((x)->crc =  crc16_update_revtab(s, (uint8_t *)(x) + 4, l - 4))

#endif /* __CRC_H */

