/**
  ******************************************************************************
  * @file    emu_ht7136.c
  * <AUTHOR> @date    2024
  * @brief   ht7136 计量芯片驱动
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include <math.h>
#include "bsp_cfg.h"
#include "mic.h"
#include "utils.h"
#include "debug.h"
#include "eeprom.h"
#include "hal_gpio.h"
#include "hal_timer.h"
#include "hal_flash.h"
#include "mic.h"

// typedef struct
// {
//     uint8_t mod; // 0-计量数据地址 1-第二套计量参数寄存器组，3-校表数据地址 
//     uint8_t reg; // 寄存器地址
//     uint8_t len; // 寄存器长度
// } mic_reg_s;

/// ht7132 register 定义
/// 计量参数和状态寄存器
#define r_DeviceID      0x00    /// (mic_reg_s){0x00, 0x3, 0x00}  /// 0x7122A0  Device ID
#define r_Pa            0x01    /// (mic_reg_s){0x00, 0x3, 0x01}  /// 0x000000  A 相有功功率
#define r_Pb            0x02    /// (mic_reg_s){0x00, 0x3, 0x02}  /// 0x000000  B 相有功功率
#define r_Pc            0x03    /// (mic_reg_s){0x00, 0x3, 0x03}  /// 0x000000  C 相有功功率
#define r_Pt            0x04    /// (mic_reg_s){0x00, 0x3, 0x04}  /// 0x000000  合相有功功率
#define r_Qa            0x05    /// (mic_reg_s){0x00, 0x3, 0x05}  /// 0x000000  A 相无功功率
#define r_Qb            0x06    /// (mic_reg_s){0x00, 0x3, 0x06}  /// 0x000000  B 相无功功率
#define r_Qc            0x07    /// (mic_reg_s){0x00, 0x3, 0x07}  /// 0x000000  C 相无功功率
#define r_Qt            0x08    /// (mic_reg_s){0x00, 0x3, 0x08}  /// 0x000000  合相无功功率
#define r_Sa            0x09    /// (mic_reg_s){0x00, 0x3, 0x09}  /// 0x000000  A 相视在功率
#define r_Sb            0x0A    /// (mic_reg_s){0x00, 0x3, 0x0A}  /// 0x000000  B 相视在功率
#define r_Sc            0x0B    /// (mic_reg_s){0x00, 0x3, 0x0B}  /// 0x000000  C 相视在功率
#define r_St            0x0C    /// (mic_reg_s){0x00, 0x3, 0x0C}  /// 0x000000  合相视在功率
#define r_UaRms         0x0D    /// (mic_reg_s){0x00, 0x3, 0x0D}  /// 0x000000  A 相电压有效值
#define r_UbRms         0x0E    /// (mic_reg_s){0x00, 0x3, 0x0E}  /// 0x000000  B 相电压有效值
#define r_UcRms         0x0F    /// (mic_reg_s){0x00, 0x3, 0x0F}  /// 0x000000  C 相电压有效值
#define r_IaRms         0x10    /// (mic_reg_s){0x00, 0x3, 0x10}  /// 0x000000  A 相电流有效值
#define r_IbRms         0x11    /// (mic_reg_s){0x00, 0x3, 0x11}  /// 0x000000  B 相电流有效值
#define r_IcRms         0x12    /// (mic_reg_s){0x00, 0x3, 0x12}  /// 0x000000  C 相电流有效值
#define r_ItRms         0x13    /// (mic_reg_s){0x00, 0x3, 0x13}  /// 0x000000  三相电流矢量和的有效值
#define r_Pfa           0x14    /// (mic_reg_s){0x00, 0x3, 0x14}  /// 0x000000  A 相功率因数
#define r_Pfb           0x15    /// (mic_reg_s){0x00, 0x3, 0x15}  /// 0x000000  B 相功率因数
#define r_Pfc           0x16    /// (mic_reg_s){0x00, 0x3, 0x16}  /// 0x000000  C 相功率因数
#define r_Pft           0x17    /// (mic_reg_s){0x00, 0x3, 0x17}  /// 0x000000  合相功率因数
#define r_Pga           0x18    /// (mic_reg_s){0x00, 0x3, 0x18}  /// 0x000000  A 相电流与电压相角
#define r_Pgb           0x19    /// (mic_reg_s){0x00, 0x3, 0x19}  /// 0x000000  B 相电流与电压相角
#define r_Pgc           0x1A    /// (mic_reg_s){0x00, 0x3, 0x1A}  /// 0x000000  C 相电流与电压相角
#define r_INTFlag       0x1B    /// (mic_reg_s){0x00, 0x3, 0x1B}  /// 0x000000  中断标志，读后清零
#define r_Freq          0x1C    /// (mic_reg_s){0x00, 0x3, 0x1C}  /// 0x000000  线频率
#define r_EFlag         0x1D    /// (mic_reg_s){0x00, 0x3, 0x1D}  /// 0x000000  电能寄存器的工作状态，读后清零
#define r_Epa           0x1E    /// (mic_reg_s){0x00, 0x3, 0x1E}  /// 0x000000  A 相有功电能（可配置为读后清零）
#define r_Epb           0x1F    /// (mic_reg_s){0x00, 0x3, 0x1F}  /// 0x000000  B 相有功电能（可配置为读后清零）
#define r_Epc           0x20    /// (mic_reg_s){0x00, 0x3, 0x20}  /// 0x000000  C 相有功电能（可配置为读后清零）
#define r_Ept           0x21    /// (mic_reg_s){0x00, 0x3, 0x21}  /// 0x000000  合相有功电能（可配置为读后清零）
#define r_Eqa           0x22    /// (mic_reg_s){0x00, 0x3, 0x22}  /// 0x000000  A 相无功电能（可配置为读后清零）
#define r_Eqb           0x23    /// (mic_reg_s){0x00, 0x3, 0x23}  /// 0x000000  B 相无功电能（可配置为读后清零）
#define r_Eqc           0x24    /// (mic_reg_s){0x00, 0x3, 0x24}  /// 0x000000  C 相无功电能（可配置为读后清零）
#define r_Eqt           0x25    /// (mic_reg_s){0x00, 0x3, 0x25}  /// 0x000000  合相无功电能（可配置为读后清零）
#define r_YUaUb         0x26    /// (mic_reg_s){0x00, 0x3, 0x26}  /// 0x000000  Ua 与 Ub 的电压夹角
#define r_YUaUc         0x27    /// (mic_reg_s){0x00, 0x3, 0x27}  /// 0x000000  Ua 与 Uc 的电压夹角
#define r_YUbUc         0x28    /// (mic_reg_s){0x00, 0x3, 0x28}  /// 0x000000  Ub 与 Uc 的电压夹角
#define r_RmsADC7       0x29    /// (mic_reg_s){0x00, 0x3, 0x29}  /// 0x000000  第七路 ADC 输入信号的有效值
#define r_TPSD          0x2A    /// (mic_reg_s){0x00, 0x3, 0x2A}  /// 0x000000  温度传感器的输出
#define r_UtRms         0x2B    /// (mic_reg_s){0x00, 0x3, 0x2B}  /// 0x000000  三相电压矢量和的有效值
#define r_Sflag         0x2C    /// (mic_reg_s){0x00, 0x3, 0x2C}  /// 0x000000  存放断相、相序、SIG 等标志状态
#define r_BckReg        0x2D    /// (mic_reg_s){0x00, 0x3, 0x2D}  /// 0x0000000  通讯数据备份寄存器
#define r_BckReg_addr   0x2D                          /// 0x000000  通讯数据备份寄存器
#define r_ComChksum     0x2E    /// (mic_reg_s){0x00, 0x3, 0x2E}  /// 0x000000  通讯校验和寄存器
#define r_Sample_IA     0x2F    /// (mic_reg_s){0x00, 0x3, 0x2F}  /// 0x000000  A 相电流通道 ADC 采样数据
#define r_Sample_IB     0x30    /// (mic_reg_s){0x00, 0x3, 0x30}  /// 0x000000  B 相电流通道 ADC 采样数据
#define r_Sample_IC     0x31    /// (mic_reg_s){0x00, 0x3, 0x31}  /// 0x000000  C 相电流通道 ADC 采样数据
#define r_Sample_UA     0x32    /// (mic_reg_s){0x00, 0x3, 0x32}  /// 0x000000  A 相电压通道 ADC 采样数据
#define r_Sample_UB     0x33    /// (mic_reg_s){0x00, 0x3, 0x33}  /// 0x000000  B 相电压通道 ADC 采样数据
#define r_Sample_UC     0x34    /// (mic_reg_s){0x00, 0x3, 0x34}  /// 0x000000  C 相电压通道 ADC 采样数据
#define r_Esa           0x35    /// (mic_reg_s){0x00, 0x3, 0x35}  /// 0x000000  A 相视在电能（可配置为读后清零）
#define r_Esb           0x36    /// (mic_reg_s){0x00, 0x3, 0x36}  /// 0x000000  B 相视在电能（可配置为读后清零）
#define r_Esc           0x37    /// (mic_reg_s){0x00, 0x3, 0x37}  /// 0x000000  C 相视在电能（可配置为读后清零）
#define r_Est           0x38    /// (mic_reg_s){0x00, 0x3, 0x38}  /// 0x000000  合相视在电能（可配置为读后清零）
#define r_FstCntPa      0x39    /// (mic_reg_s){0x00, 0x3, 0x39}  /// 0x000000  A 相有功快速脉冲计数
#define r_FstCntPb      0x3A    /// (mic_reg_s){0x00, 0x3, 0x3A}  /// 0x000000  B 相有功快速脉冲计数
#define r_FstCntPc      0x3B    /// (mic_reg_s){0x00, 0x3, 0x3B}  /// 0x000000  C 相有功快速脉冲计数
#define r_FstCntPt      0x3C    /// (mic_reg_s){0x00, 0x3, 0x3C}  /// 0x000000  合相有功快速脉冲计数
#define r_PFlag         0x3D    /// (mic_reg_s){0x00, 0x3, 0x3D}  /// 0x000000  有功/无功功率方向，正向为 0，负向为 1
#define r_ChkSum        0x3E    /// (mic_reg_s){0x00, 0x3, 0x3E}  /// 0x01D4CD  校表数据校验和(三相四线模式下)01H~3FH 3 0x01E0CD 校表数据校验和(三相三线模式下)01H~3FH
#define r_InstADC7      0x3F    /// (mic_reg_s){0x00, 0x3, 0x3F}  /// 0x000000  第七路 ADC 采样数据输出
#define r_FundPa        0x40    /// (mic_reg_s){0x00, 0x3, 0x40}  /// 0x000000  A 相基波有功功率
#define r_FundPb        0x41    /// (mic_reg_s){0x00, 0x3, 0x41}  /// 0x000000  B 相基波有功功率
#define r_FundPc        0x42    /// (mic_reg_s){0x00, 0x3, 0x42}  /// 0x000000  C 相基波有功功率
#define r_FundPt        0x43    /// (mic_reg_s){0x00, 0x3, 0x43}  /// 0x000000  合相基波有功功率
#define r_FundEpa       0x44    /// (mic_reg_s){0x00, 0x3, 0x44}  /// 0x000000  A 相基波正向有功电能（可配置为读后清零）
#define r_FundEpb       0x45    /// (mic_reg_s){0x00, 0x3, 0x45}  /// 0x000000  B 相基波正向有功电能（可配置为读后清零）
#define r_FundEpc       0x46    /// (mic_reg_s){0x00, 0x3, 0x46}  /// 0x000000  C 相基波正向有功电能（可配置为读后清零）
#define r_FundEpt       0x47    /// (mic_reg_s){0x00, 0x3, 0x47}  /// 0x000000  合相基波正向有功电能(可配置为读后清零）
#define r_FundUaRrms    0x48    /// (mic_reg_s){0x00, 0x3, 0x48}  /// 0x000000  基波 A 相电压有效值
#define r_FundUbRrms    0x49    /// (mic_reg_s){0x00, 0x3, 0x49}  /// 0x000000  基波 B 相电压有效值
#define r_FundUcRrms    0x4A    /// (mic_reg_s){0x00, 0x3, 0x4A}  /// 0x000000  基波 C 相电压有效值
#define r_FundIaRrms    0x4B    /// (mic_reg_s){0x00, 0x3, 0x4B}  /// 0x000000  基波 A 相电流有效值
#define r_FundIbRrms    0x4C    /// (mic_reg_s){0x00, 0x3, 0x4C}  /// 0x000000  基波 B 相电流有效值
#define r_FundIcRrms    0x4D    /// (mic_reg_s){0x00, 0x3, 0x4D}  /// 0x000000  基波 C 相电流有效值
#define r_Fundlag       0x4E    /// (mic_reg_s){0x00, 0x3, 0x4E}  /// 0x000000  基波电能寄存器的工作状态，读后清零
#define r_SAGFlag       0x4F    /// (mic_reg_s){0x00, 0x3, 0x4F}  /// 0x000000  SAG 标志寄存器
#define r_PeakUa        0x50    /// (mic_reg_s){0x00, 0x3, 0x50}  /// 0x000000  A 相电压最大值(SAG_Peak)
#define r_PeakUb        0x51    /// (mic_reg_s){0x00, 0x3, 0x51}  /// 0x000000  B 相电压最大值(SAG_PEak)
#define r_PeakUc        0x52    /// (mic_reg_s){0x00, 0x3, 0x52}  /// 0x000000  C 相电压最大值(SAG_Peak)
#define r_FstCntQa      0x53    /// (mic_reg_s){0x00, 0x3, 0x53}  /// 0x000000  A 相无功快速脉冲计数
#define r_FstCntQb      0x54    /// (mic_reg_s){0x00, 0x3, 0x54}  /// 0x000000  B 相无功快速脉冲计数
#define r_FstCntQc      0x55    /// (mic_reg_s){0x00, 0x3, 0x55}  /// 0x000000  C 相无功快速脉冲计数
#define r_FstCntQt      0x56    /// (mic_reg_s){0x00, 0x3, 0x56}  /// 0x000000  合相无功快速脉冲计数
#define r_FundQa        0x57    /// (mic_reg_s){0x00, 0x3, 0x57}  /// 0x000000  A 相基波无功功率
#define r_FundQb        0x58    /// (mic_reg_s){0x00, 0x3, 0x58}  /// 0x000000  B 相基波无功功率
#define r_FundQc        0x59    /// (mic_reg_s){0x00, 0x3, 0x59}  /// 0x000000  C 相基波无功功率
#define r_FundQt        0x5A    /// (mic_reg_s){0x00, 0x3, 0x5A}  /// 0x000000  合相基波无功功率
#define r_Vrefgain      0x5C    /// (mic_reg_s){0x00, 0x3, 0x5C}  /// 0x000000  Vref 自动补偿系数
#define r_ChipID        0x5D    /// (mic_reg_s){0x00, 0x3, 0x5D}  /// 0x7022E1  芯片版本指示寄存器
#define r_ChkSum1       0x5E    /// (mic_reg_s){0x00, 0x3, 0x5E}  /// 0x01F2F5  新增校表寄存器校验和(60H~7FH)
#define r_ChkSumCRC     0x5F    /// (mic_reg_s){0x00, 0x3, 0x5F}  /// 0x00912A  所有校表参数校验和 CRC16(01~3F/60~7F)
#define r_HarPa         0x60    /// (mic_reg_s){0x00, 0x3, 0x60}  /// 0x000000  A 相谐波有功功率
#define r_HarPb         0x61    /// (mic_reg_s){0x00, 0x3, 0x61}  /// 0x000000  B 相谐波有功功率
#define r_HarPc         0x62    /// (mic_reg_s){0x00, 0x3, 0x62}  /// 0x000000  C 相谐波有功功率
#define r_HarPtP        0x63    /// (mic_reg_s){0x00, 0x3, 0x63}  /// 0x000000  合相正向谐波有功功率
#define r_HarPtN        0x64    /// (mic_reg_s){0x00, 0x3, 0x64}  /// 0x000000  合相反向谐波有功功率
#define r_HarUaRrms     0x65    /// (mic_reg_s){0x00, 0x3, 0x65}  /// 0x000000  谐波 A 相电压有效值
#define r_HarUbRrms     0x66    /// (mic_reg_s){0x00, 0x3, 0x66}  /// 0x000000  谐波 B 相电压有效值
#define r_HarUcRrms     0x67    /// (mic_reg_s){0x00, 0x3, 0x67}  /// 0x000000  谐波 C 相电压有效值
#define r_HarIaRrms     0x68    /// (mic_reg_s){0x00, 0x3, 0x68}  /// 0x000000  谐波 A 相电流有效值
#define r_HarIbRrms     0x69    /// (mic_reg_s){0x00, 0x3, 0x69}  /// 0x000000  谐波 B 相电流有效值
#define r_HarIcRrms     0x6A    /// (mic_reg_s){0x00, 0x3, 0x6A}  /// 0x000000  谐波 C 相电流有效值
#define r_HarEpa        0x6B    /// (mic_reg_s){0x00, 0x3, 0x6B}  /// 0x000000  A 相谐波有功电能（可配置为读后清零）
#define r_HarEpb        0x6C    /// (mic_reg_s){0x00, 0x3, 0x6C}  /// 0x000000  B 相谐波有功电能（可配置为读后清零）
#define r_HarEpc        0x6D    /// (mic_reg_s){0x00, 0x3, 0x6D}  /// 0x000000  C 相谐波有功电能（可配置为读后清零）
#define r_HarEptP       0x6E    /// (mic_reg_s){0x00, 0x3, 0x6E}  /// 0x000000  合相谐波正向有功电能(可配置为读后清零）
#define r_HarEptN       0x6F    /// (mic_reg_s){0x00, 0x3, 0x6F}  /// 0x000000  合相谐波反向有功电能(可配置为读后清零）
#define r_FundEpaN      0x70    /// (mic_reg_s){0x00, 0x3, 0x70}  /// 0x000000  A 相基波反向有功电能（可配置为读后清零）
#define r_FundEpbN      0x71    /// (mic_reg_s){0x00, 0x3, 0x71}  /// 0x000000  B 相基波反向有功电能（可配置为读后清零）
#define r_FundEpcN      0x72    /// (mic_reg_s){0x00, 0x3, 0x72}  /// 0x000000  C 相基波反向有功电能（可配置为读后清零）
#define r_FundEptN      0x73    /// (mic_reg_s){0x00, 0x3, 0x73}  /// 0x000000  合相基波反向有功电能(可配置为读后清零）
#define r_UadetCNT      0x74    /// (mic_reg_s){0x00, 0x3, 0x74}  /// 0x000000  A 相电压通道 SAG/Peak 计数值(SAG_Peak)
#define r_UbdetCNT      0x75    /// (mic_reg_s){0x00, 0x3, 0x75}  /// 0x000000  B 相电压通道 SAG/Peak 计数值(SAG_Peak)
#define r_UcdetCNT      0x76    /// (mic_reg_s){0x00, 0x3, 0x76}  /// 0x000000  C 相电压通道 SAG/Peak 计数值(SAG_Peak)
#define r_UaDetVP       0x77    /// (mic_reg_s){0x00, 0x3, 0x77}  /// 0x000000  A 相电压通道 SAG/Peak/INT 期间的最值
#define r_UbDetVP       0x78    /// (mic_reg_s){0x00, 0x3, 0x78}  /// 0x000000  B 相电压通道 SAG/Peak/INT 期间的最值
#define r_UcDetVP       0x79    /// (mic_reg_s){0x00, 0x3, 0x79}  /// 0x000000  C 相电压通道 SAG/Peak/INT 期间的最值
#define r_UabRms        0x7A    /// (mic_reg_s){0x00, 0x3, 0x7A}  /// 0x000000  AB 线电压有效值
#define r_UbcRms        0x7B    /// (mic_reg_s){0x00, 0x3, 0x7B}  /// 0x000000  BC 线电压有效值
#define r_UacRms        0x7C    /// (mic_reg_s){0x00, 0x3, 0x7C}  /// 0x000000  AC 线电压有效值
#define r_PtrWavebuff   0x7E    /// (mic_reg_s){0x00, 0x3, 0x7E}  /// 0x000000  缓冲数据指针，指示内部缓冲 buffer 已有数据长度
#define r_WaveBuff      0x7F    /// (mic_reg_s){0x00, 0x3, 0x7F}  /// 0x000000  缓冲数据寄存器，内部自增益，重复读取直至读完缓冲数据长度

///以下部分为第二套计量参数寄存器组
#define r_Epabak        0x00    /// (mic_reg_s){0x01, 0x3, 0x00}  /// 0x000000  A 相有功电能备份（可配置为读后清零）  
#define r_Epbbak        0x01    /// (mic_reg_s){0x01, 0x3, 0x01}  /// 0x000000  B 相有功电能备份（可配置为读后清零）
#define r_Epcbak        0x02    /// (mic_reg_s){0x01, 0x3, 0x02}  /// 0x000000  C 相有功电能备份（可配置为读后清零）
#define r_Eptbak        0x03    /// (mic_reg_s){0x01, 0x3, 0x03}  /// 0x000000  合相有功电能备份（可配置为读后清零）
#define r_Eqabak        0x04    /// (mic_reg_s){0x01, 0x3, 0x04}  /// 0x000000  A 相无功电能备份（可配置为读后清零）
#define r_Eqbbak        0x05    /// (mic_reg_s){0x01, 0x3, 0x05}  /// 0x000000  B 相无功电能备份（可配置为读后清零）
#define r_Eqcbak        0x06    /// (mic_reg_s){0x01, 0x3, 0x06}  /// 0x000000  C 相无功电能备份（可配置为读后清零）
#define r_Eqtbak        0x07    /// (mic_reg_s){0x01, 0x3, 0x07}  /// 0x000000  合相无功电能备份（可配置为读后清零）
#define r_FstCntPabak   0x08    /// (mic_reg_s){0x01, 0x3, 0x08}  /// 0x000000  A 相有功快速脉冲计数备份
#define r_FstCntPbbak   0x09    /// (mic_reg_s){0x01, 0x3, 0x09}  /// 0x000000  B 相有功快速脉冲计数备份
#define r_FstCntPcbak   0x0A    /// (mic_reg_s){0x01, 0x3, 0x0A}  /// 0x000000  C 相有功快速脉冲计数备份
#define r_FstCntPtbak   0x0B    /// (mic_reg_s){0x01, 0x3, 0x0B}  /// 0x000000  合相有功快速脉冲计数备份
#define r_FstCntQabak   0x0C    /// (mic_reg_s){0x01, 0x3, 0x0C}  /// 0x000000  A 相无功快速脉冲计数备份
#define r_FstCntQbbak   0x0D    /// (mic_reg_s){0x01, 0x3, 0x0D}  /// 0x000000  B 相无功快速脉冲计数备份
#define r_FstCntQcbak   0x0E    /// (mic_reg_s){0x01, 0x3, 0x0E}  /// 0x000000  C 相无功快速脉冲计数备份
#define r_FstCntQtbak   0x0F    /// (mic_reg_s){0x01, 0x3, 0x0F}  /// 0x000000  合相无功快速脉冲计数备份
#define r_Esabak        0x10    /// (mic_reg_s){0x01, 0x3, 0x10}  /// 0x000000  A 相视在电能备份（可配置为读后清零）
#define r_Esbbak        0x11    /// (mic_reg_s){0x01, 0x3, 0x11}  /// 0x000000  B 相视在电能备份（可配置为读后清零）
#define r_Escbak        0x12    /// (mic_reg_s){0x01, 0x3, 0x12}  /// 0x000000  C 相视在电能备份（可配置为读后清零）
#define r_Estbak        0x13    /// (mic_reg_s){0x01, 0x3, 0x13}  /// 0x000000  合相视在电能备份（可配置为读后清零）
#define r_FundEpabak    0x14    /// (mic_reg_s){0x01, 0x3, 0x14}  /// 0x000000  A 相基波正向有功电能备份（可配置为读后清零）
#define r_FundEpbbak    0x15    /// (mic_reg_s){0x01, 0x3, 0x15}  /// 0x000000  B 相基波正向有功电能备份（可配读后清零）
#define r_FundEpcbak    0x16    /// (mic_reg_s){0x01, 0x3, 0x16}  /// 0x000000  C 相基波正向有功电能备份（可配读后清零）
#define r_FundEptbak    0x17    /// (mic_reg_s){0x01, 0x3, 0x17}  /// 0x000000  合相基波正向有功电能备份(可配读后清零）
#define r_FundEpabakN   0x18    /// (mic_reg_s){0x01, 0x3, 0x18}  /// 0x000000  A 相基波反向有功电能备份（可配读后清零）
#define r_FundEpbbakN   0x19    /// (mic_reg_s){0x01, 0x3, 0x19}  /// 0x000000  B 相基波反向有功电能备份（可配读后清零）
#define r_FundEpcbakN   0x1A    /// (mic_reg_s){0x01, 0x3, 0x1A}  /// 0x000000  C 相基波反向有功电能备份（可配读后清零）
#define r_FundEptbakN   0x1B    /// (mic_reg_s){0x01, 0x3, 0x1B}  /// 0x000000  合相基波反向有功电能备份(可配读后清零）
#define r_HarEpabak     0x1C    /// (mic_reg_s){0x01, 0x3, 0x1C}  /// 0x000000  A 相谐波有功电能备份（可配读后清零）
#define r_HarEpbbak     0x1D    /// (mic_reg_s){0x01, 0x3, 0x1D}  /// 0x000000  B 相谐波有功电能备份（可配置为读后清零）
#define r_HarEpcbak     0x1E    /// (mic_reg_s){0x01, 0x3, 0x1E}  /// 0x000000  C 相谐波有功电能备份（可配置为读后清零）
#define r_HarEptPbak    0x1F    /// (mic_reg_s){0x01, 0x3, 0x1F}  /// 0x000000  合相谐波正向有功电能备份(可配读后清零）
#define r_HarEptNbak    0x20    /// (mic_reg_s){0x01, 0x3, 0x20}  /// 0x000000  合相谐波反向有功电能备份(可配读后清零）

/////校表数据
#define Reserved        0x00    /// (mic_reg_s){0x02, 0x2, 0x00} /// 0xAAAA    校表参数寄存器起始标志   
#define w_ModeCfg       0x01    /// (mic_reg_s){0x02, 0x2, 0x01} /// 0x89AA    模式相关控制
#define w_PGACtrl       0x02    /// (mic_reg_s){0x02, 0x2, 0x02} /// 0x0000    ADC 增益选择
#define w_EMUCfg        0x03    /// (mic_reg_s){0x02, 0x2, 0x03} /// 0x0804    EMU 模块配置寄存器
#define w_PgainA        0x04    /// (mic_reg_s){0x02, 0x2, 0x04} /// 0x0000    A 相有功功率增益
#define w_PgainB        0x05    /// (mic_reg_s){0x02, 0x2, 0x05} /// 0x0000    B 相有功功率增益
#define w_PgainC        0x06    /// (mic_reg_s){0x02, 0x2, 0x06} /// 0x0000    C 相有功功率增益
#define w_QgainA        0x07    /// (mic_reg_s){0x02, 0x2, 0x07} /// 0x0000    A 相无功功率增益
#define w_QgainB        0x08    /// (mic_reg_s){0x02, 0x2, 0x08} /// 0x0000    B 相无功功率增益
#define w_QgainC        0x09    /// (mic_reg_s){0x02, 0x2, 0x09} /// 0x0000    C 相无功功率增益
#define w_SgainA        0x0A    /// (mic_reg_s){0x02, 0x2, 0x0A} /// 0x0000    A 相视在功率增益
#define w_SgainB        0x0B    /// (mic_reg_s){0x02, 0x2, 0x0B} /// 0x0000    B 相视在功率增益
#define w_SgainC        0x0C    /// (mic_reg_s){0x02, 0x2, 0x0C} /// 0x0000    C 相视在功率增益
#define w_PhSregApq0    0x0D    /// (mic_reg_s){0x02, 0x2, 0x0D} /// 0x0000    A 相相位校正 0
#define w_PhSregBpq0    0x0E    /// (mic_reg_s){0x02, 0x2, 0x0E} /// 0x0000    B 相相位校正 0
#define w_PhSregCpq0    0x0F    /// (mic_reg_s){0x02, 0x2, 0x0F} /// 0x0000    C 相相位校正 0
#define w_PhSregApq1    0x10    /// (mic_reg_s){0x02, 0x2, 0x10} /// 0x0000    A 相相位校正 1
#define w_PhSregBpq1    0x11    /// (mic_reg_s){0x02, 0x2, 0x11} /// 0x0000    B 相相位校正 1
#define w_PhSregCpq1    0x12    /// (mic_reg_s){0x02, 0x2, 0x12} /// 0x0000    C 相相位校正 1
#define w_PoffsetA      0x13    /// (mic_reg_s){0x02, 0x2, 0x13} /// 0x0000    A 相有功功率 offset 校正
#define w_PoffsetB      0x14    /// (mic_reg_s){0x02, 0x2, 0x14} /// 0x0000    B 相有功功率 offset 校正
#define w_PoffsetC      0x15    /// (mic_reg_s){0x02, 0x2, 0x15} /// 0x0000    C 相有功功率 offset 校正
#define w_QPhscal       0x16    /// (mic_reg_s){0x02, 0x2, 0x16} /// 0x0000    基波无功相位校正
#define w_UgainA        0x17    /// (mic_reg_s){0x02, 0x2, 0x17} /// 0x0000    A 相电压增益
#define w_UgainB        0x18    /// (mic_reg_s){0x02, 0x2, 0x18} /// 0x0000    B 相电压增益
#define w_UgainC        0x19    /// (mic_reg_s){0x02, 0x2, 0x19} /// 0x0000    C 相电压增益
#define w_IgainA        0x1A    /// (mic_reg_s){0x02, 0x2, 0x1A} /// 0x0000    A 相电流增益
#define w_IgainB        0x1B    /// (mic_reg_s){0x02, 0x2, 0x1B} /// 0x0000    B 相电流增益
#define w_IgainC        0x1C    /// (mic_reg_s){0x02, 0x2, 0x1C} /// 0x0000    C 相电流增益
#define w_Istarup       0x1D    /// (mic_reg_s){0x02, 0x2, 0x1D} /// 0x0160    起动电流阈值设置
#define w_Hfconst       0x1E    /// (mic_reg_s){0x02, 0x2, 0x1E} /// 0x0500    高频脉冲输出设置
#define w_FailVoltage   0x1F    /// (mic_reg_s){0x02, 0x2, 0x1F} /// 0x0600    失压阈值设置（三相四线模式） 2 0x1200 失压阈值设置（三相三线模式）
#define w_GainADC7      0x20    /// (mic_reg_s){0x02, 0x2, 0x20} /// 0x0000    第七路 ADC 输入信号增益
#define w_QoffsetA      0x21    /// (mic_reg_s){0x02, 0x2, 0x21} /// 0x0000    A 相无功功率 offset 校正
#define w_QoffsetB      0x22    /// (mic_reg_s){0x02, 0x2, 0x22} /// 0x0000    B 相无功功率 offset 校正
#define w_QoffsetC      0x23    /// (mic_reg_s){0x02, 0x2, 0x23} /// 0x0000    C 相无功功率 offset 校正
#define w_UaRmsoffset   0x24    /// (mic_reg_s){0x02, 0x2, 0x24} /// 0x0000    A 相电压有效值 offset 校正
#define w_UbRmsoffset   0x25    /// (mic_reg_s){0x02, 0x2, 0x25} /// 0x0000    B 相电压有效值 offset 校正
#define w_UcRmsoffset   0x26    /// (mic_reg_s){0x02, 0x2, 0x26} /// 0x0000    C 相电压有效值 offset 校正
#define w_IaRmsoffset   0x27    /// (mic_reg_s){0x02, 0x2, 0x27} /// 0x0000    A 相电流有效值 offset 校正
#define w_IbRmsoffset   0x28    /// (mic_reg_s){0x02, 0x2, 0x28} /// 0x0000    B 相电流有效值 offset 校正
#define w_IcRmsoffset   0x29    /// (mic_reg_s){0x02, 0x2, 0x29} /// 0x0000    C 相电流有效值 offset 校正
#define w_UoffsetA      0x2A    /// (mic_reg_s){0x02, 0x2, 0x2A} /// 0x0000    A 相电压通道 ADC offset 校正
#define w_UoffsetB      0x2B    /// (mic_reg_s){0x02, 0x2, 0x2B} /// 0x0000    B 相电压通道 ADC offset 校正
#define w_UoffsetC      0x2C    /// (mic_reg_s){0x02, 0x2, 0x2C} /// 0x0000    C 相电压通道 ADC offset 校正
#define w_IoffsetA      0x2D    /// (mic_reg_s){0x02, 0x2, 0x2D} /// 0x0000    A 相电流通道 ADC offset 校正
#define w_IoffsetB      0x2E    /// (mic_reg_s){0x02, 0x2, 0x2E} /// 0x0000    B 相电流通道 ADC offset 校正
#define w_IoffsetC      0x2F    /// (mic_reg_s){0x02, 0x2, 0x2F} /// 0x0000    C 相电流通道 ADC offset 校正
#define w_EMUIE         0x30    /// (mic_reg_s){0x02, 0x2, 0x30} /// 0x0001    中断使能
#define w_ModuleCFG     0x31    /// (mic_reg_s){0x02, 0x2, 0x31} /// 0x3527    电路模块配置寄存器
#define w_AllGain       0x32    /// (mic_reg_s){0x02, 0x2, 0x32} /// 0x0000    全通道增益，用于校正 ref 自校正
#define w_HFDouble      0x33    /// (mic_reg_s){0x02, 0x2, 0x33} /// 0x0000    脉冲常数加倍选择
#define w_FundGain      0x34    /// (mic_reg_s){0x02, 0x2, 0x34} /// 0x2C59    基波增益校正
#define w_PinCtrl       0x35    /// (mic_reg_s){0x02, 0x2, 0x35} /// 0x000F    数字 pin 上下拉电阻选择控制
#define w_Pstartup      0x36    /// (mic_reg_s){0x02, 0x2, 0x36} /// 0x0030    起动功率阈值设置
#define w_Iregion0      0x37    /// (mic_reg_s){0x02, 0x2, 0x37} /// 0x7FFF    相位补偿区域设置寄存器
#define w_Cyclength     0x38    /// (mic_reg_s){0x02, 0x2, 0x38} /// 0x1000    SAG 数据长度设置寄存器
#define w_SAGLvl        0x39    /// (mic_reg_s){0x02, 0x2, 0x39} /// 0x4500    SAG 检测阈值设置寄存器
#define w_PEAKLvl       0x3A    /// (mic_reg_s){0x02, 0x2, 0x3A} /// 0x0000    PEAK 检测阈值设置寄存器
#define w_UINTLvl       0x3B    /// (mic_reg_s){0x02, 0x2, 0x3B} /// 0x0000    电压中断 INT 检测阈值设置寄存器
#define w_InRmsoffse    0x3C    /// (mic_reg_s){0x02, 0x2, 0x3C} /// 0x0000    第七路电流有效值 offset 校正
#define w_IoffsetN      0x3D    /// (mic_reg_s){0x02, 0x2, 0x3D} /// 0x0000    第七路电流通道 ADC offset 校正
#define w_HFconst_Har   0x3E    /// (mic_reg_s){0x02, 0x2, 0x3E} /// 0x0000    谐波高频脉冲输出设置
#define w_Iregion1      0x60    /// (mic_reg_s){0x02, 0x2, 0x60} /// 0x0000    相位补偿区域设置寄存器 1
#define w_PhSregApq2    0x61    /// (mic_reg_s){0x02, 0x2, 0x61} /// 0x0000    A 相相位校正 2
#define w_PhSregBpq2    0x62    /// (mic_reg_s){0x02, 0x2, 0x62} /// 0x0000    B 相相位校正 2
#define w_PhSregCpq2    0x63    /// (mic_reg_s){0x02, 0x2, 0x63} /// 0x0000    C 相相位校正 2
#define w_PoffsetAL     0x64    /// (mic_reg_s){0x02, 0x1, 0x64} /// 0x00      A 相有功功率 offset 校正低字节
#define w_PoffsetBL     0x65    /// (mic_reg_s){0x02, 0x1, 0x65} /// 0x00      B 相有功功率 offset 校正低字节
#define w_PoffsetCL     0x66    /// (mic_reg_s){0x02, 0x1, 0x66} /// 0x00      C 相有功功率 offset 校正低字节
#define w_QoffsetAL     0x67    /// (mic_reg_s){0x02, 0x1, 0x67} /// 0x00      A 相无功功率 offset 校正低字节
#define w_QoffsetBL     0x68    /// (mic_reg_s){0x02, 0x1, 0x68} /// 0x00      B 相无功功率 offset 校正低字节
#define w_QoffsetCL     0x69    /// (mic_reg_s){0x02, 0x1, 0x69} /// 0x00      C 相无功功率 offset 校正低字节
#define w_ItRmsoffset   0x6A    /// (mic_reg_s){0x02, 0x2, 0x6A} /// 0x0000    电流矢量和 offset 校正寄存器
#define w_TPSoffset     0x6B    /// (mic_reg_s){0x02, 0x2, 0x6B} /// 0x0000    TPS 初值校正寄存器
#define w_TPSgain       0x6C    /// (mic_reg_s){0x02, 0x2, 0x6C} /// 0x0000    TPS 斜率校正寄存器
#define w_TCcoffA       0x6D    /// (mic_reg_s){0x02, 0x2, 0x6D} /// 0xFEFF    Vrefgain 的二次系数
#define w_TCcoffB       0x6E    /// (mic_reg_s){0x02, 0x2, 0x6E} /// 0xEF7A    Vrefgain 的一次系数
#define w_TCcoffC       0x6F    /// (mic_reg_s){0x02, 0x2, 0x6F} /// 0x047C    Vrefgain 的常数项
#define w_EMCfg         0x70    /// (mic_reg_s){0x02, 0x2, 0x70} /// 0x0000    新增算法控制寄存器 1
#define w_OILVL         0x71    /// (mic_reg_s){0x02, 0x2, 0x71} /// 0x0000    过流阈值设置寄存器
#define w_ANACfg1       0x72    /// (mic_reg_s){0x02, 0x2, 0x72} /// 0x0000    模拟控制寄存器 1
#define w_ANACfg2       0x73    /// (mic_reg_s){0x02, 0x2, 0x73} /// 0x0000    模拟控制寄存器 2
#define w_ANACfg3       0x74    /// (mic_reg_s){0x02, 0x2, 0x74} /// 0x0000    模拟控制寄存器 3
#define w_DIGCfg        0x75    /// (mic_reg_s){0x02, 0x2, 0x75} /// 0x0000    新增数字算法控制寄存器 2
#define w_Ixphase       0x76    /// (mic_reg_s){0x02, 0x3, 0x76} /// 0x000000  电流通道移采样点控制寄存器，其中高中低 3 个字节分别对应 Ia/Ib/Ic
#define w_Uxphase       0x77    /// (mic_reg_s){0x02, 0x3, 0x77} /// 0x000000  电压通道移采样点控制寄存器，其中高中低 3 个字节分别对应 I0/Ub/Uc

///以下为特殊命令
#define w_WaveCommand   0x40    /// (mic_reg_s){0x02, 0x2, 0x40} /// 0x0000    波形数据缓冲起动命令（0xC0 命令）  
#define w_WaveAdd       0x41    /// (mic_reg_s){0x02, 0x2, 0x41} /// 0x0000    指定缓冲数据起始读取点（C1 命令）
#define w_BakReg        0x42    /// (mic_reg_s){0x02, 0x2, 0x42} /// 0x0000    能量寄存器备份命令
#define w_CalRegClr     0x43    /// (mic_reg_s){0x02, 0x2, 0x43} /// 0x0000    清校表参数（0xC3 命令）
#define w_SynCoef       0x44    /// (mic_reg_s){0x02, 0x2, 0x44} /// 0x0000    同步数据功能系数
#define w_SynCtrl       0x45    /// (mic_reg_s){0x02, 0x2, 0x45} /// 0x0000    同步数据功能控制
#define w_Regsel        0x46    /// (mic_reg_s){0x02, 0x2, 0x46} /// 0x0000    计量/校表参数寄存器切换(0xC6 命令)
#define w_WrPreg        0x49    /// (mic_reg_s){0x02, 0x2, 0x49} /// 0x005A    写保护寄存器，默认打开，可写
#define r_FrontCom      0x4C    /// (mic_reg_s){0x02, 0x2, 0x4C} /// 0x0000    第二计量参数读取前导命令
#define r_ContCom       0x4D    /// (mic_reg_s){0x02, 0x2, 0x4D} /// 0x0000    连读读命令字扩展命令
#define w_ModeCfg2      0x50    /// (mic_reg_s){0x02, 0x2, 0x50} /// 0x0000    模式控制命令(3.3V 全失压)
#define VOLDROPCFG      0x51    /// (mic_reg_s){0x02, 0x2, 0x51} /// 0x0000    全失压参数配置寄存器(3.3V 全失压)
#define VOLDROPIF       0x52    /// (mic_reg_s){0x02, 0x2, 0x52} /// 0x0000    全失压标志输出寄存器(3.3V 全失压)
#define w_SrstRge       0x53    /// (mic_reg_s){0x02, 0x2, 0x53} /// 0x0000    软件复位寄存器（0xD3 命令）


typedef union
{
    struct
    {
        uint16_t chk;       // 
        uint16_t cs;        // 校表参数校验和
        uint16_t U_gain;    // 电压增益
        uint16_t I_gain;    // 电流增益
        uint16_t P_gain;    // 功率增益
        uint16_t Phs_ofst;  // 相位校准0
        uint16_t Phs_ofst1; // 相位校准1
        uint16_t Phs_ofst2; // 相位校准2
        uint16_t P_ofst;    // 功率偏移校准
        uint16_t I_ofst;    // 电流偏移校准
        uint16_t Q_ofst;    // 无功偏置校准
        uint16_t P_ofstl;   // 功率偏移校准低字节
        uint16_t Q_ofstl;   // 无功偏置校准低字节
        uint16_t HFconst;   // 高频脉冲常数
        uint16_t I_RatioN;  // 电流调整系数
        uint16_t I_zero_reg;
	};
	uint8_t reserve[32];
}CalPara_s;

typedef struct
{
    CalPara_s     cal_para[CHANNEL_NUM];    
    MeasurePara_s mic_para;
}EepromReserveSpace_s;

typedef struct
{
    SMALLOC(EepromReserveSpace_s, space, 512);  /// 如果这里编译报错，需在datastore.h中修改 CAL_DATA_SIZE 的大小(修改为128的整数倍)直到编译通过，默认是 256.
} MicStore_s;

/// @brief 默认校表参数
static const CalPara_s cal_para_default = {
    .cs         = 0,
    .U_gain     = 0,    
    .I_gain     = 0,    
    .P_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .I_ofst     = 0,
    .Q_ofst     = 0,
    .HFconst    = 128,
    .I_RatioN   = 1,
    .I_zero_reg = 0,
};
/// @brief 零线通道默认值
static const CalPara_s cal_para_N_default = {
    .cs         = 0,
    .U_gain     = 0,    
    .I_gain     = 0,    
    .P_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .I_ofst     = 0,
    .Q_ofst     = 0,
    .HFconst    = 128,
    .I_RatioN   = 1,
    .I_zero_reg = 0,
};

static const MeasurePara_s mic_para_default =
{
    .cs         = 0,
    .acc_mode   = MIC_ALGACC_MODE,          // 默认代数和计量方式
    .wiring_mode= MIC_WIRE_3P4W_MODE,
    .led_mode   = 0,
    .constant   = METER_CONST,
};


// 常数定义
#define DATA_2EXP12             4096ul     // 2 ^ 12
#define DATA_2EXP13             8192ul     // 2 ^ 13
#define DATA_2EXP14             16384ul    // 2 ^ 14 
#define DATA_2EXP15             32768ul    // 2 ^ 15
#define DATA_2EXP16             65536ul    // 2 ^ 16
#define DATA_2EXP20             1048576ul  // 2 ^ 20
#define DATA_2EXP23             8388608ul  // 2 ^ 23
#define DATA_2EXP24             16777216ul // 2 ^ 24
#define DATA_2EXP31             0x80000000ul // 2 ^ 31

#define CAL_POWER               (((float)METER_CONST * cal_para[CHANNEL_1].HFconst) / 3089.90f)   // 单位: 1W 公式: P=preg*25920*10^6/2^23*EC*HFConst
#define EMU_PWR_START           (uint32_t)(START_PWR * 0.8 * CAL_POWER)                           // 计量芯片启动功率阈值
#define CAL_POFFSET(Pe,Preal)   ((float)(Preal-Pe)*DATA_2EXP31*METER_CONST*cal_para[CHANNEL_1].HFconst/2.592f/pow(10,10))    // 公式: Poffset = INT[(Preal*EC*HFCONST*2^31*(-Err%))/(2.592*10^10)]
#define SAG_LVL_THD             (uint32_t)((INPUT_VOLTAGE * 1.414 * 32 * 50) / 1000)              // sag 判断阈值
#define SAG_CYCLE               (4)     // (SGA判断半周波数)
#define SAMPLE_NUM              6       // 校表采样次数
/* 零线路电流参数 */
#define Gs                      16      // 零线通道放大增益(零线使用锰铜采样时定义): 1/2/8/16


#ifdef COM_MEASURE_IC
#define MSPI_TRANS(x,m)         hal_spi_trans(COM_MEASURE_IC, x,m)
#define MSPI_ON()               hal_spi_deviceon(COM_MEASURE_IC)
#define MSPI_OFF()              hal_spi_deviceoff(COM_MEASURE_IC)
#else
#define MSPI_TRANS(x,m)         0
#define MSPI_ON()
#define MSPI_OFF()
#endif
#define DUMMY_BYTE              0xFF

#define CAL_CS16(ptr,len)       crc16(0,(uint8_t*)(ptr) + 4, len - 4)

#define ee_cal_read(x,y,z)      eeprom.read(x,y,z)
#define ee_cal_write(x,y,z)     eeprom.write(x,y,z)

#define mcu_cal_read(x,y,z)     hal_flash.read((MCU_CAL_DATA_BASE + x), y, z)
#define mcu_cal_write(x,y,z)    hal_flash.write((MCU_CAL_DATA_BASE + x), y, z)
#define MEASURE_PARA_ADDR       (member_offset(EepromReserveSpace_s, mic_para))  // 计量参数地址

uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph);

#if USE_EMU_AT_LOSS_VOLTAGE
static void (*power_off_running)(void);
#endif
static uint8_t led_act_remain_time;         // 有功电能脉冲LED计时器
static uint8_t led_rea_remain_time;         // 无功电能脉冲LED计时器
#if USE_LED_VAPULSE
static uint8_t led_app_remain_time;         // 视在电能脉冲LED计时器
#endif
static uint8_t       pulse_cnt[2];          // 合相有功, 无功
static CalPara_s     cal_para[CHANNEL_NUM]; // att7022e-校正参数
static MeasurePara_s measure_para;          // 计量参数
InstantVal_s         instant_value;         // 瞬时计量数据块,如电流电压频率功率等等
static bool          measure_status;        // 计量状态

/// @brief spi初始化
/// @param  
static void emu_spi_init(void)
{
    hal_gpio.mic_init(GPIO_OPEN);
}

/// @brief 计量芯片寄存器读取
/// @param  reg 寄存器地址
/// @return 寄存器值
static uint32_t emu_register_read(uint8_t reg)
{
    uint8_t  i;
    uint32_t dat = 0;
    uint32_t chksum;

    for(i = 3; i > 0; i--)
    {
        MSPI_ON();
        MSPI_TRANS(reg, 1);
        dat = (uint32_t)MSPI_TRANS(DUMMY_BYTE, 0);  dat <<= 8;
        dat |= MSPI_TRANS(DUMMY_BYTE, 0);           dat <<= 8;
        dat |= MSPI_TRANS(DUMMY_BYTE, 0);
        MSPI_OFF();

        MSPI_ON();
        MSPI_TRANS(r_BckReg_addr, 1);
        chksum = (uint32_t)MSPI_TRANS(DUMMY_BYTE, 0);  chksum <<= 8;
        chksum |= MSPI_TRANS(DUMMY_BYTE, 0);           chksum <<= 8;
        chksum |= MSPI_TRANS(DUMMY_BYTE, 0);
        MSPI_OFF();

        if(dat == chksum) break;
    }

    return dat;
}

/// @brief 计量芯片寄存器写入
/// @param reg 寄存器地址
/// @param dat 寄存器值 
/// @return TRUE 成功 FALSE 失败
static bool emu_register_write(uint8_t reg, uint32_t dat)
{
    uint8_t i;
    uint32_t chksum;

    for(i = 3; i > 0; i--)
    {
        MSPI_ON();
        MSPI_TRANS((reg | 0x80), 1);
        MSPI_TRANS((uint8_t)(dat >> 16), 1);
        MSPI_TRANS((uint8_t)(dat >> 8),  1);
        MSPI_TRANS((uint8_t)(dat),       1);
        MSPI_OFF();

        MSPI_ON();
        MSPI_TRANS(r_BckReg_addr, 1);
        chksum = MSPI_TRANS(DUMMY_BYTE,  0); chksum <<= 8;
        chksum |= MSPI_TRANS(DUMMY_BYTE, 0); chksum <<= 8;
        chksum |= MSPI_TRANS(DUMMY_BYTE, 0);
        MSPI_OFF();

        if(dat == chksum) return TRUE;
    }

    return FALSE;
}

static void emu_reset(void)
{
#if USE_HDW_RST_MIC
    // 开关计量芯片电源彻底复位计量芯片
    IO_EMU_PWR_OFF();
    hal_timer.xms(2);
    IO_EMU_PWR_ON();
    hal_timer.xms(40);
#endif
    /// 软件复位计量芯片
    emu_register_write(w_SrstRge, 0x000000);
    hal_timer.xms(25);
    measure_status = FALSE;
}


/// @brief 校验和处理
/// @param type 0-清除校验和，1—获取校验和，2-检查校验和
/// @return TRUE 校验和正确，校验和错误
static bool emu_reg_check_sum_pro(uint8_t type)
{
    static uint32_t emu_reg_check_sum;          // att7022e-校验值01-3F
    static uint32_t emu_reg_check_sum2;         // att7022e-校验值01-3F,60-7F
    switch(type)
    {
        case 0:
            emu_reg_check_sum  = 0;
            emu_reg_check_sum2 = 0;
            return TRUE;
        case 1:
            emu_reg_check_sum  = emu_register_read(r_ChkSum);
            emu_reg_check_sum2 = emu_register_read(r_ChkSumCRC);
            return TRUE;
        case 2:
            if(emu_reg_check_sum  == emu_register_read(r_ChkSum) && \
               emu_reg_check_sum2 == emu_register_read(r_ChkSumCRC)) return TRUE;
            break;
        case 3:
            emu_reg_check_sum  = ~emu_reg_check_sum;
            emu_reg_check_sum2 = ~emu_reg_check_sum2;
            return TRUE;
        default:
            break;
    }
    return FALSE;
}

static bool emu_para_refresh(const CalPara_s* para, uint8_t hfconst_gain)
{
    uint32_t hf_const, emucfg = 0x7C84, emc_cfg, istart;

    /// 校验和处理
    if(0x7122A0 != emu_register_read(r_DeviceID)) return FALSE;
    if(emu_reg_check_sum_pro(2)) return TRUE;

    /// 复位计量芯片
    emu_reset();

    if((hfconst_gain == 2) || (hfconst_gain == 4) || (hfconst_gain == 8))
    {
        hf_const = para[0].HFconst / hfconst_gain;
    }
    else
    {
        hf_const = para[0].HFconst;
    }

    if(measure_para.wiring_mode == MIC_WIRE_3P3W_MODE)
    {
        emc_cfg = 0x0003;  // bit0: 在外部引脚SEL=1时，通过寄存器ModSel控制位进行工作模式判别, ModSel =0为三相四线制；ModSel =1 为三相三线制
        if(measure_para.acc_mode == MIC_ABSACC_MODE)
        {
            emucfg |= (0x40 | 0x08); // bit6: 1, 三相四线制使用代数和累加方式，三相三线下使用绝对值和累加方式；
        }
    }
    else     
    {   /// MIC_WIRE_3P4W_MODE)
        emc_cfg = 0x0002;  // bit0: 在外部引脚SEL=1时，通过寄存器ModSel控制位进行工作模式判别, ModSel =0为三相四线制；ModSel =1 为三相三线制
        if(measure_para.acc_mode == MIC_ALGACC_MODE)
        {
            // bit6: 1,  三相四线制使用代数和累加方式，三相三线下使用绝对值和累加方式；
            // bit3: =1，视在功率/能量寄存器采用RMS方式计, =0，视在功率/能量寄存器采用PQS方式计量
            emucfg |= 0x40;
        }
        else
        {
            emucfg |= 0x08;
        }
    }
    istart = (uint32_t)(0.8 * BASE_CURRENT * START_CURRENT_RADIO * cal_para[0].I_RatioN * DATA_2EXP13);

    if(emu_register_write(w_CalRegClr,  0x000000))  // 重设校表参数寄存器到ATT芯片上电默认值
    if(emu_register_write(w_WrPreg,     0x00005A))  // 允许写入校表参数寄存器

    if(emu_register_write(w_EMCfg,      emc_cfg))   // bi5: EnHarmonic 1 选择谐波测量； 0 选择基波测量
    if(emu_register_write(w_DIGCfg,     0x8000))
    if(emu_register_write(w_ModuleCFG,  0x3537))

    if(emu_register_write(w_PGACtrl,    0x0103))    // 电压通道2倍增益，零线路通道16倍增益
    //if(emu_register_write(w_PGACtrl,    0x0100))  // 电压通道2倍增益

    if(emu_register_write(w_Hfconst,    hf_const))
    if(emu_register_write(w_Pstartup,   EMU_PWR_START))
    if(emu_register_write(w_Istarup,    istart))
    if(emu_register_write(w_PgainA,     para[0].P_gain))
    if(emu_register_write(w_PgainB,     para[1].P_gain))
    if(emu_register_write(w_PgainC,     para[2].P_gain))
    if(emu_register_write(w_QgainA,     para[0].P_gain))
    if(emu_register_write(w_QgainB,     para[1].P_gain))
    if(emu_register_write(w_QgainC,     para[2].P_gain))
    if(emu_register_write(w_SgainA,     para[0].P_gain))
    if(emu_register_write(w_SgainB,     para[1].P_gain))
    if(emu_register_write(w_SgainC,     para[2].P_gain))

    if(emu_register_write(w_PhSregApq0, para[0].Phs_ofst))
    if(emu_register_write(w_PhSregBpq0, para[1].Phs_ofst))
    if(emu_register_write(w_PhSregCpq0, para[2].Phs_ofst))
    if(emu_register_write(w_PhSregApq1, para[0].Phs_ofst))
    if(emu_register_write(w_PhSregBpq1, para[1].Phs_ofst))
    if(emu_register_write(w_PhSregCpq1, para[2].Phs_ofst))
    if(emu_register_write(w_PhSregApq2, para[0].Phs_ofst))
    if(emu_register_write(w_PhSregBpq2, para[1].Phs_ofst))
    if(emu_register_write(w_PhSregCpq2, para[2].Phs_ofst))
    // 小电流有功校准
    if(emu_register_write(w_PoffsetA,   para[0].P_ofst))
    if(emu_register_write(w_PoffsetB,   para[1].P_ofst))
    if(emu_register_write(w_PoffsetC,   para[2].P_ofst))

    if(emu_register_write(w_PoffsetAL,  para[0].P_ofstl))
    if(emu_register_write(w_PoffsetBL,  para[1].P_ofstl))
    if(emu_register_write(w_PoffsetCL,  para[2].P_ofstl))
    // 小电流无功校准
    if(emu_register_write(w_QoffsetA,   para[0].Q_ofst))
    if(emu_register_write(w_QoffsetB,   para[1].Q_ofst))
    if(emu_register_write(w_QoffsetC,   para[2].Q_ofst))

    if(emu_register_write(w_QoffsetAL,  para[0].Q_ofstl))
    if(emu_register_write(w_QoffsetBL,  para[1].Q_ofstl))
    if(emu_register_write(w_QoffsetCL,  para[2].Q_ofstl))

    if(emu_register_write(w_UgainA,     para[0].U_gain))
    if(emu_register_write(w_UgainB,     para[1].U_gain))
    if(emu_register_write(w_UgainC,     para[2].U_gain))
    if(emu_register_write(w_IgainA,     para[0].I_gain))
    if(emu_register_write(w_IgainB,     para[1].I_gain))
    if(emu_register_write(w_IgainC,     para[2].I_gain))
    if(emu_register_write(w_GainADC7,   para[3].I_gain))

    //if(emu_register_write(w_EMUIE, 0x0040)) //0x0040  (1<<6)开SAG中断
#if EMU_SAG_CHK
    if(emu_register_write(w_Cyclength,  SAG_CYCLE))
    if(emu_register_write(w_SAGLvl,     SAG_LVL_THD))
#endif

    if(emu_register_write(w_ModeCfg,    0xB97F))   // 921.6kHz,
    if(emu_register_write(w_EMUCfg,     emucfg))
    if(emu_register_write(w_WrPreg,     0x000000)) //不允许写入校表参数寄存器
    if(emu_register_write(w_Regsel,     0x000000)) //选择读出计量寄存器而不是校表参数寄存器
    {
        hal_timer.xms(30);      // 手册描述复位后25ms可以完成启动
        emu_reg_check_sum_pro(1);
        DBG_PRINTF(P_EMU, T);
        DBG_PRINTF(P_EMU, D, "\r\nht7136 init success!......\r\n");
    }
    return FALSE;
}
/// @brief 数据检出
/// @param  
static void emu_calpara_checkout(void)
{
    uint8_t    chn;
    CalPara_s* ptr;

    /* 校验RAM中的计量参数, EEPROM和CODEFLASH都存放有计量参数 !!!掉电涉及电能显示，可能用到变比 */
    if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
    {
        ee_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
        if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
        {
            mcu_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
            if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
            {
                measure_para = mic_para_default;
                measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            }
        }
        DBG_PRINTF(P_EMU, D, "mesure_para check out faile!!  MEASURE_PARA_ADDR: %x\r\n", MEASURE_PARA_ADDR);
    }

    /* 校验RAM中的校表参数. EEPROM和CODEFLASH都存放有校表参数 */
    for(chn = 0, ptr = cal_para; chn < CHANNEL_NUM; chn++, ptr++)
    {
        if(ptr->cs != CAL_CS16(ptr, sizeof(CalPara_s)))
        {
            ee_cal_read(chn * sizeof(CalPara_s), ptr, sizeof(CalPara_s));
            if(ptr->cs != CAL_CS16(ptr, sizeof(CalPara_s)))
            {
                mcu_cal_read(chn * sizeof(CalPara_s), ptr, sizeof(CalPara_s));
                if(ptr->cs != CAL_CS16(ptr, sizeof(CalPara_s)))
                {
                    if(chn == CHANNEL_NETURAL)
                    {
                        *ptr = cal_para_N_default;
                    }
                    else
                    {
                        *ptr = cal_para_default;
                    }
                    instant_value.stus.uncal |= 1 << chn;
                    ptr->cs = CAL_CS16(ptr, sizeof(CalPara_s));
                }
                else
                {
                    ee_cal_write(chn * sizeof(CalPara_s), ptr, sizeof(CalPara_s));
                }
            }
            else
            {
                CalPara_s tmp;
                mcu_cal_read(chn * sizeof(CalPara_s), &tmp, sizeof(CalPara_s));
                if(tmp.cs != CAL_CS16(&tmp, sizeof(CalPara_s)))
                {
                    mcu_cal_write(chn * sizeof(CalPara_s), ptr, sizeof(CalPara_s));
                }
            }
        }
    }
}

static bool emu_verify(void)
{
    static uint8_t  cnt = 3;

    /* 校验RAM中的校表参数 */
    emu_calpara_checkout();

    /* 校验计量芯片内的参数 */
    if(emu_para_refresh(cal_para, 0))
    {
        cnt = 5;
        instant_value.stus.error = 0;
        return TRUE; 
    }
    else if(cnt > 0 && --cnt == 0) /* 如果计量芯片数据读写不正常 */
    {
        instant_value.stus.error = 1;
    }

    return FALSE;
}

/// @brief 获取频率
/// @param  
/// @return 
static float emu_freq_val(void)
{
    uint32_t freq = emu_register_read(r_Freq);
    return (uint16_t)((freq * 100) >> 13);  // 频率 = UFREQ / (2 ^13)  unit: 0.01Hz
}

/// @brief 获取功率因素
/// @param ph 相
static float emu_pf_val(EMU_PHASE_TYPE ph)
{
    float pf;

    if(equ(instant_value.pwr_s[ph], 0)) return 1.0;
    pf = fabs(instant_value.pwr_p[ph] / instant_value.pwr_s[ph]);
    if(pf > 1.0) return  1.0;
    return pf;
}

/// @brief 获取压压角
static float emu_vv_angle_val(EMU_PHASE_TYPE ph)
{
    uint32_t angle;
    if(ph < A_PHASE) return 0;
    angle = emu_register_read(r_YUaUb + ph - 1);

    /* angle = (angle * 1800) >> 20; */
    return (float)((angle * 225) >> 17);
}

/// @brief 获取电压-电流夹角
static float emu_vi_angle_val(EMU_PHASE_TYPE ph)
{
    uint32_t angle;
    if(ph < A_PHASE) return 0;
    angle = emu_register_read(r_Pga + ph - 1);
    angle &= ~(0xFFE00000);
    /* angle = (angle * 1800) >> 20; */
    return (float)((angle * 225) >> 17);
}

/// @brief 获取电压值
/// @param chn 
/// @return 
static float emu_vol_val(SAMPLE_CHANNEL chn)
{
    uint16_t vrms;
    vrms = (uint16_t)((emu_register_read(r_UaRms + chn)) >> 13); // 计算瞬时电压有效值 unit:0.1 V
    if(vrms <= MIN_WORK_VOLTAGE)  vrms = 0;
    return (float)vrms;
}

/// @brief 获取矢量和电压值
static float emu_v_vol_val(void)
{
    return (float)((emu_register_read(r_UtRms)) >> 12); // 计算矢量和电压有效值 unit:0.1 V
}

/// @brief 获取电流值
static float emu_cur_val(SAMPLE_CHANNEL chn)
{
    if(chn == CHANNEL_NETURAL)
        return (float)((emu_register_read(r_RmsADC7)) >> 13) / cal_para[chn].I_RatioN;      // 电流 unit: 0.001A
    else
        return (float)((emu_register_read(r_IaRms + chn)) >> 13) / cal_para[chn].I_RatioN;  // 电流 unit: 0.001A
}

/// @brief 获取高精度零线电流值
static float emu_cur_val_h(SAMPLE_CHANNEL chn)
{
    if(chn == CHANNEL_NETURAL)
    {
        uint32_t reg;
        float tmp1, tmp2;
        reg = emu_register_read(r_RmsADC7);
        if(reg > cal_para[chn].I_zero_reg)
        {
            tmp1 = reg / 1000.0;
            tmp2 = cal_para[chn].I_zero_reg / 1000.0;
            reg = (uint32_t)(sqrt(tmp1 * tmp1 - tmp2 * tmp2) * 1000);
            // 计算公式 ((reg * 10000) >> 13) / I_RatioN;        // 电流 unit: 0.0001A
            return (float)((reg * 625) >> 9) / cal_para[chn].I_RatioN;
        }
        else return 0;
    }
    else
        return 0;//((emu_register_read(r_IaRms + chn) * 1000) >> 13) / cal_para[chn].I_RatioN;  // 电流 unit: 0.001A
}

/// @brief 获取三相矢量和电流值
static float emu_v_cur_val(void)
{
    return ((float)(emu_register_read(r_ItRms) * 1000) >> 12) / cal_para[0].I_RatioN;            // 电流 unit: 0.001A
}

/// @brief 获取功率值--用于校表
static float emu_power_val_cal(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    int32_t val = 0;
    uint8_t reg, index;

    index = (ph + 3) % 4;
    switch(type)
    {
        case P_POWER:
        reg = r_Pa + index;
        break;

        case Q_POWER:
        reg = r_Qa + index;
        break;

        case S_POWER:
        reg = r_Sa + index;
        break;
    }

    val = emu_register_read(reg);
    if(val >= 0x800000) val -= (1ul << 24);

    if(index == 3) return ((float)val / (CAL_POWER / 2)); // 合相功率 unit: 1W
    return ((float)val / CAL_POWER);           // 分相功率 unit: 1W
}

/// @brief 获取功率值
static (float) emu_power_val(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    return ((float))(emu_power_val_cal(type, ph) * 16);
}

/// @brief 根据功率符号计算相位
static PWR_QUADRANT emu_quadrant_in(EMU_PHASE_TYPE ph)
{
    if(instant_value.pwr_p[ph] >= 0)
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_1 : QUADRANT_4;
    }
    else
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_2 : QUADRANT_3;
    }
}

/* Public functions ----------------------------------------------------------*/
/// @brief 刷新实时数据
void instant_refresh(void)
{
    uint8_t  i;
    uint32_t tmp;
    int32_t  reg_pwr_p[3];
    int32_t  reg_pwr_q[3];
    int32_t  reg_pwr_s[3];

    if(emu_verify())
    {
        if (measure_status == FALSE)
        {
            measure_status = TRUE;
            /// 如果跌落试验增加了电能，这里可以读取一次电能清除寄存器
            // measure_pulse_get(P_POWER, T_PHASE);
            // measure_pulse_get(P_POWER, A_PHASE);
            // measure_pulse_get(P_POWER, B_PHASE);
            // measure_pulse_get(P_POWER, C_PHASE);

            // measure_pulse_get(Q_POWER, T_PHASE);
            // measure_pulse_get(Q_POWER, A_PHASE);
            // measure_pulse_get(Q_POWER, B_PHASE);
            // measure_pulse_get(Q_POWER, C_PHASE);

            // measure_pulse_get(S_POWER, T_PHASE);
            // measure_pulse_get(S_POWER, A_PHASE);
            // measure_pulse_get(S_POWER, B_PHASE);
            // measure_pulse_get(S_POWER, C_PHASE);
        }
        
        /* 更新计量状态 */
        instant_value.stus.lword &= ~0x00000FFF;
        tmp = emu_register_read(r_Sflag);
        instant_value.stus.seq_rev   = boolof(tmp & bitmask(3));
        instant_value.stus.i_seq_rev = boolof(tmp & bitmask(4)); //电流相序反
        instant_value.stus.pa_start  = !boolof(tmp & bitmask(9));
        instant_value.stus.pb_start  = !boolof(tmp & bitmask(10));
        instant_value.stus.pc_start  = !boolof(tmp & bitmask(11));
        tmp = emu_register_read(r_PFlag);
        instant_value.stus.lword |= tmp & 0x000000FF; // 功率反向标志
        // tmp = emu_register_read(r_SAGFlag);
        // instant_value.stus.lword |= ((tmp << 8) & 0x00003F00);

        /* 读瞬时参数 */
        instant_value.freq = emu_freq_val();
        for(i = 0; i < PHASE_NUM; i++)
        {
            instant_value.vrms[i]      = emu_vol_val((SAMPLE_CHANNEL)(CHANNEL_1 + i));
            // instant_value.pwr_s[1 + i] = emu_power_val(S_POWER, (EMU_PHASE_TYPE)(A_PHASE + i)); // unit: 1va
            instant_value.vv_angle[i]  = emu_vv_angle_val((EMU_PHASE_TYPE)(A_PHASE + i));
            reg_pwr_s[i] = emu_power_val(S_POWER, (EMU_PHASE_TYPE)(A_PHASE + i)); // unit: 1/16va
            reg_pwr_p[i] = emu_power_val(P_POWER, (EMU_PHASE_TYPE)(A_PHASE + i)); // unit: 1/16w
            reg_pwr_q[i] = emu_power_val(Q_POWER, (EMU_PHASE_TYPE)(A_PHASE + i)); // unit: 1/16var
            if(instant_value.vrms[i] < MIN_WORK_VOLTAGE) // 电压小于40.0V时，不做电压逆相序判断
            {
                instant_value.stus.seq_rev = 0;
            }
        }

        memset(instant_value.pwr_abs, 0, sizeof(instant_value.pwr_abs));
        memset(instant_value.pwr_imp, 0, sizeof(instant_value.pwr_imp));
        if(fabs(reg_pwr_p[0]) < START_PWR
        && fabs(reg_pwr_p[1]) < START_PWR
        && fabs(reg_pwr_p[2]) < START_PWR
        && fabs(reg_pwr_q[0]) < START_PWR
        && fabs(reg_pwr_q[1]) < START_PWR
        && fabs(reg_pwr_q[2]) < START_PWR) // 用有功和无功作为启动判断
        {
            instant_value.pwr_p[0] = 0;
            instant_value.pwr_q[0] = 0;
            instant_value.pwr_s[0] = 0;
            instant_value.pf[0]    = 1.0;
            instant_value.v_irms   = 0;
            instant_value.stus.lword   &= ~(0x000001FF); // 反向、启动、电流逆向序状态清零
            instant_value.stus.seq_rev = 0;
            instant_value.stus.i_seq_rev = 0;
            for(i = 0; i < PHASE_NUM; i++)
            {
                instant_value.pwr_p[1 + i] = 0; // unit: 1w
                instant_value.pwr_q[1 + i] = 0; // unit: 1var
                instant_value.pwr_s[1 + i] = 0; // unit: 1va
                instant_value.irms[i]      = 0;
                instant_value.pf[1 + i]    = 1.0;
                instant_value.vi_angle[i]  = 0;
            }
        }
        else
        {
            instant_value.pwr_p[0] = emu_power_val(P_POWER, T_PHASE) >> 4; // unit: 1w
            instant_value.pwr_q[0] = emu_power_val(Q_POWER, T_PHASE) >> 4; // unit: 1var
            instant_value.pwr_s[0] = emu_power_val(S_POWER, T_PHASE) >> 4; // unit: 1va
            if(instant_value.pwr_p[0] < 0 && instant_value.pwr_s[0] > 0)
            {
                instant_value.pwr_s[0] = -instant_value.pwr_s[0];
            }

            /* 分相有效值 */
            for(i = 0; i < PHASE_NUM; i++)
            {
                uint8_t j = i + 1;
                if((fabs(reg_pwr_p[i]) < START_PWR) && (fabs(reg_pwr_q[i]) < START_PWR))
                {
                    instant_value.pwr_p[j] = 0;
                    instant_value.pwr_q[j] = 0;
                    instant_value.pwr_s[j] = 0;
                    instant_value.irms[i]  = 0;
                    instant_value.pf[j]    = 1.0;
                    instant_value.stus.lword  &= ~(0x0011UL << i); // 清对应相的反向标志
                    instant_value.vi_angle[i] = 0;
                }
                else
                {
                    instant_value.pwr_p[j] = reg_pwr_p[i] >> 4; // unit: 1w
                    instant_value.pwr_q[j] = reg_pwr_q[i] >> 4; // unit: 1var
                    instant_value.pwr_s[j] = reg_pwr_s[i] >> 4; // unit: 1vah
                    if(instant_value.pwr_p[j] < 0 && instant_value.pwr_s[j] > 0)
                    {
                        instant_value.pwr_s[j] = -instant_value.pwr_s[j];
                    }
                    instant_value.irms[i] = emu_cur_val((SAMPLE_CHANNEL)i);
                    instant_value.pf[j]   = emu_pf_val((EMU_PHASE_TYPE)j);
                    instant_value.vi_angle[i] = emu_vi_angle_val((EMU_PHASE_TYPE)j);
                    instant_value.pwr_abs[P_POWER]  += fabs(instant_value.pwr_p[j]);
                    instant_value.pwr_abs[Q_POWER]  += fabs(instant_value.pwr_q[j]);
                    instant_value.pwr_abs[S_POWER]  += fabs(instant_value.pwr_s[j]);
                    if(instant_value.pwr_p[j] > 0) instant_value.pwr_imp[P_POWER] += instant_value.pwr_p[j];
                    if(instant_value.pwr_q[j] > 0) instant_value.pwr_imp[Q_POWER] += instant_value.pwr_q[j];
                    if(instant_value.pwr_s[j] > 0) instant_value.pwr_imp[S_POWER] += instant_value.pwr_s[j];
                }

                if(instant_value.irms[i] < START_CURRENT)
                {
                    instant_value.irms[i] = 0;
                    //instant_value.stus.seq_rev = 0;
                }
            }
            /* 合相有效值 */
            instant_value.v_irms = emu_v_cur_val();
            if(instant_value.v_irms < START_CURRENT) instant_value.v_irms = 0; // 矢量和电流滤波

        #if defined(POLYPHASE_METER)
            if(measure_para.acc_mode == MIC_ABSACC_MODE)
            {
                if(instant_value.pwr_abs[S_POWER] == 0) instant_value.pf[0] = 1.0;
                else instant_value.pf[0] = instant_value.pwr_abs[P_POWER] / instant_value.pwr_abs[S_POWER];
            }
            else
        #endif
            {
                if(instant_value.pwr_s[0] == 0) instant_value.pf[0] = 1.0;
                else instant_value.pf[0] = instant_value.pwr_p[0] / instant_value.pwr_s[0];
            }
            if(instant_value.pf[0] > 1.0) instant_value.pf[0] = 1.0;
        }

        instant_value.v_vrms = emu_v_vol_val();
        instant_value.n_irms = emu_cur_val(CHANNEL_NETURAL);
        if(instant_value.n_irms < START_CURRENT) instant_value.n_irms = 0; // 零线电流滤波
        //instant_value.n_irms_h = emu_cur_val_h(CHANNEL_NETURAL);
        for(i = 0; i < (PHASE_NUM + 1); i++)
        {
            instant_value.quadrant[i] = emu_quadrant_in((EMU_PHASE_TYPE)i);
        }

        DBG_PRINTF(P_EMU, D, "\r\n measure instant value: \r\n");
        DBG_PRINTF(P_EMU, D, "VOL:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vrms[0], instant_value.vrms[1], instant_value.vrms[2]);
        DBG_PRINTF(P_EMU, D, "CUR:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.irms[0], instant_value.irms[1], instant_value.irms[2]);
        DBG_PRINTF(P_EMU, D, "V-V:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vv_angle[0], instant_value.vv_angle[1], instant_value.vv_angle[2]);
        DBG_PRINTF(P_EMU, D, "V-I:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vi_angle[0], instant_value.vi_angle[1], instant_value.vi_angle[2]);
        DBG_PRINTF(P_EMU, D, "ACT:  %-10.4f\t%-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.pwr_p[1], instant_value.pwr_p[2], instant_value.pwr_p[3], instant_value.pwr_p[0]);
        DBG_PRINTF(P_EMU, D, "PF :  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.pf[0], instant_value.pf[1], instant_value.pf[2]);
        DBG_PRINTF(P_EMU, D, "FREQ: %-10.4f\r\n", instant_value.freq);
        DBG_PRINTF(P_EMU, D, "\r\n");
    }
    instant_value.stus.abs = boolof(measure_para.acc_mode == MIC_ABSACC_MODE);
}

/// 电能获取
uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    uint8_t pulse_num = 0;
    uint8_t reg, index;

    index = (ph + 3) % 4;
    switch(type)
    {
        case P_POWER:
            reg = r_Epa + index;
            break;
        case Q_POWER:
            reg = r_Eqa + index;
            break;
        case S_POWER:
            reg = r_Esa + index;
            break;
    }
    pulse_num = (uint8_t)emu_register_read(reg);
    // if(ph == T_PHASE) /// 数脉冲计数器
    // {
    //     if(type == P_POWER)
    //     {
    //         HAL_CRITICAL_STATEMENT
    //         (
    //             pulse_num = pulse_cnt[0];
    //             pulse_cnt[0] = 0;
    //         );
    //     }
    //     else if(type == Q_POWER)
    //     {
    //         HAL_CRITICAL_STATEMENT
    //         (
    //             pulse_num = pulse_cnt[1];
    //             pulse_cnt[1] = 0;
    //         );
    //     }
    // }

    return (pulse_num < MAX_PULSE_PER_SECOND) ? pulse_num : 0;
}

/// 快速脉冲计数器获取，掉电时使用
uint8_t measure_fast_cnt_add(EMU_PHASE_TYPE ph, uint32_t* fast_cnt, uint8_t isclac)
{
    uint8_t reg = r_FstCntPa + (ph + 3) % 4;
    if(!isclac)
    {
        int32_t val = emu_register_read(reg);
        if(val >= 0x800000) val -= (1ul << 24); // 快速脉冲计数寄存器为负值处理
        (*fast_cnt) += labs(val);
    }
    else
    {
        reg = (*fast_cnt) / cal_para[CHANNEL_1].HFconst;
        (*fast_cnt) %= cal_para[CHANNEL_1].HFconst;
    }
    return reg;
}

/// @brief 计量芯片初始化
/// @param pwr_down 1-掉电时初始化，0-上电时初始化
void measure_ic_init(uint8_t pwr_down)
{
    DBG_PRINTF(P_EMU, D, "\r\nht7136 init : \r\n");
    emu_calpara_checkout();

    for(uint8_t i = 0; i < 4; i++){ instant_value.quadrant[i] = QUADRANT_1; }

    if(!pwr_down)
    {
        emu_spi_init();
        emu_reset();

        HAL_CRITICAL_STATEMENT
        (
            memset(pulse_cnt, 0, sizeof(pulse_cnt));
        );

        emu_reg_check_sum_pro(0); // 清除校验
        memset(cal_para, 0, CHANNEL_NUM * sizeof(CalPara_s));
    }
}

/// @brief 计量芯片关闭
/// @param  
void measure_ic_off(void)
{
    emu_register_write(w_WrPreg, 0x00005A);
    measure_status = FALSE;
    for(int i = 0; i < 3; i++)
    {
        if(emu_register_write(w_EMUCfg, 0)) break;
    }
}

/// @brief 排序
/// @param arr 
/// @param len 
static void select_sort(float *arr, uint32_t len)
{
    float tmp;
    uint32_t i, k, j;

    for (i = 0; i < len; i++)
    {
        k = i;                           //保存当前下标
        for (j = i + 1; j < len; j++)
        {
            if (arr[k] > arr[j])
                k = j;                  //找到最小值
        }
        if (k != i)                     //将最小值放到当前下标
        {
            tmp = arr[i];
            arr[i] = arr[k];
            arr[k] = tmp;
        }
    }
}

/// @brief 获取电压电流功率数据用于校表
/// @param pdat_samp 
void calibrate_sample(void* pdat_samp)
{
    uint8_t i, j, delete_num;
    float Urms[3][SAMPLE_NUM];
    float Irms[4][SAMPLE_NUM];
    float power_p[3][SAMPLE_NUM];
    float power_q[3][SAMPLE_NUM];

    CalibrateData_s* pDate = pdat_samp;
    memset(pDate, 0x00, sizeof(CalibrateData_s));

    delete_num = SAMPLE_NUM / 4;

    for(j = 0; j < SAMPLE_NUM; j ++)
    {
        hal_timer.msdly(200);     // 延时等待寄存器更新(ht7136 设置更新频率 2HZ)
        for(i = 0; i < 3; i ++)
        {
            Urms[i][j] = (float)emu_register_read(r_UaRms+i) * 10 / DATA_2EXP13;
            Irms[i][j] = (float)emu_register_read(r_IaRms+i) * 1000 / DATA_2EXP13;
            power_p[i][j] = emu_power_val_cal(P_POWER, (EMU_PHASE_TYPE)(A_PHASE+i));
            power_q[i][j] = emu_power_val_cal(Q_POWER, (EMU_PHASE_TYPE)(A_PHASE+i));
        }
        Irms[3][j] = (float)emu_register_read(r_RmsADC7) * 1000 / DATA_2EXP13;
    }
    // 选择排序
    for(i = 0; i < 3; i ++)
    {
        select_sort(Urms[i], SAMPLE_NUM);
        select_sort(Irms[i], SAMPLE_NUM);
        select_sort(power_p[i], SAMPLE_NUM);
        select_sort(power_q[i], SAMPLE_NUM);
    }
    select_sort(Irms[3], SAMPLE_NUM);

    // 去头去尾，剩余采样数据累加求平均
    for(j = delete_num; j < (SAMPLE_NUM - delete_num); j++) // 去掉最小最大值
    {
        for(i = 0; i < 3; i ++)
        {
            pDate->Urms[i] += Urms[i][j];
            pDate->Irms[i] += Irms[i][j];
            pDate->power_p[i] += power_p[i][j];
            pDate->power_q[i] += power_q[i][j];
        }
        pDate->Irms[3] += Irms[3][j];
    }
    for(i = 0; i < 3; i ++)
    {
        pDate->Urms[i] /= (SAMPLE_NUM - delete_num * 2);
        pDate->Irms[i] /= (SAMPLE_NUM - delete_num * 2);
        pDate->power_p[i] /= (SAMPLE_NUM - delete_num * 2);
        pDate->power_q[i] /= (SAMPLE_NUM - delete_num * 2);
        pDate->power_s[i] = sqrt(pDate->power_p[i]*pDate->power_p[i] + pDate->power_q[i]*pDate->power_q[i]);
        //pDate->Pf[i] = pDate->power_p[i] * 1000 / pDate->power_s[i];
    }
    pDate->Irms[3] /= (SAMPLE_NUM - delete_num * 2);
}


//#pragma optimize=none
/// @brief 校表处理
/// @param chn 通道
/// @param step 校表步骤
/// @param pdat_std 标准数据缓冲
/// @param pdat_samp 采样数据缓冲
/// @return 
uint32_t calibrate_proc(uint8_t chn, uint8_t step, void* pdat_std, void* pdat_samp)
{
    CalPara_s* ptr;
    float err;

    // 校表标准值/采样值结构体
    CalibrateData_s Data_Std,Data_Samp;

    memcpy(&Data_Std, pdat_std, sizeof(CalibrateData_s));
    memcpy(&Data_Samp, pdat_samp, sizeof(CalibrateData_s));

    // 三相表
    if(chn > 3) return 0;
    ptr = cal_para + chn;
    switch(step)
    {
        case STEP_INIT:  // 校表初始化
        /* 载入初始校表参数 */
        if(chn == CHANNEL_NETURAL)
        {
            *ptr = cal_para_N_default;
        }
        else
        {
            *ptr = cal_para_default;
        }
        instant_value.stus.uncal |= 1 << chn;
        break;

        case STEP_HFCONST:  // 高频脉冲常数调整
        {
            // 高频脉冲常数校准
            uint8_t i;
            float error[3];
            for(i = 0; i < 3; i ++)
            {
            #if MFCCMD_DOPGAIN_ENABLE
                error[i] = (Data_Samp.power_p[i] - Data_Std.power_p[i]) / Data_Std.power_p[i];
            #else
                error[i] = (Data_Samp.power_s[i] - Data_Std.power_s[i]) / Data_Std.power_s[i];
            #endif
            }
            select_sort(error, 3);
            if(error[2] - error[0] > 0.1)  // 三相初始误差相差10%以上，判定为硬件异常，不允许校表
            {
                return 1;
            }
            err = (error[0] + error[1] + error[2]) / 3; // 取三相平均误差计算高频脉冲常数
            cal_para[CHANNEL_1].HFconst = (uint16_t)(cal_para[CHANNEL_1].HFconst * (1 + err));
            // 处理为8的整数倍，用于做脉冲加倍检定
            cal_para[CHANNEL_1].HFconst = (cal_para[CHANNEL_1].HFconst + 4) / 8 *8;
            cal_para[CHANNEL_2].HFconst = cal_para[CHANNEL_1].HFconst;
            cal_para[CHANNEL_3].HFconst = cal_para[CHANNEL_1].HFconst;
            cal_para[CHANNEL_NETURAL].HFconst = cal_para[CHANNEL_1].HFconst;
            // 设置HFconst
            emu_register_write(w_WrPreg, 0x00005A);  // 允许写入校表参数寄存器
            emu_register_write(w_Hfconst, cal_para[CHANNEL_1].HFconst);
            emu_register_write(w_WrPreg, 0x000000);  //不允许写入校表参数寄存器

            // 延时等待计量刷新
            hal_timer.msdly(30);
            emu_reg_check_sum_pro(1);
        }
        break;

        case STEP_SAMPLE:  // 电表计量采样平均处理
        {
            calibrate_sample(pdat_samp);
            break;
        }

        case STEP_VOL:  // 校正电压, 输入电流单位0.1V
        err = Data_Std.Urms[chn] / Data_Samp.Urms[chn] - 1;
        err *= DATA_2EXP15;
        if(err < 0) err += DATA_2EXP16; // 负数则取绝对值
            
        ptr->U_gain = (uint16_t)(err + 0.5);  // 四舍五入后得到校准参数*/
        break;

        case STEP_CUR:  // 校正电流, 输入电流单位0.001A
        {
            // 电流有效值系数校准
            if(chn == CHANNEL_1 || chn == CHANNEL_NETURAL)
            {
                cal_para[chn].I_RatioN = (uint16_t)(Data_Samp.Irms[chn] / Data_Std.Irms[chn]);
                if(chn == CHANNEL_1)
                {
                    cal_para[CHANNEL_2].I_RatioN = cal_para[chn].I_RatioN;
                    cal_para[CHANNEL_3].I_RatioN = cal_para[chn].I_RatioN;
                }
            }

            // 电流增益校正
            err = Data_Std.Irms[chn] / (Data_Samp.Irms[chn] / cal_para[chn].I_RatioN) - 1;
            err *= DATA_2EXP15;
            if(err < 0) err += DATA_2EXP16;       // 负数则取绝对值
            ptr->I_gain = (uint16_t)(err + 0.5);  // 四舍五入后得到校准参数*/
            break;
        }

        case STEP_POWER:  // 校正功率增益, 输入误差单位0.0001
#if MFCCMD_DOPGAIN_ENABLE
        err = Data_Samp.power_p[chn] / Data_Std.power_p[chn] - 1;
#else
        err = Data_Samp.power_s[chn] / Data_Std.power_s[chn] - 1;
#endif
        err = -err / (1 + err);
        err *= DATA_2EXP15;
        if(err < 0)                         // 负数则取绝对值
            err += DATA_2EXP16;
        ptr->P_gain = (uint16_t)(err + 0.5);  // 四舍五入后得到校准参数*/
        break;

        case STEP_PHASE:  // 校正相角, 输入误差单位0.0001
        {
#if MFCCMD_DOPGAIN_ENABLE
            err = Data_Samp.power_p[chn] / Data_Std.power_p[chn] - 1;
#else
            {
                float pf_std, pf_samp;
                pf_std  = Data_Std.power_p[chn]  / Data_Std.power_s[chn];  if(pf_std)  pf_std = 1.0;
                pf_samp = Data_Samp.power_p[chn] / Data_Samp.power_s[chn]; if(pf_samp) pf_samp = 1.0;
                err = pf_samp / pf_std - 1;
            }
#endif
            err = -err / 1.732;
            err *= DATA_2EXP15;
            if(err < 0) err += DATA_2EXP16;                          // 负数则取绝对值 
            ptr->Phs_ofst = (uint16_t)(err + 0.5);  // 四舍五入后得到校准参数*/
            break;
        }

        case STEP_CUR_N:  // 零线电流默认值处理
        {
            err = 0;
            for(uint8_t i = 0; i < 3; i ++)
            {
                // 补码转换成原码
                err += (int16)cal_para[i].I_gain;
            }
            err /= 3;
            if(err < 0){ err += DATA_2EXP16; }
            cal_para[3].I_gain = (uint16_t)(err + 0.5);
            cal_para[3].I_RatioN = cal_para[CHANNEL_1].I_RatioN;
            break;
        }
        case STEP_IZERO: // 零漂电流校准
        if(chn == CHANNEL_NETURAL) ptr->I_zero_reg = emu_register_read(r_RmsADC7);
        else ptr->I_zero_reg = emu_register_read(r_IaRms + chn);
        break;

        case STEP_IOFST:    // 校正电流偏置
        break;

        case STEP_POFST:    // 校正功率偏置
        {
            int32_t offset;
            if((Data_Samp.power_p[chn] < 0)) return 1;
            offset = (int32_t)CAL_POFFSET(Data_Samp.power_p[chn], Data_Std.power_p[chn]);
            ptr->P_ofst = (uint16_t)(offset >> 8);
            ptr->P_ofstl = (uint16_t)(offset & 0xFF);
        }
        break;

        case STEP_QOFST:    // 无功功率偏置
        {
            int32_t offset;
            if((Data_Samp.power_q[chn] < 0)) return 1;
            offset = (int32_t)CAL_POFFSET(Data_Samp.power_q[chn], Data_Std.power_q[chn]);
            ptr->Q_ofst = (uint16_t)(offset >> 8);
            ptr->Q_ofstl = (uint16_t)(offset & 0xFF);
        }
        break;

        case STEP_SETPARA:  // 设置计量参数
        {
            MeasurePara_s *para = (MeasurePara_s*)pdat_std;
            if(para != &measure_para){ *para = measure_para; }
            measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            if(ee_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s)))
            {
                mcu_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
                //regChecksum = ~regChecksum;
                measure_ic_init(0); // 切换计量模式需重新初始化计量
                return 1;
            }
            return 0;
        }
        case STEP_GETPARA: return (uint32_t)&measure_para; // 读计量参数

        case STEP_CONST: // 刷新计量参数(脉冲常数 -加倍)
        emu_reg_check_sum_pro(3); // 触发EMU刷新参数
        emu_para_refresh(cal_para, *(uint8_t*)pdat_std);
        return 0;

        case STEP_REFRESH: // 刷新计量参数
        emu_reg_check_sum_pro(3); // 触发EMU刷新参数
        emu_para_refresh(cal_para, 0);
        hal_timer.msdly(3000);
        return 0;

        case STEP_READREG:  // 计量寄存器
        {
            uint16_t reg = get_lsbdata16(pdat_std);
            if((reg & 0x7FFF) > r_WaveBuff) return 0;
            if(reg & 0x8000) emu_register_write(w_Regsel, 0x00005A); //选择读出校表参数寄存器
            set_msbdata32(pdat_std, emu_register_read((uint8_t)reg));
            if(reg & 0x8000) emu_register_write(w_Regsel, 0x000000); //选择读出计量寄存器
        }
        return 4;

#if MIC_SUPPORT_SET_CAL_PARA
        case STEP_REDCAL:
        memcpy(pdat_std, &cal_para[chn], sizeof(CalPara_s));
        return sizeof(CalPara_s);

        case STEP_SETCAL:
        memcpy(&cal_para[chn], pdat_std, sizeof(CalPara_s));
        ptr = &cal_para[chn];
        if(ptr->cs != CAL_CS16(ptr, sizeof(CalPara_s))) return 1;
        regChecksum = 0;
    #endif

        case STEP_SAVE:  // 0x5A 保存校表参数
        mcu_cal_write(chn * sizeof(CalPara_s), ptr, sizeof(CalPara_s));
        ee_cal_write(chn *  sizeof(CalPara_s), ptr, sizeof(CalPara_s));
        instant_value.stus.uncal &= ~(1 << chn);
        return 0;

        default: // 其他，返回错误
        return 1;
    }

    /* 计算校表参数校验和 */
    ptr->cs = CAL_CS16(ptr, sizeof(CalPara_s));

    return 0;
}
#if 0
///相角获取
#define ANGLE_360       360.0
#define PI              3.141592
float measure_phase_angle_get(tAngleVector angle)
{
    float angle_val[2] = {0,0};
    tVector* vector = &angle.ref;
    float* vv_angle = instant_value.vv_angle;
    float* vi_angle = instant_value.vi_angle;

    for(uint8_t i = 0; i < 2; i++)
    {
        if(vector->type == VOL_VECTOR)
        {
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = ANGLE_360 - vv_angle[vector->chn - CHANNEL_2];
            }
        }
        else if(vector->type == CUR_VECTOR)
        {
            if(vector->chn == CHANNEL_1)
            {
                angle_val[i] = -(*vi_angle);
            }
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = -vv_angle[vector->chn - CHANNEL_2] - vi_angle[vector->chn - CHANNEL_1];
            }
        }
        vector++;
    }
    angle_val[0] = angle_val[1] - angle_val[0];

    if(more(angle_val[0], ANGLE_360)) angle_val[0] = ANGLE_360;
    else if(less(angle_val[0], 0.0)) angle_val[0] = ANGLE_360 - 0.1 + angle_val[0];

    return angle_val[0];
}
#endif


void mic_reset(void)
{
    // measure_para = mic_para_default;
    // calibrate_proc(0, STEP_SETPARA, &measure_para, NULL);
}

#if USE_EMU_AT_LOSS_VOLTAGE
void mic_power_off_running(void)
{
    if(power_off_running != NULL) 
    {
        //刷新电流

        //判断全失压
        power_off_running();

        //
    }
}

void mic_power_off_callback(void func(void))
{
    power_off_running = func;
}
#endif
/// @brief 计量芯片接口
const struct mic_s mic = {
    .ins                  = &instant_value,
    .init                 = measure_ic_init,
    .off                  = measure_ic_off,
    .ins_val_refresh      = instant_refresh,
#if USE_EMU_AT_LOSS_VOLTAGE
    .poweroff_run         = mic_power_off_running,
    .poweroff_callback    = mic_power_off_callback,
#else
    .poweroff_run         = NULL,
    .poweroff_callback    = NULL,
#endif
    .pulse                = measure_pulse_get,
    .cali                 = calibrate_proc,
    .measure_fast_cnt_add = measure_fast_cnt_add,
};

///
