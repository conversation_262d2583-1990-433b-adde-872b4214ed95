/**
 ******************************************************************************
* @file    typedef.h
* <AUTHOR> @date    2024
* @brief   类型定义
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __TYPEDEF_H
#define __TYPEDEF_H

#include <assert.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>

/* \brief Portable data type defines */
typedef signed char        int8,  s8;
typedef unsigned char      uint8, u8;
typedef signed short       int16, s16;
typedef unsigned short     uint16,u16;
typedef signed long        int32, s32;
typedef unsigned long      uint32,u32;
typedef float              fp32,  f32;
typedef double             fp64,  f64;
typedef signed long long   int64, s64;
typedef unsigned long long uint64,u64;


/* \brief Function pointer define */
typedef void (*funcPointer) (void);

/* \brief Bits string defines */
typedef union
{
    struct { char b0:1; char b1:1; char b2:1; char b3:1;
             char b4:1; char b5:1; char b6:1; char b7:1; };
    uint8_t h;
} bits8_t;
typedef union
{
    struct { char b0 :1; char b1 :1; char b2 :1; char b3 :1;
             char b4 :1; char b5 :1; char b6 :1; char b7 :1;
             char b8 :1; char b9 :1; char b10:1; char b11:1;
             char b12:1; char b13:1; char b14:1; char b15:1; };
    uint16_t hh;
} bits16_t;
typedef union
{
    struct { char b0 :1; char b1 :1; char b2 :1; char b3 :1;
             char b4 :1; char b5 :1; char b6 :1; char b7 :1;
             char b8 :1; char b9 :1; char b10:1; char b11:1;
             char b12:1; char b13:1; char b14:1; char b15:1;
             char b16:1; char b17:1; char b18:1; char b19:1;
             char b20:1; char b21:1; char b22:1; char b23:1;
             char b24:1; char b25:1; char b26:1; char b27:1;
             char b28:1; char b29:1; char b30:1; char b31:1; };
    uint32_t hhhh;
} bits32_t;

/* \brief Combin defines, it is very useful */
typedef union { uint8_t b[2]; uint16_t w; } u8_16_t, union16;
typedef union { uint8_t b[4]; uint16_t w[2]; uint32_t l; } u8_32_t, union32;
#ifdef __LONG_LONG_SIZE__
typedef union { uint8_t b[8]; uint16_t w[4]; uint32_t l[2]; uint64_t ll; } u8_64_t, union64;
#endif

/* \brief Octet string defines */
typedef struct { size_t len; const char* str; } cstring_t;
typedef struct { size_t len; uint8_t* str; } octstr_t, string_t;
typedef struct { size_t len; const uint8_t* str; } coctstr_t;

#define MAX_SECONDS 0xBC19137F   //最大的秒数，转换为时间戳时，超过该值则表示无效时间戳，99-12-31 23:59:59

#ifndef TRUE
#define TRUE          1
#endif

#ifndef FALSE
#define FALSE         0
#endif

#ifndef NULL
#define NULL   (void*)0
#endif

#ifndef st
#define st(x)         do { x } while (__LINE__ == -1)
#endif

/* \brief Get bit mask value from the bit no. ect. the mask of third bit is 0x0008 */
#ifndef bitmask
#define bitmask(x)     (1u << (x))
#endif

#ifndef lbitmask
#define lbitmask(x)    (1ul << (x))
#endif

#ifndef vbitmask
#define vbitmask(x, v) (((uint32_t)(v)) << (x))
#endif

/**
  * \brief  Translate expression statement or variable to bool value.
  * \param  [in]  x-the statement.
  * \return The result TRUE(1) or FALSE(0) in \a x.
  */
#ifndef boolof
#define boolof(x)    ((x) ? TRUE : FALSE)
#endif

/// 返回数组元素个数
/// x-数组名称
#ifndef eleof
#define eleof(x)     (sizeof(x) / sizeof((x)[0]))
#endif


#ifdef member_offset
#undef member_offset
#endif

/// 定义结构体成员偏移, 返回偏移量
/// T-结构体名称
/// member-成员名称
/// \note 该宏定义的目的是为了兼容不同平台的结构体成员偏移定义
#ifndef member_offset
#define member_offset(T, member)   ((size_t)(&(((T*)0)->member)))
#endif

/// 在结构体数组中获取第num个元素的偏移地址
#ifndef offsetptr
#define offsetptr(T, num)     ((size_t)((T*)0 + (num)))
#endif

/// 定义结构体成员大小，返回成员大小
#ifndef member_size
#define member_size(T, member)    (sizeof(((T*)0)->member))
#endif



/// x数据实际长度，size对齐单位，返回分配对齐长度
#ifndef alignof
#define alignof(x,size)  ((size)*(((uint32)(x)+(size)-1)/(size)))
#endif

/**
  * \brief  Counts the trailing zero bits of the given value considered as a 32-bit integer.
  * \param  [in]  u-Value of which to count the trailing zero bits.
  * \return The count of trailing zero bits in \a u.
  */
#if (defined __GNUC__) || (defined __CC_ARM)
#define ctz(u)              __builtin_ctz(u)
#else
#define ctz(u)             ((u) & (1ul <<  0) ?  0 : \
                            (u) & (1ul <<  1) ?  1 : \
                            (u) & (1ul <<  2) ?  2 : \
                            (u) & (1ul <<  3) ?  3 : \
                            (u) & (1ul <<  4) ?  4 : \
                            (u) & (1ul <<  5) ?  5 : \
                            (u) & (1ul <<  6) ?  6 : \
                            (u) & (1ul <<  7) ?  7 : \
                            (u) & (1ul <<  8) ?  8 : \
                            (u) & (1ul <<  9) ?  9 : \
                            (u) & (1ul << 10) ? 10 : \
                            (u) & (1ul << 11) ? 11 : \
                            (u) & (1ul << 12) ? 12 : \
                            (u) & (1ul << 13) ? 13 : \
                            (u) & (1ul << 14) ? 14 : \
                            (u) & (1ul << 15) ? 15 : \
                            (u) & (1ul << 16) ? 16 : \
                            (u) & (1ul << 17) ? 17 : \
                            (u) & (1ul << 18) ? 18 : \
                            (u) & (1ul << 19) ? 19 : \
                            (u) & (1ul << 20) ? 20 : \
                            (u) & (1ul << 21) ? 21 : \
                            (u) & (1ul << 22) ? 22 : \
                            (u) & (1ul << 23) ? 23 : \
                            (u) & (1ul << 24) ? 24 : \
                            (u) & (1ul << 25) ? 25 : \
                            (u) & (1ul << 26) ? 26 : \
                            (u) & (1ul << 27) ? 27 : \
                            (u) & (1ul << 28) ? 28 : \
                            (u) & (1ul << 29) ? 29 : \
                            (u) & (1ul << 30) ? 30 : \
                            (u) & (1ul << 31) ? 31 : \
                            32)
#endif


/**
  * \brief  Counts the trailing zero bits of the given value considered as a 8-bit integer.
  * \param  [in]  u-Value of which to count the trailing zero bits.
  * \return The count of trailing zero bits in \a u.
  */
#if (defined __GNUC__) || (defined __CC_ARM)
#define ctz(u)              __builtin_ctz(u)
#else
#define ctz8(u)             ((u) & (1ul <<  0) ?  0 : \
                             (u) & (1ul <<  1) ?  1 : \
                             (u) & (1ul <<  2) ?  2 : \
                             (u) & (1ul <<  3) ?  3 : \
                             (u) & (1ul <<  4) ?  4 : \
                             (u) & (1ul <<  5) ?  5 : \
                             (u) & (1ul <<  6) ?  6 : \
                             7)
#endif

/**
  * \brief Calls the routine at address \a addr. It generates a long call opcode.
  * \param [in]  addr-Address of the routine to call.
  * \note  It may be used as a long jump opcode in some special cases.
  */
#define long_call(addr)     ((*(void (*)(void))(addr))())

/// 声明存储关联定义
/// \param t 数据类型
/// \param n 成员名称
/// \param l 保留数据长度，如果预留长度太小，将导致编译错误
#if defined(WIN32)
#define SMALLOC(t,n,l) union{t n; uint8 buf_##n[l*((l/sizeof(t))/(l/sizeof(t)))]; uint8 test_##n[l*((l/sizeof(t))/(l/sizeof(t))) - 1];}
#else
#define SMALLOC(t,n,l) union{t n; uint8 buf_##n[l*((l/sizeof(t))/(l/sizeof(t)))];}
#endif

/** \brief Stringize.
 *
 * Stringize a preprocessing token, this token being allowed to be \#defined.
 *
 * May be used only within macros with the token passed as an argument if the
 * token is \#defined.
 *
 * For example, writing STRINGZ(PIN) within a macro \#defined by PIN_NAME(PIN)
 * and invoked as PIN_NAME(PIN0) with PIN0 \#defined as A0 is equivalent to
 * writing "A0".
 */
#define STRINGZ(x)                                #x

/** \brief Absolute stringize.
 *
 * Stringize a preprocessing token, this token being allowed to be \#defined.
 *
 * No restriction of use if the token is \#defined.
 *
 * For example, writing ASTRINGZ(PIN0) anywhere with PIN0 \#defined as A0 is
 * equivalent to writing "A0".
 */
#define ASTRINGZ(x)                               STRINGZ(x)

//Swap a 16-bit integer
#define _SWAP16(x) ( \
   (((x) & 0x00FF) << 8) | \
   (((x) & 0xFF00) >> 8))

//Swap a 32-bit integer
#define _SWAP32(x) ( \
   (((x) & 0x000000FFUL) << 24) | \
   (((x) & 0x0000FF00UL) << 8) | \
   (((x) & 0x00FF0000UL) >> 8) | \
   (((x) & 0xFF000000UL) >> 24))

//Swap a 64-bit integer
#define _SWAP64(x) ( \
   (((x) & 0x00000000000000FFULL) << 56) | \
   (((x) & 0x000000000000FF00ULL) << 40) | \
   (((x) & 0x0000000000FF0000ULL) << 24) | \
   (((x) & 0x00000000FF000000ULL) << 8) | \
   (((x) & 0x000000FF00000000ULL) >> 8) | \
   (((x) & 0x0000FF0000000000ULL) >> 24) | \
   (((x) & 0x00FF000000000000ULL) >> 40) | \
   (((x) & 0xFF00000000000000ULL) >> 56))

#endif /* __TYPEDEF_H */


/** \name
 *
 * 将 N 个预处理符号拼接在一起，这些记号可以是 #define 定义的.
 *
 * 只能在宏中使用，并且作为参数传递的记号必须是已定义的。
 *
 * 例如，在一个名为 UTYPE(WIDTH) 的宏中写 TPASTE2(U, WIDTH)，然后以 UTYPE(UL_WIDTH) 形式调用，并且 UL_WIDTH 被定义为 32，这等同于直接写 U32。
 *
 * @{ */
#define TPASTE2( a, b)                            a##b
#define TPASTE3( a, b, c)                         a##b##c
#define TPASTE4( a, b, c, d)                      a##b##c##d
#define TPASTE5( a, b, c, d, e)                   a##b##c##d##e
#define TPASTE6( a, b, c, d, e, f)                a##b##c##d##e##f
#define TPASTE7( a, b, c, d, e, f, g)             a##b##c##d##e##f##g
#define TPASTE8( a, b, c, d, e, f, g, h)          a##b##c##d##e##f##g##h
#define TPASTE9( a, b, c, d, e, f, g, h, i)       a##b##c##d##e##f##g##h##i
#define TPASTE10(a, b, c, d, e, f, g, h, i, j)    a##b##c##d##e##f##g##h##i##j

/** \brief Macro repeat.
 *
 * 这个宏代表一种横向重复结构.
 *
 * \param[in] count  对宏进行重复调用的次数。有效值的范围从 0 到 MREPEAT_LIMIT
 * \param[in] macro  该宏是一个形式为 macro(n, __VA_ARGS__) 的二元操作。它由 MREPEAT 宏展开，其中 n 是当前的重复次数，__VA_ARGS__ 是辅助数据参数。
 * \param[in] data   传递给宏的辅助数据。
 *
 * \return       <tt>macro(0, data) macro(1, data) ... macro(count - 1, data)</tt>
 */
#define M1REPEAT(count, macro, ...) TPASTE2(M1REPEAT, count) (macro, __VA_ARGS__)

#define M1REPEAT1(  macro, ...)    macro( 1, __VA_ARGS__)
#define M1REPEAT2(  macro, ...)    M1REPEAT1(  macro, __VA_ARGS__)   macro( 2, __VA_ARGS__)
#define M1REPEAT3(  macro, ...)    M1REPEAT2(  macro, __VA_ARGS__)   macro( 3, __VA_ARGS__)
#define M1REPEAT4(  macro, ...)    M1REPEAT3(  macro, __VA_ARGS__)   macro( 4, __VA_ARGS__)
#define M1REPEAT5(  macro, ...)    M1REPEAT4(  macro, __VA_ARGS__)   macro( 5, __VA_ARGS__)
#define M1REPEAT6(  macro, ...)    M1REPEAT5(  macro, __VA_ARGS__)   macro( 6, __VA_ARGS__)
#define M1REPEAT7(  macro, ...)    M1REPEAT6(  macro, __VA_ARGS__)   macro( 7, __VA_ARGS__)
#define M1REPEAT8(  macro, ...)    M1REPEAT7(  macro, __VA_ARGS__)   macro( 8, __VA_ARGS__)
#define M1REPEAT9(  macro, ...)    M1REPEAT8(  macro, __VA_ARGS__)   macro( 9, __VA_ARGS__)

#define M1REPEAT10( macro, ...)    M1REPEAT9(  macro, __VA_ARGS__)   macro(10, __VA_ARGS__)
#define M1REPEAT11( macro, ...)    M1REPEAT10( macro, __VA_ARGS__)   macro(11, __VA_ARGS__)
#define M1REPEAT12( macro, ...)    M1REPEAT11( macro, __VA_ARGS__)   macro(12, __VA_ARGS__)
#define M1REPEAT13( macro, ...)    M1REPEAT12( macro, __VA_ARGS__)   macro(13, __VA_ARGS__)
#define M1REPEAT14( macro, ...)    M1REPEAT13( macro, __VA_ARGS__)   macro(14, __VA_ARGS__)
#define M1REPEAT15( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(15, __VA_ARGS__)
#define M1REPEAT16( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(16, __VA_ARGS__)
#define M1REPEAT17( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(17, __VA_ARGS__)
#define M1REPEAT18( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(18, __VA_ARGS__)
#define M1REPEAT19( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(19, __VA_ARGS__)

#define M1REPEAT20( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(20, __VA_ARGS__)
#define M1REPEAT21( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(21, __VA_ARGS__)
#define M1REPEAT22( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(22, __VA_ARGS__)
#define M1REPEAT23( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(23, __VA_ARGS__)
#define M1REPEAT24( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(24, __VA_ARGS__)
#define M1REPEAT25( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(25, __VA_ARGS__)
#define M1REPEAT26( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(26, __VA_ARGS__)
#define M1REPEAT27( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(27, __VA_ARGS__)
#define M1REPEAT28( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(28, __VA_ARGS__)
#define M1REPEAT29( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(29, __VA_ARGS__)

#define M1REPEAT30( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(30, __VA_ARGS__)
#define M1REPEAT31( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(31, __VA_ARGS__)
#define M1REPEAT32( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(32, __VA_ARGS__)
#define M1REPEAT33( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(33, __VA_ARGS__)
#define M1REPEAT34( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(34, __VA_ARGS__)
#define M1REPEAT35( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(35, __VA_ARGS__)
#define M1REPEAT36( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(36, __VA_ARGS__)
#define M1REPEAT37( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(37, __VA_ARGS__)
#define M1REPEAT38( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(38, __VA_ARGS__)
#define M1REPEAT39( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(39, __VA_ARGS__)

#define M1REPEAT40( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(40, __VA_ARGS__)
#define M1REPEAT41( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(41, __VA_ARGS__)
#define M1REPEAT42( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(42, __VA_ARGS__)
#define M1REPEAT43( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(43, __VA_ARGS__)
#define M1REPEAT44( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(44, __VA_ARGS__)
#define M1REPEAT45( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(45, __VA_ARGS__)
#define M1REPEAT46( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(46, __VA_ARGS__)
#define M1REPEAT47( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(47, __VA_ARGS__)
#define M1REPEAT48( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(48, __VA_ARGS__)
#define M1REPEAT49( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(49, __VA_ARGS__)

#define M1REPEAT50( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(50, __VA_ARGS__)
#define M1REPEAT51( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(51, __VA_ARGS__)
#define M1REPEAT52( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(52, __VA_ARGS__)
#define M1REPEAT53( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(53, __VA_ARGS__)
#define M1REPEAT54( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(54, __VA_ARGS__)
#define M1REPEAT55( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(55, __VA_ARGS__)
#define M1REPEAT56( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(56, __VA_ARGS__)
#define M1REPEAT57( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(57, __VA_ARGS__)
#define M1REPEAT58( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(58, __VA_ARGS__)
#define M1REPEAT59( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(59, __VA_ARGS__)

#define M1REPEAT60( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(60, __VA_ARGS__)
#define M1REPEAT61( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(61, __VA_ARGS__)
#define M1REPEAT62( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(62, __VA_ARGS__)
#define M1REPEAT63( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(63, __VA_ARGS__)
#define M1REPEAT64( macro, ...)    M1REPEAT14( macro, __VA_ARGS__)   macro(64, __VA_ARGS__)
/** @} */
/** @} */
#ifdef __cplusplus
}
#endif
