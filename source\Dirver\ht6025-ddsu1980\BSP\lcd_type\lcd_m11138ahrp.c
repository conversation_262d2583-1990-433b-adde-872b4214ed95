/**
 ******************************************************************************
* @file    lcd_m11138ahrp.c
* <AUTHOR> @date    2024
* @brief   lcd_m11138ahrp 显示屏定义
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"
#if USE_LCD_EXT_DRIVER
#include "ext_lcd_driver.h"
#else
#include "hal_lcd.h"
#endif
/* 定义LCD主屏, 辅屏数显个数 */
#define LCD_MS_DIGITS       8  ///定义主屏8的个数
#define LCD_LS_DIGITS       0  ///定义主屏未尾小8的个数
#define LCD_PS_DIGITS       0  ///定义辅屏8的个数

#define LCD_MS_MINUS_SIGN   0   ///定义主屏前是否有单独的负号



/* 定义LCD COM0~8的SEG */
/*****************************************
 * 
 * 统一格式  
 * SEG_XY   数码8， X-序号，Y-8段 如：第一个数的G段-SEG_1G
 *          符号，  X-固定为S，Y-按照液晶符号序号
 *          小数点，X-固定为DP，Y-按照液晶小数点序号 
 * 
******************************************/
//seg 1
#define SEG_4A          COM0(LCD_SEG01)
#define SEG_4F          COM1(LCD_SEG01)
#define SEG_4E          COM2(LCD_SEG01)
#define SEG_4D          COM3(LCD_SEG01)
//seg2
#define SEG_4B          COM0(LCD_SEG02)
#define SEG_4G          COM1(LCD_SEG02)
#define SEG_4C          COM2(LCD_SEG02)
#define SEG_S13         COM3(LCD_SEG02)
//seg3
#define SEG_5A          COM0(LCD_SEG03)
#define SEG_5F          COM1(LCD_SEG03)
#define SEG_5E          COM2(LCD_SEG03)
#define SEG_5D          COM3(LCD_SEG03)
//seg4
#define SEG_5B          COM0(LCD_SEG04)
#define SEG_5G          COM1(LCD_SEG04)
#define SEG_5C          COM2(LCD_SEG04)
#define SEG_DP1         COM3(LCD_SEG04)
//seg5
#define SEG_6A          COM0(LCD_SEG05)
#define SEG_6F          COM1(LCD_SEG05)
#define SEG_6E          COM2(LCD_SEG05)
#define SEG_6D          COM3(LCD_SEG05)
//seg6
#define SEG_6B          COM0(LCD_SEG06)
#define SEG_6G          COM1(LCD_SEG06)
#define SEG_6C          COM2(LCD_SEG06)
#define SEG_DP2         COM3(LCD_SEG06)
//seg7
#define SEG_7A          COM0(LCD_SEG07)
#define SEG_7F          COM1(LCD_SEG07)
#define SEG_7E          COM2(LCD_SEG07)
#define SEG_7D          COM3(LCD_SEG07)
//seg8
#define SEG_7B          COM0(LCD_SEG08)
#define SEG_7G          COM1(LCD_SEG08)
#define SEG_7C          COM2(LCD_SEG08)
#define SEG_DP3         COM3(LCD_SEG08)
//seg9
#define SEG_8A          COM0(LCD_SEG09)
#define SEG_8F          COM1(LCD_SEG09)
#define SEG_8E          COM2(LCD_SEG09)
#define SEG_8D          COM3(LCD_SEG09)
//seg10
#define SEG_8B          COM0(LCD_SEG10)
#define SEG_8G          COM1(LCD_SEG10)
#define SEG_8C          COM2(LCD_SEG10)
#define SEG_DP4         COM3(LCD_SEG10)
//seg11
#define SEG_9A          COM0(LCD_SEG11)
#define SEG_9F          COM1(LCD_SEG11)
#define SEG_9E          COM2(LCD_SEG11)
#define SEG_9D          COM3(LCD_SEG11)
//seg12
#define SEG_9B          COM0(LCD_SEG12)
#define SEG_9G          COM1(LCD_SEG12)
#define SEG_9C          COM2(LCD_SEG12)
#define SEG_S14         COM3(LCD_SEG12)
//seg13
#define SEG_S18         COM0(LCD_SEG13)
#define SEG_S17         COM1(LCD_SEG13)
#define SEG_S16         COM2(LCD_SEG13)
#define SEG_S15         COM3(LCD_SEG13)
//seg14
#define SEG_DP5         COM0(LCD_SEG14)
#define SEG_S21         COM1(LCD_SEG14)
#define SEG_S22         COM2(LCD_SEG14)
#define SEG_S23         COM3(LCD_SEG14)
//seg15
#define SEG_S19         COM0(LCD_SEG15)
#define SEG_S20         COM1(LCD_SEG15)
#define SEG_S24         COM2(LCD_SEG15)
#define SEG_S10         COM3(LCD_SEG15)
//seg16
#define SEG_S9          COM0(LCD_SEG16)
#define SEG_1C          COM1(LCD_SEG16)
#define SEG_1G          COM2(LCD_SEG16)
#define SEG_1B          COM3(LCD_SEG16)
//seg17
#define SEG_1D          COM0(LCD_SEG17)
#define SEG_1E          COM1(LCD_SEG17)
#define SEG_1F          COM2(LCD_SEG17)
#define SEG_1A          COM3(LCD_SEG17)
//seg18
#define SEG_S8          COM0(LCD_SEG18)
#define SEG_S7          COM1(LCD_SEG18)
#define SEG_S6          COM2(LCD_SEG18)
#define SEG_S5          COM3(LCD_SEG18)
//seg19
#define SEG_S1          COM0(LCD_SEG19)
#define SEG_S2          COM1(LCD_SEG19)
#define SEG_S3          COM2(LCD_SEG19)
#define SEG_S4          COM3(LCD_SEG19)
//seg20
#define SEG_3B          COM0(LCD_SEG20)
#define SEG_3G          COM1(LCD_SEG20)
#define SEG_3C          COM2(LCD_SEG20)
#define SEG_S12         COM3(LCD_SEG20)
//seg21
#define SEG_3A          COM0(LCD_SEG21)
#define SEG_3F          COM1(LCD_SEG21)
#define SEG_3E          COM2(LCD_SEG21)
#define SEG_3D          COM3(LCD_SEG21)
//seg22
#define SEG_2B          COM0(LCD_SEG22)
#define SEG_2G          COM1(LCD_SEG22)
#define SEG_2C          COM2(LCD_SEG22)
#define SEG_S11         COM3(LCD_SEG22)
//seg23
#define SEG_2A          COM0(LCD_SEG23)
#define SEG_2F          COM1(LCD_SEG23)
#define SEG_2E          COM2(LCD_SEG23)
#define SEG_2D          COM3(LCD_SEG23)

#define SEG_NULL            0

#define I_A                 0x0001
#define I_B                 0x0002
#define I_C                 0x0004
#define I_D                 0x0008
#define I_E                 0x0010
#define I_F                 0x0020
#define I_G                 0x0040
#define I_I                 0x0080

/*  lcd digit and char macro define */
#define SPACE           0  //all the SEGments of the char off
#define CHAR_0          (I_A|I_B|I_C|I_D|I_E|I_F)
#define CHAR_1          (I_B|I_C)
#define CHAR_2          (I_A|I_B|I_D|I_E|I_G)
#define CHAR_3          (I_A|I_B|I_C|I_D|I_G)
#define CHAR_4          (I_B|I_C|I_F|I_G)
#define CHAR_5          (I_A|I_C|I_D|I_F|I_G)
#define CHAR_6          (I_A|I_C|I_D|I_E|I_F|I_G)
#define CHAR_7          (I_A|I_B|I_C)
#define CHAR_8          (I_A|I_B|I_C|I_D|I_E|I_F|I_G)
#define CHAR_9          (I_A|I_B|I_C|I_D|I_F|I_G)
#define CHAR_A          (I_A|I_B|I_C|I_E|I_F|I_G)    // 'A'
#define CHAR_b          (I_C|I_D|I_E|I_F|I_G)        // 'b'
#define CHAR_C          (I_A|I_D|I_E|I_F)            // 'C'
#define CHAR_d          (I_B|I_C|I_D|I_E|I_G)        // 'd'
#define CHAR_E          (I_A|I_D|I_E|I_F|I_G)        // 'E'
#define CHAR_F          (I_A|I_E|I_F|I_G)            // 'F'
#define CHAR_G          (I_A|I_C|I_D|I_E|I_F)        // 'G'
#define CHAR_H          (I_C|I_E|I_F|I_G)            // 'h'
#define CHAR_I          (I_E|I_F)                    // 'I'
#define CHAR_J          (I_B|I_C|I_D|I_E)            // 'J'
#define CHAR_k          (I_E|I_F|I_G)                // 'k'
#define CHAR_L          (I_D|I_E|I_F)                // 'L'
#define CHAR_m          (I_A|I_C|I_E)                // 'm'
#define CHAR_N          (I_A|I_B|I_C|I_E|I_F)        // 'N'
#define CHAR_O          (I_A|I_B|I_C|I_D|I_E|I_F)    // 'O'
#define CHAR_P          (I_A|I_B|I_E|I_F|I_G)        // 'P'
#define CHAR_q          (I_A|I_B|I_C|I_F|I_G)        // 'q'
#define CHAR_r          (I_E|I_G)                    // 'r'
#define CHAR_S          (I_A|I_C|I_D|I_F|I_G)        // 'S'
#define CHAR_t          (I_D|I_E|I_F|I_G)            // 't'
#define CHAR_u          (I_B|I_C|I_D|I_E|I_F)        // 'u'
#define CHAR_V          (I_C|I_D|I_E)                // 'V'
#define CHAR_w          (I_B|I_D|I_F)                // 'w'
#define CHAR_x          (I_B|I_C|I_E|I_F|I_G)        // 'x'
#define CHAR_y          (I_B|I_C|I_D|I_F|I_G)        // 'y'
#define CHAR_Z          (I_A|I_B|I_D|I_E|I_G)        // 'Z'
#define CHAR_c          (I_D|I_E|I_G)                // 'c'
#define CHAR_n          (I_C|I_E|I_G)                // 'n'
#define CHAR_o          (I_C|I_D|I_E|I_G)            // 'o'
#define CHAR_           (I_G)                        // '-'  短杠 
#define CHAR__          (I_D)                        // '_'  下划线


/* Private typedef -----------------------------------------------------------*/
typedef uint16_t          SEG_TYPE_t;     ///
typedef struct
{
    const SEG_TYPE_t* seg_tab;
    const SEG_TYPE_t* dot_tab;
    uint8_t seg_num;
    uint8_t dot_num;
    uint8_t digit_num;
} Screen_s;

/// @brief 主屏数字段址表, 从左到右
static const SEG_TYPE_t ms_digit_segs[][7] =
{
    //数据显示
    {SEG_2A,   SEG_2B,   SEG_2C,   SEG_2D,   SEG_2E,   SEG_2F,   SEG_2G},
    {SEG_3A,   SEG_3B,   SEG_3C,   SEG_3D,   SEG_3E,   SEG_3F,   SEG_3G},
    {SEG_4A,   SEG_4B,   SEG_4C,   SEG_4D,   SEG_4E,   SEG_4F,   SEG_4G},
    {SEG_5A,   SEG_5B,   SEG_5C,   SEG_5D,   SEG_5E,   SEG_5F,   SEG_5G},
    {SEG_6A,   SEG_6B,   SEG_6C,   SEG_6D,   SEG_6E,   SEG_6F,   SEG_6G},
    {SEG_7A,   SEG_7B,   SEG_7C,   SEG_7D,   SEG_7E,   SEG_7F,   SEG_7G},
    {SEG_8A,   SEG_8B,   SEG_8C,   SEG_8D,   SEG_8E,   SEG_8F,   SEG_8G},
    {SEG_9A,   SEG_9B,   SEG_9C,   SEG_9D,   SEG_9E,   SEG_9F,   SEG_9G},
};
/// @brief 主屏小数点段址表
static const SEG_TYPE_t ms_dot_segs[] =
{
    SEG_NULL, SEG_NULL, SEG_NULL, SEG_DP1, SEG_DP2, SEG_DP3, SEG_DP4
};

#if LCD_PS_DIGITS != 0
/// @brief 副屏数字段址表, 从左到右
static const SEG_TYPE_t ps_digit_segs[][7] =
{
    //ID 数据标识
    {SEG_13A, SEG_13B, SEG_13C, SEG_13D, SEG_13E, SEG_13F, SEG_13G},
    {SEG_14A, SEG_14B, SEG_14C, SEG_14D, SEG_14E, SEG_14F, SEG_14G},
    {SEG_15A, SEG_15B, SEG_15C, SEG_15D, SEG_15E, SEG_15F, SEG_15G},
    {SEG_16A, SEG_16B, SEG_16C, SEG_16D, SEG_16E, SEG_16F, SEG_16G},
    {SEG_17A, SEG_17B, SEG_17C, SEG_17D, SEG_17E, SEG_17F, SEG_17G},
    {SEG_18A, SEG_18B, SEG_18C, SEG_18D, SEG_18E, SEG_18F, SEG_18G},
    {SEG_19A, SEG_19B, SEG_19C, SEG_19D, SEG_19E, SEG_19F, SEG_19G},
    {SEG_20A, SEG_20B, SEG_20C, SEG_20D, SEG_20E, SEG_20F, SEG_20G},
    //组成序号
    {SEG_23A, SEG_23B, SEG_23C, SEG_23D, SEG_23E, SEG_23F, SEG_23G},
    {SEG_24A, SEG_24B, SEG_24C, SEG_24D, SEG_24E, SEG_24F, SEG_24G},
};

/// @brief 副屏小数点段址表
static const SEG_TYPE_t ps_dot_segs[] =
{
   SEG_NULL, SEG_DP10, SEG_NULL, SEG_DP11, SEG_NULL, SEG_DP12, SEG_NULL,
};
#endif

static const Screen_s digit_screen[] =  // 显示屏的参数
{
    {
        .seg_tab    = ms_digit_segs[0],
        .seg_num    = sizeof(ms_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(ms_digit_segs),
        .dot_tab    = ms_dot_segs,
        .dot_num    = eleof(ms_dot_segs),
    },
#if LCD_PS_DIGITS != 0
    {
        .seg_tab    = ps_digit_segs[0],
        .seg_num    = sizeof(ps_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(ps_digit_segs),
        .dot_tab    = ps_dot_segs,
        .dot_num    = eleof(ps_dot_segs),
    },
#endif
};

static const uint8_t screen_number = eleof(digit_screen);

/// @brief 单位
static const SEG_TYPE_t unit_none[]          = {SEG_NULL};
static const SEG_TYPE_t unit_sign[]          = {SEG_NULL};    // 负号
static const SEG_TYPE_t unit_time[]          = {SEG_DP1, SEG_DP3, SEG_DP5, SEG_S8}; // hh:mm:ss
static const SEG_TYPE_t unit_date[]          = {SEG_DP1, SEG_DP3};                   //YY.MM.DD

static const SEG_TYPE_t unit_kWh[]           = {SEG_S17, SEG_S18, SEG_S19, SEG_S20};
static const SEG_TYPE_t unit_kVAh[]          = {SEG_NULL};
static const SEG_TYPE_t unit_kvarh[]         = {SEG_NULL};

static const SEG_TYPE_t unit_kW[]            = {SEG_S18, SEG_S19, SEG_S20};
static const SEG_TYPE_t unit_kVA[]           = {SEG_NULL};
static const SEG_TYPE_t unit_kvar[]          = {SEG_NULL};

static const SEG_TYPE_t unit_V[]             = {SEG_S21};
static const SEG_TYPE_t unit_A[]             = {SEG_S22};
static const SEG_TYPE_t unit_Hz[]            = {SEG_NULL};

static const SEG_TYPE_t icon_money[]         = {SEG_S23}; //元

/// @brief 图标
static const SEG_TYPE_t icon_arrow_pq[]      = {SEG_NULL}; // 四象限坐标
static const SEG_TYPE_t icon_arrow_q1[]      = {SEG_NULL}; // 1象限
static const SEG_TYPE_t icon_arrow_q2[]      = {SEG_NULL}; // 2象限
static const SEG_TYPE_t icon_arrow_q3[]      = {SEG_NULL}; // 3象限
static const SEG_TYPE_t icon_arrow_q4[]      = {SEG_NULL}; // 4象限

static const SEG_TYPE_t icon_dang_qian[]     = {SEG_S3};                                                                    // 当前
static const SEG_TYPE_t icon_shang_1yue[]    = {SEG_S4, SEG_1B, SEG_1C};                                            // 上1月
static const SEG_TYPE_t icon_shang_2yue[]    = {SEG_S4, SEG_1A, SEG_1B, SEG_1D, SEG_1E, SEG_1G};                    // 上2月
static const SEG_TYPE_t icon_shang_3yue[]    = {SEG_S4, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1G};                    // 上3月
static const SEG_TYPE_t icon_shang_4yue[]    = {SEG_S4, SEG_1B, SEG_1C, SEG_1F, SEG_1G};                            // 上4月
static const SEG_TYPE_t icon_shang_5yue[]    = {SEG_S4, SEG_1A, SEG_1C, SEG_1D, SEG_1F, SEG_1G};                    // 上5月
static const SEG_TYPE_t icon_shang_6yue[]    = {SEG_S4, SEG_1A, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G};            // 上6月
static const SEG_TYPE_t icon_shang_7yue[]    = {SEG_S4, SEG_1A, SEG_1B, SEG_1C};                                    // 上7月
static const SEG_TYPE_t icon_shang_8yue[]    = {SEG_S4, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G};    // 上8月
static const SEG_TYPE_t icon_shang_9yue[]    = {SEG_S4, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1F, SEG_1G};            // 上9月
static const SEG_TYPE_t icon_shang_10yue[]   = {SEG_S4, SEG_S9, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F};    // 上10月
static const SEG_TYPE_t icon_shang_11yue[]   = {SEG_S4, SEG_S9, SEG_1B, SEG_1C};                                    // 上11月
static const SEG_TYPE_t icon_shang_12yue[]   = {SEG_S4, SEG_S9, SEG_1A, SEG_1B, SEG_1D, SEG_1E, SEG_1G};            // 上12月

static const SEG_TYPE_t icon_zu_he[]         = {SEG_NULL};          // 组合
static const SEG_TYPE_t icon_neg[]           = {SEG_NULL}; // 反向
static const SEG_TYPE_t icon_pos[]           = {SEG_NULL}; // 正向S
static const SEG_TYPE_t icon_react[]         = {SEG_NULL}; // 无功
static const SEG_TYPE_t icon_act[]           = {SEG_NULL}; // 有功
static const SEG_TYPE_t icon_total[]         = {SEG_S6};          // 总
static const SEG_TYPE_t icon_tariff[]        = {SEG_S7}; // 费率

static const SEG_TYPE_t icon_react_q1[]      = {SEG_NULL};         // 无功1象限
static const SEG_TYPE_t icon_react_q2[]      = {SEG_NULL};         // 无功2象限
static const SEG_TYPE_t icon_react_q3[]      = {SEG_NULL};// 无功3象限
static const SEG_TYPE_t icon_react_q4[]      = {SEG_NULL};// 无功4象限

static const SEG_TYPE_t icon_tariff_1[]      = {SEG_S7, SEG_1B, SEG_1C};                                         //费率1
static const SEG_TYPE_t icon_tariff_2[]      = {SEG_S7, SEG_1A, SEG_1B, SEG_1D, SEG_1E, SEG_1G};                 //费率2
static const SEG_TYPE_t icon_tariff_3[]      = {SEG_S7, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1G};                 //费率3
static const SEG_TYPE_t icon_tariff_4[]      = {SEG_S7, SEG_1B, SEG_1C, SEG_1F, SEG_1G};                         //费率4
static const SEG_TYPE_t icon_tariff_5[]      = {SEG_S7, SEG_1A, SEG_1C, SEG_1D, SEG_1F, SEG_1G};                 //费率5
static const SEG_TYPE_t icon_tariff_6[]      = {SEG_S7, SEG_1A, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G};         //费率6
static const SEG_TYPE_t icon_tariff_7[]      = {SEG_S7, SEG_1A, SEG_1B, SEG_1C};                                 //费率7
static const SEG_TYPE_t icon_tariff_8[]      = {SEG_S7, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G}; //费率8
static const SEG_TYPE_t icon_tariff_9[]      = {SEG_S4, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1F, SEG_1G};         //费率9
static const SEG_TYPE_t icon_tariff_10[]     = {SEG_S4, SEG_S9, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F}; //费率10
static const SEG_TYPE_t icon_tariff_11[]     = {SEG_S4, SEG_S9, SEG_1B, SEG_1C};                                 //费率11
static const SEG_TYPE_t icon_tariff_12[]     = {SEG_S4, SEG_S9, SEG_1A, SEG_1B, SEG_1D, SEG_1E, SEG_1G};         //费率12

static const SEG_TYPE_t icon_power[]         = {SEG_NULL};          // 功率
static const SEG_TYPE_t icon_power_A[]       = {SEG_NULL}; // A相功率
static const SEG_TYPE_t icon_power_B[]       = {SEG_NULL}; // B相功率
static const SEG_TYPE_t icon_power_C[]       = {SEG_NULL}; // C相功率

static const SEG_TYPE_t icon_voltage_A[]     = {SEG_NULL}; // A相电压
static const SEG_TYPE_t icon_voltage_B[]     = {SEG_NULL}; // B相电压
static const SEG_TYPE_t icon_voltage_C[]     = {SEG_NULL}; // C相电压

static const SEG_TYPE_t icon_current_A[]     = {SEG_NULL}; // A相电流
static const SEG_TYPE_t icon_current_B[]     = {SEG_NULL}; // B相电流
static const SEG_TYPE_t icon_current_C[]     = {SEG_NULL}; // C相电流
static const SEG_TYPE_t icon_current_N[]     = {SEG_NULL}; // N路电流

static const SEG_TYPE_t icon_pf_A[]          = {SEG_NULL}; // A相功率因数
static const SEG_TYPE_t icon_pf_B[]          = {SEG_NULL}; // B相功率因数
static const SEG_TYPE_t icon_pf_C[]          = {SEG_NULL}; // C相功率因数

// static const SEG_TYPE_t icon_jie_ti[]        = {SEG_S35};                    // 阶梯
static const SEG_TYPE_t icon_energy[]        = {SEG_S5};           // 电量
// static const SEG_TYPE_t icon_remain_kWh[]    = {SEG_S36, SEG_S38, SEG_S39};  // 剩余电量
// static const SEG_TYPE_t icon_remain_money[]  = {SEG_S36, SEG_S38, SEG_S40};  // 剩余电费
// static const SEG_TYPE_t icon_demand[]        = {SEG_S37, SEG_S39};           // 需量
// static const SEG_TYPE_t icon_voltage_miss[]  = {SEG_S42, SEG_S43};           // 失压
// static const SEG_TYPE_t icon_current_miss[]  = {SEG_S42, SEG_S44};           // 失流
// static const SEG_TYPE_t icon_shi_jian[]      = {SEG_S46, SEG_S47};           // 时间
// static const SEG_TYPE_t icon_shi_duan[]      = {SEG_S46, SEG_S48};           // 时段

// static const SEG_TYPE_t icon_Ua[]            = {SEG_S64};          // Ua  电压指示符号
// static const SEG_TYPE_t icon_Ub[]            = {SEG_S65};          // Ub
// static const SEG_TYPE_t icon_Uc[]            = {SEG_S66};          // Uc

// static const SEG_TYPE_t icon_Ia[]            = {SEG_S69};          // Ia  电流指示符号
// static const SEG_TYPE_t icon_Ib[]            = {SEG_S71};          // Ib
// static const SEG_TYPE_t icon_Ic[]            = {SEG_S73};          // Ic

// static const SEG_TYPE_t icon_Pa_s[]          = {SEG_S68};          // -A 功率符号    
// static const SEG_TYPE_t icon_Pb_s[]          = {SEG_S70};          // -B 功率符号
// static const SEG_TYPE_t icon_Pc_s[]          = {SEG_S72};          // -C 功率符号

// static const SEG_TYPE_t icon_cur_suite[]     = {SEG_S74};          // 当前套阶梯电价
// static const SEG_TYPE_t icon_bak_suite[]     = {SEG_S75};          // 备用套阶梯电价

// static const SEG_TYPE_t icon_L_1[]           = {SEG_S56, SEG_21B, SEG_21C};                                                // 电价1
// static const SEG_TYPE_t icon_L_2[]           = {SEG_S56, SEG_21A, SEG_21B, SEG_21D, SEG_21E, SEG_21G};                     // 电价2
// static const SEG_TYPE_t icon_L_3[]           = {SEG_S56, SEG_21A, SEG_21B, SEG_21C, SEG_21D, SEG_21G};                     // 电价3
// static const SEG_TYPE_t icon_L_4[]           = {SEG_S56, SEG_21B, SEG_21C, SEG_21F, SEG_21G};                              // 电价4
// static const SEG_TYPE_t icon_L_5[]           = {SEG_S56, SEG_21A, SEG_21C, SEG_21D, SEG_21F, SEG_21G};                     // 电价5
// static const SEG_TYPE_t icon_L_6[]           = {SEG_S56, SEG_21A, SEG_21C, SEG_21D, SEG_21E, SEG_21F, SEG_21G};            // 电价6
// static const SEG_TYPE_t icon_L_7[]           = {SEG_S56, SEG_21A, SEG_21B, SEG_21C};                                       // 电价7
// static const SEG_TYPE_t icon_L_8[]           = {SEG_S56, SEG_21A, SEG_21B, SEG_21C, SEG_21D, SEG_21E, SEG_21F, SEG_21G};   // 电价8

// static const SEG_TYPE_t icon_signal_tower[]  = {SEG_S79};                                   // 信号塔
// static const SEG_TYPE_t icon_signal[]        = {SEG_S80, SEG_S81, SEG_S82, SEG_S83};        // 信号强度符

static const SEG_TYPE_t icon_mudle_comm[]    = {SEG_S1};                                   // 通信模块通讯中
// static const SEG_TYPE_t icon_ir_comm[]       = {SEG_S85};                                   // 红外通讯中
// static const SEG_TYPE_t icon_4851_comm[]     = {SEG_S85, SEG_S86};                          // 4851通讯中
// static const SEG_TYPE_t icon_4852_comm[]     = {SEG_S85, SEG_S87};                          // 4852通讯中
// static const SEG_TYPE_t icon_ir_verify[]     = {SEG_S88};                                   // 红外认证通过
// static const SEG_TYPE_t icon_hang_up[]       = {SEG_S89};                                   // 挂起
static const SEG_TYPE_t icon_key[]           = {SEG_S2};                                   // 测试密钥状态
// static const SEG_TYPE_t icon_alarm[]         = {SEG_S91};                                   // 报警指示

static const SEG_TYPE_t icon_battery1_low[]  = {SEG_S15};                                   // 电池1低电量

// static const SEG_TYPE_t icon_rev_seq[]       = {SEG_S94};                                   // 逆相序指示
// static const SEG_TYPE_t icon_success[]       = {SEG_S95};                                   // IC卡读卡成功
// static const SEG_TYPE_t icon_fail[]          = {SEG_S96};                                   // IC卡读卡失败
static const SEG_TYPE_t icon_purchase[]      = {SEG_S14};                                   // 请购电，剩余金额偏低时闪烁
static const SEG_TYPE_t icon_relay_off[]     = {SEG_S16};                                   // 拉闸，拉闸状态指示

/// @brief 数字字段码表
static const uint8_t segs_digit_tab[] =
{
    CHAR_0, CHAR_1, CHAR_2, CHAR_3, CHAR_4,
    CHAR_5, CHAR_6, CHAR_7, CHAR_8, CHAR_9,
};

/// @brief 字母字段码表
static const uint8_t segs_alp_tab[] =
{
    CHAR_A, CHAR_b, CHAR_C, CHAR_d, CHAR_E, CHAR_F, CHAR_G,
    CHAR_H, CHAR_I, CHAR_J, CHAR_k, CHAR_L, CHAR_m, CHAR_N,
    CHAR_o, CHAR_P, CHAR_q, CHAR_r, CHAR_S, CHAR_t,
    CHAR_u, CHAR_V, CHAR_w, CHAR_x, CHAR_y, CHAR_Z,
};


