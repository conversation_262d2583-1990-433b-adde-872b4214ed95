/******************************************************************************
 * @file        rn8xxx_ll_sea_trng.c
 * @brief       TRNG functions
 * <AUTHOR> Technology
 *
 * @note
 * Copyright (C) , Renergy Technology Inc. All rights reserved.
 *****************************************************************************/
#include "rn8xxx_ll_sea_trng.h"

/**
 * @brief trng_gen
 * @param random the expected random numbers' address
 * @param data_len the expected random numbers' length
 *
 */
void LL_TRNG_gen(uint32_t random[], uint8_t data_len)
{
    uint8_t i;
    uint32_t cnt;
    sea_trng_init();
    *(uint32_t*)(0x5000C3C0) = 0xE4u;
    SEA_CYPHER->TRNG_CTL1 = 0x6543210U;
    SEA_CYPHER->TRNG_CTL2 = 0x884u;
    SEA_CYPHER->Status = 0x40U;
    SEA_CYPHER->TRNG_CTL0 = 0x001Fu;

    for (i = 0U; i < data_len; i++) {
      if (0x0U == (i & 0x3U)) {
        cnt = 0U;
        while((SEA_CYPHER->Status & 0x40U)==0U){
          if(cnt++ > 8000U){
            break;
          }
        }
      }
      random[i] = SEA_CYPHER->TRNG_BUF[i & 0x3U];
    }

    SEA_CYPHER->TRNG_CTL0 = 0x0U;
}
/* r2725 */
