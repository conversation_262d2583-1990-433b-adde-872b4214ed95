/**
  ******************************************************************************
  * @file    flash.c
  * <AUTHOR> @version V1.0.1
  * @date    2024
  * @brief   本文件主要包含对External Data Flash的调用。
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "ext_flash.h"
#include <string.h>
#include "debug.h"



/* Private typedef -----------------------------------------------------------*/
typedef enum
{
    M25_WREN = 0x06,        // write enable
    M25_WRDI = 0x04,        // write disable
    M25_RDID = 0x9F,        // read identification
    M25_RDSR = 0x05,        // read status register
    M25_WRSR = 0x01,        // write status register
    M25_READ = 0x03,        // read data bytes
    M25_FAST_READ = 0x0B,   // read data bytes in higher speed
    M25_PP   = 0x02,        // page program
    M25_PW   = 0x0A,        // page write
    M25_PE   = 0xDB,        // page erase
    M25_SE   = 0x20,        // sector erase
    M25_BE   = 0xD8,        // bulk erase
    M25_CE   = 0xC7,        // chip erase
    M25_DP   = 0xB9,        // deep power down
    M25_RDP  = 0xAB,        // Release from deep power-down
} m25pxx_command;

typedef enum
{
    M25_WIP = 0x01,         // write in process bit
    M25_WEL = 0x02,         // write enable latch bit
    M25_BP0 = 0x04,         // block protect bit 0
    M25_BP1 = 0x08,         // block protect bit 1
    M25_SRWD= 0x80,         // status register write protect
} m25pxx_status;

/* DATAFLASH 厂家枚举定义 */
enum
{
    DF_ATMEL    = 0x001f, // ATMEL
    DF_NUMONYX  = 0x0020, // NUMONYX(ST)
    DF_MXIC     = 0x00c2, // MXIC(万宏)
    DF_XTX      = 0x005e, // XTX芯天下
    DF_FM       = 0x00A1, // 复旦微
    DF_XM       = 0x0014, // 新芯
    DF_PUYA     = 0x0085, // 普冉
    DF_BOYA     = 0x0068, // 博雅
	DF_HB       = 0x00EF, // 华邦
} DF_MANU_ID;

/* Private define ------------------------------------------------------------*/
#define DUMMY_BYTE          0xFF

/* Private macro -------------------------------------------------------------*/
#ifdef COM_DATAFLASH
/* DataFlash SPI驱动 */
///m-1发送，0接受
#define DSPI_TRANS(x,m)       hal_spi_trans(COM_DATAFLASH, x,m)
#define DSPI_ON()             hal_spi_deviceon(COM_DATAFLASH)
#define DSPI_OFF()            hal_spi_deviceoff(COM_DATAFLASH)
#else
#define DSPI_TRANS(x,m)         0
#define DSPI_ON()
#define DSPI_OFF()
#endif

/// @brief 读取flashID
/// @param  
/// @return 
static uint32_t flash_read_id(void)
{
    uint32_t df_id = 0;

    DSPI_ON();
    DSPI_TRANS(0x9F, 1);
    df_id  = DSPI_TRANS(DUMMY_BYTE, 0), df_id <<= 8; // 厂家标识
    df_id |= DSPI_TRANS(DUMMY_BYTE, 0), df_id <<= 8; // Memory类型标识
    df_id |= DSPI_TRANS(DUMMY_BYTE, 0);              // 容量大小标识
    DSPI_OFF();

    if(df_id == 0x00FFFFFF) df_id = 0;

    return df_id;
}
/* Private variables ---------------------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/// @brief Enable data flash write.
/// @param  
/// @return 
static bool flash_write_en(void)
{
    DSPI_ON();
    DSPI_TRANS(M25_WREN, 1);
    DSPI_OFF();
    return TRUE;
}

/// @brief 等待写结束
/// @param cnt 
static void flash_wait_for_write_end(uint16_t cnt)
{
    char stus;// = 0xff;

    while(--cnt != 0)
    {
    	hal_mcu.wait_us(1000);
    	DSPI_ON();
    	DSPI_TRANS(M25_RDSR ,1);
    	stus = DSPI_TRANS(DUMMY_BYTE, 0);
    	DSPI_OFF();

    	if(!(stus & M25_WIP)) break;
        HAL_WDG_RESET();
    }
}

/// @brief 读取flash状态
/// @param  
/// @return 
static uint8_t flash_read_status(void)
{
    uint8_t status = 0xff;

    DSPI_ON();
    DSPI_TRANS(M25_RDSR, 1);
    status = DSPI_TRANS(DUMMY_BYTE, 0);
    DSPI_OFF();

    return status;
}

/// @brief 写flash状态
/// @param status 
/// @return 
static bool flash_write_status(uint8_t status)
{
    flash_write_en();
    DSPI_ON();
    DSPI_TRANS(M25_WRSR, 1);
    DSPI_TRANS(status, 1);
    DSPI_OFF();
    flash_wait_for_write_end(20);
    return TRUE;
}

/// @brief flash写，可以跨页写，不能跨扇区写，只在本文件调用
/// @param addr 
/// @param dat 
/// @param length 
/// @return 
static bool flash_program_page(uint32_t addr, const uint8_t* dat, uint16_t length)
{
    while(length > 0)
    {
        uint16_t wlen = addr % M25_PAGE_LEN;
        if((length + wlen) <= M25_PAGE_LEN)
            wlen = length;
        else
            wlen = M25_PAGE_LEN - wlen;

    	flash_write_en();
	    DSPI_ON();
	    DSPI_TRANS(M25_PP, 1);
	    DSPI_TRANS((uint8_t)(addr >> 16), 1);
	    DSPI_TRANS((uint8_t)(addr >> 8), 1);
	    DSPI_TRANS((uint8_t)addr, 1);
        addr += wlen;
        length -= wlen;
	    while(wlen > 0)
	    {
	        DSPI_TRANS(*dat, 1);
	        dat++, wlen--;
	    }
	    DSPI_OFF();
	    flash_wait_for_write_end(60);
    }

    return TRUE;
}

/// @brief flash擦除扇区，只在本文件调用
/// @param addr 
/// @return 
bool flash_sector_erase(uint32_t addr)
{
    flash_write_en();
    DSPI_ON();
    DSPI_TRANS(M25_SE, 1);
    DSPI_TRANS((uint8_t)(addr >> 16), 1);
    DSPI_TRANS((uint8_t)(addr >> 8), 1);
    DSPI_TRANS((uint8_t)addr, 1);
    DSPI_OFF();

    flash_wait_for_write_end(5000);
    return TRUE;
}

/// @brief flash全片擦除
/// @param  
/// @return 
bool flash_chip_erase(void)
{
    uint8_t status = flash_read_status();

    if(status & (M25_BP0 | M25_BP1 | M25_SRWD))
    {
        status &= ~(M25_BP0 | M25_BP1 | M25_SRWD);
        if(!flash_write_status(status))
        {
            return FALSE;
        }
    }

    flash_write_en();
    DSPI_ON();
    DSPI_TRANS(M25_CE, 1);
    DSPI_OFF();

    flash_wait_for_write_end(60000);
    return TRUE;
}

/// @brief flash读取数据
/// @param addr 
/// @param dat 
/// @param length 
/// @return 
bool bsp_flash_read_bytes(uint32_t addr, void* dat, uint32_t length)
{
    uint8_t* p = (uint8_t*)dat;
    addr &= ~TAG_DF_ROLL_ACCESS;

    if(hal_mcu.pwrdn_query()) return FALSE;

    DSPI_ON();
    DSPI_TRANS(M25_READ, 1);
    DSPI_TRANS((uint8_t)(addr >> 16), 1);
    DSPI_TRANS((uint8_t)(addr >> 8), 1);
    DSPI_TRANS((uint8_t)addr, 1);
    while(length > 0)
    {
        *p = DSPI_TRANS(DUMMY_BYTE, 0);
        p++, length--;
    }
    HAL_WDG_RESET();
    DSPI_OFF();

    return TRUE;
}

/// @brief flash写判断,扫描flash数据是否相同，相同则不用擦除，不同则需要擦除再写
/// @param addr 
/// @param data 
/// @param len 
/// @return 0, 数据相同，不用写;
///         1, 数据不同，需擦除后再写;
///         2, 数据不同，无需擦除可以直接写;
///         ff, 掉电或异常
static uint8_t flash_write_check(uint32_t addr, void* data, uint32_t len)
{
    uint8_t* pdata = (uint8_t*)data;
    uint8_t revByte,result;

    if(hal_mcu.pwrdn_query()) return 0xff;
    if(pdata == NULL) return 1; // 空指针，返回1，用此方式擦除扇区

    result = 0;
    DSPI_ON();
    DSPI_TRANS(M25_READ, 1);
    DSPI_TRANS((uint8_t)(addr >> 16), 1);
    DSPI_TRANS((uint8_t)(addr >> 8), 1);
    DSPI_TRANS((uint8_t)addr, 1);
    while(len > 0)
    {
        revByte = DSPI_TRANS(DUMMY_BYTE, 0);
        if(*pdata != (revByte & (*pdata))) // 该数据位存在无法写成1的为位（flash正常空间位1可以写成0）
        {
            DSPI_OFF();
            return 1;
        }
        else if(*pdata != revByte) // 数据不同但是可以直接成目标数据
        {
            result = 2;
        }
        pdata++, len--;
    }
    DSPI_OFF();

    return result;
}

/// @brief flash写数据，支持直接写，无需考虑擦除扇区，数据指针为空是擦除数据. 注意地址为非扇区对齐时，如果发送擦除，只回写前面部分数据，后面部分数据丢失
/// @param df_id 
/// @param addr TAG_DF_ROLL_ACCESS， 有预留1块扇区，回写数据时不用写入全部扇区，只写入扇区前面部分数据，否则全部回写
/// @param dat 
/// @param num 
/// @return 
bool flash_write_bytes(uint16_t df_id, uint32_t addr, const void* dat, uint32_t num)
{
    uint16_t remain_len;
    uint8_t check_result;
    const uint8_t* pdat = (const uint8_t*)dat;
    bool b_tag = boolof(addr & TAG_DF_ROLL_ACCESS);
    addr &= ~TAG_DF_ROLL_ACCESS;

	if(addr + num >= M25_SizeGet(df_id)) return FALSE; // 写入越界
    while(num > 0)
    {
        uint8_t buffer[M25_SECTOR_LEN];
        remain_len = addr % M25_SECTOR_LEN;     // 计算本次写操作的偏移量
        if((remain_len + num) > M25_SECTOR_LEN) // 计算本次操作需要写入的数据长度
        {
            remain_len = M25_SECTOR_LEN - remain_len; // 写入数据长度大于扇区剩余长度，则只写入扇区剩余长度
            num -= remain_len;
        }
        else
        {
            remain_len = num;    ///写入数据长度小于扇区剩余长度，则写入剩余长度
            num = 0;
        }

        check_result = flash_write_check(addr,(void*)pdat, remain_len); // 检查数据是否相同
        if(check_result == 0xff) return false;  ///检查到掉电或异常
        if(check_result == 2) // 需要写入操作
        {
            if(!flash_program_page(addr, pdat, remain_len))
            {
                return false;
            }
        }
        else if(check_result == 1) // 需要擦除扇区后再次写入操作
        {
            uint8_t* pbuf;
            uint16_t oplen;
            uint32_t tmpAddr = addr & ( ~((uint32_t)M25_SECTOR_LEN - 1)); // 计算当前扇区首地址

            oplen = addr %  M25_SECTOR_LEN;  // 计算本次操作需要写入的偏移量
            pbuf = buffer + oplen;           // 计算本次操作需要写入的起始地址

            bsp_flash_read_bytes(tmpAddr, buffer, M25_SECTOR_LEN);  // 读取当前扇区数据

            if(pdat != NULL)
            {
                memcpy(pbuf, pdat, remain_len); // 写入数据到buffer
            }
            else
            {
                memset(pbuf, 0xFF , remain_len); // 写入全0xFF数据到buffer，在扇区擦除后相当于没有写入操作
            }
            if(!flash_sector_erase(tmpAddr)) // 扇区擦除
            {
                return false;
            }
            if(b_tag)
            {
                oplen = remain_len + oplen; // 已经预留1块扇区，不用回写全部数据，曲线回调只需要要回写扇区前面部分数据即可
            }
            else
            {
                oplen = M25_SECTOR_LEN;    // 计算本次操作需要回写的长度（扇区全部数据）
            }
            if(!flash_program_page(tmpAddr, buffer, oplen)) // 回写数据
            {
                return false;
            }
    	}
        if(pdat != NULL) pdat = pdat + remain_len;
	    addr += remain_len;
        HAL_WDG_RESET();
    }

    return TRUE;
}

/// @brief flash写数据，地址最好是扇区对齐的，否则需要擦除扇区再写，数据指针为空则不操作
/// @param df_id  
/// @param addr  起始地址最好是扇区对齐的，否则需要擦除再写
/// @param dat 
/// @param num 
/// @return 
bool flash_program_bytes(uint16_t df_id, uint32_t addr, const void* dat, uint32_t num)
{
    uint16_t remain_len;
    const uint8_t* pdat = (const uint8_t*)dat;

	if(dat == NULL || addr + num >= M25_SizeGet(df_id)) return FALSE; // 写入越界
    while(num > 0)
    {
        if((addr % M25_SECTOR_LEN) == 0) flash_sector_erase(addr);

        remain_len = addr % M25_PAGE_LEN;
        if((remain_len + num) >= M25_PAGE_LEN)
        {
            remain_len = M25_PAGE_LEN - remain_len;
            num -= remain_len;
        }
        else
        {
            remain_len = num;
            num = 0;
        }
        flash_program_page(addr, pdat, remain_len);
        addr += remain_len;
        pdat = pdat + remain_len;
    }

    return TRUE;
}

/// @brief 外部flash检测
/// @param  
/// @return 
uint32_t bsp_ext_flash_check(void)
{
    uint16_t cnt = 15;
    while(--cnt != 0)
    {
        uint32_t df_id = flash_read_id();
        switch(df_id >> 16)
        {
            case DF_ATMEL:
            case DF_NUMONYX:
            case DF_MXIC:
            case DF_XTX:
            case DF_FM:
            case DF_XM:
            case DF_PUYA:
            case DF_BOYA:
			case DF_HB:
            {
                if(M25_SizeGet(df_id) < DATAFLASH_SIZE)
                {
                    DBG_PRINTF(P_FLASH, D,"DATAFLASH_actual_size:%d KBytes\n", M25_SizeGet(df_id) / 1024);
                    DBG_PRINTF(P_FLASH, D,"DATAFLASH_Configured_size:%d KBytes\n", DATAFLASH_SIZE / 1024);
                    assert(M25_SizeGet(df_id) >= DATAFLASH_SIZE);
                    break;
                }
            }
            return df_id;
        }

        hal_mcu.wait_us(1000);
        HAL_WDG_RESET();
    }

    return 0; // 表示不认识的厂家或未读出或flash大小错误
}
/// @brief 外部flash写数据，指针为空则为擦除数据
/// @param addr 
/// @param dat 
/// @param len 
/// @return 
bool bsp_extflash_write_bytes(uint32_t addr, const void* dat, uint32_t len)
{
    uint32_t df_id = bsp_ext_flash_check();
    if(df_id == 0 || hal_mcu.pwrdn_query()) return false;// 防止掉电FLASH异常
    return flash_write_bytes((uint16_t)df_id, addr, dat, len);
}
/// @brief 外部flash写数据，用于boot区，应用层禁止调用
/// @param addr  起始地址必须为sector对齐的
/// @param dat 
/// @param len 
/// @return 
bool bsp_extflash_program_bytes(uint32_t addr, const void* dat, uint32_t len)
{
    uint32_t df_id = bsp_ext_flash_check();
    if(df_id == 0 || hal_mcu.pwrdn_query()) return false;// 防止掉电FLASH异常
    return flash_program_bytes((uint16_t)df_id, addr, dat, len);
}

/// @brief 声明dataflash子模块对象
const struct extflash_s extflash =
{
    .check              = bsp_ext_flash_check,
    .read               = bsp_flash_read_bytes,
    .write              = bsp_extflash_write_bytes,
    .program            = bsp_extflash_program_bytes,
};

