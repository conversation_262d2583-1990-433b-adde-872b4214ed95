/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c4.c
*    Describe:  DLT645-2007协议，04类数据部分     
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "typedef.h"
#include "DLT645_2007_id.h"
#include "timeapp.h"
#include "demand.h"
#include "step_tariff.h"
#include "tariff.h"
#include "dispApp.h"
#include "power_event.h"
#include "local_port.h"
#include "bsp_cfg.h"
#include "billing.h"
#include "payment.h"
#include "loadcurve.h"

#define ITEM2(n)    (uint16_t)(n)
#define pe_offset(member) member_offset(pe_para_s, member)
#define pe_sizeof(member) member_size(pe_para_s, member)

/// @brief 读取数据处理
/// @param p_info 
/// @return 
static uint16_t dlt_645_read_4(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;
    uint32_t tmpu32;
    uint16_t item, len;
    uint8_t index;
    clock_s clock_tmp;
    uint8_t no_bkl = true;

    DBG_PRINTF(P_645, D, "\r\n dlt_645_read_4--");
    if(buff == NULL) 
    {
        p_data = p_info->snd_dat;
        memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    }
    if((p_info->id&0xFFFF0000) == 0x04000000)
    {
        
        item = ITEM2(p_info->id);
        DBG_PRINTF(P_645, D, "ITEM2: %x:", item);
        switch(item)
        {
            case ITEM2(C4_DATE_WEEK            ): //@0x04000101 ///  日期星期             年月日星期
                hex_to_msbbcd(p_data, &mclock.datetime->year, 4), p_data += 4;
                break;
            case ITEM2(C4_TIME                 ): //@0x04000102 ///  时间                 时分秒    
                hex_to_msbbcd(p_data, &mclock.datetime->hour, 3), p_data += 3; 
                break;
            case ITEM2(C4_MD_PERIOD            ): //@0x04000103 ///  最大需量周期         分  
                tmpu32 = demand.para_get()->period;
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_MD_SLIP_PERIOD       ): //@0x04000104 ///  需量滑差时间         分 
                tmpu32 = demand.para_get()->slip_period;
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_PLUSE_WIDTH          ): //@0x04000105 ///  校表脉冲宽度         毫秒 
                tmpu32 = 80;
                uint32_to_lsbbcd(p_data, tmpu32, 2), p_data += 2;
                break;
            case ITEM2(C4_ZONE_SWITCH_TIME     ): //@0x04000106 ///  两套时区表切换时间   年月日时分 
                // clock_s clock_tmp;
                tariff.zone_activate_passive_time_get(&clock_tmp, 1);
                hex_to_msbbcd(p_data, &clock_tmp.hour, 2), p_data += 2;
                hex_to_msbbcd(p_data, &clock_tmp.year, 3), p_data += 3;
                break;
            case ITEM2(C4_SCHEDULE_SWITCH_TIME ): //@0x04000107 ///  两套日时段表切换时间 年月日时分 
                // clock_s clock_tmp;
                tariff.day_activate_passive_time_get(&clock_tmp, 1);
                hex_to_msbbcd(p_data, &clock_tmp.hour, 2), p_data += 2;
                hex_to_msbbcd(p_data, &clock_tmp.year, 3), p_data += 3;
                break;
            case ITEM2(C4_TARIFF_SWITCH_TIME   ): //@0x04000108 ///  两套费率电价切换时间 年月日时分 
                // clock_s clock_tmp;
                clock_tmp = *step_tariff.passive_tf_active_time_get();
                hex_to_msbbcd(p_data, &clock_tmp.hour, 2), p_data += 2;
                hex_to_msbbcd(p_data, &clock_tmp.year, 3), p_data += 3;
                
                break;
            case ITEM2(C4_STEP_SWITCH_TIME     ): //@0x04000109 ///  两套阶梯度切换时间   年月日时分
                // clock_s clock_tmp;
                clock_tmp = *step_tariff.passive_step_active_time_get();
                hex_to_msbbcd(p_data, &clock_tmp.hour, 2), p_data += 2;
                hex_to_msbbcd(p_data, &clock_tmp.year, 3), p_data += 3;
                
                break;
            case ITEM2(C4_ZONE_NUM             ): //@0x04000201 ///  年时区数  
            { 
                zone_list_s zone_list;
                tariff.zone_profile_get(&zone_list, 0);
                uint32_to_lsbbcd(p_data, zone_list.zone_num, 1), p_data += 1;
            }
                break;
            case ITEM2(C4_SCHEDULE_TAB_NUM     ): //@0x04000202 ///  日时段表数  
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                uint32_to_lsbbcd(p_data, day_list.day_num, 1), p_data += 1;
            }
                break;
            case ITEM2(C4_SCHEDULE_NUM         ): //@0x04000203 ///  日时段数    
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                uint32_to_lsbbcd(p_data, day_list.day[0].schedule_num, 1), p_data += 1;
            }
                break;
            case ITEM2(C4_TARIFF_NUM           ): //@0x04000204 ///  费率数       
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                uint32_to_lsbbcd(p_data, day_list.tf_num, 1), p_data += 1;
            }
                break;
            case ITEM2(C4_HOLIDAY_NUM          ): //@0x04000205 ///  公共假日数    
            {
                special_day_list_s special_day_list;
                tariff.special_day_get(&special_day_list, 0);
                uint32_to_lsbbcd(p_data, special_day_list.entry_num, 2), p_data += 2;
            }    
                break;
            case ITEM2(C4_HARMONIC_NUM         ): //@0x04000206 ///  谐波分析次数      

                break;
            case ITEM2(C4_STEP_NUM             ): //@0x04000207 ///  梯度数
            {
                step_tab_s *step_tab = (step_tab_s *)step_tariff.active_step_get();
                uint32_to_lsbbcd(p_data, step_tab->step_num, 1), p_data += 1;
            }
                break;
            case ITEM2(C4_DISP_ITEM_NUM        ): //@0x04000301 ///  自动循环显示屏数          NN
                tmpu32 = display.id_list_get_num(DISP_LIST1);
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_DISP_TIME            ): //@0x04000302 ///  每屏显示时间              NN
                tmpu32 = display.para_get()->time.auto_Period;
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_ENG_DISP_DECI_NUM    ): //@0x04000303 ///  显示电能小数位数          NN
                tmpu32 = display.para_get()->spe.spe_format.energy_decimal;
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_POWER_DISP_DECI_NUM  ): //@0x04000304 ///  显示功率(最大需量)小数位数 NN
                tmpu32 = display.para_get()->spe.spe_format.power_decimal;
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_KEY_DISP_ITEM_NUM    ): //@0x04000305 ///  按键循环显示屏数           NN
                tmpu32 = display.id_list_get_num(DISP_LIST2);
                uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_CT                   ): //@0x04000306 ///  电流互感器变比            NNNNNN
                break;
            case ITEM2(C4_PT                   ): //@0x04000307 ///  电流互感器变比             NNNNNN
                break;
            case ITEM2(C4_COMM_ADDR            ): //@0x04000401 ///  通信地址
                p_data += api.meter_sn_get(p_data);
                break;
            case ITEM2(C4_METER_NO             ): //@0x04000402 ///  表号
                p_data += api.meter_sn_get(p_data);
                break;
            case ITEM2(C4_AMC_NO               ): //@0x04000403 ///  资产管理编码(ASCII 码)
                len = api.product_info_get(PRODUCT_INFO(asset_code), p_data);
                memcpy(p_data, p_data + 1, len - 1);
                p_data += len - 1;
                break;
            case ITEM2(C4_BASE_VOLTAGE         ): //@0x04000404 ///  额定电压(ASCII 码)
                len = strlen(INPUT_VOLTAGE_STR);
                len = (len > 6) ? 6 : len;
                memcpy(p_data, INPUT_VOLTAGE_STR, len), p_data += len;
                if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_BASE_CURRENT         ): //@0x04000405 ///  额定电流/基本电流(ASCII 码)
                len = strlen(BASE_CURRENT_STR);
                len = (len > 6) ? 6 : len;
                memcpy(p_data, BASE_CURRENT_STR, len), p_data += len;
                if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_MAX_CURRENT          ): //@0x04000406 ///  最大电流(ASCII 码)
                len = strlen(MAXIMUM_CURRENT_STR);
                len = (len > 6) ? 6 : len;
                memcpy(p_data, MAXIMUM_CURRENT_STR, len), p_data += len;
                if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_ACT_ACCURACY         ): //@0x04000407 ///  有功准确度等级(ASCII 码)
                len = strlen(ACT_ACCURACY_LEVEL_STR);
                len = (len > 4) ? 4 : len;
                memcpy(p_data, ACT_ACCURACY_LEVEL_STR, len), p_data += len;
                if(len < 4) {len = 4 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 4, 4);
                break;
            case ITEM2(C4_REA_ACCURACY         ): //@0x04000408 ///  无功准确度等级(ASCII 码)
                len = strlen(REACT_ACCURACY_LEVEL_STR);
                len = (len > 4) ? 4 : len;
                memcpy(p_data, REACT_ACCURACY_LEVEL_STR, len), p_data += len;
                if(len < 4) {len = 4 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 4, 4);
                break;
            case ITEM2(C4_ACT_CONST            ): //@0x04000409 ///  电表有功常数
                uint32_to_lsbbcd(p_data, METER_CONST, 3), p_data += 3;
                break;
            case ITEM2(C4_REA_CONST            ): //@0x0400040A ///  电表无功常数
                uint32_to_lsbbcd(p_data, METER_CONST, 3), p_data += 3;
                break;
            case ITEM2(C4_METER_TYPE           ): //@0x0400040B ///  电表型号(ASCII 码)
                len = strlen(HDW_VERSION);
                len = (len > 10) ? 10 : len;
                memcpy(p_data, HDW_VERSION, len), p_data += len;
                if(len < 10) {len = 10 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 10, 10);
                break;
            case ITEM2(C4_PRODUCT_DATE         ): //@0x0400040C ///  生产日期(ASCII 码)
                len = api.product_info_get(PRODUCT_INFO(manufacture_date), p_data);
                memcpy(p_data, p_data + 1, len - 1);
                p_data += len - 1;
                break;
            case ITEM2(C4_PROTOCOL_VER         ): //@0x0400040D ///  协议版本号(ASCII 码) 
                len = strlen(PROTOCOL_VER);
                len = (len > 16) ? 16 : len;
                memcpy(p_data, PROTOCOL_VER, len), p_data += len;
                if(len < 16) {len = 16 - len, memset(p_data, ' ', len), p_data += len;}
                string_reverse(p_data - 16, 16);
                break;
            case ITEM2(C4_CUSTOMER_CODE        ): //@0x0400040E ///  客户编号 BCD
                len = api.product_info_get(PRODUCT_INFO(user_code), p_data);
                memcpy(p_data, p_data + 1, len - 1);
                p_data += len - 1;
                break;

            case ITEM2(C4_METER_STATUS_FF      ): //@0x040005FF ///  电表状态数据块
                no_bkl = false;
                break;
            case ITEM2(C4_METER_STATUS_1       ): //@0x04000501 ///  电表状态1
                tmpu32 = mstatus.get_645status(1);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_2       ): //@0x04000502 ///  电表状态2
                tmpu32 = mstatus.get_645status(2);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_3       ): //@0x04000503 ///  电表状态3
                tmpu32 = mstatus.get_645status(3);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_4       ): //@0x04000504 ///  电表状态4
                tmpu32 = mstatus.get_645status(4);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_5       ): //@0x04000505 ///  电表状态5
                tmpu32 = mstatus.get_645status(5);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_6       ): //@0x04000506 ///  电表状态6
                tmpu32 = mstatus.get_645status(6);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                if(no_bkl) break;
            case ITEM2(C4_METER_STATUS_7       ): //@0x04000507 ///  电表状态7
                tmpu32 = mstatus.get_645status(7);
                set_lsbdata16(p_data, tmpu32), p_data += 2;
                break;

            case ITEM2(C4_kWh_COMB_TYPE        ): //@0x04000601 ///  有功组合方式
                *p_data++ = energy.para_get()->comb_kWh_code;
                break;
            case ITEM2(C4_kvar_COMB1_TYPE      ): //@0x04000602 ///  无功组合1方式 
                *p_data++ = energy.para_get()->comb1_kvarh_code;
                break;
            case ITEM2(C4_kvar_COMB2_TYPE      ): //@0x04000603 ///  无功组合2方式
                *p_data++ = energy.para_get()->comb2_kvarh_code;
                break;
            case ITEM2(C4_IR_38K               ): //@0x04000701 ///  远红外通讯速率特征字
                #if defined(COM_IR)
                *p_data++ = local_port.baud_rate_get_645(PHY_CHN_1);      
                #endif    
                break;
            case ITEM2(C4_IR                   ): //@0x04000702 ///  近红外通讯速率特征字
                break;
            case ITEM2(C4_COM_1                ): //@0x04000703 ///  通讯口1通讯速率特征字
                #if defined(COM_MODULE)
                *p_data++ = local_port.baud_rate_get_645(PHY_CHN_3);      
                #endif 
                break;
            case ITEM2(C4_COM_2                ): //@0x04000704 ///  通讯口2通讯速率特征字
                #if defined(COM_RS4851)
                *p_data++ = local_port.baud_rate_get_645(PHY_CHN_4);     
                #endif 
                break;
            case ITEM2(C4_COM_3                ): //@0x04000705 ///  通讯口3通讯速率特征字
                #if defined(COM_RS4852)
                *p_data++ = local_port.baud_rate_get_645(PHY_CHN_5);     
                #endif 
                break;
            case ITEM2(C4_WEEKEND_STATUS       ): //@0x04000801 ///  周休日特征字
                {
                    week_list_s list;
                    tariff.week_profile_table_get(&list, 0);
                    *p_data++ = list.week_character;
                }
                break;
            case ITEM2(C4_WEEKEND_SCH_TAB_NO   ): //@0x04000802 ///  周休日采用的日时段表序号
                {
                    week_list_s list;
                    tariff.week_profile_table_get(&list, 0);
                    *p_data++ = list.day_id;
                }
                break;
            case ITEM2(C4_LOAD_PROFILE_MODE    ): //@0x04000901 ///  负荷记录模式字
                {
                    *p_data++ = loadcurve.para_get()->mode.value;
                }
                break;
            case ITEM2(C4_FIXED_TIME_FRZ_MODE  ): //@0x04000902 ///  定时冻结数据模式字
                break;
            case ITEM2(C4_INS_FRZ_MODE         ): //@0x04000903 ///  瞬时冻结数据模式字
                break;
            case ITEM2(C4_CONTRACT_FRZ_MODE    ): //@0x04000904 ///  约定冻结数据模式字
                break;
            case ITEM2(C4_HOUR_FRZ_MODE        ): //@0x04000905 ///  整点冻结数据模式字
                break;
            case ITEM2(C4_DAY_FRZ_MODE         ): //@0x04000906 ///  日  冻结数据模式字
                break;
            case ITEM2(C4_LOAD_PROFILE_START   ): //@0x04000A01 ///  负荷记录起始时间
                {
                    p_data += mclock.format_to645(p_data, &loadcurve.para_get()->start_time, CLOCK_MDhm);
                }
                break;
            case ITEM2(C4_LOAD_PROFILE1_PERIOD ): //@0x04000A02 ///  第1类负荷记录周期
        #if LC2_ENABLE
            case ITEM2(C4_LOAD_PROFILE2_PERIOD ): //@0x04000A03 ///  第2类负荷记录周期
        #endif
        #if LC3_ENABLE
            case ITEM2(C4_LOAD_PROFILE3_PERIOD ): //@0x04000A04 ///  第3类负荷记录周期
        #endif
        #if LC4_ENABLE
            case ITEM2(C4_LOAD_PROFILE4_PERIOD ): //@0x04000A05 ///  第4类负荷记录周期
        #endif
        #if LC5_ENABLE
            case ITEM2(C4_LOAD_PROFILE5_PERIOD ): //@0x04000A06 ///  第5类负荷记录周期
        #endif
        #if LC6_ENABLE
            case ITEM2(C4_LOAD_PROFILE6_PERIOD ): //@0x04000A07 ///  第6类负荷记录周期
        #endif
                {
                    index = (uint8_t)(p_info->id - C4_LOAD_PROFILE1_PERIOD);
                    tmpu32 =  loadcurve.para_get()->period[index] / 60; // 单位：分钟
                    uint32_to_lsbbcd(p_data, tmpu32, 2), p_data += 2;
                }
                break;

            case ITEM2(C4_BL1_DATE             ): //@0x04000B01 ///  每月第1结算日
                {
                    uint8_t mon_day[2];
                    memcpy(mon_day, billing.para_get()->month_billing_time[0], 2);
                    hex_to_msbbcd(p_data, mon_day, 2), p_data += 2;
                }
                break;
            case ITEM2(C4_BL2_DATE             ): //@0x04000B02 ///  每月第2结算日
                {
                    uint8_t mon_day[2];
                    memcpy(mon_day, billing.para_get()->month_billing_time[1], 2);
                    hex_to_msbbcd(p_data, mon_day, 2), p_data += 2;
                }
                break;
            case ITEM2(C4_BL3_DATE             ): //@0x04000B03 ///  每月第3结算日
                {
                    uint8_t mon_day[2];
                    memcpy(mon_day, billing.para_get()->month_billing_time[2], 2);
                    hex_to_msbbcd(p_data, mon_day, 2), p_data += 2;
                }
                break;
            case ITEM2(C4_PASSWORD1            ): //@0x04000C01 ///  0级密码
                break;
            case ITEM2(C4_PASSWORD2            ): //@0x04000C02 ///  1级密码
                break;
            case ITEM2(C4_PASSWORD3            ): //@0x04000C03 ///  2级密码
                break;
            case ITEM2(C4_PASSWORD4            ): //@0x04000C04 ///  3级密码
                break;
            case ITEM2(C4_PASSWORD5            ): //@0x04000C05 ///  4级密码
                break;
            case ITEM2(C4_PASSWORD6            ): //@0x04000C06 ///  5级密码
                break;
            case ITEM2(C4_PASSWORD7            ): //@0x04000C07 ///  6级密码
                break;
            case ITEM2(C4_PASSWORD8            ): //@0x04000C08 ///  7级密码
                break;
            case ITEM2(C4_PASSWORD9            ): //@0x04000C09 ///  8级密码
                break;
            case ITEM2(C4_PASSWORDA            ): //@0x04000C0A ///  9级密码
                break;
        #if SW_PAYMENT_EN
            case ITEM2(C4_LOW_kWh_ALARM1_VALUE ): //@0x04000F01 ///  低电量报警值1
                uint32_to_lsbbcd(p_data, pay.para_get()->warning_threshold1.data, 4), p_data += 4;
                break;
            case ITEM2(C4_LOW_kWh_ALARM2_VALUE ): //@0x04000F02 ///  低电量报警值2
                uint32_to_lsbbcd(p_data, pay.para_get()->warning_threshold2.data, 4), p_data += 4;
                break;
            case ITEM2(C4_STORE_kWh_MAX        ): //@0x04000F03 ///  囤积电量限值
                uint32_to_lsbbcd(p_data, pay.para_get()->max_credit, 4), p_data += 4;
                break;
            case ITEM2(C4_OVERTRAFT_MAX        ): //@0x04000F04 ///  透支电量限值
                uint32_to_lsbbcd(p_data, pay.para_get()->preset_credit_amount.data, 4), p_data += 4;
                break;
            case ITEM2(C4_LOW_MONE_ALARM1_VALUE): //@0x04001001 ///  低金额报警值1
                uint32_to_lsbbcd(p_data, pay.para_get()->warning_threshold1.data, 4), p_data += 4;
                break;
            case ITEM2(C4_LOW_MONE_ALARM2_VALUE): //@0x04001002 ///  低金额报警值2
                uint32_to_lsbbcd(p_data, pay.para_get()->warning_threshold2.data, 4), p_data += 4;
                break;
            case ITEM2(C4_OVERTRAFT_MONEY_MAX  ): //@0x04001003 ///  透支金额限值
                uint32_to_lsbbcd(p_data, pay.para_get()->preset_credit_amount.data, 4), p_data += 4;
                break;
            case ITEM2(C4_STORE_MONE_MAX       ): //@0x04001004 ///  囤积金额限值
                uint32_to_lsbbcd(p_data, pay.para_get()->max_credit, 4), p_data += 4;
                break;
            case ITEM2(C4_CONNECT_THRESHOLD  ):  //@0x04001005 ///  合闸允许电量阈值
                uint32_to_lsbbcd(p_data, pay.para_get()->connect_threshold.data, 4), p_data += 4;
                break;
        #endif
            case ITEM2(C4_hour_FRZ_START_TIME  ): //@0x04001201 ///  整点冻结开始时间

                break;
            case ITEM2(C4_hour_FRZ_END_TIME    ): //@0x04001202 ///  整点冻结结束时间
                break;
            case ITEM2(C4_day_FRZ_TIME         ): //@0x04001203 ///  日冻结开始时间
                {
                    uint8_t mon_day[2];
                    memcpy(mon_day, billing.para_get()->daily_frozen_time, 2);
                    hex_to_msbbcd(p_data, mon_day, 2), p_data += 2;
                }
                break;
            case ITEM2(C4_GPRS_SIGNAL_STRENGTH ): //@0x04001301 ///  信号强度

                break;
            case ITEM2(C4_RELAY_OFF_DELAY      ): //@0x04001302 ///  继电器跳闸延时 跳闸告警时间

                break;
        }
    }
    else
    {
        item = ITEM(p_info->id);
        index = (uint8_t)p_info->id;
        DBG_PRINTF(P_645, D, "ITEM: %x:", item);
        switch(item)
        {
            case ITEM(C4_ZONE1_TABLE          ): //@0x04010000 ///  第1套时区表数据 起始日期及日时段表号
            // case ITEM(C4_ZONE1_SCH1_TAB       ): //@0x04010001 ///  第1套时区第1日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH2_TAB       ): //@0x04010002 ///  第1套时区第2日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH3_TAB       ): //@0x04010003 ///  第1套时区第3日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH4_TAB       ): //@0x04010004 ///  第1套时区第4日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH5_TAB       ): //@0x04010005 ///  第1套时区第5日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH6_TAB       ): //@0x04010006 ///  第1套时区第6日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH7_TAB       ): //@0x04010007 ///  第1套时区第7日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH8_TAB       ): //@0x04010008 ///  第1套时区第8日时段表  起始时间及费率号
                {
                    if(index == 0)
                    {
                        zone_list_s list; 
                        tariff.zone_profile_get(&list, 0); 
                        for(int i = 0; i < TARIFF_ZONE_NUM; i++)
                        {
                            hex_to_msbbcd(p_data, (uint8_t *)&list.zone[i], 3), p_data += 3;
                        }
                    }
                    else if(index <= TARIFF_SCH_TAB_NUM)
                    {
                        day_list_s list;
                        tariff.day_profile_table_get(&list, 0);
                        for(int i = 0; i < TARIFF_SCH_TAB_NUM; i++)
                        {
                            hex_to_msbbcd(p_data, (uint8_t *)&list.day[index - 1].action[i], 3), p_data += 3;
                        }
                    }
                }
                break;
            case ITEM(C4_ZONE2_TABLE          ): //@0x04020000 ///  第2套时区表数据 起始日期及日时段表号
            // case ITEM(C4_ZONE2_SCH1_TAB       ): //@0x04020001 ///  第2套时区第1日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH2_TAB       ): //@0x04020002 ///  第2套时区第2日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH3_TAB       ): //@0x04020003 ///  第2套时区第3日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH4_TAB       ): //@0x04020004 ///  第2套时区第4日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH5_TAB       ): //@0x04020005 ///  第2套时区第5日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH6_TAB       ): //@0x04020006 ///  第2套时区第6日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH7_TAB       ): //@0x04020007 ///  第2套时区第7日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH8_TAB       ): //@0x04020008 ///  第2套时区第8日时段表  起始时间及费率号
                {
                    if(index == 0)
                    {
                        zone_list_s list; 
                        tariff.zone_profile_get(&list, 1); 
                        for(int i = 0; i < TARIFF_ZONE_NUM; i++)
                        {
                            hex_to_msbbcd(p_data, (uint8_t *)&list.zone[i], 3), p_data += 3;
                        }
                    }
                    else if(index <= TARIFF_SCH_TAB_NUM)
                    {
                        day_list_s list;
                        tariff.day_profile_table_get(&list, 1);
                        for(int i = 0; i < TARIFF_SCH_TAB_NUM; i++)
                        {
                            hex_to_msbbcd(p_data, (uint8_t *)&list.day[index - 1].action[i], 3), p_data += 3;
                        }
                    }
                }
                break;
            case ITEM(C4_HOLIDAY_TABLE(1)     ): //@0x04030000 ///  第 N 公共假日日期及日时段表号 1-50
                if(index > 0 && index <= TARIFF_HOLIDAY_NUM)
                {
                    special_day_list_s list;
                    tariff.special_day_get(&list, 0);
                    hex_to_msbbcd(p_data, (uint8_t *)&list.entry[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_AUTO_DISP_ITEM(1)    ): //@0x04040100 ///  自动循环显示第 N 屏显示数据项(是否分屏) 1- 254
                {
                    if(index > 0 && index <= 254)
                    {
                        uint32_t screen_id;
                        uint8_t  num;
                        num = display.id_get(DISP_LIST1, index - 1, &screen_id);
                        memcpy(p_data, &screen_id, 4), p_data += 4;
                        *p_data = num, p_data += 1;
                    }
                }
                break;
            case ITEM(C4_KEY_DISP_ITEM(1)     ): //@0x04040200 ///  按键循环显示第 N 屏显示数据项(是否分屏) 1- 254
                {
                    if(index > 0 && index <= 254)
                    {
                        uint32_t screen_id;
                        uint8_t  num;
                        num = display.id_get(DISP_LIST2, index - 1, &screen_id);
                        memcpy(p_data, &screen_id, 4), p_data += 4;
                        *p_data = num, p_data += 1;
                    }
                }
                break;
            case ITEM(C4_TARIFF_TAB1_PRICE(1) ): //@0x04050100 ///  第1套费率电价 N 1-12
                if(index > 0 && index <= TARIFF_RATE_NUM)
                {
                    tariff_tab_s *ptr = (tariff_tab_s *)step_tariff.active_tf_get();
                    uint32_to_lsbbcd(p_data, ptr->price[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_TARIFF_TAB2_PRICE(1) ): //@0x04050200 ///  第2套费率电价 N 1-12
                if(index > 0 && index <= TARIFF_RATE_NUM)
                {
                    tariff_tab_s *ptr = (tariff_tab_s *)step_tariff.passive_tf_get();
                    uint32_to_lsbbcd(p_data, ptr->price[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_STEP_TAB1_VALUE(1)   ): //@0x04060000 ///  第1套阶梯值   N 1-7
                if(index > 0 && index <= STEP_TARIFF_NUM)
                {
                    step_tab_s *step_tab = (step_tab_s *)step_tariff.active_step_get();
                    uint32_to_lsbbcd(p_data, step_tab->value[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_STEP_TAB1_PRICE(1)   ): //@0x04060100 ///  第1套阶梯电价 N 1-7
                if(index > 0 && index <= STEP_TARIFF_NUM)
                {
                    index = (uint8_t)p_info->id;
                    step_tab_s *step_tab = (step_tab_s *)step_tariff.active_step_get();
                    uint32_to_lsbbcd(p_data, step_tab->price[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_STEP_TAB2_VALUE(1)   ): //@0x04060200 ///  第2套阶梯值   N 1-7
                if(index > 0 && index <= STEP_TARIFF_NUM)
                {
                    step_tab_s *step_tab = (step_tab_s *)step_tariff.passive_step_get();
                    uint32_to_lsbbcd(p_data, step_tab->value[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_STEP_TAB2_PRICE(1)   ): //@0x04060300 ///  第2套阶梯电价 N 1-7
                if(index > 0 && index <= STEP_TARIFF_NUM)
                {
                    index = (uint8_t)p_info->id;
                    step_tab_s *step_tab = (step_tab_s *)step_tariff.passive_step_get();
                    uint32_to_lsbbcd(p_data, step_tab->price[index - 1], 4), p_data += 4;
                }
                break;
            case ITEM(C4_LOSS_VOL_LIMIT_MAX   ): //@0x04090101 ///  失压事件电压触发上限
            // case ITEM(C4_LOSS_VOL_LIMIT_MIN   ): //@0x04090102 ///  失压事件电压恢复下限
            // case ITEM(C4_LOSS_VOL_LIMIT_CUR   ): //@0x04090103 ///  失压事件电流触发下限
            // case ITEM(C4_LOSS_VOL_LIMIT_time  ): //@0x04090104 ///  失压事件判定延时时间
            #if EVENT_LOSS_VOL_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->vol_loss_thd_v_l;  len = 2;
                            break;
                        case 3:
                            value = power_event.para_get()->vol_loss_thd_i;    len = 3;
                            break;
                        case 4:
                            value = power_event.para_get()->vol_loss_thd_time; len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_LOW_VOL_LIMIT_MAX    ): //@0x04090201 ///  欠压事件电压触发上限
            // case ITEM(C4_LOW_VOL_LIMIT_time   ): //@0x04090202 ///  欠压事件判定延时时间
            #if EVENT_LOW_VOL_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->vol_low_thd_v;     len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->vol_low_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_OVR_VOL_LIMIT_MAX    ): //@0x04090301 ///  过压事件电压触发上限
            // case ITEM(C4_OVR_VOL_LIMIT_time   ): //@0x04090301 ///  过压事件判定延时时间
            #if EVENT_OVR_VOL_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->vol_ovr_thd_v;     len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->vol_ovr_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_MISS_PH_LIMIT_VOL    ): //@0x04090401 ///  断相事件电压触发上限
            // case ITEM(C4_MISS_PH_LIMIT_CUR    ): //@0x04090402 ///  断相事件电流触发上限
            // case ITEM(C4_MISS_PH_LIMIT_TIME   ): //@0x04090403 ///  断相事件判定延时时间
            #if EVENT_MISS_VOL_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->vol_miss_thd_v;    len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->vol_miss_thd_i;    len = 3;
                            break;
                        case 3:
                            value = power_event.para_get()->vol_miss_thd_time;len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_VOL_UNB_PER          ): //@0x04090501 ///  电压不平衡率限值
            // case ITEM(C4_VOL_UNB_TIME         ): //@0x04090502 ///  电压不平衡率判定延时时间
            #if EVENT_V_REV_SQR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->vol_unb_thd;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->vol_unb_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_CUR_UNB_PER          ): //@0x04090601 ///  电流不平衡率限值
            // case ITEM(C4_CUR_UNB_TIME         ): //@0x04090601 ///  电流不平衡率判定延时时间
            #if EVENT_I_REV_SQR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->i_unb_thd;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->i_unb_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_LOS_CUR_V            ): //@0x04090701 ///  失流事件电压触发下限
            // case ITEM(C4_LOS_CUR_I_MAX        ): //@0x04090701 ///  失流事件电流触发上限
            // case ITEM(C4_LOS_CUR_I_MIN        ): //@0x04090701 ///  失流事件电流触发下限
            // case ITEM(C4_LOS_CUR_TIME         ): //@0x04090701 ///  失流事件判定延时时间
            #if EVENT_LOS_CUR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->i_loss_thd_v;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->i_loss_thd_i_h;  len = 3;
                            break;
                        case 3:
                            value = power_event.para_get()->i_loss_thd_i_l;    len = 3;
                            break;
                        case 4:
                            value = power_event.para_get()->i_loss_thd_time; len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_OVR_CUR_I            ): //@0x04090801 ///  过流事件电流触发下限
            // case ITEM(C4_OVR_CUR_TIME         ): //@0x04090801 ///  过流事件判定延时时间
            #if EVENT_OVR_CUR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->i_ovr_thd_i;  len = 3;
                            break;
                        case 2:
                            value = power_event.para_get()->i_ovr_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_MISS_CUR_V           ): //@0x04090901 ///  断流事件电压触发下限
            // case ITEM(C4_MISS_CUR_V           ): //@0x04090901 ///  断流事件电流触发上限
            // case ITEM(C4_MISS_CUR_TIME        ): //@0x04090901 ///  断流事件判定延时时间
            #if EVENT_MISS_CUR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->i_miss_thd_v;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->i_miss_thd_i;  len = 3;
                            break;
                        case 3:
                            value = power_event.para_get()->i_miss_thd_time;    len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_REV_POWER            ): //@0x04090A01 ///  潮流反向事件有功功率触发下限
            // case ITEM(C4_REV_TIME             ): //@0x04090A01 ///  潮流反向事件判定延时时间
            #if EVENT_REV_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->p_rev_thd_p;  len = 3;
                            break;
                        case 2:
                            value = power_event.para_get()->p_rev_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_OVER_LOAD_POWER      ): //@0x04090B01 ///  过载事件有功功率触发下限
            // case ITEM(C4_OVER_LOAD_TIME       ): //@0x04090B01 ///  过载事件判定延时时间
            #if EVENT_OVR_LOAD_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->p_ovr_thd_p;  len = 3;
                            break;
                        case 2:
                            value = power_event.para_get()->p_ovr_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_VOL__MAX             ): //@0x04090C01 ///  电压考核上限
            // case ITEM(C4_VOL__MIN             ): //@0x04090C01 ///  电压考核下限
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 2;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
            case ITEM(C4_OVR_MD_kW_THD        ): //@0x04090D01 ///  有功需量超限事件需量触发下限
            // case ITEM(C4_OVR_MD_kvar_THD      ): //@0x04090D01 ///  无功需量超限事件需量触发下限
            // case ITEM(C4_OVR_MD_TIME          ): //@0x04090D01 ///  需量超限事件判定延时时间
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 2;
                //             break;
                //         case 3:
                //             value = power_event.para_get()->vol_loss_thd_i;    len = 1;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
            case ITEM(C4_LOW_PF_THD           ): //@0x04090E01 ///  总功率因数超下限阀值
            // case ITEM(C4_LOW_PF_THD           ): //@0x04090E01 ///  总功率因数超下限判定延时时间
            #if EVENT_LOW_PF_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = power_event.para_get()->pf_low_thd_pf;  len = 2;
                            break;
                        case 2:
                            value = power_event.para_get()->pf_low_thd_time;  len = 1;
                            break;
                    }
                    uint32_to_lsbbcd(p_data, value, len), p_data += len;
                }
            #endif
                break;
            case ITEM(C4_CUR_UNB2_THD         ): //@0x04090F01 ///  电流严重不平衡限值
            // case ITEM(C4_CUR_UNB2_TIME        ): //@0x04090F01 ///  电流严重不平衡触发延时时间
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 1;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
        }
    }

    if(buff == NULL)
    {
        if((p_data - p_info->snd_dat) == 4) 
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
            return 1;
        } // 无数据
        return (uint16_t)(p_data - p_info->snd_dat);
    }
    return (uint16_t)(p_data - buff);
}

/// @brief 
/// @param p_info 
/// @param buff 
/// @return 返回错误码 
static uint8_t dlt_645_write_4(DLT645_2007_MSG_S *p_info, uint8_t *data, uint32_t opt_code, uint8_t psw)
{
    uint8_t *p_data = data;
    uint32_t tmpu32;
    uint16_t item, len;
    uint8_t index;
    clock_s clock_tmp;
    uint8_t no_bkl = true;

    DBG_PRINTF(P_645, D, "\r\n dlt_645_write_4--");

    if((p_info->id&0xFFFF0000) == 0x04000000) ///
    {
        item = ITEM2(p_info->id);
        DBG_PRINTF(P_645, D, "ITEM2: %x:", item);
        switch(item)
        {
            case ITEM2(C4_DATE_WEEK            ): //@0x04000101 ///  日期星期             年月日星期
            {
                clock_tmp = *mclock.datetime;
                bcd_to_msbhex(&clock_tmp.year, p_data, 4);
                clock_tmp.stus.value = 0;
                return mclock.sync_time_set(&clock_tmp) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_TIME                 ): //@0x04000102 ///  时间                 时分秒    
            {
                clock_tmp = *mclock.datetime;
                bcd_to_msbhex(&clock_tmp.hour, p_data, 3);
                clock_tmp.stus.value = 0;
                return mclock.sync_time_set(&clock_tmp) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_MD_PERIOD            ): //@0x04000103 ///  最大需量周期         分  
            {
                tmpu32 = lsbbcd_to_hex32(p_data, 1);
                return demand.para_set(member_offset(demand_para_s, period), &tmpu32) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_MD_SLIP_PERIOD       ): //@0x04000104 ///  需量滑差时间         分 
            {
                tmpu32 = lsbbcd_to_hex32(p_data, 1);
                return demand.para_set(member_offset(demand_para_s, slip_period), &tmpu32) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_PLUSE_WIDTH          ): //@0x04000105 ///  校表脉冲宽度         毫秒 
                break;
            case ITEM2(C4_ZONE_SWITCH_TIME     ): //@0x04000106 ///  两套时区表切换时间   年月日时分 
            {
                bcd_to_msbhex(&clock_tmp.hour, p_data, 2), p_data += 2; 
                bcd_to_msbhex(&clock_tmp.year, p_data, 3), p_data += 3; 
                return tariff.zone_activate_passive_time_set(&clock_tmp, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_SCHEDULE_SWITCH_TIME ): //@0x04000107 ///  两套日时段表切换时间 年月日时分 
            {
                bcd_to_msbhex(&clock_tmp.hour, p_data, 2), p_data += 2; 
                bcd_to_msbhex(&clock_tmp.year, p_data, 3), p_data += 3; 
                return tariff.day_activate_passive_time_set(&clock_tmp, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_TARIFF_SWITCH_TIME   ): //@0x04000108 ///  两套费率电价切换时间 年月日时分 
            {
                bcd_to_msbhex(&clock_tmp.hour, p_data, 2), p_data += 2; 
                bcd_to_msbhex(&clock_tmp.year, p_data, 3), p_data += 3; 
                return step_tariff.passive_tf_active_time_set(&clock_tmp) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_STEP_SWITCH_TIME     ): //@0x04000109 ///  两套阶梯度切换时间   年月日时分
            {
                bcd_to_msbhex(&clock_tmp.hour, p_data, 2), p_data += 2; 
                bcd_to_msbhex(&clock_tmp.year, p_data, 3), p_data += 3; 
                return step_tariff.passive_step_active_time_set(&clock_tmp) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_ZONE_NUM             ): //@0x04000201 ///  年时区数  
            { 
                zone_list_s zone_list;
                tariff.zone_profile_get(&zone_list, 0);
                zone_list.zone_num = lsbbcd_to_hex32(p_data, 1);
                return tariff.zone_profile_set(&zone_list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_SCHEDULE_TAB_NUM     ): //@0x04000202 ///  日时段表数  
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                day_list.day_num = lsbbcd_to_hex32(p_data, 1);
                return tariff.day_profile_table_set(&day_list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_SCHEDULE_NUM         ): //@0x04000203 ///  日时段数    
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                day_list.day[0].schedule_num = lsbbcd_to_hex32(p_data, 1);
                return tariff.day_profile_table_set(&day_list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_TARIFF_NUM           ): //@0x04000204 ///  费率数       
            {
                day_list_s day_list;
                tariff.day_profile_table_get(&day_list, 0);
                day_list.tf_num = lsbbcd_to_hex32(p_data, 1);
                return tariff.day_profile_table_set(&day_list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_HOLIDAY_NUM          ): //@0x04000205 ///  公共假日数    
            {
                special_day_list_s special_day_list;
                tariff.special_day_get(&special_day_list, 0);
                special_day_list.entry_num = lsbbcd_to_hex32(p_data, 2);
                return tariff.special_day_set(&special_day_list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_HARMONIC_NUM         ): //@0x04000206 ///  谐波分析次数      

                break;
            case ITEM2(C4_STEP_NUM             ): //@0x04000207 ///  梯度数
            // {
            //     const step_tab_s *step_tab = step_tariff.active_step_get();
            //     uint32_to_lsbbcd(p_data, step_tab->step_num, 1), p_data += 1;
            // }
                break;
            case ITEM2(C4_DISP_ITEM_NUM        ): //@0x04000301 ///  自动循环显示屏数          NN
                // tmpu32 = display.id_list_get_num(DISP_LIST1);
                // uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_DISP_TIME            ): //@0x04000302 ///  每屏显示时间              NN
                // tmpu32 = display.para_get()->time.auto_Period;
                // uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_ENG_DISP_DECI_NUM    ): //@0x04000303 ///  显示电能小数位数          NN
                // tmpu32 = display.para_get()->spe.spe_format.energy_decimal;
                // uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_POWER_DISP_DECI_NUM  ): //@0x04000304 ///  显示功率(最大需量)小数位数 NN
                // tmpu32 = display.para_get()->spe.spe_format.power_decimal;
                // uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_KEY_DISP_ITEM_NUM    ): //@0x04000305 ///  按键循环显示屏数           NN
                // tmpu32 = display.id_list_get_num(DISP_LIST2);
                // uint32_to_lsbbcd(p_data, tmpu32, 1), p_data += 1;
                break;
            case ITEM2(C4_CT                   ): //@0x04000306 ///  电流互感器变比            NNNNNN
                break;
            case ITEM2(C4_PT                   ): //@0x04000307 ///  电流互感器变比             NNNNNN
                break;
            case ITEM2(C4_COMM_ADDR            ): //@0x04000401 ///  通信地址
                // p_data += api.meter_sn_get(p_data);
                break;
            case ITEM2(C4_METER_NO             ): //@0x04000402 ///  表号
            {
                uint8_t buf[1 + METER_SN_LEN];
                *buf = METER_SN_LEN;
                memcpy(buf + 1, p_data, METER_SN_LEN);
                return api.product_info_set(PRODUCT_INFO(meter_sn), buf, 1 + METER_SN_LEN) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_AMC_NO               ): //@0x04000403 ///  资产管理编码(ASCII 码)
            {
                uint8_t buf[1 + ASSET_CODE_LEN];
                *buf = ASSET_CODE_LEN;
                memcpy(buf + 1, p_data, ASSET_CODE_LEN);
                return api.product_info_set(PRODUCT_INFO(asset_code), buf, 1 + ASSET_CODE_LEN) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_BASE_VOLTAGE         ): //@0x04000404 ///  额定电压(ASCII 码)
                // len = strlen(INPUT_VOLTAGE_STR);
                // len = (len > 6) ? 6 : len;
                // memcpy(p_data, INPUT_VOLTAGE_STR, len), p_data += len;
                // if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_BASE_CURRENT         ): //@0x04000405 ///  额定电流/基本电流(ASCII 码)
                // len = strlen(BASE_CURRENT_STR);
                // len = (len > 6) ? 6 : len;
                // memcpy(p_data, BASE_CURRENT_STR, len), p_data += len;
                // if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_MAX_CURRENT          ): //@0x04000406 ///  最大电流(ASCII 码)
                // len = strlen(MAXIMUM_CURRENT_STR);
                // len = (len > 6) ? 6 : len;
                // memcpy(p_data, MAXIMUM_CURRENT_STR, len), p_data += len;
                // if(len < 6) {len = 6 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 6, 6);
                break;
            case ITEM2(C4_ACT_ACCURACY         ): //@0x04000407 ///  有功准确度等级(ASCII 码)
                // len = strlen(ACT_ACCURACY_LEVEL_STR);
                // len = (len > 4) ? 4 : len;
                // memcpy(p_data, ACT_ACCURACY_LEVEL_STR, len), p_data += len;
                // if(len < 4) {len = 4 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 4, 4);
                break;
            case ITEM2(C4_REA_ACCURACY         ): //@0x04000408 ///  无功准确度等级(ASCII 码)
                // len = strlen(REACT_ACCURACY_LEVEL_STR);
                // len = (len > 4) ? 4 : len;
                // memcpy(p_data, REACT_ACCURACY_LEVEL_STR, len), p_data += len;
                // if(len < 4) {len = 4 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 4, 4);
                break;
            case ITEM2(C4_ACT_CONST            ): //@0x04000409 ///  电表有功常数
                // uint32_to_lsbbcd(p_data, METER_CONST, 3), p_data += 3;
                break;
            case ITEM2(C4_REA_CONST            ): //@0x0400040A ///  电表无功常数
                // uint32_to_lsbbcd(p_data, METER_CONST, 3), p_data += 3;
                break;
            case ITEM2(C4_METER_TYPE           ): //@0x0400040B ///  电表型号(ASCII 码)
                // len = strlen(HDW_VERSION);
                // len = (len > 10) ? 10 : len;
                // memcpy(p_data, HDW_VERSION, len), p_data += len;
                // if(len < 10) {len = 10 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 10, 10);
                break;
            case ITEM2(C4_PRODUCT_DATE         ): //@0x0400040C ///  生产日期(ASCII 码)
            {
                uint8_t buf[1 + METER_DATE_LEN];
                *buf = METER_DATE_LEN;
                memcpy(buf + 1, p_data, METER_DATE_LEN);
                return api.product_info_set(PRODUCT_INFO(manufacture_date), buf, 1 + METER_DATE_LEN) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_PROTOCOL_VER         ): //@0x0400040D ///  协议版本号(ASCII 码) 
                // len = strlen(PROTOCOL_VER);
                // len = (len > 16) ? 16 : len;
                // memcpy(p_data, PROTOCOL_VER, len), p_data += len;
                // if(len < 16) {len = 16 - len, memset(p_data, ' ', len), p_data += len;}
                // string_reverse(p_data - 16, 16);
                break;
            case ITEM2(C4_CUSTOMER_CODE        ): //@0x0400040E ///  客户编号 BCD
            {
                uint8_t buf[1 + USER_CODE_LEN];
                *buf = USER_CODE_LEN;
                memcpy(buf + 1, p_data, USER_CODE_LEN);
                return api.product_info_set(PRODUCT_INFO(user_code), buf, 1 + USER_CODE_LEN) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_METER_STATUS_FF      ): //@0x040005FF ///  电表状态数据块
                break;
            case ITEM2(C4_METER_STATUS_1       ): //@0x04000501 ///  电表状态1
                break;
            case ITEM2(C4_METER_STATUS_2       ): //@0x04000502 ///  电表状态2
                break;
            case ITEM2(C4_METER_STATUS_3       ): //@0x04000503 ///  电表状态3
                break;
            case ITEM2(C4_METER_STATUS_4       ): //@0x04000504 ///  电表状态4
                break;
            case ITEM2(C4_METER_STATUS_5       ): //@0x04000505 ///  电表状态5
                break;
            case ITEM2(C4_METER_STATUS_6       ): //@0x04000506 ///  电表状态6
                break;
            case ITEM2(C4_METER_STATUS_7       ): //@0x04000507 ///  电表状态7
                break;

            case ITEM2(C4_kWh_COMB_TYPE        ): //@0x04000601 ///  有功组合方式
            {
                return energy.para_set(member_offset(energy_para_s, comb_kWh_code), p_data, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_kvar_COMB1_TYPE      ): //@0x04000602 ///  无功组合1方式 
            {
                return energy.para_set(member_offset(energy_para_s, comb1_kvarh_code), p_data, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_kvar_COMB2_TYPE      ): //@0x04000603 ///  无功组合2方式
            {
                return energy.para_set(member_offset(energy_para_s, comb2_kvarh_code), p_data, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_IR_38K               ): //@0x04000701 ///  远红外通讯速率特征字
                #if defined(COM_IR)
                return local_port.baud_rate_set_645(PHY_CHN_1, *p_data);
                #else
                break;
                #endif   
            case ITEM2(C4_IR                   ): //@0x04000702 ///  近红外通讯速率特征字
                break;
            case ITEM2(C4_COM_1                ): //@0x04000703 ///  通讯口1通讯速率特征字
                #if defined(COM_MODULE)
                return local_port.baud_rate_set_645(PHY_CHN_3, *p_data); 
                #else
                break;
                #endif
            case ITEM2(C4_COM_2                ): //@0x04000704 ///  通讯口2通讯速率特征字
                #if defined(COM_RS4851)
                return local_port.baud_rate_set_645(PHY_CHN_4, *p_data);    
                #else
                break;
                #endif 
            case ITEM2(C4_COM_3                ): //@0x04000705 ///  通讯口3通讯速率特征字
                #if defined(COM_RS4852)
                return local_port.baud_rate_set_645(PHY_CHN_5, *p_data);   
                #else
                break;
                #endif
            case ITEM2(C4_WEEKEND_STATUS       ): //@0x04000801 ///  周休日特征字
            {
                week_list_s list;
                tariff.week_profile_table_get(&list, 0);
                list.week_character = *p_data;
                return tariff.week_profile_table_set(&list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            } 
            case ITEM2(C4_WEEKEND_SCH_TAB_NO   ): //@0x04000802 ///  周休日采用的日时段表序号
            {
                week_list_s list;
                tariff.week_profile_table_get(&list, 0);
                list.day_id = *p_data;
                return tariff.week_profile_table_set(&list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            case ITEM2(C4_LOAD_PROFILE_MODE    ): //@0x04000901 ///  负荷记录模式字
                {
                    tmpu32 = lsbbcd_to_hex32(p_data, 1);
                    return loadcurve.para_set(member_offset(lc_para_s, mode), &tmpu32, member_size(lc_para_s, mode)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                break;
            case ITEM2(C4_FIXED_TIME_FRZ_MODE  ): //@0x04000902 ///  定时冻结数据模式字
                break;
            case ITEM2(C4_INS_FRZ_MODE         ): //@0x04000903 ///  瞬时冻结数据模式字
                break;
            case ITEM2(C4_CONTRACT_FRZ_MODE    ): //@0x04000904 ///  约定冻结数据模式字
                break;
            case ITEM2(C4_HOUR_FRZ_MODE        ): //@0x04000905 ///  整点冻结数据模式字
                break;
            case ITEM2(C4_DAY_FRZ_MODE         ): //@0x04000906 ///  日  冻结数据模式字
                break;
            case ITEM2(C4_LOAD_PROFILE_START   ): //@0x04000A01 ///  负荷记录起始时间
                {
                    clock_s clock;
                    mclock.unformat_frm645(p_data, &clock, CLOCK_MDhm);
                    clock.second = 0;  // 秒置零    
                    clock.year   = 24; // 通配 任意有效值，使用时通配
                    return loadcurve.para_set(member_offset(lc_para_s, start_time), &clock, member_size(lc_para_s, start_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                break;
            case ITEM2(C4_LOAD_PROFILE1_PERIOD ): //@0x04000A02 ///  第1类负荷记录周期
        #if LC2_ENABLE
            case ITEM2(C4_LOAD_PROFILE2_PERIOD ): //@0x04000A03 ///  第2类负荷记录周期
        #endif
        #if LC3_ENABLE
            case ITEM2(C4_LOAD_PROFILE3_PERIOD ): //@0x04000A04 ///  第3类负荷记录周期
        #endif
        #if LC4_ENABLE
            case ITEM2(C4_LOAD_PROFILE4_PERIOD ): //@0x04000A05 ///  第4类负荷记录周期
        #endif
        #if LC5_ENABLE
            case ITEM2(C4_LOAD_PROFILE5_PERIOD ): //@0x04000A06 ///  第5类负荷记录周期
        #endif
        #if LC6_ENABLE
            case ITEM2(C4_LOAD_PROFILE6_PERIOD ): //@0x04000A07 ///  第6类负荷记录周期
        #endif
                {
                    index = (uint8_t)(p_info->id - C4_LOAD_PROFILE1_PERIOD);
                    tmpu32 = lsbbcd_to_hex32(p_data, 2) * 60;  // 单位分钟转秒
                    return loadcurve.para_set(member_offset(lc_para_s, period[index]), &tmpu32, member_size(lc_para_s, period[0])) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                break;

            case ITEM2(C4_BL1_DATE             ): //@0x04000B01 ///  每月第1结算日
                {
                    uint8_t mon_day[2];
                    bcd_to_msbhex(mon_day, p_data, 2);
                    return billing.para_set(member_offset(billing_para_s, month_billing_time[0]), mon_day, 2) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            case ITEM2(C4_BL2_DATE             ): //@0x04000B02 ///  每月第2结算日
                {
                    uint8_t mon_day[2];
                    bcd_to_msbhex(mon_day, p_data, 2);
                    return billing.para_set(member_offset(billing_para_s, month_billing_time[1]), mon_day, 2) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                break;
            case ITEM2(C4_BL3_DATE             ): //@0x04000B03 ///  每月第3结算日
                {
                    uint8_t mon_day[2];
                    bcd_to_msbhex(mon_day, p_data, 2);
                    return billing.para_set(member_offset(billing_para_s, month_billing_time[2]), mon_day, 2) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                break;
            case ITEM2(C4_PASSWORD1            ): //@0x04000C01 ///  0级密码
                break;
            case ITEM2(C4_PASSWORD2            ): //@0x04000C02 ///  1级密码
                break;
            case ITEM2(C4_PASSWORD3            ): //@0x04000C03 ///  2级密码
                break;
            case ITEM2(C4_PASSWORD4            ): //@0x04000C04 ///  3级密码
                break;
            case ITEM2(C4_PASSWORD5            ): //@0x04000C05 ///  4级密码
                break;
            case ITEM2(C4_PASSWORD6            ): //@0x04000C06 ///  5级密码
                break;
            case ITEM2(C4_PASSWORD7            ): //@0x04000C07 ///  6级密码
                break;
            case ITEM2(C4_PASSWORD8            ): //@0x04000C08 ///  7级密码
                break;
            case ITEM2(C4_PASSWORD9            ): //@0x04000C09 ///  8级密码
                break;
            case ITEM2(C4_PASSWORDA            ): //@0x04000C0A ///  9级密码
                break;
        #if SW_PAYMENT_EN
            case ITEM2(C4_LOW_kWh_ALARM1_VALUE ): //@0x04000F01 ///  低电量报警值1
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, warning_threshold1.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_LOW_kWh_ALARM2_VALUE ): //@0x04000F02 ///  低电量报警值2
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, warning_threshold2.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_STORE_kWh_MAX        ): //@0x04000F03 ///  囤积电量限值
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, max_credit), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_OVERTRAFT_MAX        ): //@0x04000F04 ///  透支电量限值
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, preset_credit_amount.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_LOW_MONE_ALARM1_VALUE): //@0x04001001 ///  低金额报警值1
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, warning_threshold1.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_LOW_MONE_ALARM2_VALUE): //@0x04001002 ///  低金额报警值2
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, warning_threshold2.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_OVERTRAFT_MONEY_MAX  ): //@0x04001003 ///  透支金额限值
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, preset_credit_amount.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_STORE_MONE_MAX       ): //@0x04001004 ///  囤积金额限值
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, max_credit), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            case ITEM2(C4_CONNECT_THRESHOLD  ):  //@0x04001005 ///  合闸允许电量阈值
                tmpu32 = lsbbcd_to_hex32(p_data, 4);
                return pay.para_set(member_offset(pay_para_s, connect_threshold.data), &tmpu32, 4) ? ERR_CODE_NONE : ERR_CODE_OTHER;
        #endif
            case ITEM2(C4_hour_FRZ_START_TIME  ): //@0x04001201 ///  整点冻结开始时间

                break;
            case ITEM2(C4_hour_FRZ_END_TIME    ): //@0x04001202 ///  整点冻结结束时间
                break;
            case ITEM2(C4_day_FRZ_TIME         ): //@0x04001203 ///  日冻结开始时间
                {
                    uint8_t mon_day[2];
                    bcd_to_msbhex(mon_day, p_data, 2);
                    return billing.para_set(member_offset(billing_para_s, daily_frozen_time), mon_day, 2) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            case ITEM2(C4_GPRS_SIGNAL_STRENGTH ): //@0x04001301 ///  信号强度

                break;
            case ITEM2(C4_RELAY_OFF_DELAY      ): //@0x04001302 ///  继电器跳闸延时 跳闸告警时间

                break;
        }
    }
    else
    {
        item = ITEM(p_info->id);
        index = (uint8_t)p_info->id;
        DBG_PRINTF(P_645, D, "ITEM: %x:", item);
        switch(item)
        {
            case ITEM(C4_ZONE1_TABLE          ): //@0x04010000 ///  第1套时区表数据 起始日期及日时段表号
            // case ITEM(C4_ZONE1_SCH1_TAB       ): //@0x04010001 ///  第1套时区第1日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH2_TAB       ): //@0x04010002 ///  第1套时区第2日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH3_TAB       ): //@0x04010003 ///  第1套时区第3日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH4_TAB       ): //@0x04010004 ///  第1套时区第4日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH5_TAB       ): //@0x04010005 ///  第1套时区第5日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH6_TAB       ): //@0x04010006 ///  第1套时区第6日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH7_TAB       ): //@0x04010007 ///  第1套时区第7日时段表  起始时间及费率号
            // case ITEM(C4_ZONE1_SCH8_TAB       ): //@0x04010008 ///  第1套时区第8日时段表  起始时间及费率号
            {
                if(index == 0)//时区表
                {
                    zone_list_s list; 
                    uint8_t len = p_info->data_len / 3;
                    if(len > TARIFF_ZONE_NUM) return ERR_CODE_ZONE_OVR;
                    for(int i = 0; i < len; i++)
                    {
                        bcd_to_msbhex((uint8_t *)&list.zone[i], p_data, 3), p_data += 3;
                    }
                    return tariff.zone_profile_set(&list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                else if(index <= TARIFF_SCH_TAB_NUM)
                {
                    day_list_s list;
                    uint8_t len = p_info->data_len / 3;
                    if(len > TARIFF_SCH_TAB_NUM) return ERR_CODE_SCH_OVR;
                    for(int i = 0; i < len; i++)
                    {
                        bcd_to_msbhex((uint8_t *)&list.day[index].action[i], p_data, 3), p_data += 3;
                    }
                    return tariff.day_profile_table_set(&list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            }
            break;
            case ITEM(C4_ZONE2_TABLE          ): //@0x04020000 ///  第2套时区表数据 起始日期及日时段表号
            // case ITEM(C4_ZONE2_SCH1_TAB       ): //@0x04020001 ///  第2套时区第1日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH2_TAB       ): //@0x04020002 ///  第2套时区第2日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH3_TAB       ): //@0x04020003 ///  第2套时区第3日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH4_TAB       ): //@0x04020004 ///  第2套时区第4日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH5_TAB       ): //@0x04020005 ///  第2套时区第5日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH6_TAB       ): //@0x04020006 ///  第2套时区第6日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH7_TAB       ): //@0x04020007 ///  第2套时区第7日时段表  起始时间及费率号
            // case ITEM(C4_ZONE2_SCH8_TAB       ): //@0x04020008 ///  第2套时区第8日时段表  起始时间及费率号
            {
                if(index == 0)//时区表
                {
                    zone_list_s list; 
                    uint8_t len = p_info->data_len / 3;
                    if(len > TARIFF_ZONE_NUM) return ERR_CODE_ZONE_OVR;
                    for(int i = 0; i < len; i++)
                    {
                        bcd_to_msbhex((uint8_t *)&list.zone[i], p_data, 3), p_data += 3;
                    }
                    return tariff.zone_profile_set(&list, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
                else if(index <= TARIFF_SCH_TAB_NUM)
                {
                    day_list_s list;
                    uint8_t len = p_info->data_len / 3;
                    if(len > TARIFF_SCH_TAB_NUM) return ERR_CODE_SCH_OVR;
                    for(int i = 0; i < len; i++)
                    {
                        bcd_to_msbhex((uint8_t *)&list.day[index].action[i], p_data, 3), p_data += 3;
                    }
                    return tariff.day_profile_table_set(&list, 1) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            }
            break;
            case ITEM(C4_HOLIDAY_TABLE(1)     ): //@0x04030000 ///  第 N 公共假日日期及日时段表号 1-50
            if(index > 0 && index <= TARIFF_HOLIDAY_NUM)
            {
                special_day_list_s list;
                tariff.special_day_get(&list, 0);
                bcd_to_msbhex((uint8_t *)&list.entry[index - 1], p_data, 4), p_data += 4;
                return tariff.special_day_set(&list, 0) ? ERR_CODE_NONE : ERR_CODE_OTHER;
            }
            break;
            case ITEM(C4_AUTO_DISP_ITEM(1)    ): //@0x04040100 ///  自动循环显示第 N 屏显示数据项(是否分屏) 1- 254
            {
                if(index > 0 && index <= 254)
                {
                    uint32_t screen_id;
                    
                    if(p_info->data_len != 5 && p_info->data_len != 4) return ERR_CODE_OTHER;
                    memcpy(&screen_id, p_data, 4);
                    return display.id_set(DISP_LIST1, index, screen_id) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            }
            break;
            case ITEM(C4_KEY_DISP_ITEM(1)     ): //@0x04040200 ///  按键循环显示第 N 屏显示数据项(是否分屏) 1- 254
            {
                if(index > 0 && index <= 254)
                {
                    uint32_t screen_id;
                    
                    if(p_info->data_len != 5 && p_info->data_len != 4) return ERR_CODE_OTHER;
                    memcpy(&screen_id, p_data, 4);
                    return display.id_set(DISP_LIST2, index, screen_id) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                }
            }
            break;
            case ITEM(C4_TARIFF_TAB1_PRICE(1) ): //@0x04050100 ///  第1套费率电价 N 1-12
                // if(index > 0 && index <= TARIFF_RATE_NUM)
                // {
                //     tariff_tab_s *ptr = (tariff_tab_s *)step_tariff.active_tf_get();
                //     uint32_to_lsbbcd(p_data, ptr->price[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_TARIFF_TAB2_PRICE(1) ): //@0x04050200 ///  第2套费率电价 N 1-12
                // if(index > 0 && index <= TARIFF_RATE_NUM)
                // {
                //     tariff_tab_s *ptr = (tariff_tab_s *)step_tariff.passive_tf_get();
                //     uint32_to_lsbbcd(p_data, ptr->price[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_STEP_TAB1_VALUE(1)   ): //@0x04060000 ///  第1套阶梯值   N 1-7
                // if(index > 0 && index <= STEP_TARIFF_NUM)
                // {
                //     step_tab_s *step_tab = (step_tab_s *)step_tariff.active_step_get();
                //     uint32_to_lsbbcd(p_data, step_tab->value[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_STEP_TAB1_PRICE(1)   ): //@0x04060100 ///  第1套阶梯电价 N 1-7
                // if(index > 0 && index <= STEP_TARIFF_NUM)
                // {
                //     index = (uint8_t)p_info->id;
                //     step_tab_s *step_tab = (step_tab_s *)step_tariff.active_step_get();
                //     uint32_to_lsbbcd(p_data, step_tab->price[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_STEP_TAB2_VALUE(1)   ): //@0x04060200 ///  第2套阶梯值   N 1-7
                // if(index > 0 && index <= STEP_TARIFF_NUM)
                // {
                //     step_tab_s *step_tab = (step_tab_s *)step_tariff.passive_step_get();
                //     uint32_to_lsbbcd(p_data, step_tab->value[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_STEP_TAB2_PRICE(1)   ): //@0x04060300 ///  第2套阶梯电价 N 1-7
                // if(index > 0 && index <= STEP_TARIFF_NUM)
                // {
                //     index = (uint8_t)p_info->id;
                //     step_tab_s *step_tab = (step_tab_s *)step_tariff.passive_step_get();
                //     uint32_to_lsbbcd(p_data, step_tab->price[index - 1], 4), p_data += 4;
                // }
                break;
            case ITEM(C4_LOSS_VOL_LIMIT_MAX   ): //@0x04090101 ///  失压事件电压触发上限
            // case ITEM(C4_LOSS_VOL_LIMIT_MIN   ): //@0x04090102 ///  失压事件电压恢复下限
            // case ITEM(C4_LOSS_VOL_LIMIT_CUR   ): //@0x04090103 ///  失压事件电流触发下限
            // case ITEM(C4_LOSS_VOL_LIMIT_time  ): //@0x04090104 ///  失压事件判定延时时间
            #if EVENT_LOSS_VOL_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_loss_thd_v_h), &value, pe_sizeof(vol_loss_thd_v_h)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_loss_thd_v_l), &value, pe_sizeof(vol_loss_thd_v_l)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 3:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(vol_loss_thd_i), &value, pe_sizeof(vol_loss_thd_i)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 4: 
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(vol_loss_thd_time), &value, pe_sizeof(vol_loss_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_LOW_VOL_LIMIT_MAX    ): //@0x04090201 ///  欠压事件电压触发上限
            // case ITEM(C4_LOW_VOL_LIMIT_time   ): //@0x04090202 ///  欠压事件判定延时时间
            #if EVENT_LOW_VOL_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_low_thd_v), &value, pe_sizeof(vol_low_thd_v)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(vol_low_thd_time), &value, pe_sizeof(vol_low_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_OVR_VOL_LIMIT_MAX    ): //@0x04090301 ///  过压事件电压触发上限
            // case ITEM(C4_OVR_VOL_LIMIT_time   ): //@0x04090301 ///  过压事件判定延时时间
            #if EVENT_OVR_VOL_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_ovr_thd_v), &value, pe_sizeof(vol_ovr_thd_v)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(vol_ovr_thd_time), &value, pe_sizeof(vol_ovr_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_MISS_PH_LIMIT_VOL    ): //@0x04090401 ///  断相事件电压触发上限
            // case ITEM(C4_MISS_PH_LIMIT_CUR    ): //@0x04090402 ///  断相事件电流触发上限
            // case ITEM(C4_MISS_PH_LIMIT_TIME   ): //@0x04090403 ///  断相事件判定延时时间
            #if EVENT_MISS_VOL_EN
                if(index > 0 && index <= 3)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_miss_thd_v), &value, pe_sizeof(vol_miss_thd_v)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(vol_miss_thd_i), &value, pe_sizeof(vol_miss_thd_i)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 3:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(vol_miss_thd_time), &value, pe_sizeof(vol_miss_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_VOL_UNB_PER          ): //@0x04090501 ///  电压不平衡率限值
            // case ITEM(C4_VOL_UNB_TIME         ): //@0x04090502 ///  电压不平衡率判定延时时间
            #if EVENT_V_UNB_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(vol_unb_thd), &value, pe_sizeof(vol_unb_thd)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(vol_unb_thd_time), &value, pe_sizeof(vol_unb_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_CUR_UNB_PER          ): //@0x04090601 ///  电流不平衡率限值
            // case ITEM(C4_CUR_UNB_TIME         ): //@0x04090601 ///  电流不平衡率判定延时时间
            #if EVENT_I_UNB_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(i_unb_thd), &value, pe_sizeof(i_unb_thd)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(i_unb_thd_time), &value, pe_sizeof(i_unb_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_LOS_CUR_V            ): //@0x04090701 ///  失流事件电压触发下限
            // case ITEM(C4_LOS_CUR_I_MAX        ): //@0x04090701 ///  失流事件电流触发上限
            // case ITEM(C4_LOS_CUR_I_MIN        ): //@0x04090701 ///  失流事件电流触发下限
            // case ITEM(C4_LOS_CUR_TIME         ): //@0x04090701 ///  失流事件判定延时时间
            #if EVENT_LOS_CUR_EN
                if(index > 0 && index <= 4)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(i_loss_thd_v), &value, pe_sizeof(i_loss_thd_v)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(i_loss_thd_i_h), &value, pe_sizeof(i_loss_thd_i_h)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 3:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(i_loss_thd_i_l), &value, pe_sizeof(i_loss_thd_i_l)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 4:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(i_loss_thd_time), &value, pe_sizeof(i_loss_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_OVR_CUR_I            ): //@0x04090801 ///  过流事件电流触发下限
            // case ITEM(C4_OVR_CUR_TIME         ): //@0x04090801 ///  过流事件判定延时时间
            #if EVENT_OVR_CUR_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(i_ovr_thd_i), &value, pe_sizeof(i_ovr_thd_i)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(i_ovr_thd_time), &value, pe_sizeof(i_ovr_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_MISS_CUR_V           ): //@0x04090901 ///  断流事件电压触发下限
            // case ITEM(C4_MISS_CUR_V           ): //@0x04090901 ///  断流事件电流触发上限
            // case ITEM(C4_MISS_CUR_TIME        ): //@0x04090901 ///  断流事件判定延时时间
            #if EVENT_MISS_CUR_EN
                if(index > 0 && index <= 3)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(i_miss_thd_v), &value, pe_sizeof(i_miss_thd_v)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(i_miss_thd_i), &value, pe_sizeof(i_miss_thd_i)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 3:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(i_miss_thd_time), &value, pe_sizeof(i_miss_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_REV_POWER            ): //@0x04090A01 ///  潮流反向事件有功功率触发下限
            // case ITEM(C4_REV_TIME             ): //@0x04090A01 ///  潮流反向事件判定延时时间
            #if EVENT_REV_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(p_rev_thd_p), &value, pe_sizeof(p_rev_thd_p)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(p_rev_thd_time), &value, pe_sizeof(p_rev_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_OVER_LOAD_POWER      ): //@0x04090B01 ///  过载事件有功功率触发下限
            // case ITEM(C4_OVER_LOAD_TIME       ): //@0x04090B01 ///  过载事件判定延时时间
            #if EVENT_OVR_LOAD_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 3);
                            return power_event.para_set(pe_offset(p_ovr_thd_p), &value, pe_sizeof(p_ovr_thd_p)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(p_ovr_thd_time), &value, pe_sizeof(p_ovr_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_VOL__MAX             ): //@0x04090C01 ///  电压考核上限
            // case ITEM(C4_VOL__MIN             ): //@0x04090C01 ///  电压考核下限
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 2;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
            case ITEM(C4_OVR_MD_kW_THD        ): //@0x04090D01 ///  有功需量超限事件需量触发下限
            // case ITEM(C4_OVR_MD_kvar_THD      ): //@0x04090D01 ///  无功需量超限事件需量触发下限
            // case ITEM(C4_OVR_MD_TIME          ): //@0x04090D01 ///  需量超限事件判定延时时间
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 2;
                //             break;
                //         case 3:
                //             value = power_event.para_get()->vol_loss_thd_i;    len = 1;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
            case ITEM(C4_LOW_PF_THD           ): //@0x04090E01 ///  总功率因数超下限阀值
            // case ITEM(C4_LOW_PF_THD           ): //@0x04090E01 ///  总功率因数超下限判定延时时间
            #if EVENT_LOW_PF_EN
                if(index > 0 && index <= 2)
                {
                    uint32_t value;
                    uint8_t  len;
                    switch(index)
                    {
                        case 1:
                            value = lsbbcd_to_hex32(p_data, 2);
                            return power_event.para_set(pe_offset(pf_low_thd_pf), &value, pe_sizeof(pf_low_thd_pf)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                        case 2:
                            value = lsbbcd_to_hex32(p_data, 1);
                            return power_event.para_set(pe_offset(pf_low_thd_time), &value, pe_sizeof(pf_low_thd_time)) ? ERR_CODE_NONE : ERR_CODE_OTHER;
                    }
                }
            #endif
                break;
            case ITEM(C4_CUR_UNB2_THD         ): //@0x04090F01 ///  电流严重不平衡限值
            // case ITEM(C4_CUR_UNB2_TIME        ): //@0x04090F01 ///  电流严重不平衡触发延时时间
                // if(index > 0 && index <= 4)
                // {
                //     uint32_t value;
                //     uint8_t  len;
                //     switch(index)
                //     {
                //         case 1:
                //             value = power_event.para_get()->vol_loss_thd_v_h;  len = 2;
                //             break;
                //         case 2:
                //             value = power_event.para_get()->vol_loss_thd_v_l;  len = 1;
                //             break;
                //     }
                //     uint32_to_lsbbcd(p_data, value, len), p_data += len;
                // }
                break;
        }
    }


    return ERR_CODE_OTHER;
}



/// end of file
