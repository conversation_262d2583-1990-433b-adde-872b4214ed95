/**
  ******************************************************************************
  * @file    hal_spi.c
  * <AUTHOR> @date    2024
  * @brief   本模块完成MASTER SPI总线的硬件驱动(MCU 硬件SPI).
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  * ht6025 SPI 配置顺序：
  * 1、 将 spi 相关引脚配置为 gpio 功能；
  * 2、 初始化 spi 模块配置，比如模式，波特率等，推荐最后打开 SPI_EN；
  * 3、 将 spi 相关引脚配置为 spi 复用功能 ；
  * 4、 多从机应用中若要修改 spi 模块的配置，比如模式，波特率等，先关闭 SPI_EN,然后修改 spi 模块配
       置后再打开 SPI_EN.
  ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_spi.h"
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/// @brief 读一个字符
__INLINE uint8_t mcu_spi_char_read(void)
{
    return (uint8_t)(HT_SPI0->SPIDAT);
}

/// @brief 写一个字符到发送FIFO
/// @param ch 
/// @return 
__INLINE void mcu_spi_char_write(uint8_t ch)
{
    volatile uint8_t flag;
	uint16_t time_out = 10000;

    while((HT_SPI0->SPISTA & SPI_SPISTA_SPIF) && time_out) //清除状态
    {
        flag = HT_SPI0->SPISTA;
        flag = HT_SPI0->SPIDAT; //
        time_out--;
    }

    HT_SPI0->SPIDAT = (uint16_t)ch; // 写数据到发送FIFO

    time_out = 10000;
    while((HT_SPI0->SPISTA & SPI_SPISTA_SPIF) == 0 && (--time_out != 0)) {} // 等待发送完毕
}

/* Public functions ----------------------------------------------------------*/
/**
  * @brief  SPI总线向从设备发送一个字符并收回一个字符.
  * @param  [in]  ch-发送字符
  * @retval 回收字符
  */
char hal_spi0_trans(uint8_t ch, uint8_t mod)
{
    mcu_spi_char_write(ch);
    ch = mcu_spi_char_read();
    return ch;
}

/** @brief Open 一个SPI总线
  * @param  [in]  kbps-总线速率值, 单位KBPS
  * @retval None
  */
void hal_spi0_open(uint16_t kbps)
{
    HAL_SAFETY_WR(HT_CMU->CLKCTRL0 |= CMU_CLKCTRL0_SPI0EN;);   ///使能SPI0时钟
	hal_spi0_deviceoff();
    ///开启SPI总线，SCK在空闲状态时被设置为高电平，SPI工作在Master模式，表示高位（MSB）将在SCK的第一个动态沿之前半个周期被发送，Fspi = Fsys/16，在主/从模式中打开SPI_CS输入
	HT_SPI0->SPICON = (SPI_SPICON_SPIEN | SPI_Master | SPI_CPHA_1Edge | SPI_CPOL_high | SPI_BaudRatePrescaler_16);  // 
}

/* @brief Close 一个SPI总线 */
void hal_spi0_close(void)
{
    hal_spi0_deviceoff();
    HT_SPI0->SPICON &= (~SPI_SPICON_SPIEN);        ///关闭SPI总线
}

void hal_spi0_deviceon(void)
{
    HT_SPI0->SPISSN &= (~0x01);                                 ///SPI CS拉低          
}

void hal_spi0_deviceoff(void)
{
    HT_SPI0->SPISSN |= 0x03;                       ///SPI CS拉高 
}

/** @} */
/** @} */
/** @} */
