/**
 ******************************************************************************
 * @file    afn_14.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 14 请求 3 类数据，事件
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);


rsp_err_t afn0E_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *ptr = rsp->apdu;
    switch(req->fn)
    {
        case 1:    // F1：事件数据
        {

        }
    }

    return ACK_RIGHT;
}
const gdw376_table_s afn0E_table = {
    .afn    = AFN_REQ_CLASS3_DATA,    ///< 功能码
    .reset  = NULL,                   ///< 复位函数
    .verify = NULL,                   ///< 验证函数
    .get    = NULL,                   ///< 获取函数
    .set    = NULL,                   ///< 设置函数
};

// end of file
