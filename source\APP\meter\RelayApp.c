#include "app.h"
#include "app_config.h"
#include "relayapp.h"
#include "key.h"
#include "datastore.h"
#include "utils.h"

#define CONTROL_CRC16               0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(CONTROL_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(CONTROL_CRC16, struct, len)

#define CONTROL_PARA_ADDR           nvm_addr(NVM_CTRL_PARA)
#define CONTROL_DATA_ADDR           nvm_addr(NVM_CTRL_DATA)

#define CON_MIN_VOLTAGE             (RELAY_CONTROL_MIN_VOLTAGE)

/* 上电5秒内及电压低于80V不允许执行继电器动作，否则易造成电表异常复位 */
#if defined(POLYPHASE_METER)
    #define CONTROL_ACTION_ENABLE()      ((ctrl_power_on_cnt >= RELAY_PWRON_STABLE_TIME) && ((mstatus.reg->ctrl.mic_err) ||\
        (mic.ins->vrms[0] > CON_MIN_VOLTAGE || mic.ins->vrms[1] > CON_MIN_VOLTAGE || mic.ins->vrms[2] > CON_MIN_VOLTAGE)))
    
    #define CONTROL_SECOND_PRO_ENABLE() ((mstatus.reg->ctrl.mic_err) || \
        (mic.ins->vrms[0] > CON_MIN_VOLTAGE || mic.ins->vrms[1] > CON_MIN_VOLTAGE || mic.ins->vrms[2] > CON_MIN_VOLTAGE))
#else
    #define CONTROL_ACTION_ENABLE()      ((ctrl_power_on_cnt >= RELAY_PWRON_STABLE_TIME) && ((mstatus.reg->ctrl.mic_err) ||\
        (mic.ins->vrms[0] > CON_MIN_VOLTAGE || mic.ins->vrms[1] > CON_MIN_VOLTAGE)))
    
    #define CONTROL_SECOND_PRO_ENABLE() ((mstatus.reg->ctrl.mic_err) ||\
        (mic.ins->vrms[0] > CON_MIN_VOLTAGE || mic.ins->vrms[1] > CON_MIN_VOLTAGE))
#endif

/* 负荷控制参数 */
typedef struct
{
    uint16_t timer;     // 继电器受负荷控制的计时器
    uint16_t counter;   // 继电器受负荷控制的计数器
    bool     rly_lock;  // 继电器受控状态锁住，需解锁后，才能恢复自动合闸功能
}ctrl_load_s;



/* Private constants ---------------------------------------------------------*/
extern const ctrl_para_s  control_default_para; ///默认参数
static const ctrl_para_s* control_running_para; ///运行参数，指向code flash

/* Private variables ---------------------------------------------------------*/
__no_init static ctrl_load_s ctrl_load[RLY_NUM];  // 负荷控制参数

static uint32_t          rly_opt_code;            // 远程控制操作者代码
static ctrl_data_s       control_data[RLY_NUM];   // 模块运行数据
static TYPE_STUS_CONTROL ctrl_out_stus[RLY_NUM];  // 继电器模块事件输出状态
static RLY_OUT_STATE     rly_out_state[RLY_NUM];  // 继电器物理状态
static rly_ctrl_stus_s   rly_ctrl_stus[RLY_NUM];  // 继电器受控的事件状态
static uint8_t           ctrl_power_on_cnt;       // 上电计时器

/* Private function prototypes -----------------------------------------------*/
bool control_local(local_ctrl_s   type, RLY_TYPE_t type_rly);
bool control_state_query(uint16_t state);
void control_load_ctrl_clr(RLY_TYPE_t type);

/* Private functions ---------------------------------------------------------*/
/// @brief 参数计算检验后存入NVM中
/// @param ofst
/// @param val
/// @param len
/// @return
static bool control_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    ctrl_para_s para;
    if(ofst != 0) memcpy(&para, control_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
#if SW_PAYMENT_EN
    para.current_filter.debt = 1; // 本地费控模式下，欠费拉闸，强制打开
#endif
    CRC16_CAL(&para, sizeof(para));
    control_running_para = (const ctrl_para_s*)CONTROL_PARA_ADDR;
    return nvm.write((uint32_t)control_running_para, &para, sizeof(para));
}

/// @brief 参数指针初始化并检验参数
/// @param
static void control_para_load(void)
{
    control_running_para = (const ctrl_para_s*)CONTROL_PARA_ADDR;
    if(CRC16_CHK(control_running_para, sizeof(ctrl_para_s)) == false)
    {
        control_running_para = &control_default_para;
    }
}

/// @brief 数据计算检验后存入NVM
/// @param dat
/// @param wr_nvm 是否写NVM
/// @return
static bool control_data_save(ctrl_data_s* dat, bool wr_nvm)
{
    CRC16_CAL(dat, sizeof(ctrl_data_s));
    return wr_nvm ? nvm.write(CONTROL_DATA_ADDR + (uint32_t)dat - (uint32_t)control_data, dat, sizeof(ctrl_data_s)) : true;
}

/// @brief 数据初始化并检验
/// @param
static void control_data_load(RLY_TYPE_t type, ctrl_data_s* dat)
{
    if(CRC16_CHK(dat, sizeof(ctrl_data_s)) == false)
    {
        nvm.read(CONTROL_DATA_ADDR + offsetptr(ctrl_data_s, type), dat, sizeof(ctrl_data_s));
        if(CRC16_CHK(dat, sizeof(ctrl_data_s)) == false)
        {
            /* 初始化时，默认电表是物理拉闸状态，逻辑合闸状态，再通过自我检测实现第一次上电自动合闸 */
            rly_out_state[type] = RLY_DISCONNECTED;
            memset(dat, 0, sizeof(ctrl_data_s));
            dat->cur_state  = CONNECTED; 
            dat->last_state = DISCONNECTED;
            control_data_save(dat, false);
        }
    }
}

/// @brief 保电状态合闸
/// @param type 继电器类型
static void control_keep_on_mode_scan(RLY_TYPE_t type)
{
    ctrl_data_s* dat = &control_data[type];
    bool save_flag = false;

    if(ctrl_power_on_cnt < RELAY_PWRON_STABLE_TIME) return;  // 上电5秒内不允许执行保电状态合闸

    if(mclock.is_valid(&dat->keep_mode_end_time) && 
       mclock.compare(&dat->keep_mode_end_time) <= 0)  // 保电有效时间过期时，自动退出保电状态
    {
        dat->keep_mode = false;
        mclock.invalid_set(&dat->keep_mode_end_time);
        save_flag = true;
    }

    if(dat->keep_mode == true)
    {
#if RLY_INSIDE_ENABLE
        if(type == TYPE_RLY_1 && dat->cur_state == DISCONNECTED)
        {
            ctrl_out_stus[type] |= STUS_READY_CONNECT(type);
            dat->last_state = dat->cur_state;
            dat->cur_state  = READY_FOR_RECONNECTION;
            dat->reason.ext_off.lword = 0;
            dat->reason.ext_off.remote_rdy = 1;
            save_flag = true;
        }
#endif
#if RLY_OUTSIDE_ENABLE
        if(type == TYPE_RLY_2 && dat->cur_state != CONNECTED)
        {
            if(!CONTROL_ACTION_ENABLE()) return;
            ctrl_out_stus[type] |= STUS_CONNECTED(type);
            relay.action((RLY_TYPE_t)type, MODE_RLY_ON);
            dat->last_state = dat->cur_state;
            dat->cur_state  = CONNECTED;
            dat->reason.ext_off.lword = 0;
            save_flag = true;
        }
#endif
    }

    if(save_flag) control_data_save(dat, true);
}

/// @brief 远程合闸
/// @param type 继电器类型
/// @return 
static bool control_remote_reconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if(dat->cur_state == DISCONNECTED)
    {
        ctrl_out_stus[type] |= STUS_CONNECTED(type) | STUS_REMOTE_CONNECTED(type);
        relay.action(type, MODE_RLY_ON);
        dat->last_state = dat->cur_state;
        dat->cur_state = CONNECTED;
        dat->reason.evt_off.lword = 0;
        dat->reason.ext_off.lword = 0;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 远程拉闸
/// @param type 
/// @return 
static bool control_remote_disconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if(dat->cur_state != DISCONNECTED && dat->keep_mode == false)
    {
        ctrl_out_stus[type] |= STUS_DISCONNECTED(type) | STUS_REMOTE_DISCONNECTED(type);
        relay.action(type, MODE_RLY_OFF);
        dat->last_state = dat->cur_state;
        dat->cur_state = DISCONNECTED;
        dat->rly_off_cnt++;
        dat->reason.ext_off.remote = 1;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 远程预合闸
/// @param type 
/// @return 
static bool control_remote_ready_reconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];
    if(type > RLY_NUM) return false;
    if(dat->cur_state == DISCONNECTED && (dat->keep_mode == false))
    { 
    #if RLY_OUTSIDE_ENABLE
        if(type == TYPE_RLY_2)
        {
            relay.action(type, MODE_RLY_ON);  /// 预合闸时外置继电器直接合闸
            dat->cur_state = CONNECTED;
            dat->reason.ext_off.lword = 0;
            // dat->reason.ext_off.remote_rdy = 0;
            ctrl_out_stus[type] |= STUS_READY_CONNECT(type);
        }
    #endif
    #if RLY_INSIDE_ENABLE
        if(type == TYPE_RLY_1)
        {
            dat->cur_state = READY_FOR_RECONNECTION;
            dat->reason.ext_off.lword = 0;
            dat->reason.ext_off.remote_rdy = 1;
            ctrl_out_stus[type] |= STUS_CONNECTED(type);
        }
    #endif
        dat->last_state = dat->cur_state;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 手动合闸
/// @param type 
/// @return 
static bool control_manual_reconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if(dat->cur_state == READY_FOR_RECONNECTION)  //只有预合闸状态才能手动合闸
    {
        ctrl_out_stus[type] |= STUS_CONNECTED(type) | STUS_MANU_CONNECTED(type);
        relay.action(type, MODE_RLY_ON);
        dat->last_state = dat->cur_state;
        dat->cur_state = CONNECTED;
        dat->reason.evt_off.lword = 0;
        dat->reason.ext_off.lword = 0;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 手动拉闸
/// @param type 
/// @return 
static bool control_manual_disconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if((dat->cur_state == CONNECTED) && (dat->keep_mode == false))
    {
        ctrl_out_stus[type] |= STUS_DISCONNECTED(type) | STUS_MANU_DISCONNECTED(type) | STUS_READY_CONNECT(type);
        relay.action(type, MODE_RLY_OFF);
        dat->last_state = dat->cur_state;
        dat->cur_state  = READY_FOR_RECONNECTION;
        dat->rly_off_cnt++;
        dat->reason.ext_off.manual = 1;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 本地拉闸
/// @param type 
/// @param reason 拉闸原因
/// @return 
static bool control_local_disconnect(RLY_TYPE_t type, rly_off_reason_s reason)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if((dat->cur_state == CONNECTED) && (dat->keep_mode == false))
    {
        ctrl_out_stus[type] |= STUS_READY_CONNECT(type) | STUS_LOCAL_DISCONNECTED(type) | STUS_DISCONNECTED(type);
        relay.action(type, MODE_RLY_OFF);
        dat->last_state = dat->cur_state;
        dat->cur_state  = READY_FOR_RECONNECTION;  
        dat->rly_off_cnt++;
        dat->reason = reason;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 本地合闸
/// @param type 
/// @return 
static bool control_local_reconnect(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];

    if(dat->cur_state == READY_FOR_RECONNECTION)
    {
        ctrl_out_stus[type] |= STUS_CONNECTED(type) | STUS_LOCAL_CONNECTED(type);
        relay.action(type, MODE_RLY_ON);
        dat->last_state = dat->cur_state;
        dat->cur_state = CONNECTED;
        dat->reason.evt_off.lword = 0;
        dat->reason.ext_off.lword = 0;
        return control_data_save(dat, true);
    }
    return false;
}

/// @brief 扫描本地控制请求
static void control_local_ctrl_request_scan(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;
    ctrl_data_s*       dat  = &control_data[type];
    control_status_s   cur  = mstatus.reg->ctrl;
    control_status_s   his  = dat->history_evt_stus;

    /* 根据电表生产和运营模式调整继电器控制 */
    if(mstatus.reg->running.factory)
    {
        cur.lword = 0;
    }
    else if(mstatus.reg->running.non_activity) // 运营模式，不检测开端盖
    {
        cur.bot_cov_opn = 0;
    }

    rly_ctrl_stus[type].evt_off.lword = cur.lword & para->current_filter.lword; // 当前本地拉闸请求事件状态更新
    his.lword |= cur.lword;   // 历史本地拉闸请求事件状态更新

    his.lword &= para->history_filter.lword;
    if(his.lword != dat->history_evt_stus.lword) //更新继电器历史控制状态
    {
        dat->history_evt_stus.lword |= his.lword;
        control_data_save(dat, true);
    }
}

/// @brief 超负荷拉闸
/// @param type 
/// @param b_ov 
/// @param reason 
static void control_over_load_ctrl_action(RLY_TYPE_t type, bool b_ov, rly_off_reason_s reason)
{
    ctrl_load_s* load_ctrl = &ctrl_load[type];

    if(b_ov) // 功率超限
    {
        reason.evt_off.over_load= 1;
        if(control_local_disconnect(type, reason))
        {
            load_ctrl->counter++;
            load_ctrl->timer = 0;
        }
    }
    else
    {
        /* 连续超功率拉闸大于N次后不清除超功率拉闸请求 */
        if(load_ctrl->counter <= control_running_para->over_power_cnt_thd)
        {
            if(!load_ctrl->rly_lock)
            {
                /* 连续超功率拉闸小于N次允许自动合闸 */
                control_local_reconnect(type);
            }
        }
        else
        {
            load_ctrl->rly_lock = true;
        }

        /* 未超功率持续超过半小时就清逻辑 */
        if(load_ctrl->timer++ > 1800)
        {
            load_ctrl->counter  = 0;
            load_ctrl->timer    = 0;
            load_ctrl->rly_lock = false;
        }
    }
}

/// @brief 继电器本地控制扫描任务接口，包括事件拉闸、欠费拉合闸请求
/// @param type 
static void control_local_ctrl_action(RLY_TYPE_t type)
{
    rly_ctrl_stus_s  status = rly_ctrl_stus[type];
    rly_off_reason_s reason = control_data[type].reason;

    // 事件触发的拉合闸使能状态过滤
    status.evt_off.lword |= control_data[type].history_evt_stus.lword;

    // 生产或非运营模式下清零外部控制
    if(mstatus.reg->running.factory || mstatus.reg->running.non_activity)
    {
        status.ext_off.lword = 0;
    }

    // 根据状态控制继电器 
    if(status.evt_off.lword != 0) // 本地事件触发拉闸
    {
        if(status.evt_off.over_load) // 超功率拉闸单独处理
        {
            control_over_load_ctrl_action(type, true, reason);
        }
        else
        {
            reason.evt_off.lword = status.evt_off.lword;
            control_local_disconnect(type, reason);
        }
    }
    else if(status.ext_off.lword != 0) // 其他模块触发拉闸
    {
        reason.ext_off.lword = status.ext_off.lword;
        control_local_disconnect(type, reason);
    }
    else // 本地事件及其他模块触发拉闸条件都恢复
    {
        if(!reason.ext_off.manual
        && !reason.ext_off.remote
        && !reason.ext_off.remote_rdy)   // 无外部远程/手动控制拉闸
        {
            if(reason.evt_off.over_load) // 超功率拉闸恢复单独处理
            {
                control_over_load_ctrl_action(type, false, reason);
            }
            // 和电流有关，继电器有关的事件不支持自动本地合闸，防止由于断闸后条件恢复反复拉合闸
        #if !RELAY_CURRENT_EVT_AUTO_RECOVERY
            else if(!reason.evt_off.i_over
                && !reason.evt_off.i_unb
                && !reason.evt_off.i_rev
                && !reason.evt_off.pf_low)
        #endif
            {
                control_local_reconnect(type);
            }
        }
    }
}

/// @brief 远程控制继电器延时拉闸
/// @param type 
/// @param ctrl_type 
static void control_remote_ctrl_scan(RLY_TYPE_t type)
{
    const ctrl_para_s* para = control_running_para;


}

/// @brief 继电器状态检测
/// @param type 
/// @return 
RLY_STATE_t control_rly_status_get(RLY_TYPE_t type)
{
    ctrl_state_s cur_state = control_data[type].cur_state;

    // 继电器检测电路使能时返回硬件状态
#if USE_RLY_CHK && RLY_INSIDE_ENABLE
    if(type == TYPE_RLY_1) { return relay.status(type); } 
#endif

#if USE_RLY_CHK_EX && RLY_OUTSIDE_ENABLE
    if(type == TYPE_RLY_2) { return relay.status(type); }
#endif

    //  由于底层没有继电器检测电路，配合电流大小判断继电器拉合状态
    if(cur_state == CONNECTED) 
    {
        return STA_RLY_ON; //合闸状态无法判断，直接返回逻辑状态
    }
    else
    {
        float Imax;
#if defined(POLYPHASE_METER)
        Imax = max(mic.ins->irms[0], mic.ins->irms[1]);
        Imax = max(Imax, mic.ins->irms[2]);
        if(Imax > (2 * START_CURRENT_RADIO * BASE_CURRENT))
#else
        Imax = max(mic.ins->n_irms, mic.ins->irms[0]);
        if(Imax > (4 * START_CURRENT_RADIO * BASE_CURRENT))
#endif
        {
            return STA_RLY_ON;    // 电流过大，认为继电器是合闸状态
        }
        return STA_RLY_OFF;
    }
}

static void control_rly_poweron_protect(RLY_TYPE_t type, uint8_t powon_sec)
{
    ctrl_state_s cur_state = control_data[type].cur_state;

    if(ctrl_power_on_cnt == powon_sec)
    {
        if(cur_state == CONNECTED)
        {
            relay.action(type, MODE_RLY_ON);
        }
        else
        {
            relay.action(type, MODE_RLY_OFF);
        }
    }
}

/* Public functions ----------------------------------------------------------*/
/// @brief 控制模块初始化
void control_init(void)
{
    ctrl_data_s* dat = control_data;
    control_para_load();
    for(uint8_t i = 0; i < RLY_NUM; i++, dat++)
    {
        /* 检出负控参数和数据 */
        control_data_load((RLY_TYPE_t)i, dat);
        rly_out_state[i] = (dat->cur_state == CONNECTED) ? RLY_CONNECTED : RLY_DISCONNECTED;
        rly_ctrl_stus[i].ext_off = dat->lst_ext_off;
    }
}

/// @brief 控制模块秒任务
void control_second_run(void)
{
    static uint8_t time[RLY_NUM], count[RLY_NUM], period_flag[RLY_NUM];
    ctrl_data_s* dat = control_data;

    if(!CONTROL_SECOND_PRO_ENABLE()) return;  // 电压不足不做控制条件的秒计数
    if(ctrl_power_on_cnt < 255) ctrl_power_on_cnt++;

    for(uint16_t i = 0; i < RLY_NUM; i++, dat++)
    {
        bool b_rly_err = false;

        b_rly_err = dat->relay_err;

        /// 上电后根据继电器状态拉合闸
        control_rly_poweron_protect((RLY_TYPE_t)i, RELAY_PWRON_DELAY_TIME);

        /* 检出负控参数和数据 */
        control_para_load();
        control_data_load((RLY_TYPE_t)i, dat);
        control_keep_on_mode_scan((RLY_TYPE_t)i);
        
        RLY_STATE_t state = control_rly_status_get((RLY_TYPE_t)i);
        control_remote_ctrl_scan((RLY_TYPE_t)i);
        control_local_ctrl_request_scan((RLY_TYPE_t)i);
        control_local_ctrl_action((RLY_TYPE_t)i);
        switch(state)
        {
            case STA_RLY_OFF:
            {
                rly_out_state[i] = RLY_DISCONNECTED;
                if(dat->cur_state == CONNECTED)
                {
                    /* 继电器物理状态与控制逻辑不一致时，每10秒自检一次，最多做10次自检，
                       超过10次自检后，状态仍不一致，则认为继电器故障 */
                    if(!b_rly_err || period_flag[i])
                    {
                        if(time[i]++ >= 10)
                        {
                            time[i] = 0;
                            if(count[i]++ < 10)
                            {
                                relay.action((RLY_TYPE_t)i, MODE_RLY_ON);
                            }
                            else
                            {
                                if(period_flag[i] == false) // 不重复记录事件
                                {
                                    ctrl_out_stus[i] |= STUS_RLY_ERR(i);
                                }
                                b_rly_err = true;
                            }
                        }
                    }
                    else
                    {
                        /* 已经异常的情况下，周期性每天3点13S后再做一次异常检测重试 */
                        if(mclock.datetime->hour == 3 && mclock.datetime->minute == 0 && mclock.datetime->second == 13)
                        {
                            time[i] = 0;
                            count[i] = 0;
                            period_flag[i] = true;
                        }
                    }
                }
                else
                {
                    time[i] = 0;
                    count[i] = 0;
                    b_rly_err = false;
                    period_flag[i] = false;
                }
                break;
            }
            case STA_RLY_ON:
            {
                rly_out_state[i] = RLY_CONNECTED;
                if(dat->cur_state != CONNECTED)
                {
                    /* 继电器物理状态与控制逻辑不一致时，每10秒自检一次，最多做10次自检，
                       超过10次自检后，状态仍不一致，则认为继电器故障 */
                    if(!b_rly_err || period_flag[i])
                    {
                        if(time[i]++ >= 10)
                        {
                            time[i] = 0;
                            if(count[i]++ < 10)
                            {
                                relay.action((RLY_TYPE_t)i, MODE_RLY_OFF);
                            }
                            else
                            {
                                if(period_flag[i] == false) // 不重复记录事件
                                {
                                    ctrl_out_stus[i] |= STUS_RLY_ERR(i);
                                }
                                b_rly_err = true;
                            }
                        }
                    }
                    else
                    {
                        /* 已经异常的情况下，周期性每天2点13S后再做一次异常检测重试 */
                        if(mclock.datetime->hour == 3 && mclock.datetime->minute == 0 && mclock.datetime->second == 13)
                        {
                            time[i] = 0;
                            count[i] = 0;
                            period_flag[i] = true;
                        }
                    }
                }
                else
                {
                    time[i] = 0;
                    count[i] = 0;
                    b_rly_err = false;
                    period_flag[i] = false;
                }
                break;
            }
            default:
                return;

        }

        dat->relay_err = b_rly_err;

        if(control_state_query(STUS_DISCONNECTED(i)))  // 有拉闸事件
        {
            dat->rly_off_stamp = *mclock.datetime; // 记录继电器断开时间
            control_data_save(dat, true);
        }
    }
}

/// @brief 继电器远程控制
/// @param type 
/// @param type_rly 
/// @param dat 前4个字节为操作者代码，后面依次为N1、N2、N3～N8
///            N1为控制命令类型，N1=1AH代表跳闸，N1=1BH代表合闸允许，N1=2AH代表报警，N1=2BH代表报警解除，
///            N1=3AH代表保电，N1=3BH代表保电解除。N2保留。N3～N8代表命令有效截止时间，数据格式为ssmmhhDDMMYY
/// @return 
bool control_remote(remote_ctrl_s type, RLY_TYPE_t type_rly, const uint8_t *dat)
{
    uint8_t *p_data = (uint8_t *)dat;
    uint32_t opt_code;
    uint32_t delay_time;// 延时时间
    clock_s  time;

    memcpy(&opt_code, p_data, 4), p_data += 4;
    type = (remote_ctrl_s)(*p_data), p_data++; //控制命令类型
    delay_time = (uint32_t)(*p_data) * 60, p_data++;//分钟转秒
    mclock.unformat_frm645(p_data, &time, CLOCK_YMDhms);//时间
    switch(type)
    {
        case TYPE_REMOTE_OFF:
            if(!CONTROL_ACTION_ENABLE()) return false;
            if(!control_remote_disconnect(type_rly)) return false;
            rly_opt_code = opt_code; // 记录远程控制的opt_code
            break;
        case TYPE_REMOTE_ON:
            if(!CONTROL_ACTION_ENABLE()) return false;
            if(control_data[type_rly].cur_state != DISCONNECTED) return false;
            {
                rly_ctrl_stus_s status = rly_ctrl_stus[type_rly];
                status.evt_off.lword |= control_data[type_rly].history_evt_stus.lword;
                //  当拉闸由外部模块控制引起时，允许远程合闸
                if(status.evt_off.lword != 0) return false;     // 有拉闸事件时不允许远程合闸
                if(!control_remote_reconnect(type_rly)) return false;
                control_load_ctrl_clr(type_rly); // 远程合闸成功后，清继电器负荷控制参数
                rly_opt_code = opt_code; // 记录远程控制的opt_code
            }
            break;
        case TYPE_REMOTE_READY_ON: // 预合闸
            if(control_data[type_rly].cur_state != DISCONNECTED) return false;
            if(!control_remote_ready_reconnect(type_rly)) return false;
            break;
        case TYPE_REMOTE_ON_DELAY:  
        case TYPE_REMOTE_READY_DELAY:
            control_data[type_rly].delay_ctrl_type = type;
            control_data[type_rly].ctrl_delay      = delay_time;
            return control_data_save(&control_data[type_rly], true);
        case TYPE_REMOTE_ALARM_ON : //报警
            break;
        case TYPE_REMOTE_ALARM_OFF: //报警解除
            break;
        case TYPE_REMOTE_KEEP_ON :  //保电 
            control_data[type_rly].keep_mode = true;
            if(mclock.is_valid(&time)) { control_data[type_rly].keep_mode_end_time = time; }
            else { mclock.invalid_set(&control_data[type_rly].keep_mode_end_time); }
            return control_data_save(&control_data[type_rly], true);   
        case TYPE_REMOTE_KEEP_DIS:  //保电解除
        {
            control_data[type_rly].keep_mode = false;
            mclock.invalid_set(&control_data[type_rly].keep_mode_end_time);
            return control_data_save(&control_data[type_rly], true);
        }
    }
    return true;
}

/// @brief 手动控制继电器
/// @param type 
/// @param type_rly 
/// @return 
bool control_manual(manual_ctrl_s type, RLY_TYPE_t type_rly)
{
    if(!CONTROL_ACTION_ENABLE()) return false;

    switch(type)
    {
        case TYPE_MANUAL_OFF:
            if(!control_manual_disconnect(type_rly)) return false;
            break;
        case TYPE_MANUAL_ON:
        {
            /* 根据电表生产和运营模式调整继电器控制 */
            rly_ctrl_stus_s status = rly_ctrl_stus[type_rly];
            status.evt_off.lword |= control_data[type_rly].history_evt_stus.lword;
            /* 当拉闸由外部控制引起时，允许手动合闸 */
            if(status.evt_off.lword != 0) return false;     // 有拉闸事件时，不允许手动合闸
            if(!control_manual_reconnect(type_rly)) return false;
            control_load_ctrl_clr(type_rly); // 合闸成功后，清继电器负荷控制参数
            break;
        }
    }
    return true;
}

/// @brief 本地控制继电器
/// @param type 
/// @param type_rly 
bool control_local(local_ctrl_s type, RLY_TYPE_t type_rly)
{
    ctrl_data_s* dat = &control_data[type_rly];
    rly_ctrl_stus_s* stus = &rly_ctrl_stus[type_rly];

    if(!CONTROL_ACTION_ENABLE()) return false;
    switch(type)
    {
        case TYPE_LOCAL_OFF:
        {
            rly_off_reason_s reason = dat->reason;
            reason.ext_off.remote = 1;
            if(!control_local_disconnect(type_rly, reason)) return false;
        }
        break;
        case TYPE_LOCAL_ON:
            if(dat->cur_state != READY_FOR_RECONNECTION) return false;
            /* 根据电表生产和运营模式调整继电器控制 */
            rly_ctrl_stus_s status = rly_ctrl_stus[type_rly];
            status.evt_off.lword |= dat->history_evt_stus.lword;
            /* 当拉闸由外部模块控制引起时，允许远程合闸 */
            if(status.evt_off.lword != 0) return false;     // 判断是否能执行合闸
            if(!control_local_reconnect(type_rly)) return false;
            control_load_ctrl_clr(type_rly); // 远程合闸成功后，清继电器负荷控制参数
            //stus->ext_off.lword = 0; // 清外部模块控制继电器请求状态
            //limit.over_status_clr(LMT_CLR_RLY_ACTION);
            break;
    }

    dat->lst_ext_off = stus->ext_off;
    control_data_save(dat, false);
    return true;
}

/// @brief 获取继电器控制模块参数
/// @param type 
/// @return 
const ctrl_para_s* control_para_get(RLY_TYPE_t type)
{
    return control_running_para;
}

/// @brief 设置继电器控制模块参数
/// @param type 
/// @param ofst 
/// @param val 
/// @param len 
bool control_para_set(RLY_TYPE_t type, uint16_t ofst, const void* val)
{
    uint16_t len = 0;
    if(type >= RLY_NUM) return false;

    switch(ofst)
    {
        case member_offset(ctrl_para_s, over_power_cnt_thd):
            len = member_size(ctrl_para_s, over_power_cnt_thd);
            break;
        case member_offset(ctrl_para_s, malignant_load_cnt_thd):
            len = member_size(ctrl_para_s, malignant_load_cnt_thd);
            break;
        case member_offset(ctrl_para_s, current_filter):
            len = member_size(ctrl_para_s, current_filter);
            break;
        case member_offset(ctrl_para_s, history_filter):
            len = member_size(ctrl_para_s, history_filter);
            break;
        default: return false;
    }

    return control_para_save(ofst, val, len);
}

/// @brief 获取继电器物理状态
/// @param type 
/// @return 
RLY_OUT_STATE control_out_state_get(RLY_TYPE_t type)
{
    return rly_out_state[type];
}

/// @brief 获取继电器逻辑状态
/// @param mode 0 - 当前状态，1 - 上次状态
/// @param type 
/// @return 
ctrl_state_s control_ctrl_state_get(uint8_t mode, RLY_TYPE_t type)
{
    if(mode == 0)
    {
        return control_data[type].cur_state;
    }
    else
    {
        return control_data[type].last_state;
    }
}

/// @brief 获取继电器控制数据
ctrl_data_s *control_data_get(RLY_TYPE_t type)
{
    return &control_data[type];
}

uint32_t control_opt_code_get(void)
{
    return rly_opt_code;
}

/// @brief 获取继电器断开原因
rly_off_reason_s control_disconnect_reason_get(RLY_TYPE_t type, uint8_t state)
{
    rly_off_reason_s reason = control_data[type].reason;   // 拉闸时刻的拉闸原因
    if(state == 0) // 当前继电器控制可能拉闸原因
    {
        reason.evt_off.lword = rly_ctrl_stus[type].evt_off.lword;
        reason.ext_off.lword |= rly_ctrl_stus[type].ext_off.lword;
        reason.evt_off.lword |= control_data[type].history_evt_stus.lword;
    }

    if(control_data[type].cur_state == CONNECTED)
    {
        reason.evt_off.lword = 0;
        reason.ext_off.lword = 0;
    }

    return reason;
}

/// @brief 获取继电器模块事件状态
/// @param state 
/// @return 
bool control_state_query(uint16_t state)
{
    uint8_t idx = state & 0x03;
    state &= 0xFFFC;
    return boolof(ctrl_out_stus[idx] & state);
}

/// @brief 清继电器控制模块事件状态
void control_state_clr(void)
{
    for(uint8_t i = 0; i < RLY_NUM; i++)
    {
        ctrl_out_stus[i] = 0;
    }
}

/// @brief 清负荷控制数据
/// @param type 
void control_load_ctrl_clr(RLY_TYPE_t type)
{
    rly_ctrl_stus[type].ext_off.lword = 0; // 清外部模块控制继电器请求状态
    memset(&ctrl_load[type], 0, sizeof(ctrl_load_s));
    control_data[type].lst_ext_off = rly_ctrl_stus[type].ext_off;
    control_data_save(&control_data[type], false);
}
 
/// @brief 清历史控制数据
bool control_his_ctrl_clr(RLY_TYPE_t type)
{
    if((mstatus.reg->ctrl.lword & control_running_para->history_filter.lword) == 0)
    {
        control_data[type].history_evt_stus.lword = 0;
        control_data_save(&control_data[type], true);
        return true;
    }
    else return false;
}


void control_power_down_save(void)
{

}


void control_reset(uint8_t type)
{
    ctrl_data_s* dat = control_data;

    ctrl_power_on_cnt = 0;

    if(type & SYS_PARA_RESET)
    {
        control_para_save(0, &control_default_para, sizeof(ctrl_para_s));
    }
    for(uint8_t i = 0; i < RLY_NUM; i++, dat++)
    {
        if(type & SYS_DATA_RESET)
        {
            if(dat->cur_state != CONNECTED)
            {
                relay.action((RLY_TYPE_t)i, MODE_RLY_ON);
            }
            rly_out_state[i]  = RLY_CONNECTED;
            memset(&ctrl_load[i], 0, sizeof(ctrl_load_s));
            ctrl_out_stus[i] = 0;
            memset(&rly_ctrl_stus[i], 0, sizeof(rly_ctrl_stus_s));
            memset(dat, 0, sizeof(ctrl_data_s));

            dat->cur_state = CONNECTED;
            dat->last_state = DISCONNECTED;
            control_data_save(dat, boolof(type != SYS_GLOBAL_RESET));
        }
    }
}

/// @brief 声明control对象
const struct control_t control =
{
    .reset                  = control_reset,
    .para_get               = control_para_get,
    .para_set               = control_para_set,
    .state_query            = control_state_query,
    .state_clr              = control_state_clr,
    .out_state_get          = control_out_state_get,
    .ctrl_state_get         = control_ctrl_state_get,
    .data_get               = control_data_get,
    .disconnect_reason_get  = control_disconnect_reason_get,
    .load_ctrl_clr          = control_load_ctrl_clr,
    .his_ctrl_clr           = control_his_ctrl_clr,
    .remote                 = control_remote,
    .manual                 = control_manual,
    .local                  = control_local,
};

/// @brief 继电器控制模块任务接口
const struct app_task_t control_task =
{
    .init       = control_init,
    .second_run = control_second_run,
};
