/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   lpm.c
 *
 * @brief  Source file for Low Power Mode functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "lpm.h"
/**
 *******************************************************************************
 ** \addtogroup LpmGroup
 ******************************************************************************/
//@{

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/                                     

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 *****************************************************************************
 ** \brief 进入深度睡眠模式
 **
 ** \input bOnExit - TRUE:当退出异常处理后，自动再次进入休眠；
 **                  FALSE：唤醒后不再自动进入休眠
 ** 
 ** \retval NULL                                     
 *****************************************************************************/
void Lpm_GotoDeepSleep(boolean_t bOnExit)
{
    SCB->SCR |= SCB_SCR_SLEEPDEEP_Msk;
	
	if(bOnExit == TRUE)
	{
		SCB->SCR |= SCB_SCR_SLEEPONEXIT_Msk;
	}
	else
	{
		SCB->SCR &= ~SCB_SCR_SLEEPONEXIT_Msk;
	}

    __WFI();
}

/**
 *****************************************************************************
 ** \brief 进入普通睡眠模式
 **
 ** \input bOnExit - TRUE:当退出异常处理后，自动再次进入休眠；
 **                  FALSE：唤醒后不再自动进入休眠
 ** 
 ** \retval NULL                                     
 *****************************************************************************/
void Lpm_GotoSleep(boolean_t bOnExit)
{
    SCB->SCR &= ~SCB_SCR_SLEEPDEEP_Msk;
	
    if(bOnExit == TRUE)
	{
		SCB->SCR |= SCB_SCR_SLEEPONEXIT_Msk;
	}
	else
	{
		SCB->SCR &= ~SCB_SCR_SLEEPONEXIT_Msk;
	}
    __WFI();
}
                        
//@} // LpmGroup                                                                           

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
