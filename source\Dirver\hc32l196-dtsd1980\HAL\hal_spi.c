/**
  ******************************************************************************
  * @file    hal_spi.c
  * <AUTHOR> @date    2024
  * @brief   本模块完成MASTER SPI总线的硬件驱动(MCU 硬件SPI).
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  * ht6025 SPI 配置顺序：
  * 1、 将 spi 相关引脚配置为 gpio 功能；
  * 2、 初始化 spi 模块配置，比如模式，波特率等，推荐最后打开 SPI_EN；
  * 3、 将 spi 相关引脚配置为 spi 复用功能 ；
  * 4、 多从机应用中若要修改 spi 模块的配置，比如模式，波特率等，先关闭 SPI_EN,然后修改 spi 模块配
       置后再打开 SPI_EN.
  ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_spi.h"
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

#define HW_SPI0 M0P_SPI0
#define SPI0_CLK SysctrlPeripheralSpi0
#define SPI0_RST ResetMskSpi0

#define HW_SPI1 M0P_SPI1
#define SPI1_CLK SysctrlPeripheralSpi1
#define SPI1_RST ResetMskSpi1

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/// @brief 读一个字符
__INLINE uint8_t mcu_spi_char_read(void)
{
    uint8_t  ch = 0;
    uint16_t time_out;

    time_out = 10000;
    while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    HW_SPI0->DATA = 0x00;
    time_out     = 10000;
    while(FALSE == HW_SPI0->STAT_f.RXNE && time_out--) { ; }    // 等待接受缓冲区非空
    ch       = HW_SPI0->DATA;
    time_out = 10000;
    while(TRUE == HW_SPI0->STAT_f.BUSY && time_out--) { ; }    // 等待SPI总线空闲

    return ch;
}

/// @brief 写一个字符到发送FIFO
/// @param ch
/// @return
__INLINE void mcu_spi_char_write(uint8_t ch)
{
    uint16_t time_out;

    time_out = 10000;
    while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    HW_SPI0->DATA = ch;
    time_out     = 10000;
    while(FALSE == HW_SPI0->STAT_f.RXNE && time_out--) { ; }    // 等待接受缓冲区非空
    ch       = HW_SPI0->DATA;
    time_out = 10000;
    while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    time_out = 10000;
    while(TRUE == HW_SPI0->STAT_f.BUSY && time_out--) { ; }    // 等待SPI总线空闲
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  SPI0总线向从设备发送一个字符并收回一个字符.
 * @param  [in]  ch-发送字符
 * @retval 回收字符
 */
char hal_spi0_trans(uint8_t ch, uint8_t mod)
{
    uint16_t time_out = 10000;

    while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) {}    // 等待发送缓冲区空
    HW_SPI0->DATA = ch;
    time_out     = 10000;
    while(FALSE == HW_SPI0->STAT_f.RXNE && time_out--) {}    // 等待接受缓冲区非空
    ch = HW_SPI0->DATA;

    // 关闭片选前处理总线状态
    //  time_out = 10000;
    //  while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    //  time_out = 10000;
    //  while(TRUE == HW_SPI0->STAT_f.BUSY && time_out--) { ; }    // 等待SPI总线空闲

    return ch;
}

/** @brief Open SPI0总线
 * @param  [in]  kbps-总线速率值, 单位3000 KBPS
 * @retval None
 */
void hal_spi0_open(uint16_t kbps)
{
    M0P_SYSCTRL->PERI_CLKEN0 |= (1UL << SPI0_CLK);    // 打开SPI0时钟
    M0P_RESET->PERI_RESET0 &= ~(uint32_t)SPI0_RST;    // 复位spi模块
    M0P_RESET->PERI_RESET0 |= (uint32_t)SPI0_RST;

    hal_spi0_deviceoff();

    /// 开启SPI总线，SCK在空闲状态时被设置为高电平，SPI工作在Master模式，表示高位（MSB）将在SCK的第一个动态沿之前半个周期被发送，Fspi = Fsys/16，在主/从模式中打开SPI_CS输入
    HW_SPI0->CR  = 0;
    HW_SPI0->SSN = TRUE;
    HW_SPI0->CR  = (uint32_t)SpiMskMaster |      // 配置为主机模式
                 (uint32_t)SpiClkMskDiv8 |      // 波特率：PCLK/8 
                 (uint32_t)SpiMskcpollow |      // 极性为低
                 (uint32_t)SpiMskCphafirst |    // 第一边沿采样
                 (uint32_t)0x40;
    HW_SPI0->STAT = 0x00;
}

/* @brief Close SPI0总线 */
void hal_spi0_close(void)
{
    hal_spi0_deviceoff();

    M0P_SYSCTRL->PERI_CLKEN0 |= (1UL << SPI0_CLK);    // 打开SPI0时钟   // 关闭SPI0时钟
    HW_SPI0->CR = HW_SPI0->CR | 0xBF;                  // SPI模块使能控制 bit6 spen = 0(禁止)
}

void hal_spi0_deviceon(void)
{
    HW_SPI0->SSN = FALSE;    // SPI CS拉低
}

void hal_spi0_deviceoff(void)
{
    uint16_t time_out;

    time_out = 10000;
    while(FALSE == HW_SPI0->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    time_out = 10000;
    while(TRUE == HW_SPI0->STAT_f.BUSY && time_out--) { ; }    // 等待SPI总线空闲

    HW_SPI0->SSN = TRUE;    // SPI CS拉高
}

/**
 * @brief  SPI1总线向从设备发送一个字符并收回一个字符.
 * @param  [in]  ch-发送字符
 * @retval 回收字符
 */
char hal_spi1_trans(uint8_t ch, uint8_t mod)
{
    uint16_t time_out = 10000;

    while(FALSE == HW_SPI1->STAT_f.TXE && time_out--) {}    // 等待发送缓冲区空
    HW_SPI1->DATA = ch;
    time_out     = 10000;
    while(FALSE == HW_SPI1->STAT_f.RXNE && time_out--) {}    // 等待接受缓冲区非空
    ch = HW_SPI1->DATA;
    
    return ch;
}

/** @brief Open SPI1总线
 * @param  [in]  kbps-总线速率值, 单位3000 KBPS
 * @retval None
 */
void hal_spi1_open(uint16_t kbps)
{
    M0P_SYSCTRL->PERI_CLKEN0 |= (1UL << SPI1_CLK);    // 打开SPI0时钟
    M0P_RESET->PERI_RESET0 &= ~(uint32_t)SPI1_RST;    // 复位spi模块
    M0P_RESET->PERI_RESET0 |= (uint32_t)SPI1_RST;

    hal_spi1_deviceoff();

    /// 开启SPI总线，SCK在空闲状态时被设置为高电平，SPI工作在Master模式，表示高位（MSB）将在SCK的第一个动态沿之前半个周期被发送，Fspi = Fsys/16，在主/从模式中打开SPI_CS输入
    HW_SPI1->CR  = 0;
    HW_SPI1->SSN = TRUE;
    HW_SPI1->CR  = (uint32_t)SpiMskMaster |      // 配置为主机模式
                 (uint32_t)SpiClkMskDiv8 |      // 波特率：PCLK/8 
                 (uint32_t)SpiMskcpollow |      // 极性为低
                 (uint32_t)SpiMskCphasecond |    // 第二边沿采样
                 (uint32_t)0x40;
    HW_SPI1->STAT = 0x00;
}

/* @brief Close SPI1总线 */
void hal_spi1_close(void)
{
    hal_spi1_deviceoff();

    M0P_SYSCTRL->PERI_CLKEN0 |= (1UL << SPI1_CLK);    // 打开SPI1时钟   // 关闭SPI1时钟
    HW_SPI1->CR = HW_SPI1->CR | 0xBF;                  // SPI模块使能控制 bit6 spen = 0(禁止)
}

void hal_spi1_deviceon(void)
{
    HW_SPI1->SSN = FALSE;    // SPI CS拉低
}

void hal_spi1_deviceoff(void)
{
    uint16_t time_out;

    time_out = 10000;
    while(FALSE == HW_SPI1->STAT_f.TXE && time_out--) { ; }    // 等待发送缓冲区空
    time_out = 10000;
    while(TRUE == HW_SPI1->STAT_f.BUSY && time_out--) { ; }    // 等待SPI总线空闲

    HW_SPI1->SSN = TRUE;    // SPI CS拉高
}

/** @} */
/** @} */
/** @} */
