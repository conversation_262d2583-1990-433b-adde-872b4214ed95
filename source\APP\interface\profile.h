/********************************************************************************
  * @file    profile.h
  * <AUTHOR> @date    2024
  * @brief   事件，曲线，冻结捕获
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

#ifndef __PROFILE_H__
#define __PROFILE_H__

#include "typedef.h"
#include "event.h"
#include "datastore.h"
#include "loadcurve.h"

#define PRF_DATA_NORMAL               0x00  // 正常数据捕获
#define PRF_DATA_INVALID              0x01  // 曲线捕获无效数据
#define PRF_OBJ_DEF_RECOVER_EN        0x00  // 默认升级不直接恢复曲线默认值
#define MAX_CAPTURE_DATA_LEN          1200  // 最大捕获数据总长度(修改节假日事件1126字节)

typedef struct lc_get_struct
{
    uint32_t time;       // 起始时间
    uint8_t  type;       // 读取类型 0最早N条记录 1指定时间段起始N条记录，2最新1条记录 
    uint8_t  get_num;    // 预读取记录数
    uint16_t pointer;    // 读取起始位置
    uint16_t valid_num;  // 有效记录数
    uint16_t index;      // 记录索引
    uint16_t size;       // 记录大小 
    uint8_t  frame_num;  // 帧数
}lc_get_s;


typedef struct
{
    uint8_t offset;
    uint8_t size;
}evt_member_s;

struct profile_s
{
    uint16_t (*evt1_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt2_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt3_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt4_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt5_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt6_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt7_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt8_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
    uint16_t (*evt_cov_capture)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);
#if LC1_ENABLE
    uint16_t (*lc1_capture)(uint8_t *buf, uint32_t time, uint8_t time_status, uint8_t staus);
#endif
#if LC2_ENABLE
    uint16_t (*lc2_capture)(uint8_t *buf, uint32_t time, uint8_t time_status, uint8_t staus);
#endif
#if LC1_ENABLE || LC2_ENABLE
    bool (*lc_fetch)(NVM_LOG_t idx, lc_get_s* res, uint8_t* buf, uint16_t buf_size);
#endif
#if LC1_ENABLE
    uint8_t (*lc1_fetch_for_376)(lc_get_s* res, uint8_t* buf, lc1_member_t member, uint8_t typ);
#endif
    /// @brief 获取每条记录实际占用空间大小
    /// @param nvm_idx 
    /// @return 
    evt_member_s (*rcd_size_get)(uint8_t nvm_idx, uint8_t member);

    uint16_t (*evt_get)(uint8_t *buf, uint16_t point, evt_type_t evt_type, uint8_t member);
    uint32_t (*evt_cnt_get)(evt_type_t evt_type);
};

extern const struct profile_s profile;

 #endif /* __PROFILE_H__ */
