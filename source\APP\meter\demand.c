/********************************************************************************
  * @file    demand.c
  * <AUTHOR> @date    2024
  * @brief   需量计算
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "app.h"
#include "demand.h"
#include "energy.h"
#include "tariff.h"

#define DEMAND_CRC16                0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(DEMAND_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(DEMAND_CRC16, struct, len)

#define DEMAND_PARA_ADDR     nvm_addr(NVM_DEMAND_PARA)
#define MD_ADDR              nvm_addr(NVM_DEMAND_DATA)

/* Private constants ---------------------------------------------------------*/
// demand_type_t 需量类型获取的电能类型数组
static const ENERGY_TYPE dd_energy_type[] =
{
    TYPE_ENERGY_ADD_ACT,
#if DEMAND_SUB_ACT_ENABLE
    TYPE_ENERGY_SUB_ACT,
#endif
#if DEMAND_ADD_REA_ENABLE
    TYPE_ENERGY_ADD_REA,
#endif
#if DEMAND_ADD_APP_ENABLE
    TYPE_ENERGY_ADD_APP,
#endif
#if DEMAND_POS_ACT_ENABLE
    TYPE_ENERGY_POS_ACT,
#endif
#if DEMAND_NEG_ACT_ENABLE
    TYPE_ENERGY_NEG_ACT,
#endif
#if DEMAND_POS_REA_ENABLE
    TYPE_ENERGY_POS_REA,
#endif
#if DEMAND_NEG_REA_ENABLE
    TYPE_ENERGY_NEG_REA,
#endif
#if DEMAND_Qx_REA_ENABLE
    TYPE_ENERGY_Q1_REA,
    TYPE_ENERGY_Q2_REA,
    TYPE_ENERGY_Q3_REA,
    TYPE_ENERGY_Q4_REA,
#endif
#if DEMAND_POS_APP_ENABLE
    TYPE_ENERGY_POS_APP,
#endif
#if DEMAND_NEG_APP_ENABLE
    TYPE_ENERGY_NEG_APP,
#endif
};

extern const demand_para_s  demand_default_para; ///默认参数
static const demand_para_s* demand_running_para; ///运行参数，指向codeflash

/* Private variables ---------------------------------------------------------*/
static demand_block_s dd_block[DEMAND_TYPE_NUM]; // 需量数据块
static MD_STUS dd_out_stus; // 需量事件输出
static uint8_t dd_tariff_rate;

/* Private function prototypes -----------------------------------------------*/
void demand_blk_value_clr(demand_type_t type);

/* Private functions ---------------------------------------------------------*/
/// @brief 参数计算检验后存入NVM中
/// @param ofst
/// @param val
/// @param len
/// @return
static bool demand_para_checkin(uint16_t ofst, const void* val, uint16_t len)
{
    demand_para_s para;
    if(ofst != 0) memcpy(&para, demand_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(demand_para_s));
    demand_running_para = (const demand_para_s*)DEMAND_PARA_ADDR;
    return nvm.write((uint32_t)demand_running_para, &para, sizeof(para));
}

/// @brief 参数指针初始化并检验参数
/// @param
static void demand_para_checkout(void)
{
    demand_running_para = (const demand_para_s*)DEMAND_PARA_ADDR;
    if(CRC16_CHK(demand_running_para, sizeof(demand_para_s)) == false)
    {
        demand_running_para = &demand_default_para;
    }
    // else
    // {
    //     if((demand_running_para->period < demand_running_para->slip_period) ||
    //        (demand_running_para->period % demand_running_para->slip_period != 0) ||
    //        ((demand_running_para->period / demand_running_para->slip_period) > DEMAND_MAX_SLIP_NUM)||
    //        (demand_running_para->period > 60)||
    //        (demand_running_para->slip_period > 5))
    //     {
    //         demand_running_para = &demand_default_para;
    //     }
    // }
}

/// @brief 数据计算检验后存入NVM
/// @param wr_nvm 是否写NVM
/// @return
static bool md_data_checkin(uint8_t type, MD_s* md)
{
    uint32_t addr = MD_ADDR + member_offset(MD_block_s, md[type]);
    CRC16_CAL(md, sizeof(MD_s));
    return nvm.write(addr, md, sizeof(MD_s));
}

/// @brief 数据初始化并检验
/// @param
static void md_data_checkout(uint8_t type, MD_s* md)
{
    uint32_t addr = MD_ADDR + member_offset(MD_block_s, md[type]);
    nvm.read(addr, md, sizeof(MD_s));
    if(CRC16_CHK(md, sizeof(MD_s)) == false)
    {
        memset(md, 0, sizeof(MD_s));
    }
}

/// @brief 需量计算
/// @param rate 
static void demand_calc(uint8_t rate)
{
    uint32_t window = demand_running_para->period * 60;
    uint32_t slip_period = demand_running_para->slip_period * 60;
    uint8_t  number_of_slips = (uint8_t)(demand_running_para->period / demand_running_para->slip_period); // 需量滑差数量

    for(uint8_t type = 0; type < DEMAND_TYPE_NUM; type++)
    {
        demand_block_s* db = &dd_block[type];
        
        /* 1. 实时累加秒电能脉冲增量用于计算当前需量 */
        int16_t sec_pulse = energy.phs_sec_pulse_get(T_PHASE, dd_energy_type[type]);

        /* 11. 累加每秒电能脉冲 */
        db->period[0].value[db->slip_idx] += sec_pulse;
        if(dd_tariff_rate <= DEMAND_TARIFF_RATE_NUM)
        {
            db->period[dd_tariff_rate].value[db->slip_idx] += sec_pulse;
        }

        /* 12. 计算各费率下的当前需量 */
        for(uint8_t i = 0; i <= DEMAND_TARIFF_RATE_NUM; i++)
        {
            int32_t total_pulse = 0;
            for(uint8_t j = 0; j < number_of_slips; j++)
            {
                total_pulse += db->period[i].value[j];
            }
            db->cur_avg_val[i] = (int32_t)(total_pulse * (3600 * 1000.0 / METER_CONST / window) * 10); ///保留4位小数，所以要乘以10
            DBG_PRINTF(P_DEMAND, D, "\r\n demand_type : %d  -> cur_avg_val[%d]: %d",   type, i, db->cur_avg_val[i]); 
        }

        /* 2. 查询是否到了需量周期点 */
        if((mclock.datetime->u32datetime % slip_period) == 0) // 需量滑差周期到达
        {
            if(db->stus.dd_all_ready) {db->stus.md_capture_f = true;}  // 当第一个需量周期完成后，按滑差间隔开始最大需量记录
            if(++db->slip_idx == number_of_slips) // 需量周期完成
            {
                db->stus.dd_all_ready = true;   // 第一个需量周期完成
            }

            if(db->stus.dd_all_ready)
            {
                db->stus.dd_capture = true;
            }

            db->stus.dd_next_slip = true;

            /* 清除最早的滑差周期数据缓存 */
            db->slip_idx %= number_of_slips;
            for(uint8_t i = 0; i <= DEMAND_TARIFF_RATE_NUM; i++)
            {
                db->period[i].value[db->slip_idx] = 0;
            }
        }

        /* 3. 一个完整的滑差周期计算 */
        if(db->stus.dd_capture)
        {
            db->stus.dd_capture = false;
            db->capture_time = *mclock.datetime;
            for(uint8_t i = 0; i <= DEMAND_TARIFF_RATE_NUM; i++)
            {
                db->lst_avg_val[i] = db->cur_avg_val[i];
                DBG_PRINTF(P_DEMAND, D, "\r\n demand_type : %d  ------> lst_avg_val[%d]: %d",   type, i, db->lst_avg_val[i]); 
            }
            if(db->stus.dd_start)
            {
                mclock.seconds_to_calendar(&db->start_time.cale, mclock.datetime->u32datetime - demand_running_para->period);
                db->start_time.stus.value = mclock.datetime->stus.value;
            }

            if(db->stus.md_capture_f) {db->stus.md_capture = true;}
        }

        /* 4. 捕获最大需量 */
        if(db->stus.md_capture)
        {
            MD_s md;
            bool b_save_md = false;

            /* 刷新当前最大需量 */
            md_data_checkout(type, &md);
            for(uint8_t i = 0; i <= DEMAND_TARIFF_RATE_NUM; i++)
            {
                if(db->lst_avg_val[i] > md.reg[i].value)
                {
                    md.reg[i].value = db->lst_avg_val[i];
                    md.reg[i].capture_time = db->capture_time;
                    DBG_PRINTF(P_DEMAND, D, "\r\n demand_type : %d  max demad ------> value[%d]: %d  ",   type, i, md.reg[i].value); 
                    DBG_PRINTF(P_DEMAND, D, " *** capture_time: %d-%d-%d %d:%d:%d",   md.reg[i].capture_time.year, md.reg[i].capture_time.month, md.reg[i].capture_time.day,
                    md.reg[i].capture_time.hour, md.reg[i].capture_time.minute, md.reg[i].capture_time.second); 
                    b_save_md = true;
                }
            }
            if(b_save_md)
            {
                md_data_checkin(type, &md);
            }
            db->stus.md_capture = false;
        }
    }

    dd_tariff_rate = rate;
}


/* Public functions ----------------------------------------------------------*/
/// @brief 需量模块初始化
void demand_init(void)
{
    demand_para_checkout();
    demand_blk_value_clr(DEMAND_TYPE_NUM);
    dd_tariff_rate = tariff.cur_rate_get();
}

/// @brief 需量模块秒任务
void demand_second_run(void)
{
    /* 1. 检出需量参数 */
    demand_para_checkout();

    /* 2. 更改时钟清空需量滑差缓冲 */
    if(mclock.state_query(STUS_CLOCK_BC_EVENT | STUS_CLOCK_SHIFT_EVENT)) 
    {
        demand_blk_value_clr(DEMAND_TYPE_NUM);
    }

    /* 3. 需量计算 */
    demand_calc(tariff.cur_rate_get());

}

/// @brief 获取需量各状态
/// @param state  预获取的状态位
/// @return true - 触发   false - 未触发
bool demand_state_query(MD_STUS state)
{
    return boolof(dd_out_stus & state);
}

/// @brief 需量模块状态清除
/// @param  
void demand_state_clr(void)
{
    dd_out_stus = 0;
}

/// @brief 需量缓冲清除
void demand_blk_value_clr(demand_type_t type)
{
    uint8_t num;

    if(type == DEMAND_TYPE_NUM) // 全类型清除
    {
        num = eleof(dd_energy_type);
        type = (demand_type_t)0;
    }
    else
    {
        num = type + 1; // 按类型清除
    }

    for(; type < num; type++)
    {
        memset(&dd_block[type], 0, sizeof(demand_block_s));
        mclock.invalid_set(&dd_block[type].capture_time);
        dd_block[type].start_time = *mclock.datetime;
        dd_block[type].stus.dd_start = true;
    }
}

/// @brief 清除最大需量记录
void demand_max_value_clr(void)
{
    nvm.write(MD_ADDR, NULL, sizeof(MD_block_s));
}

/// @brief 需量模块复位
/// @param type 复位类型
/// @return 
void demand_reset(uint8_t type)
{
    /* 恢复默认参数 */
    if(type & SYS_PARA_RESET)
    {
        demand_para_checkin(0, &demand_default_para, sizeof(demand_para_s));
    }

    /* 清除所有需量数据 */
    demand_blk_value_clr(DEMAND_TYPE_NUM);
    dd_tariff_rate = tariff.cur_rate_get();

    /* 清除所有最大需量记录 */
    if(type & SYS_DATA_RESET)
    {
        if(type != SYS_GLOBAL_RESET) demand_max_value_clr();
        dd_out_stus = 0;
    }
}

/// @brief 获取需量参数
const demand_para_s* demand_para_get(void)
{
    return demand_running_para;
}

/// @brief 设置需量参数
bool demand_para_set(uint16_t ofst, const void* val)
{
    switch(ofst)
    {
        case member_offset(demand_para_s, slip_period): //滑差周期
        {
            uint16_t slip_period = (uint16_t)(*(uint8_t*)val);
            if(slip_period != 1 && slip_period != 2 && slip_period != 3 && slip_period != 5) return false;
            dd_out_stus |= STUS_MD_PRG_PERIOD_NUM;
            demand_para_checkin(ofst, &slip_period, 2);
            break;
        }

        case member_offset(demand_para_s, period):       //需量周期
        {
            uint16_t period = (uint16_t)(*(uint8_t*)val);
            if(period != 5 && period != 10 && period != 15 && period != 30 && period != 60) return false;
            dd_out_stus |= STUS_MD_PRG_PERIOD;
            demand_para_checkin(ofst, &period, 2);
            break;
        }

        default: return false;
    }

    demand_blk_value_clr(DEMAND_TYPE_NUM); // 设置需量参数, 则清空需量数据块
    return true;
}

/// @brief 获取需量数据块
demand_block_s* demand_blk_value_get(demand_type_t type)
{
    return &dd_block[type];
}

/// @brief 获取最大需量
/// @param type 最大需量的类型
/// @param rate 0 总费率, 1~? 分费率
/// @return 最大需量及捕获时间
MD_reg_s demand_max_value_get(demand_type_t type, uint8_t rate)
{
    MD_s md;
    md_data_checkout(type, &md);
    if(md.reg[rate].value == 0)
    {
        // mclock.invalid_set(&md.reg[rate].capture_time);
        memset(&md.reg[rate].capture_time, 0x00, sizeof(clock_s));
    }
    return md.reg[rate];
}


/// @brief 声明需量模块对象
const struct demand_s demand =
{
    .reset                      = demand_reset,
    .state_query                = demand_state_query,
    .state_clr                  = demand_state_clr,
    .para_get                   = demand_para_get,
    .para_set                   = demand_para_set,
    .blk_value_get              = demand_blk_value_get,
    .blk_value_clr              = demand_blk_value_clr,
    .max_value_get              = demand_max_value_get,
    .max_value_clr              = demand_max_value_clr,
};

/// @brief 声明需量模块任务接口
const struct app_task_t demand_task =
{
    .init                       = demand_init,
    .second_run                 = demand_second_run,
};

    