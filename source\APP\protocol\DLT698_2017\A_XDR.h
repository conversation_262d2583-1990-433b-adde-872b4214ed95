/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      A_XDR.c
 *    Describe:      A-XDR 编码规则
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#ifndef __A_XDR_H__
#define __A_XDR_H__

#include "DLT_698_typedef.h"

uint8_t  a_xdr_encode_obj(uint8_t * out, uint16_t ic, const uint8_t * obis, uint8_t  attr, uint16_t data_idx);
uint8_t  a_xdr_encode_null_sorta_xdr_encode_null_sort(uint8_t * out);
uint8_t  a_xdr_en_scaler_unit(uint8_t * out, int8_t scaler, class_unit_t unit);
uint8_t  a_xdr_en_asn1_len(uint8_t * out, uint16_t len);
uint8_t  a_xdr_de_asn1_len(uint16_t* out_len, const uint8_t * in);
uint16_t a_xdr_en_bit_string(uint8_t * out, const void* in, uint16_t len);
uint16_t a_xdr_encode_octet_string(uint8_t * out, const void* in, uint16_t len);
uint16_t EncodeVisibleString(uint8_t * out, const void* in, uint16_t len);
uint16_t a_xdr_en_data(uint8_t * out, DLT698_data_type_t asn_data_type, const void* in);
uint16_t a_xdr_de_data(uint8_t * in_out, uint16_t in_len);
uint16_t DataLenQuery(const uint8_t * in);
uint16_t DlmsDataTypeLenGet(uint8_t * in, uint8_t * encode_len);
uint16_t a_xdr_en_compact_array(uint8_t * in_out, uint16_t len, bool isheader);
uint16_t a_xdr_compact_array(uint8_t * in_out, uint16_t len, uint16_t max_buf_len);


#endif /* __A_XDR_H__ */

