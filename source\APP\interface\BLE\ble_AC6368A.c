/**
 ******************************************************************************
* @file    AC6368A.c
* <AUTHOR> @date    2024
* @brief   蓝牙透传芯片AC6368A驱动，本文件实现了AC6368A芯片的AT命令处理和透传功能。
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "pt.h"
#include "ble.h"
#include "bsp.h"
#include "debug.h"
#include "api.h"

/* Private typedef -----------------------------------------------------------*/
typedef enum
{
    BLE_INIT_STATUS,
    BLE_CFG_STATUS,           // 参数配置阶段
    BLE_TRANSPARENT_STATUS,   // 透传阶段
} BLE_STATUS;

typedef struct
{
    struct pt atpt, trypt;      ///子线程号
    uint16_t  msg_total_len;    ///接收消息总长度
    uint16_t  bufsize;          ///接收缓冲大小
    octstr_t  rxbuf;            ///接收器
    void     *param;            ///命令参数
    uint8_t   error_code;       ///错误码
    uint8_t   lock;             ///接收时，防止互斥
    bool      ret;
} ble_chn_t;

typedef struct
{
    struct pt  mpt;              ///子线程号
    BLE_STATUS work_status;      ///当前工作状态
    ble_chn_t *chn;              ///Command channel excute
} ble_sta_t;

typedef struct
{
    uint16_t (*request)(uint8_t *buf, void *param);
    bool     (*parse)(const uint8_t *buf, uint16_t len);
    uint8_t *cmd;
} ble_cmd_t;

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static struct pt pt_ble_process;  ///消息处理主线程号
static ble_chn_t ble_channel;
static ble_sta_t ble_state =
{
    .chn = &ble_channel,
    .work_status = BLE_INIT_STATUS,
};

static uint8_t com_rxtx_buf[256];   ///用于BLE内部串口收发缓冲 -> 需根据协议调整

/* Private constants ---------------------------------------------------------*/
static uint16_t create_general(uint8_t *buf, void *param);
static bool parse_general_resp(const uint8_t *buf, uint16_t len);
static uint16_t create_set_dn(uint8_t *buf, void *param);

const ble_cmd_t ble_cmd_list[] =
{
    {create_general,    parse_general_resp, "AT...\r\n"},       // 命令进入AT模式
    {create_set_dn,     parse_general_resp, NULL},              // 修改设备名为表号
    {create_general,    parse_general_resp, "AT+EXIT\r\n"},     // 命令退出AT模式
};

/* Private function prototypes -----------------------------------------------*/
/// @brief 生成通用AT命令
/// @param buf
/// @param param
/// @return
static uint16_t create_general(uint8_t *buf, void *param)
{
    char* cmd = (char*) param;
    uint16_t len = strlen(cmd);
    memcpy(buf, cmd, len);
    return len;
}

/// @brief 解析通用应答
/// @param buf
/// @param len
/// @return
static bool parse_general_resp(const uint8_t *buf, uint16_t len)
{
    if(strstr((char *)buf, "OK") != NULL) { return true; }
    return false;
}

/// @brief 设置device name
/// @param buf
/// @param param
/// @return
static uint16_t create_set_dn(uint8_t *buf, void *param)
{
    uint16_t len = 0;
    uint8_t  temp_buf[METER_SN_LEN ]; // 已经去掉长度 

    len = api.meter_sn_get(temp_buf);
    len = sprintf((char *)buf, "AT+NAME=YJ%02x%02x%02x%02x%02x%02x\r", 
                   temp_buf[5], temp_buf[4], temp_buf[3], temp_buf[2], temp_buf[1], temp_buf[0]);
    return len;
}

/// @brief PT线程初始化
/// @param
static void ble_pt_restart(void)
{
    PT_INIT(&ble_state.mpt);
    PT_INIT(&ble_state.chn->atpt);
    PT_INIT(&ble_state.chn->trypt);
}

/// @brief PT线程延时函数
/// @param pt
/// @param delay
/// @return
static char pt_delay(struct pt* pt, uint32 delay)
{
    static SwTimer_s ti;

    PT_BEGIN(pt);
    hal_timer.interval(&ti, delay);
    PT_WAIT_UNTIL(pt, hal_timer.expired(&ti));
    PT_END(pt);
}

/// @brief AT命令请求
/// @param chn
/// @param request
/// @return
static char at_command(ble_chn_t *chn, uint8_t request)
{
    static SwTimer_s tim;
    uint8_t result = 0;
    uint16_t len;

    PT_BEGIN(&chn->atpt);
    chn->ret = false;
    chn->param = ble_cmd_list[request].cmd;
    len = ble_cmd_list[request].request(com_rxtx_buf, chn->param);
    DBG_PRINTF(P_BLE, D, "AT command tx:");
    DBG_PRINTF(P_BLE, M, com_rxtx_buf, len);
    hal_uart.send(COM_BLE, com_rxtx_buf, len);
    //while(hal_uart.send_over_query(COM_BLE)) {}

    hal_timer.interval(&tim, 500);
    PT_WAIT_UNTIL(&chn->atpt, chn->lock == 0);
    chn->lock++;
    PT_WAIT_UNTIL(&chn->atpt, (len = hal_uart.recv(COM_BLE)) || hal_timer.expired(&tim));
    if(len != 0)
    {
        DBG_PRINTF(P_BLE, D, "AT command rx:");
        DBG_PRINTF(P_BLE, M, com_rxtx_buf, len);
        chn->ret = true;
        result = ble_cmd_list[request].parse(com_rxtx_buf, len); // result: 0-err, 1-true, >2 -继续接收
        chn->ret = result > 0 ? true : false;
    }
    else
    {
        chn->ret = false; // 超时
    }
    chn->lock--;
    PT_END(&chn->atpt);   ///AT命令请求结束
}

/// @brief 模块应用请求
/// @param chn
/// @param trytimes
/// @param delay
/// @param request
/// @return
static char module_request(ble_chn_t *chn, uint8_t trytimes, uint16_t delay, uint8_t request)
{
    static uint8_t repeat = 0;

    PT_BEGIN(&chn->trypt);
    do
    {
        PT_SPAWN(&chn->trypt, &chn->atpt, at_command(chn, request)); // 生成AT指令请求线程
        if(!chn->ret)
        {
            DBG_PRINTF(P_BLE, D, "\r\n>> err_Request = %d, repeat = %d <<\r\n", request, repeat);
            PT_WAIT_THREAD(&chn->trypt, pt_delay(&chn->atpt, delay));  // 超时后延时重试
        }
    }while(++repeat < trytimes && !chn->ret);
    repeat = 0;     /// 重置重试次数
    chn->error_code = chn->ret ? 0 : request;   /// 错误码

    PT_END(&chn->trypt);
}

/// @brief 蓝牙配置参数
/// @param state
/// @return
static char ble_configurate(ble_sta_t *state)
{
    static uint8_t i = 0;
    ble_chn_t* chn = state->chn;

    PT_BEGIN(&state->mpt);

    /// 复位模块 
    bsp.module_off(BLE_MOD);  // rst低电平有效
    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 1));     // RST有效信号宽度ns级
    bsp.module_on(BLE_MOD);   // 完成复位

    /// AT指令配置
    for(i = 0; i < eleof(ble_cmd_list); i++)
    {
        PT_WAIT_THREAD(&state->mpt, module_request(chn, 1, 100, i)); 
        //PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 1)); // 通信设置后短暂等待
        if(chn->error_code){ break; } /// 出错，退出配置流程 
    }
   // 退出AT模式，进入透传模式

    // bsp.module_off(INT_MOD);  // rst低电平有效
    // PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 1));     // RST有效信号宽度ns级
    // bsp.module_on(INT_MOD);   // 完成复位

    if(!chn->error_code)
    {
        state->work_status = BLE_TRANSPARENT_STATUS;
        PT_EXIT(&state->mpt);    ///完成初始化，退出配置线程
    }
    else state->work_status = BLE_CFG_STATUS;

    DBG_PRINTF(P_BLE, D, "\r\n>> err_InitAt code = %d <<", chn->error_code);
    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 2000)); // 出错，延时2s退出配置线程
    //ble_init();
    PT_END(&state->mpt);   ///初始化失败，结束配置线程
}

/// @brief 帧解析接收。未收完前一直返回0
/// @param com
/// @param func
/// @return
static uint16_t multi_frame_recv(void* para, const uint8_t* msg, uint16_t len)
{
    ble_chn_t* chn = (ble_chn_t*)para;
    if(chn->rxbuf.len + len < chn->bufsize) ///如果空间足够，则存入缓冲。否则丢弃
    {
        memcpy(chn->rxbuf.str + chn->rxbuf.len, msg, len);
        chn->rxbuf.len += len;
        if(chn->msg_total_len == 0) /// 接收到的第一个数据块。解析帧总长度
        {
            chn->msg_total_len = api.protocol_len_parse(chn->rxbuf.str, chn->rxbuf.len, PROTOCOL_645);  ///解析帧总长度
        }

        if(chn->msg_total_len != 0 && chn->rxbuf.len >= chn->msg_total_len) /// 接收到完整帧
        {
            return chn->rxbuf.len;
        }
    }
    return 0;
}

/// @brief 蓝牙透明传输
/// @param state
/// @return
static char ble_transparent(ble_sta_t *state)
{
    static SwTimer_s blk_timer;
    ble_chn_t* chn = state->chn;
    uint16_t len = hal_uart.frame_recv(COM_BLE, multi_frame_recv, chn);

    PT_BEGIN(&state->mpt);
    if(chn->rxbuf.len) hal_timer.interval(&blk_timer, 1000); //收到数据开启定时器
    PT_WAIT_UNTIL(&state->mpt, (len != 0) || (chn->rxbuf.len && hal_timer.expired(&blk_timer))); // 等待接收数据或者超时
    if(len != 0 || chn->rxbuf.len != 0)   /// 如果正确收到或者收到数据后超时，都认为接收完成
    {
        chn->ret = true;
        ble_state.chn->msg_total_len = 0;
        DBG_PRINTF(P_BLE, D, "BLE msg_total_len=%d, recieved:%d", chn->msg_total_len, chn->rxbuf.len);
        DBG_PRINTF(P_BLE, M, chn->rxbuf.str, chn->rxbuf.len);
    }
    PT_END(&state->mpt);
}

/// @brief BLE模块控制主线程处理
/// @param ble_main_thread
PT_THREAD(ble_main_thread(struct pt *pt))
{
    PT_BEGIN(pt);

    while(1)
    {
        if(ble_state.work_status == BLE_INIT_STATUS)
        {
            PT_WAIT_THREAD(pt, pt_delay(&ble_state.chn->atpt, 100)); // 无处理，等待外部调用初始化触发状态转移
            DBG_PRINTF(P_BLE, D, "Current BLE state: BLE_CFG_STATUS\r\n");
        }
        else if(ble_state.work_status == BLE_CFG_STATUS)
        {
            PT_SPAWN(pt, &ble_state.mpt, ble_configurate(&ble_state));    //
            DBG_PRINTF(P_BLE, D, "Current BLE state: BLE_TRANSPARENT_STATUS\r\n");
        }
        else
        {
            PT_SPAWN(pt, &ble_state.mpt, ble_transparent(&ble_state));     //
        }
    }

    PT_END(pt);
}


/* Public functions ----------------------------------------------------------*/
/// @brief BLE模块初始化
/// @param com
/// @param rxbuf
/// @param bufsize
void ble_init(uint8_t* rxbuf, uint16_t bufsize)
{
    // BLE_STATUS status;   // 状态变量

    PT_INIT(&pt_ble_process);

    // status = ble_state.work_status;
    memset(&ble_state,   0, sizeof(ble_sta_t));
    memset(&ble_channel, 0, sizeof(ble_chn_t));
    // ble_state.work_status = status;

    ble_state.chn            = &ble_channel;
    ble_state.chn->bufsize   = bufsize;
    ble_state.chn->rxbuf.str = rxbuf;
    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_38400BPS, com_rxtx_buf, sizeof(com_rxtx_buf));
    ble_pt_restart();

    // if(ble_state.work_status != BLE_TRANSPARENT_STATUS)   // 若已经进入透传，不再进入配置流程
    // {
        ble_state.work_status = BLE_CFG_STATUS;
    // }
}

/// @brief BLE模块接收数据处理
/// @param
/// @return
uint16_t ble_recv(void)
{
    uint16_t len;

    ble_main_thread(&pt_ble_process);
    if(ble_state.work_status != BLE_TRANSPARENT_STATUS) return 0; /// 如果还没进入透传mode，则直接返回0
    if(!ble_state.chn->ret) return 0; /// 如果没有收到数据，则直接返回0
    ble_state.chn->ret = false;
    len = ble_state.chn->rxbuf.len;
    ble_state.chn->rxbuf.len = 0;
    return  len;
}

/// @brief BLE模块发送数据处理
/// @param msg
/// @param len
void ble_send(uint8_t *msg, uint16_t len)
{
    hal_uart.send(COM_BLE, msg, len);
}

/// @brief BLE模块发送数据查询是否完成
/// @param
/// @return
bool ble_send_over_query(void)
{
    return hal_uart.send_over_query(COM_BLE);
}

//end of file
