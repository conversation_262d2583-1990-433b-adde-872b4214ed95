/**
 ******************************************************************************
 * @file    QGWD10376.h
 * <AUTHOR> @date    2025
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __QGWD10376_H
#define __QGWD10376_H

#include "typedef.h"

#define HEAD_FE_NUM 0    // 帧头前导码个数: 0 - 4

// 当启动标识位 PRM=1 时，功能码定义
// 0 — 备用
// 1  发送/确认         复位命令
// 2～3 — 备用
// 4  发送/无回答       用户数据
// 5～8 — 备用
// 9  请求/响应帧       链路测试
// 10 请求/响应帧       请求 1 级数据
// 11 请求/响应帧       请求 2 级数据
// 12～15 — 备用
typedef enum prm1_fun_enum
{
    REQ_FUN_REV0            = 0x00,    ///< 备用
    REQ_FUN_RESET           = 0x01,    ///< 复位命令
    REQ_FUN_REV2            = 0x02,    ///< 备用
    REQ_FUN_REV3            = 0x03,    ///< 备用
    REQ_FUN_USER_DATA       = 0x04,    ///< 用户数据
    REQ_FUN_REV5            = 0x05,    ///< 备用
    REQ_FUN_REV6            = 0x06,    ///< 备用
    REQ_FUN_REV7            = 0x07,    ///< 备用
    REQ_FUN_REV8            = 0x08,    ///< 备用
    REQ_FUN_LINK_TEST       = 0x09,    ///< 链路测试
    REQ_FUN_REQ_CLASS1_DATA = 0x0A,    ///< 请求 1 级数据
    REQ_FUN_REQ_CLASS2_DATA = 0x0B,    ///< 请求 2 级数据
    REQ_FUN_REV12           = 0x0C,    ///< 备用
} prm1_fun_t;

// 当启动标识位 PRM=0 时，功能码定义
// 0 确认 认可
// 1～7 — 备用
// 8 响应帧 用户数据
// 9 响应帧 否认：无所召唤的数据
// 10 — 备用
// 11 响应帧 链路状态
// 12～15 — 备用
typedef enum prm0_fun_enum
{
    RSP_FUN_ACK         = 0x00,    ///< 确认 认可
    RSP_FUN_REV1        = 0x01,    ///< 备用
    RSP_FUN_REV2        = 0x02,    ///< 备用
    RSP_FUN_REV3        = 0x03,    ///< 备用
    RSP_FUN_REV4        = 0x03,    ///< 备用
    RSP_FUN_REV5        = 0x05,    ///< 备用
    RSP_FUN_REV6        = 0x06,    ///< 备用
    RSP_FUN_REV7        = 0x07,    ///< 备用
    RSP_FUN_USER_DATA   = 0x08,    ///< 响应帧 用户数据
    RSP_FUN_NO_DATA     = 0x09,    ///< 响应帧 否认：无所召唤的数据
    RSP_FUN_REV10       = 0x0A,    ///< 备用
    RSP_FUN_LINK_STATUS = 0x0B,    ///< 响应帧 链路状态
} prm0_fun_t;

typedef enum gwd376_addr_type_enum
{
    ADDR_NO_MATCH = 0,    ///< 地址不匹配
    ADDR_MATCH,           ///< 地址匹配
    ADDR_BROADCAST        ///< 广播地址
} addr_type_t;

// ERR 说明
// 0 正确
// 1 出错
// 2 保留
// 3-10 备用
// 11 响应超时
// 12 任务未执行
// 13 任务未配置
// 14 电能表模块有响应，电能表没有响应
// 15 信道忙
typedef enum rsp_err_enum
{
    ACK_RIGHT = 0,                     ///< 正确
    ACK_ERR,                           ///< 错误
    ACK_TIMEOUT             = 11,      ///< 超时
    ACK_TASK_NOT_EXECUTED   = 12,      ///< 任务未执行
    ACK_TASK_NOT_CONFIGURED = 13,      ///< 任务未配置
    ACK_METER_RESP_ERR      = 14,      ///< 电能表模块有响应，电能表没有响应
    ACK_CHANNEL_BUSY        = 15,      ///< 信道忙
    ACK_NO_ERR              = 0xFE,    ///< 无错误
    ACK_NO_RSP              = 0xFF,    ///< 无应答
} rsp_err_t;

/// @brief 应用层功能码AFN定义
typedef enum apdu_afn_enum
{
    AFN_VERIFY            = 0x00,    ///< 验证
    AFN_RESET             = 0x01,    ///< 复位
    AFN_LINK_TEST         = 0x02,    ///< 链路测试
    AFN_RELAY_CMD         = 0x03,    ///< 中继站命令
    AFN_SET_PARAM         = 0x04,    ///< 设置参数
    AFN_CTRL_CMD          = 0x05,    ///< 控制命令q
    AFN_AUTH_KEY          = 0x06,    ///< 身份认证及密钥协商
    AFN_RESERVED          = 0x07,    ///< 备用
    AFN_REQ_ACTIVE_REPORT = 0x08,    ///< 请求被级联终端主动上报
    AFN_REQ_TERMINAL_CFG  = 0x09,    ///< 请求终端配置
    AFN_QUERY_PARAM       = 0x0A,    ///< 查询参数
    AFN_REQ_TASK_DATA     = 0x0B,    ///< 请求任务数据
    AFN_REQ_CLASS1_DATA   = 0x0C,    ///< 请求 1 类数据（实时数据）
    AFN_REQ_CLASS2_DATA   = 0x0D,    ///< 请求 2 类数据（历史数据）
    AFN_REQ_CLASS3_DATA   = 0x0E,    ///< 请求 3 类数据（事件数据）
    AFN_FILE_TRANSFER     = 0x0F,    ///< 文件传输
    AFN_DATA_FORWARD      = 0x10,    ///< 数据转发
    AFN_RESERVED_11
} apdu_afn_t;

typedef enum event_erc_enum
{
    erc_1_version_change = 0x01,    ///< 版本变更事
    erc_2_loss_param     = 0x02,    ///< 丢失参数事件
    erc_3_change_param   = 0x03,    ///< 参数变更事件
    erc_4_status_change  = 0x04,    ///< 状态变更事件
    erc_5_remote_control = 0x05,    ///< 远程控制事件
    erc_6_local_control  = 0x06,    ///< 本地控制事件
    erc_7_energy_control = 0x07,    ///< 能量控制事件
    erc_14_pwr_on_off    = 0x0E,    ///< 上电/掉电事件
    erc_37_meter_open    = 0x25,    ///< 电能表开盖事件
    erc_38_terminal_open = 0x26,    ///< 电能表端盖开盖事件
    erc_41_time_sync     = 0x29,    ///< 时间同步事件
    erc_45_battery_low   = 0x2D,    ///< 电池低电事件
} erc_code_t;

typedef enum p376_frame_enum
{
    HEAD = 0,    ///< 帧头
    LENL,        ///< 帧长度低字节
    LENH,        ///< 帧长度高字节
    LENL2,       ///< 帧长度低字节2
    LENH2,       ///< 帧长度高字节2
    HEAD2,       ///< 帧头2
    CTRL,        ///< 控制域C
    ADR1L,       ///< 地址域A1 L
    ADR1H,       ///< 地址域A1 H
    ADR2L,       ///< 地址域A2 L
    ADR2H,       ///< 地址域A2 H
    ADR3,        ///< 地址域A3
    APDU,        ///< 应用层数据
} p376_frame_t;

typedef enum link_type_enum
{
    LINK_SLAVE_STATION = 0,    ///< 从动站
    LINK_START_STATION = 1,    ///< 启动站
} link_type_t;

typedef enum afn2_fn_enum
{
    afn2_f1p0_login = 1,    // 登录 F1 p0
    afn2_f2p0_logout,       // 登出 F2 p0
    afn2_f3p0_heartbeat     // 心跳 F3 p0
} afn2_fn_t;

typedef union apdu_seq_struct
{
    uint8_t u8;
    struct
    {
        uint8_t pseq_rseq : 4;    ///< PSEQ/RSEQ
        uint8_t con : 1;          ///< CON 1- 需要确认，0 - 不需要确认
        uint8_t fin : 1;          ///< FIN 1 - 最后一个分包，0 - 不是最后一个分包
        uint8_t fir : 1;          ///< FIR 1 - 第一个分包，0 - 不是第一个分包
        uint8_t tpv : 1;          ///< TPV 1 - 有时间戳，0 - 无时间戳
    };
} apdu_seq_s;

typedef union ctrl_struct
{
    uint8_t c;
    struct
    {
        uint8_t fun : 4;        ///< 功能码 prm1_fun_t prm0_fun_t
        uint8_t fcv : 1;        ///< FCV标志位
        uint8_t fcb_acd : 1;    ///< FCB/ACD标志位
        uint8_t prm : 1;        ///< PRM标志位
        uint8_t dir : 1;        ///< DIR标志位
    };
} ctrl_s;

typedef struct last_request_struct
{
    ctrl_s     ctrl;    ///< 控制域
    apdu_afn_t afn;     ///< 应用层功能码
    prm1_fun_t typ;     ///< 请求类型
    uint16_t   pn;      ///< 信息点号
    uint8_t    fn;      ///< 功能码
} last_request_s;

typedef struct req_obj_struct
{
    last_request_s last;    ///< 最后一次请求的参数

    uint8_t    *com_buf;     ///< 接收数据帧缓冲区
    uint8_t    *req_apdu;    ///< 应用层数据指针
    uint16_t    apdu_len;    ///< 应用层数据长度
    uint16_t    pn;          ///< 信息点号
    uint16_t    son_pn;      ///<
    uint8_t     msa;         ///<
    uint8_t     fn;          ///< 功能码
    uint8_t     son_fn;      ///< 子功能码
    apdu_afn_t  afn;         ///< 功能码 应用层功能码
    addr_type_t addr;        ///< 地址域
    apdu_seq_s  seq;         ///< 帧序号
    ctrl_s      ctrl;        ///< 控制域
    uint8_t     verify;      ///< 验证标志位 1-需要验证，0-不需要验证
    uint8_t     info;        ///< 可以作为请求事件的ERC码等
    bool        ack;         ///< 是否需要应答
} req_obj_s;

typedef struct rsp_obj_struct
{
    uint8_t   *rsp_buf;     ///< 应用层响应数据缓冲区
    uint8_t   *apdu;        ///< 应用层数据指针，单次
    uint16_t   rsp_len;     ///< 应用层数据单次获取长度
    uint16_t   tlen;        ///< 总长度
    uint8_t    link_fun;    ///< 链路功能码
    apdu_seq_s seq;         ///< 帧序号
    rsp_err_t  err;         ///< 错误码
} rsp_obj_s;

typedef struct gdw376_table_struct
{
    rsp_err_t (*reset)(req_obj_s *req, rsp_obj_s *rsp);     ///< 复位函数
    rsp_err_t (*verify)(req_obj_s *req, rsp_obj_s *rsp);    ///< 验证函数
    rsp_err_t (*get)(req_obj_s *req, rsp_obj_s *rsp);       ///< 获取函数
    rsp_err_t (*set)(req_obj_s *req, rsp_obj_s *rsp);       ///< 设置函数
    apdu_afn_t afn;                                         ///< 功能码
} gdw376_table_s;

struct gdw376_s
{
    /// @brief 初始化
    void (*init)(uint8_t chn, uint8_t *buff);
    /// @brief 协议处理，注意数据域长度不超过 DCU_DATA_BUF_SIZE ! ! !
    uint16_t (*msg_process)(uint8_t chn, uint8_t *ack, uint16_t len);
    /// @brief 链路测试帧获取
    /// @param buf 数据缓冲区
    /// @param typ 1表示登录，0表示心跳
    /// @return 数据长度
    uint16_t (*get_linktest_frame)(uint8_t *buf, uint8_t typ);

    /// @brief 获取停电上报数据
    uint16_t (*get_lastgasp_frame)(uint8_t *buf);

    /// @brief 透明转发数据帧
    uint16_t (*transparent_forward)(uint8_t *buf, uint8_t *apdu, uint16_t len, uint8_t com);
};

extern const struct gdw376_s gdw376;

#endif    // __QGWD10376_H
//
