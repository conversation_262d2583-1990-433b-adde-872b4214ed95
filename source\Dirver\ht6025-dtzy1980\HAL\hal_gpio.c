/**
  ******************************************************************************
  * @file    hal_gpio.c
  * <AUTHOR> @version V1.0.0
  * @date    2024
  * @brief   本模块完成MCU GPIO的初始化过程.
  * @note    中断中操作GPIO只能用PTCLR，PTSET方式，不能操作其它寄存器！！！！！
  *          如果MCU没有PTCLR\PTSET类似寄存器操作IO口输出，应当使用bit band 方式操作GPIO输出。！！！！！！ 
  *
  ******************************************************************************
  *
  *
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* 数字IO口功能定义 */
#define GPIO_Mode_IN                 0x00             // GPIO输入 
#define GPIO_Mode_OUT                0x01             // GPIO输出
#define GPIO_Mode_AF_1               0x10             // 复用功能1 
#define GPIO_Mode_AF_2               0x11             // 复用功能2
#define GPIO_In_Up                   1                // 输入上拉
#define GPIO_In_Floating             0                // 输入浮空
#define GPIO_Out_PP                  0                // 输出推挽
#define GPIO_Out_OD                  1                // 输出开漏
#define GPIO_Output_H                1                // 输出高电平
#define GPIO_Output_L                0                // 输出低电平

#define GPIO_Mode_AFx                0x10             // GPIO复用功能被使用        

/// @brief 这里列出有唤醒(外部)中断功能的GPIO
#define PIN_EXINT0                   HT_GPIOA,5
#define PIN_EXINT1                   HT_GPIOA,6
#define PIN_EXINT2                   HT_GPIOA,7
#define PIN_EXINT3                   HT_GPIOA,8
#define PIN_EXINT4                   HT_GPIOA,9
#define PIN_EXINT5                   HT_GPIOA,10
#define PIN_EXINT6                   HT_GPIOA,11
#define PIN_EXINT7                   HT_GPIOC,2
#define PIN_EXINT8                   HT_GPIOH,2
#define PIN_EXINT9                   HT_GPIOH,3

#define EXTI_RIE                     0x0001   // 上升沿中断
#define EXTI_FIE                     0x0100   // 下升沿中断
#define EXTI_RFIE                    0x0101   // 双边沿中断


void hal_gpio_exti_set(uint8_t irq, void func(void)); 
void irq_handler_exti0(void);
void irq_handler_exti1(void);
void irq_handler_exti2(void);
void irq_handler_exti3(void);
void irq_handler_exti4(void);
void irq_handler_exti5(void);
void irq_handler_exti6(void);
void irq_handler_exti7(void);
void irq_handler_exti8(void);
void irq_handler_exti9(void);


/**
  * @brief  配置MCU GPIO功能类型. 利用宏函数定义形式，编译器会自动优化无效内容。另注意效率，复位过的状态则无须配置
  * @param  [in]  x-指定的GPIO
  * @param  [in]  m-GPIO_Mode_IOIN      // 输入
  *                 GPIO_Mode_IOOUT     // 输出
  *                 GPIO_Mode_AF1       // 外设功能 1
  *                 GPIO_Mode_AF2       // 外设功能 2
  * @param  [in]  p-GPIO_Input_Floating // 输入浮空 --- 设置输入, 复用功能时有效
  *                 GPIO_Input_Up       // 输入上拉 
  *                 GPIO_Output_OD      // 输出开漏 --- 设置输出时有效
  *                 GPIO_Output_PP      // 输出推挽
  * @param  [in]  l-GPIO_Output_OD      // 输出开漏 --- 设置复用功能,输入时有效
  *                 GPIO_Output_PP      // 输出推挽 
  *                 GPIO_Output_H       // 输出高电平 --- 设置输出时有效
  *                 GPIO_Output_L       // 输出低电平
  */
#define _halGpioConfig(port, pin, m, p, l)\
{\
	if(m & GPIO_Mode_AFx)\
    {\
        port->IOCFG |= ((uint32_t)(1 << pin));\
        if(m == GPIO_Mode_AF_1) {port->AFCFG &= ~((uint32_t)(1 << pin));}else{port->AFCFG |= (uint32_t)(1 << pin);}\
        if(p == GPIO_In_Up) {port->PTUP &= ~((uint32_t)(1 << pin));}else{port->PTUP |=  (uint32_t)(1 << pin);}\
        if((uint32_t)l == GPIO_Out_OD) {port->PTOD &=  ~((uint32_t)(1 << pin));}else{port->PTOD |=  (uint32_t)(1 << pin);}\
    }\
    else\
    {\
        port->IOCFG &= ~((uint32_t)(1 << pin));\
        if(m == GPIO_Mode_IN)\
        {\
            port->PTDIR &= ~((uint32_t)(1 << pin));\
            if(p == GPIO_In_Up) {port->PTUP &= ~((uint32_t)(1 << pin));}else{port->PTUP |=  (uint32_t)(1 << pin);}\
            if(l == GPIO_Out_OD) {port->PTOD &=  ~((uint32_t)(1 << pin));}else{port->PTOD |=  (uint32_t)(1 << pin);}\
        }\
        else\
        {\
            port->PTDIR |= (uint32_t)(1 << pin);\
            if(p == GPIO_Out_OD) {port->PTOD &=  ~((uint32_t)(1 << pin));}else{port->PTOD |=  (uint32_t)(1 << pin);}\
            if((uint32_t)l == GPIO_Output_H) {port->PTSET = ((uint32_t)(1 << pin));}else{port->PTCLR = (uint32_t)(1 << pin);}\
            port->PTUP |=  (uint32_t)(1 << pin);\
        }\
    }\
}

#define gpio_config(x, m, p, l)     _halGpioConfig(HAL_GPIO_PORT(x),HAL_GPIO_PIN(x), m, p, l)
#define halGpioptupen(x)            HAL_GPIO_PTUP_EN(HAL_GPIO_PORT(x),HAL_GPIO_PIN(x))

#define halGetPin(x)  (1<<HAL_GPIO_PIN(x))


/// @brief 配置外部中断. 利用宏函数定义形，编译器会自动优化无效内容。
/// @param x-GPIO
/// @param active_level-EXTI_RIE, EXTI_FIE, EXTI_RFIE
#define _gpio_exti_config(port,pin,rfie) \
{\
    if     (port == gpio_port(PIN_EXINT0) && pin == gpio_pin(PIN_EXINT0)) \
    { HT_INT->EXTIE &= ~(0x0101); HT_INT->EXTIE  |= (rfie << 0); HT_INT->PINFLT  |= (1 << 0); irq_vector_set(INT_EXTI0, irq_handler_exti0); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT0 | INT_EXTIF_FIF_INT0); NVIC_ClearPendingIRQ(EXTI0_IRQn); NVIC_SetPriority(EXTI0_IRQn, 3); NVIC_EnableIRQ(EXTI0_IRQn);} \
    else if(port == gpio_port(PIN_EXINT1) && pin == gpio_pin(PIN_EXINT1)) \
    { HT_INT->EXTIE &= ~(0x0202); HT_INT->EXTIE  |= (rfie << 1); HT_INT->PINFLT  |= (1 << 1); irq_vector_set(INT_EXTI1, irq_handler_exti1); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT1 | INT_EXTIF_FIF_INT1); NVIC_ClearPendingIRQ(EXTI1_IRQn); NVIC_SetPriority(EXTI1_IRQn, 3); NVIC_EnableIRQ(EXTI1_IRQn);} \
    else if(port == gpio_port(PIN_EXINT2) && pin == gpio_pin(PIN_EXINT2)) \
    { HT_INT->EXTIE &= ~(0x0404); HT_INT->EXTIE  |= (rfie << 2); HT_INT->PINFLT  |= (1 << 2); irq_vector_set(INT_EXTI2, irq_handler_exti2); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT2 | INT_EXTIF_FIF_INT2); NVIC_ClearPendingIRQ(EXTI2_IRQn); NVIC_SetPriority(EXTI2_IRQn, 3); NVIC_EnableIRQ(EXTI2_IRQn);} \
    else if(port == gpio_port(PIN_EXINT3) && pin == gpio_pin(PIN_EXINT3)) \
    { HT_INT->EXTIE &= ~(0x0808); HT_INT->EXTIE  |= (rfie << 3); HT_INT->PINFLT  |= (1 << 3); irq_vector_set(INT_EXTI3, irq_handler_exti3); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT3 | INT_EXTIF_FIF_INT3); NVIC_ClearPendingIRQ(EXTI3_IRQn); NVIC_SetPriority(EXTI3_IRQn, 3); NVIC_EnableIRQ(EXTI3_IRQn);} \
    else if(port == gpio_port(PIN_EXINT4) && pin == gpio_pin(PIN_EXINT4)) \
    { HT_INT->EXTIE &= ~(0x1010); HT_INT->EXTIE  |= (rfie << 4); HT_INT->PINFLT  |= (1 << 4); irq_vector_set(INT_EXTI4, irq_handler_exti4); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT4 | INT_EXTIF_FIF_INT4); NVIC_ClearPendingIRQ(EXTI4_IRQn); NVIC_SetPriority(EXTI4_IRQn, 3); NVIC_EnableIRQ(EXTI4_IRQn);} \
    else if(port == gpio_port(PIN_EXINT5) && pin == gpio_pin(PIN_EXINT5)) \
    { HT_INT->EXTIE &= ~(0x2020); HT_INT->EXTIE  |= (rfie << 5); HT_INT->PINFLT  |= (1 << 5); irq_vector_set(INT_EXTI5, irq_handler_exti5); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT5 | INT_EXTIF_FIF_INT5); NVIC_ClearPendingIRQ(EXTI5_IRQn); NVIC_SetPriority(EXTI5_IRQn, 3); NVIC_EnableIRQ(EXTI5_IRQn);} \
    else if(port == gpio_port(PIN_EXINT6) && pin == gpio_pin(PIN_EXINT6)) \
    { HT_INT->EXTIE &= ~(0x4040); HT_INT->EXTIE  |= (rfie << 6); HT_INT->PINFLT  |= (1 << 6); irq_vector_set(INT_EXTI6, irq_handler_exti6); \
      HT_INT->EXTIF &= ~(INT_EXTIF_RIF_INT6 | INT_EXTIF_FIF_INT6); NVIC_ClearPendingIRQ(EXTI6_IRQn); NVIC_SetPriority(EXTI6_IRQn, 3); NVIC_EnableIRQ(EXTI6_IRQn);} \
    else if(port == gpio_port(PIN_EXINT7) && pin == gpio_pin(PIN_EXINT7)) \
    { HT_INT->EXTIE2 &= ~(0x0101); HT_INT->EXTIE2 |= (rfie << 0); HT_INT->PINFLT2 |= (1 << 0); irq_vector_set(INT_EXTI7, irq_handler_exti7); \
      HT_INT->EXTIF2 &= ~(INT_EXTIF2_RIF_INT7 | INT_EXTIF2_FIF_INT7); NVIC_ClearPendingIRQ(EXTI7_IRQn); NVIC_SetPriority(EXTI7_IRQn, 3); NVIC_EnableIRQ(EXTI7_IRQn);} \
    else if(port == gpio_port(PIN_EXINT8) && pin == gpio_pin(PIN_EXINT8)) \
    { HT_INT->EXTIE2 &= ~(0x0202); HT_INT->EXTIE2 |= (rfie << 1); HT_INT->PINFLT2 |= (1 << 1); irq_vector_set(INT_EXTI8, irq_handler_exti8); \
      HT_INT->EXTIF2 &= ~(INT_EXTIF2_RIF_INT8 | INT_EXTIF2_FIF_INT8); NVIC_ClearPendingIRQ(EXTI8_IRQn); NVIC_SetPriority(EXTI8_IRQn, 3); NVIC_EnableIRQ(EXTI8_IRQn);} \
    else if(port == gpio_port(PIN_EXINT9) && pin == gpio_pin(PIN_EXINT9)) \
    { HT_INT->EXTIE2 &= ~(0x0404); HT_INT->EXTIE2 |= (rfie << 2); HT_INT->PINFLT2 |= (1 << 2); irq_vector_set(INT_EXTI8, irq_handler_exti9); \
      HT_INT->EXTIF2 &= ~(INT_EXTIF2_RIF_INT9 | INT_EXTIF2_FIF_INT9); NVIC_ClearPendingIRQ(EXTI9_IRQn); NVIC_SetPriority(EXTI9_IRQn, 3); NVIC_EnableIRQ(EXTI9_IRQn);} \
}
#define gpio_exti_config(x,rfie)  _gpio_exti_config(HAL_GPIO_PORT(x),HAL_GPIO_PIN(x),rfie)

#define _gpio_exti_deConfig(port,pin) \
{ \
    if     (port == gpio_port(PIN_EXINT0) && pin == gpio_pin(PIN_EXINT0)) { NVIC_DisableIRQ(EXTI0_IRQn);} \
    else if(port == gpio_port(PIN_EXINT1) && pin == gpio_pin(PIN_EXINT1)) { NVIC_DisableIRQ(EXTI1_IRQn);} \
    else if(port == gpio_port(PIN_EXINT2) && pin == gpio_pin(PIN_EXINT2)) { NVIC_DisableIRQ(EXTI2_IRQn);} \
    else if(port == gpio_port(PIN_EXINT3) && pin == gpio_pin(PIN_EXINT3)) { NVIC_DisableIRQ(EXTI3_IRQn);} \
    else if(port == gpio_port(PIN_EXINT4) && pin == gpio_pin(PIN_EXINT4)) { NVIC_DisableIRQ(EXTI4_IRQn);} \
    else if(port == gpio_port(PIN_EXINT5) && pin == gpio_pin(PIN_EXINT5)) { NVIC_DisableIRQ(EXTI5_IRQn);} \
    else if(port == gpio_port(PIN_EXINT6) && pin == gpio_pin(PIN_EXINT6)) { NVIC_DisableIRQ(EXTI6_IRQn);} \
    else if(port == gpio_port(PIN_EXINT7) && pin == gpio_pin(PIN_EXINT7)) { NVIC_DisableIRQ(EXTI7_IRQn);} \
    else if(port == gpio_port(PIN_EXINT8) && pin == gpio_pin(PIN_EXINT8)) { NVIC_DisableIRQ(EXTI8_IRQn);} \
    else if(port == gpio_port(PIN_EXINT9) && pin == gpio_pin(PIN_EXINT9)) { NVIC_DisableIRQ(EXTI9_IRQn);} \
}
#define gpio_exti_deconfig(x)  _gpio_exti_deConfig(HAL_GPIO_PORT(x),HAL_GPIO_PIN(x))

static funcPointer exti_fun[TYPE_EXTI_NUM]; //typ 1-上升沿，0-下降沿

/// @brief IAR编译环境下的启动文件初始化调用接口,在main函数之前运行，保证在系统初始化之前完成重要GPIO配置
/// @param  
/// @return 返回值“0”代表RAM不初始化，返回值“1”代表RAM初始化
#if defined ( __ICCARM__ )
int __low_level_init(void)
{
    /// 配置电源检测口
    gpio_config(PIN_CF2_IN,     GPIO_Mode_IN,    GPIO_In_Floating,  GPIO_Out_OD); 
    gpio_config(PIN_CF1_IN,     GPIO_Mode_IN,    GPIO_In_Floating,  GPIO_Out_OD); 
    gpio_config(PIN_RELAY_LED,  GPIO_Mode_OUT,   GPIO_Out_PP,       GPIO_Output_L);
    gpio_config(PIN_LVDIN0,     GPIO_Mode_AF_1,  GPIO_In_Floating,  GPIO_Out_OD); 

	return 1;
}
#endif

/// @brief  提供给串口驱动调用。有些MCU必须先初始化串口后再打开GPIO，所以不在hal_gpio.c中提前初始化。
void hal_gpio_uart_init(uint8_t com)
{
    switch(com)
    {
        case 0:
        gpio_config(PIN_UART0_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_UART0_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        break;
        case 1:
        gpio_config(PIN_UART1_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_UART1_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        break;
        case 2:
        gpio_config(PIN_UART2_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_UART2_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_IR_CTRL,   GPIO_Mode_OUT,  GPIO_Out_PP,      GPIO_Output_L); // 红外电源控制
        break;
        case 3:
        gpio_config(PIN_UART3_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_UART3_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        break;
        case 4:
        gpio_config(PIN_UART4_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        gpio_config(PIN_UART4_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
        break;
        case 5:
        // gpio_config(PIN_UART5_RXD, GPIO_Mode_AF2, GPIO_In_Floating, GPIO_Out_PP);
        // gpio_config(PIN_UART5_TXD, GPIO_Mode_AF3, GPIO_In_Floating, GPIO_Out_PP);
        break;
    }
}


/// @brief 所有IO口寄存器监控
/// @param typ :0只扫描电源检测IO口, 1: 扫描所有IO口 
void hal_gpio_monitor(uint8_t typ)
{

}

void hal_gpio_reset(void)
{

}

/// @brief 配置GPIO外部中断, 开启NVIC中断
/// @param mode 0开启中断，1关闭中断
void hal_gpio_exti_config(uint8_t mode)
{
    if(!mode)
    {
    #ifdef PIN_METER_COVER
        gpio_exti_config(PIN_METER_COVER, EXTI_RFIE);
    #endif
    #ifdef PIN_TEM_COVER
        gpio_exti_config(PIN_TEM_COVER,   EXTI_RFIE);
    #endif
    #ifdef PIN_KEY_DWN
        gpio_exti_config(PIN_KEY_DWN,     EXTI_RIE);
    #endif
    #ifdef PIN_KEY_UP
        gpio_exti_config(PIN_KEY_UP,      EXTI_RIE);
    #endif
    }
    else
    {
    #ifdef PIN_METER_COVER
        gpio_exti_deconfig(PIN_METER_COVER);
    #endif
    #ifdef PIN_TEM_COVER
        gpio_exti_deconfig(PIN_TEM_COVER);
    #endif
    #ifdef PIN_KEY_DWN
        gpio_exti_deconfig(PIN_KEY_DWN);
    #endif
    #ifdef PIN_KEY_UP
        gpio_exti_deconfig(PIN_KEY_UP);
    #endif
    }
}

/// @brief 正常上电初始化GPIO
/// 1，输出IO的高低电平依据电路控制逻辑相应选择GPIO_Output_H或者GPIO_Output_L。
/// 2，外设功能IO配置一律采用GPIO_In_Floating，GPIO_Out_PP。
/// 3，不建议上电情况下使用GPIO中断功能,脉冲除外。
/// 4，这里只需配置用到的IO口即可，其他IO口默认配置为GPIO模式，并且关闭输入和输出使能，避免引脚浮空漏电。
/// 5，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_config(1)开启中断。
/// 6，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_set(irq, func)设置中断回调函数。
/// 7，如果需要使用按键GPIO中断功能，则在中断回调函数中调用hal_gpio_exti_clear(irq)清除中断标志。
/// 8，应用中IICSDA设置为
/// @param  
void hal_gpio_init(void)
{
	/* Analog Input ------------------------------------------------------------*/
    
#ifdef PIN_BATT_EXT
    gpio_config(PIN_BATT_EXT,   GPIO_Mode_AF_2,   GPIO_In_Floating,   GPIO_Out_OD);
#endif
#ifdef PIN_BATT_IN
    gpio_config(PIN_BATT_IN,    GPIO_Mode_AF_2,   GPIO_In_Floating,   GPIO_Out_OD);    
#endif

	hal_gpio_exti_config(1); //关闭按键外部中断
    /* Digital Input -----------------------------------------------------------*/
#ifdef PIN_KEY_DWN
    gpio_config(PIN_KEY_DWN,    GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);
#endif
#ifdef PIN_KEY_UP
    gpio_config(PIN_KEY_UP,     GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);
#endif
#ifdef PIN_METER_COVER
    gpio_config(PIN_METER_COVER,GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);
#endif
#ifdef PIN_TEM_COVER
    gpio_config(PIN_TEM_COVER,  GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);
#endif

    gpio_config(PIN_4851_CTRL,  GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_H);

    gpio_config(PIN_EXT_RELAY,  GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    gpio_config(PIN_ALARM_RELAY,GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    gpio_config(PIN_RELAY_OFF,  GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    gpio_config(PIN_RELAY_ON,   GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    
    gpio_config(PIN_LCD_BG,     GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    gpio_config(PIN_RELAY_LED,  GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);

    gpio_config(PIN_POWER_CTRL, GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    // gpio_config(PIN_VLCD_CTRL,   GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
    gpio_config(PIN_VLCD_CTRL,  GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);

    gpio_config(PIN_EE_POW,     GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_L);
    gpio_config(PIN_EE_SCL,     GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);
    gpio_config(PIN_EE_SDA,     GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);    

    gpio_config(PIN_BEEP,       GPIO_Mode_AF_1, GPIO_In_Floating,   GPIO_Out_PP);

    gpio_config(PIN_RLY_CHK,    GPIO_Mode_IN,   GPIO_In_Floating,   GPIO_Out_OD);

    gpio_config(PIN_EMU_CTL,    GPIO_Mode_OUT,  GPIO_Out_PP,        GPIO_Output_H);
    

    ///未使用引脚
    gpio_config(PIN_A_14,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_A_15,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_B_0,        GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_B_5,        GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_B_14,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_D_10,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_D_11,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_D_12,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_D_13,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    gpio_config(PIN_D_15,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_10,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_11,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_12,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_13,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_14,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
    // gpio_config(PIN_E_15,       GPIO_Mode_OUT,  GPIO_Out_OD,        GPIO_Output_H);
   
}

/**
  * @brief  低功耗运行环境GPIO初始化.
  *         默认所有功能引脚被配置为GPIO模式，开漏输出高。
  */
void hal_gpio_init_nopower(void)
{
    HT_GPIOA->IOCFG = 0x0000;    // 0-设置为IO
    HT_GPIOA->PTDIR = 0xFFFF;    // 0-输入      1-输出
    HT_GPIOA->PTOD  = 0x0000;    // 0-开漏输出  1-推挽
    HT_GPIOA->PTUP  = 0xFFFF;    // 0-使能上拉  1-禁止上拉
    HT_GPIOA->PTSET = 0xFFFF;    // 1-设置输出高

    HT_GPIOB->IOCFG = 0x0000;    // 0-设置为IO
    HT_GPIOB->PTDIR = 0x5FFF;    // 0-输入      1-输出      PB.13(SWIO) PB.15(SWCLK)设为输入  其它输出
    HT_GPIOB->PTOD  = 0x0000;    // 0-开漏输出  1-推挽
    HT_GPIOB->PTUP  = 0x5FFF;    // 0-使能上拉  1-禁止上拉
    HT_GPIOB->PTSET = 0xFFFF;    // 1-设置输出高

    HT_GPIOC->IOCFG = 0x0000;    // 0-设置为IO
    HT_GPIOC->PTDIR = 0xFFFF;    // 0-输入      1-输出
    HT_GPIOC->PTOD  = 0x0000;    // 0-开漏输出  1-推挽
    HT_GPIOC->PTUP  = 0xFFFF;    // 0-使能上拉  1-禁止上拉
    HT_GPIOC->PTSET = 0xFFFF;    // 1-设置输出高
    
    HT_GPIOD->IOCFG = 0x0000;    // 0-设置为IO
    HT_GPIOD->PTDIR = 0xFFFF;    // 0-输入      1-输出
    HT_GPIOD->PTOD  = 0x0000;    // 0-开漏输出  1-推挽
    HT_GPIOD->PTUP  = 0xFFFF;    // 0-使能上拉  1-禁止上拉
    HT_GPIOD->PTSET = 0xFFFF;    // 1-设置输出高
#ifdef PIN_EMU_CTL
    gpio_config(PIN_EMU_CTL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
#endif
    HT_GPIOE->IOCFG = 0x0000;    // 0-设置为IO
    HT_GPIOE->PTDIR = 0xFFFF;    // 0-输入      1-输出
    HT_GPIOE->PTOD  = 0x0000;    // 0-开漏输出  1-推挽
    HT_GPIOE->PTUP  = 0xFFFF;    // 0-使能上拉  1-禁止上拉
    HT_GPIOE->PTSET = 0xFFFF;    // 1-设置输出高

#ifdef PIN_KEY_DWN
    gpio_config(PIN_KEY_DWN,        GPIO_Mode_IN,   GPIO_In_Floating, GPIO_Out_OD); // 先设置为输入，再设置为中断
    gpio_config(PIN_KEY_DWN,        GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
#endif
#ifdef PIN_KEY_UP
    gpio_config(PIN_KEY_UP,         GPIO_Mode_IN,   GPIO_In_Floating, GPIO_Out_OD);
    gpio_config(PIN_KEY_UP,         GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
#endif
#ifdef PIN_METER_COVER
    gpio_config(PIN_METER_COVER,    GPIO_Mode_IN,   GPIO_In_Floating, GPIO_Out_OD);
    gpio_config(PIN_METER_COVER,    GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
#endif
#ifdef PIN_TEM_COVER
    gpio_config(PIN_TEM_COVER,      GPIO_Mode_IN,   GPIO_In_Floating, GPIO_Out_OD);
    gpio_config(PIN_TEM_COVER,      GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
#endif

    ///开启引脚中断
    hal_gpio_exti_config(0);
    gpio_config(PIN_EE_SCL, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    gpio_config(PIN_EE_SDA, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);    

    /// 未使用的引进和断电后相当于浮空的引进设置为开漏输出高

   
}

/// @brief FLASH片选、时钟、命令、数据引脚初始化
/// @param  
void hal_gpio_flash_init(GPIO_INIT_TYPE_t type)
{
    //gpio_config(FLASH_PW_PIN, GPIO_Mode_IOOUT, GPIO_Output_PP, GPIO_Output_H);
    if(type == GPIO_OPEN)
    {
        gpio_out_H(PIN_POWER_CTRL);
        gpio_config(PIN_FLASH_CS,   GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_FLASH_SCLK, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);// SCLK常态低电平 CPOL-0，CPHA-0(第一个沿采数据)
        gpio_config(PIN_FLASH_MOSI, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_FLASH_MISO, GPIO_Mode_IN,  GPIO_In_Up,  GPIO_Out_OD);  /// 没有外部上拉
    }
    else if(type == GPIO_CLOSE)
    {
        gpio_config(PIN_POWER_CTRL, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_FLASH_CS,   GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_FLASH_SCLK, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H); 
        gpio_config(PIN_FLASH_MOSI, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_FLASH_MISO, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
    } 
}

/// @brief EEPROM时钟、数据引脚初始化
/// @param  
void hal_gpio_eeprom_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_EE_POW, GPIO_Mode_OUT, GPIO_Out_PP,      GPIO_Output_H);
        gpio_config(PIN_EE_SDA, GPIO_Mode_IN,  GPIO_In_Floating, GPIO_Out_PP);
        gpio_out_L(PIN_EE_SDA);
        gpio_config(PIN_EE_SCL, GPIO_Mode_OUT, GPIO_Out_OD,      GPIO_Output_H);
    }
}

/// @brief LCD电源，时钟、数据引脚初始化
/// @param type 
void hal_gpio_lcd_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_VLCD_CTRL,GPIO_Mode_OUT,  GPIO_Out_PP,      GPIO_Output_H);
        // gpio_config(PIN_LCD_SDA,  GPIO_Mode_IN,   GPIO_In_Floating, GPIO_Out_PP);
        // gpio_out_L(PIN_LCD_SDA);
        gpio_config(PIN_LCD_SDA,  GPIO_Mode_OUT,  GPIO_Out_OD,      GPIO_Output_H);
        gpio_config(PIN_LCD_SCL,  GPIO_Mode_OUT,  GPIO_Out_OD,      GPIO_Output_H);
    }
    else if(type == GPIO_CLOSE)
    {
        gpio_config(PIN_VLCD_CTRL,GPIO_Mode_OUT,  GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_LCD_SDA,  GPIO_Mode_OUT,  GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_LCD_SCL,  GPIO_Mode_OUT,  GPIO_Out_OD, GPIO_Output_H);
    }
}

void hal_gpio_mic_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
// #define PIN_HT_DIN                  HT_GPIOD,11    // -i 计量芯片数据输入
// #define PIN_HT_DOUT                 HT_GPIOD,12    // -o 计量芯片数据输出
// #define PIN_HT_CLK                  HT_GPIOD,13    // -o 计量芯片时钟输出
// #define PIN_HT_CS                   HT_GPIOD,14    // -o 计量芯片片选输出
        gpio_out_L(PIN_EMU_CTL);
        gpio_config(PIN_HT_CS,   GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_HT_CLK,  GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L); // SCLK常态低电平,// SCLK常态低电平 CPOL-0，CPHA-1(第二个沿采数据)
        gpio_config(PIN_HT_DOUT, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_HT_DIN,  GPIO_Mode_IN,  GPIO_In_Up,  GPIO_Out_OD);
    }
    else
    {
        gpio_out_H(PIN_EMU_CTL);
        gpio_config(PIN_HT_CS,   GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_HT_CLK,  GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_HT_DOUT, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
        gpio_config(PIN_HT_DIN,  GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
    }
}

/// @brief 设置秒脉冲输出
/// @param mode mode=1:输出秒脉冲，0- 关闭
void hal_pulse_out_mode(uint8_t mode)
{
    if(mode)
    {
        gpio_config(PIN_SECOND_TOUT, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
    }
    else
    {
        gpio_config(PIN_SECOND_TOUT, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
    }
}

void hal_gpio_exti_set(uint8_t irq, void func(void))
{
    exti_fun[irq] = func;
}


// void EXTI0_IRQHandler(void)
void irq_handler_exti0(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[0] != NULL) { exti_fun[0](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT0 | INT_EXTIF_FIF_INT0));
}
// void EXTI1_IRQHandler(void)
void irq_handler_exti1(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[1] != NULL) { exti_fun[1](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT1 | INT_EXTIF_FIF_INT1));
}
// void EXTI2_IRQHandler(void)
void irq_handler_exti2(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[2] != NULL) { exti_fun[2](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT2 | INT_EXTIF_FIF_INT2));
}
// void EXTI3_IRQHandler(void)
void irq_handler_exti3(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[3] != NULL) { exti_fun[3](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT3 | INT_EXTIF_FIF_INT3));
}
// void EXTI4_IRQHandler(void)
void irq_handler_exti4(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[4] != NULL) { exti_fun[4](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT4 | INT_EXTIF_FIF_INT4));
}
// void EXTI5_IRQHandler(void)
void irq_handler_exti5(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[5] != NULL) { exti_fun[5](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT5 | INT_EXTIF_FIF_INT5));
}
// void EXTI6_IRQHandler(void)
void irq_handler_exti6(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF;

    if(exti_fun[6] != NULL) { exti_fun[6](); }

    HT_INT->EXTIF &= ~(tmp & (INT_EXTIF_RIF_INT6 | INT_EXTIF_FIF_INT6));
}

// void EXTI7_IRQHandler(void)
void irq_handler_exti7(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF2;

    if(exti_fun[7] != NULL) { exti_fun[7](); }

    HT_INT->EXTIF2 &= ~(tmp & (INT_EXTIF2_RIF_INT7 | INT_EXTIF2_FIF_INT7));
}
// void EXTI8_IRQHandler(void)
void irq_handler_exti8(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF2;

    if(exti_fun[8] != NULL) { exti_fun[8](); }

    HT_INT->EXTIF2 &= ~(tmp & (INT_EXTIF2_RIF_INT8 | INT_EXTIF2_FIF_INT8));
}
// void EXTI9_IRQHandler(void)
void irq_handler_exti9(void)
{
    volatile uint32_t tmp = HT_INT->EXTIF2;

    if(exti_fun[9] != NULL) { exti_fun[9](); }

    HT_INT->EXTIF2 &= ~(tmp & (INT_EXTIF2_RIF_INT9 | INT_EXTIF2_FIF_INT9));
}

/// @brief 声明hal_gpio子模块对象
const struct hal_gpio_t hal_gpio =
{
    .init               = hal_gpio_init,
    .init_nopower       = hal_gpio_init_nopower,
    .uart_init          = hal_gpio_uart_init,
    .pulse_out_mode     = hal_pulse_out_mode,
    .monitor            = hal_gpio_monitor,
    .exti_set           = hal_gpio_exti_set,
    .data_flash         = hal_gpio_flash_init,
    .ext_eeprom         = hal_gpio_eeprom_init,
    .ext_lcd            = hal_gpio_lcd_init,
    .mic_init           = hal_gpio_mic_init,
};

/** @} */
/** @} */
/** @} */
