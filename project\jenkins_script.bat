
@echo off
setlocal ENABLEDELAYEDEXPANSION
:: 设置脚本编码为UTF-8
@REM chcp 65001 >nul
:: 进入脚本路径
pushd %~dp0
echo [FILE] jenkins_script.bat

::获取传参
@set "git_tag=%1"
REM 配置的 Git 提交者名称
@set "GIT_COMMITTER_NAMEY_1=%2"
REM 正在检出的提交哈希。
@set "GIT_COMMIT_1=%3"
REM git链接
@set "GIT_URL_1=%4"

:: 递归执行所有build.bat，并在失败时终止
for /R %%i in (*build.bat) do (
    pushd "%%~dpi"
    echo [INFO]  build.bat dir: %%~dpi
    call "%%~nxi" || (
        popd
        echo [ERROR] file: %%~nxi
        echo [ERROR] dir: %%~dpi
        goto :ERROR
    )
    echo [INFO] success: %%~nxi
    popd
)
echo [INFO] all build.bat success!!!!

:: 执行文件复制并检查错误
call jenkins_file_copy.bat "%git_tag%" "%GIT_COMMITTER_NAMEY_1%" "%GIT_COMMIT_1%" "%GIT_URL_1%" || goto :ERROR
echo 结果压缩包：%dist_dir%
echo [INFO] file_copy OK!!!!
:: 执行文件复制并检查错误
goto :END

:ERROR
echo [ERROR] jenkins_script.bat error !!!!
popd
exit /b 1
 
:END
popd
exit /b 0