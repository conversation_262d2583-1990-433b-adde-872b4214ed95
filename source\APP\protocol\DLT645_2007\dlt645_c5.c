/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c5.c
*    Describe:  DLT645-2007协议，04类数据部分     
*               注 1: 按照电能表实际设置的费率数冻结费率电能和最大需量及发生时间。
*               注 2: n的值为从站实际冻结的费率数加1（1为总量）。
*               注 3. 电能表上电后对停电期间数据不做补冻。
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "typedef.h"
#include "DLT645_2007_id.h"

/// @brief 读取数据处理
/// @param p_info 
/// @return 
static uint16_t dlt_645_read_5(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;
    uint16_t item   = ITEM(p_info->id);

    memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    switch(item)
    {
        case ITEM(C5_TIMER_FRZ_TIME(1)       ):    //@(0x05000000 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结时间
            break;
        case ITEM(C5_TIMER_FRZ_POS_kWh(1)    ):    //@(0x05000100 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结正向有功电能数据：
            break;
        case ITEM(C5_TIMER_FRZ_NEG_kWh(1)    ):    //@(0x05000200 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结反向有功电能数据：
            break;
        case ITEM(C5_TIMER_FRZ_CMB1_kvar(1)  ):    //@(0x05000300 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_TIMER_FRZ_CMB2_kvar(1)  ):    //@(0x05000400 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_TIMER_FRZ_Q1_kvar(1)    ):    //@(0x05000500 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结第一象限无功电能数据：
            break;
        case ITEM(C5_TIMER_FRZ_Q2_kvar(1)    ):    //@(0x05000600 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结第二象限无功电能数据
            break;
        case ITEM(C5_TIMER_FRZ_Q3_kvar(1)    ):    //@(0x05000700 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结第三象限无功电能数据
            break;
        case ITEM(C5_TIMER_FRZ_Q4_kvar(1)    ):    //@(0x05000800 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结第四象限无功电能数据
            break;
        case ITEM(C5_TIMER_FRZ_POS_kW_MD(1)  ):    //@(0x05000900 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_TIMER_FRZ_NEG_kW_MD(1)  ):    //@(0x05000A00 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_TIMER_FRZ_INS(1)        ):    //@(0x05001000 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结变量数据
            break;
        case ITEM(C5_TIMER_FRZ_BLK(1)        ):    //@(0x0500FF00 | LAST_INDEX(N)) （上 N 1-12 次）定时冻结数据块
            break;
        case ITEM(C5_INS_FRZ_TIME(1)         ):    //@(0x05010000 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结时间
            break;
        case ITEM(C5_INS_FRZ_POS_kWh(1)      ):    //@(0x05010100 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结正向有功电能数据：
            break;
        case ITEM(C5_INS_FRZ_NEG_kWh(1)      ):    //@(0x05010200 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结反向有功电能数据：
            break;
        case ITEM(C5_INS_FRZ_CMB1_kvar(1)    ):    //@(0x05010300 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_INS_FRZ_CMB2_kvar(1)    ):    //@(0x05010400 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_INS_FRZ_Q1_kvar(1)      ):    //@(0x05010500 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结第一象限无功电能数据：
            break;
        case ITEM(C5_INS_FRZ_Q2_kvar(1)      ):    //@(0x05010600 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结第二象限无功电能数据
            break;
        case ITEM(C5_INS_FRZ_Q3_kvar(1)      ):    //@(0x05010700 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结第三象限无功电能数据
            break;
        case ITEM(C5_INS_FRZ_Q4_kvar(1)      ):    //@(0x05010800 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结第四象限无功电能数据
            break;
        case ITEM(C5_INS_FRZ_POS_kW_MD(1)    ):    //@(0x05010900 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_INS_FRZ_NEG_kW_MD(1)    ):    //@(0x05010A00 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_INS_FRZ_INS(1)          ):    //@(0x05011000 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结变量数据
            break;
        case ITEM(C5_INS_FRZ_BLK(1)          ):    //@(0x0501FF00 | LAST_INDEX(N))  （上 N 1-3 次）瞬时冻结数据块
            break;
        case ITEM(C5_ZONE_SW_FRZ_TIME(1)     ):    //@(0x05020000 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结时间
            break;
        case ITEM(C5_ZONE_SW_FRZ_POS_kWh(1)  ):    //@(0x05020100 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结正向有功电能数据：
            break;
        case ITEM(C5_ZONE_SW_FRZ_NEG_kWh(1)  ):    //@(0x05020200 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结反向有功电能数据：
            break;
        case ITEM(C5_ZONE_SW_FRZ_CMB1_kvar(1)):    //@(0x05020300 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_ZONE_SW_FRZ_CMB2_kvar(1)):    //@(0x05020400 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_ZONE_SW_FRZ_Q1_kvar(1)  ):    //@(0x05020500 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结第一象限无功电能数据：
            break;
        case ITEM(C5_ZONE_SW_FRZ_Q2_kvar(1)  ):    //@(0x05020600 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结第二象限无功电能数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_Q3_kvar(1)  ):    //@(0x05020700 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结第三象限无功电能数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_Q4_kvar(1)  ):    //@(0x05020800 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结第四象限无功电能数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_POS_kW_MD(1)):    //@(0x05020900 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_NEG_kW_MD(1)):    //@(0x05020A00 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_INS(1)      ):    //@(0x05021000 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结变量数据
            break;
        case ITEM(C5_ZONE_SW_FRZ_BLK(1)      ):    //@(0x0502FF00 | LAST_INDEX(N))  （上 N 1-2 次）两套时区表切换冻结数据块
            break;
        case ITEM(C5_SCH_SW_FRZ_TIME(1)      ):    //@(0x05020000 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结时间
            break;
        case ITEM(C5_SCH_SW_FRZ_POS_kWh(1)   ):    //@(0x05020100 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结正向有功电能数据：
            break;
        case ITEM(C5_SCH_SW_FRZ_NEG_kWh(1)   ):    //@(0x05020200 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结反向有功电能数据：
            break;
        case ITEM(C5_SCH_SW_FRZ_CMB1_kvar(1) ):    //@(0x05020300 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_SCH_SW_FRZ_CMB2_kvar(1) ):    //@(0x05020400 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_SCH_SW_FRZ_Q1_kvar(1)   ):    //@(0x05020500 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结第一象限无功电能数据：
            break;
        case ITEM(C5_SCH_SW_FRZ_Q2_kvar(1)   ):    //@(0x05020600 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结第二象限无功电能数据
            break;
        case ITEM(C5_SCH_SW_FRZ_Q3_kvar(1)   ):    //@(0x05020700 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结第三象限无功电能数据
            break;
        case ITEM(C5_SCH_SW_FRZ_Q4_kvar(1)   ):    //@(0x05020800 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结第四象限无功电能数据
            break;
        case ITEM(C5_SCH_SW_FRZ_POS_kW_MD(1) ):    //@(0x05020900 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_SCH_SW_FRZ_NEG_kW_MD(1) ):    //@(0x05020A00 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_SCH_SW_FRZ_INS(1)       ):    //@(0x05021000 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结变量数据
            break;
        case ITEM(C5_SCH_SW_FRZ_BLK(1)       ):    //@(0x0502FF00 | LAST_INDEX(N))  （上 N 1-2 次）两套日时段表切换冻结数据块
            break;
        case ITEM(C5_hour_FRZ_TIME(1)        ):    //@(0x05040100 | LAST_INDEX(N))  (上N 1- 254 次）整点冻结时间
            break;
        case ITEM(C5_hour_FRZ_POS_kWh(1)     ):    //@(0x05040100 | LAST_INDEX(N))  (上N 1- 254 次）整点冻结正向有功总电能
            break;
        case ITEM(C5_hour_FRZ_NEG_kWh(1)     ):    //@(0x05040100 | LAST_INDEX(N))  (上N 1- 254 次）整点冻结反向有功总电能
            break;
        case ITEM(C5_hour_FRZ_BLK(1)         ):    //@(0x05040100 | LAST_INDEX(N))  (上N 1- 254 次）整点冻结数据块
            break;
        case ITEM(C5_DAY_FRZ_TIME(1)         ):    //@(0x05060000 | LAST_INDEX(N))  （上 N 1-62 次）日冻结时间
            break;
        case ITEM(C5_DAY_FRZ_POS_kWh(1)      ):    //@(0x05060100 | LAST_INDEX(N))  （上 N 1-62 次）日冻结正向有功电能数据：
            break;
        case ITEM(C5_DAY_FRZ_NEG_kWh(1)      ):    //@(0x05060200 | LAST_INDEX(N))  （上 N 1-62 次）日冻结反向有功电能数据：
            break;
        case ITEM(C5_DAY_FRZ_CMB1_kvar(1)    ):    //@(0x05060300 | LAST_INDEX(N))  （上 N 1-62 次）日冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_DAY_FRZ_CMB2_kvar(1)    ):    //@(0x05060400 | LAST_INDEX(N))  （上 N 1-62 次）日冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_DAY_FRZ_Q1_kvar(1)      ):    //@(0x05060500 | LAST_INDEX(N))  （上 N 1-62 次）日冻结第一象限无功电能数据：
            break;
        case ITEM(C5_DAY_FRZ_Q2_kvar(1)      ):    //@(0x05060600 | LAST_INDEX(N))  （上 N 1-62 次）日冻结第二象限无功电能数据
            break;
        case ITEM(C5_DAY_FRZ_Q3_kvar(1)      ):    //@(0x05060700 | LAST_INDEX(N))  （上 N 1-62 次）日冻结第三象限无功电能数据
            break;
        case ITEM(C5_DAY_FRZ_Q4_kvar(1)      ):    //@(0x05060800 | LAST_INDEX(N))  （上 N 1-62 次）日冻结第四象限无功电能数据
            break;
        case ITEM(C5_DAY_FRZ_POS_kW_MD(1)    ):    //@(0x05060900 | LAST_INDEX(N))  （上 N 1-62 次）日冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_DAY_FRZ_NEG_kW_MD(1)    ):    //@(0x05060A00 | LAST_INDEX(N))  （上 N 1-62 次）日冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_DAY_FRZ_INS(1)          ):    //@(0x05061000 | LAST_INDEX(N))  （上 N 1-62 次）日冻结变量数据
            break;
        case ITEM(C5_DAY_FRZ_BLK(1)          ):    //@(0x0506FF00 | LAST_INDEX(N))  （上 N 1-62 次）日冻结数据块
            break;
        case ITEM(C5_STEP_SW_FRZ_TIME(1)     ):    //@(0x05070000 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结时间
        // case ITEM(C5_STEP_SW_FRZ_POS_kWh(1)  ):    //@(0x05070100 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结正向有功电能数据：
            break;
        case ITEM(C5_STEP_SW_FRZ_NEG_kWh(1)  ):    //@(0x05070200 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结反向有功电能数据：
            break;
        case ITEM(C5_STEP_SW_FRZ_CMB1_kvar(1)):    //@(0x05070300 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结组合无功 1 电能数据：
            break;
        case ITEM(C5_STEP_SW_FRZ_CMB2_kvar(1)):    //@(0x05070400 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结组合无功 2 电能数据：
            break;
        case ITEM(C5_STEP_SW_FRZ_Q1_kvar(1)  ):    //@(0x05070500 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结第一象限无功电能数据：
            break;
        case ITEM(C5_STEP_SW_FRZ_Q2_kvar(1)  ):    //@(0x05070600 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结第二象限无功电能数据
            break;
        case ITEM(C5_STEP_SW_FRZ_Q3_kvar(1)  ):    //@(0x05070700 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结第三象限无功电能数据
            break;
        case ITEM(C5_STEP_SW_FRZ_Q4_kvar(1)  ):    //@(0x05070800 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结第四象限无功电能数据
            break;
        case ITEM(C5_STEP_SW_FRZ_POS_kW_MD(1)):    //@(0x05070900 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结正向有功最大需量及发生时间数据
            break;
        case ITEM(C5_STEP_SW_FRZ_NEG_kW_MD(1)):    //@(0x05070A00 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结反向有功最大需量及发生时间数据
            break;
        case ITEM(C5_STEP_SW_FRZ_INS(1)      ):    //@(0x05071000 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结变量数据
            break;
        case ITEM(C5_STEP_SW_FRZ_BLK(1)      ):    //@(0x0507FF00 | LAST_INDEX(N))  （上 N 1-2 次）两套阶梯切换冻结数据块
            break;
    }

    if((p_data - p_info->snd_dat) == 4) 
    {
        *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
        return 1;
    } // 无数据
    return (uint16_t)(p_data - p_info->snd_dat);
}


/// end of file
