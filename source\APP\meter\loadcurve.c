/********************************************************************************
  * @file    loadcurve.c
  * <AUTHOR> @date    2024
  * @brief   负荷记录
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "loadcurve.h"
#include "datastore.h"
#include "profile.h"
#include "log.h"
#include "status.h"

/* Private typedef -----------------------------------------------------------*/
typedef struct
{ 
    bool          patch_ready;  // 补点准备开始
    ls_status_s   lc_stus;      // 负荷曲线状态字
    ls_status_s   lc_stus_cs;   // 负荷曲线状态字
    ClockStatus_s clock_stus;   // 记录上一次捕获点时钟状态
    uint32_t lstCapTime;        // 记录上一次捕获点时钟值(真实时钟 单位:秒)
    uint32_t lst_clock_adj_time;// 校时时间结束点
    uint32_t lcPwrOnTime;       // 负荷曲线模块上电时标
    int32_t  differ;            // 补/删点的个数, 补点为正值, 删点为负值
}lc_rcd_s;

/* 负荷曲线接口检索定义 */
typedef struct
{
    NVM_LOG_t idx;     // 在NVM中的存储索引
} lc_api_seek_s;

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/
#define LC_CRC16                0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(LC_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(LC_CRC16, struct, len)
#define LC_PARA_ADDR      nvm_addr(NVM_LOAD_CURVE_PARA)

/* Private constants ---------------------------------------------------------*/
/* 默认负荷曲线检索信息 */
static const lc_api_seek_s lc_api_seek[LC_TYPE_NUM] =
{
    {
        .idx = NVM_LC1_PROFILE,
    },
#if LC2_ENABLE
    {
        .idx = NVM_LC2_PROFILE,
    },
#endif
};

extern const lc_para_s  lc_default_para; ///默认参数
static const lc_para_s* lc_running_para; ///运行参数，指向codeflash

/* Private variables ---------------------------------------------------------*/
__no_init static lc_rcd_s lc_rcd[LC_TYPE_NUM];  // 负荷曲线记录
static LC_STUS lc_out_stus;                     // 负荷曲线事件输出状态字
static uint8_t lc_startcnt = 5;                 // 负荷曲线启动延时计数器
static ls_status_s lc_clock_adjust_flag;
/* Private functions ---------------------------------------------------------*/
/// @brief
/// @param ofst
/// @param val
/// @param len
/// @return
static bool lc_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    lc_para_s para;
    if(ofst != 0) memcpy(&para, lc_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(para));
    lc_running_para = (const lc_para_s*)LC_PARA_ADDR;
    return nvm.write((uint32_t)lc_running_para, &para, sizeof(para));
}

/// @brief
/// @param
static void lc_para_load(void)
{
    lc_running_para = (const lc_para_s*)LC_PARA_ADDR;
    if(CRC16_CHK(lc_running_para, sizeof(lc_para_s)) == false)
    {
        lc_running_para = &lc_default_para;
    }
}

static void lc_last_capture_time_update(LC_TYPE_t type)
{
    uint32_t  lst_clock;
    uint8_t   buf[5];
    lc_rcd_s* rcd = &lc_rcd[type];

    /* 取最近一次负荷曲线捕获点时标 */
    if(mlog.fetch(log_addr(lc_api_seek[type].idx), &buf, 5, 1) != 0xFFFF) //获取时间和时间状态
    {
        rcd->lstCapTime = *((uint32_t *)buf); // 时间
        rcd->clock_stus.value = buf[4];  // 时间状态
    }
    else
    {
        rcd->lstCapTime = mclock.datetime->u32datetime - (mclock.datetime->u32datetime % lc_running_para->period[type]); // 未曾有记录时, 取当前时间
        rcd->clock_stus = mclock.datetime->stus;
    }

    rcd->differ = 0;
}

/// @brief
/// @param lc_type
/// @param rcd
/// @param capsecs
static void lc_capture(LC_TYPE_t lc_type, lc_rcd_s* rcd, uint32_t capsecs)
{
    uint16_t len;
    uint8_t buf[MAX_CAPTURE_DATA_LEN];
    uint8_t prf_stus = PRF_DATA_NORMAL;

    if(hal_mcu.pwrdn_query()) { return; }  //掉电了，就等待上电再补

    /* 1.刷新状态 */
    if(rcd->patch_ready)
    {
        /* 负荷曲线正常处理 */
        rcd->lc_stus.data_invalid = false;
        rcd->lc_stus.data_invalid |= boolof(rcd->lc_stus.circal_error|rcd->lc_stus.clock_invalid); // 正常数据点也有数据无效的时候
    }
    else
    {
        /* 负荷曲线状态字 */
        rcd->lc_stus.data_invalid = true; // 连续补点时置数据无效标志
        prf_stus = PRF_DATA_INVALID;
    }

    /* 2.自由曲线捕获数据 */
    if(lc_api_seek[lc_type].idx != NVM_LOG_UNDEF)
    {
        if(lc_type == TYPE_LC1) { len = profile.lc1_capture(buf, capsecs, rcd->clock_stus.value, prf_stus); } //buff, 时间, 时间状态, 负荷曲线状态
    #if LC2_ENABLE
        else if(lc_type == TYPE_LC2) { len = profile.lc2_capture(buf, capsecs, rcd->clock_stus.value, prf_stus); }//buff, 时间, 时间状态, 负荷曲线状态
    #endif
        if(!mlog.roll_add(log_addr(lc_api_seek[lc_type].idx), buf, len)) return;
    }

    rcd->patch_ready = false;
    rcd->lc_stus.buffer_clr = false;
}

/* Public functions ----------------------------------------------------------*/
///@brief LC模块初始化, 初始化任务调用
void loadcurve_init(void)
{
    /* 检出负荷曲线参数 */
    lc_para_load();

    /* 初始负荷曲线记录参数 */
    for(LC_TYPE_t i = TYPE_LC1; i < LC_TYPE_NUM; i++)
    {
        lc_rcd_s* rcd = &lc_rcd[i];

        uint8_t tmp1 = rcd->lc_stus.value;
        uint8_t tmp2 = (uint8_t)(~(rcd->lc_stus_cs.value));
        if(tmp1 != tmp2)
        {
            // 没有电池导致RAM丢失，那么置掉电标志不会丢失。时钟本身异常时异常复位这里判断逻辑还是有缺陷
            rcd->lc_stus.value = 0;
            if(mclock.datetime->stus.invalid_value || mclock.datetime->stus.doubtful_value)
            {
                lc_rcd[i].lc_stus.power_down = true;
                lc_rcd[i].lc_stus_cs.value = ~lc_rcd[i].lc_stus.value;
            }
        }
        lc_rcd[i].lcPwrOnTime = mclock.datetime->u32datetime;
        lc_last_capture_time_update(i);
    }
}

///@brief LC模块空闲任务时调用
void loadcurve_idle_run(void)
{
    // 负荷曲线上电延时5s处理
    if(lc_startcnt != 0) return;

    /* 时钟无效时不记录负荷曲线 */
    if(mclock.datetime->stus.invalid_value) return;

    {
        clock_s start_time = lc_running_para->start_time;
        start_time.year    = mclock.datetime->year; //通配
        start_time.second  = 0;
        if(mclock.compare(&start_time) < 0) return; // 未到启动时间
    }
    
    if(mclock.state_query(STUS_CLOCK_SHIFT_EVENT | STUS_CLOCK_BC_EVENT))
    {
        return; // 发生过校时，本次不处理等经过秒任务后继续处理，否则相关曲线状态，数据不够准确
    }

    lc_para_load();

    /* 扫描负荷曲线记录点 */
    for(LC_TYPE_t i = TYPE_LC1; i < LC_TYPE_NUM; i++)
    {
        lc_rcd_s* rcd = &lc_rcd[i];
        uint32_t period = lc_running_para->period[i];
        uint32_t curClockSec;

        if(period == 0) continue; // 周期为0，该曲线禁用
        curClockSec = mclock.datetime->u32datetime;
    
        /* 时钟调整状态字置位 */
        if(lc_clock_adjust_flag.value)
        {
            rcd->lc_stus.clock_adjust = true;
            rcd->lc_stus.data_invalid = lc_clock_adjust_flag.data_invalid;
            rcd->lst_clock_adj_time   = curClockSec;
        }

        /* 其它状态位 */
        rcd->lc_stus.circal_error |= mstatus.reg->self_chk.measure;

        /* 时钟异常标志 */
        rcd->lc_stus.clock_invalid |= (mclock.datetime->stus.invalid_value || mclock.datetime->stus.doubtful_value);

        /* 查询曲线点是否补完 */
        if(rcd->differ == 0)
        {
            uint32_t past_num, differ;
            int32_t  time;

            time = curClockSec - rcd->lstCapTime;
            differ = labs(time) / period;
            rcd->differ = (time >= 0) ? differ : ~differ - 1;

            if(rcd->differ != 0)
            {
                log_addr_s addr = log_addr(lc_api_seek[i].idx);
                if(rcd->differ > 0)
                {
                    past_num = rcd->differ;
                    if(past_num <= addr.num) mlog.pre_earse(addr, past_num);
                }
                else past_num = -rcd->differ;

                if(past_num > addr.num) // 时间调整幅度太大则清空负荷曲线
                {
                    mlog.empty(addr);
                    lc_last_capture_time_update(i);
                }
            }
            rcd->patch_ready = true;
        }

        if(rcd->differ != 0)
        {
            clock_s lst_clock;
            uint32_t cap_sec;
            if(rcd->differ > 0) // 正向补点
            {
                uint32_t dls_begin_sec = 0, dls_end_sec = 0;

                if(rcd->lstCapTime > rcd->lcPwrOnTime)
                { // 负荷曲线时标已记录到大于掉电时间点时，清掉电标志
                    rcd->lc_stus.power_down = 0;
                    rcd->lcPwrOnTime = 0;
                }

                if(rcd->lstCapTime > rcd->lst_clock_adj_time)
                { // 负荷曲线时标已记录到大于校时时间结束点，清校时标志
                    rcd->lc_stus.clock_adjust = 0;
                    rcd->lst_clock_adj_time = 0;
                }

                rcd->differ--;
                rcd->lstCapTime += period;
                

                /* 捕获数据,捕获所用的时钟 */
                cap_sec = rcd->lstCapTime;
                lc_capture((LC_TYPE_t)i, rcd, cap_sec);
                rcd->patch_ready = false;

                /* 清除状态位 */
                if(rcd->differ == 0 && (mclock.datetime->u32datetime % period) <= 1)
                {
                    rcd->lc_stus.value = 0;
                    rcd->clock_stus = mclock.datetime->stus;
                }
            }
            else // 反向删点
            {
                log_addr_s addr = log_addr(lc_api_seek[i].idx);
                mlog.erase(addr, (uint16_t)(-rcd->differ)); // 时间往前调,删除相应点数
                lc_last_capture_time_update(i);
                /* 检查负荷曲线时标是否与当前时间匹配 */
                while(1)
                {
                    uint32_t stamp;
                    
                    if(mlog.fetch(addr, &stamp, 4, 1) == 0xFFFF) break;
                    if(stamp <= rcd->lstCapTime) break;
                    mlog.erase(addr, 1);
                }
            }
        }
    }

    lc_clock_adjust_flag.value = 0;
}

void loadcurve_second_run(void)
{
    if(lc_startcnt != 0) lc_startcnt--;

    /* 检出负荷曲线参数 */
    lc_para_load();

    /* 时钟调整状态字置位 */
    if(mclock.state_query(STUS_CLOCK_SHIFT_EVENT | STUS_CLOCK_BC_EVENT))
    {
        lc_clock_adjust_flag.clock_adjust = 1;
        if(mclock.state_query(STUS_CLOCK_INVALID))
        {
            lc_clock_adjust_flag.clock_invalid = 1;
        }
    }
}

/// @brief 掉电存储，只存储在RAM中，电池没电后丢失
/// @param  
void loadcurve_power_down_save(void)
{
    if(hal_mcu.pwrdn_query()) // 升级影响剔除
    {
        for(uint8_t i = 0; i < LC_TYPE_NUM; i++)
        {
            lc_rcd[i].lc_stus.power_down = true; // 置掉电标志
            lc_rcd[i].lc_stus_cs.value = ~lc_rcd[i].lc_stus.value; 
        }
    }
}

///@brief  获取负荷曲线各状态
///@brief  state - 预获取的状态位
///@brief  true - 触发   false - 未触发
bool loadcurve_state_query(LC_STUS state)
{
    return boolof(lc_out_stus & state);
}

///@brief  清除负荷曲线各状态
void loadcurve_state_clr(void)
{
    lc_out_stus = 0;
}

///@brief  清除负荷曲线buffer
void loadcurve_rcd_clr(LC_TYPE_t lc_type)
{
    mlog.empty(log_addr(lc_api_seek[lc_type].idx));
    lc_out_stus |= STUS_LC_CLR_PROFILE;

    /* 清负荷曲线, 立即补第一个点 */
    lc_rcd[lc_type].patch_ready = true;
    lc_rcd[lc_type].lc_stus.buffer_clr = true; // 置位有编程曲线参数引起的清空BUFFER
    lc_last_capture_time_update(lc_type);
}

void loadcurve_reset(uint8_t type)
{
    /* 恢复默认参数 */
    if(type & SYS_PARA_RESET)
    {
        lc_para_save(0, &lc_default_para, sizeof(lc_para_s));
    }

    for(LC_TYPE_t i = TYPE_LC1; i < LC_TYPE_NUM; i++)
    {
        if(type & SYS_DATA_RESET)
        {
            /* 清零存储负荷曲线点 */
            if(type != SYS_GLOBAL_RESET)
            {
                mlog.empty(log_addr(lc_api_seek[i].idx));
            }

            /* 清零相关记录 */
            memset(&lc_rcd[i], 0, sizeof(lc_rcd_s));
            lc_last_capture_time_update(i);
        }
    }

    lc_out_stus = 0;
}

//=======================loadcurve.c模块数据访问接口==============================
///@brief 获取负荷曲线参数
///@brief lc_type - 负荷曲线类型
///@brief 获取到的曲线参数
const lc_para_s* loadcurve_para_get(void)
{
    return lc_running_para;
}

///@brief 设置负荷曲线参数
///@brief lc_type - 负荷曲线类型
///@brief para - 预设值
///@brief 设置是否成功(true, false)
bool loadcurve_para_set(uint16_t ofst, void* para, uint16_t len)
{
    switch(ofst)
    {
        case member_offset(lc_para_s, period) + TYPE_LC1 * 4:
    #if LC2_ENABLE
        case member_offset(lc_para_s, period) + TYPE_LC2 * 4:
    #endif
        {
            uint32_t period = (*(uint32_t*)para); // 单位分钟转秒
            if(period != 60 && period != 300 && period != 600 && period != 900 && period != 1800 && period != 3600 && period != 86400) return false;
            lc_out_stus |= STUS_LC_PRG_PERIOD;
            break;
        }
        case member_offset(lc_para_s, start_time):
            {
                clock_s start_time = *(clock_s*)para;
                
                if(mclock.is_valid(&start_time) == false) return false;
            }
            break;
        case member_offset(lc_para_s, mode):

            break;
        default: return false;
    }
    return lc_para_save(ofst, para, len);
}



/// @brief 声明负荷曲线模块对象
const struct loadcurve_s loadcurve =
{
    .reset                  = loadcurve_reset,
    .rcd_clr                = loadcurve_rcd_clr,
    .para_get               = loadcurve_para_get,
    .para_set               = loadcurve_para_set,
    .state_query            = loadcurve_state_query,
    .state_clr              = loadcurve_state_clr,
};

/// @brief 声明负荷曲线模块任务接口
const struct app_task_t loadcurve_task =
{
    .init                   = loadcurve_init,
    .second_run             = loadcurve_second_run,
    .idle_run               = loadcurve_idle_run,
    .power_down_save        = loadcurve_power_down_save,
};


