#include "boot_entry.h"
#include "libHDiffPatch/HPatchLite/hpatch_lite.h"

#ifndef alignof
#define alignof(x,size)  ((size)*(((uint32_t)(x)+(size)-1)/(size)))
#endif


/// @brief 继承hpatchi_listener_t定义新、旧文件的物理地址
typedef struct {
    hpatchi_listener_t      base;
    const uint8_t*          old_file_addr;
    uint32_t                diff_file_addr;
    uint32_t                new_file_addr;
} TPatchListener;

///======= 移植实现压缩算法 ===========
#define _CompressPlugin_tuz
//#define _CompressPlugin_qlz

#include "decompresser_demo.h" 

///======= 移植实现读取差分文件中的旧程序文件头信息 ===========
static void get_image_addr_from_hdr_info (uint32_t diff_file_addr, void* hdr, uint8_t size)
{
    boot.file_read(diff_file_addr, hdr, size);
}

///======= 移植实现写入差分文件中的还原后的文件信息 ===========
static void set_image_addr_with_hdr_info (uint32_t diff_file_addr, void* hdr, uint8_t size)
{
    boot.file_write(diff_file_addr, hdr, size);
}

///======= 移植实现code、dataflash区程序文件内容的读写 ==========
static hpi_BOOL _do_readOld(struct hpatchi_listener_t* listener, hpi_pos_t read_from_pos, hpi_byte* out_data, hpi_size_t data_size)
{
    TPatchListener* self = (TPatchListener*)listener;
    if(data_size != 0)
    {
        memcpy(out_data, self->old_file_addr + read_from_pos, data_size);
    }
    return hpi_TRUE;
}

static hpi_BOOL _do_readDiff(hpi_TInputStreamHandle inputStream, hpi_byte* out_data, hpi_size_t* data_size)
{
    int i;
    TPatchListener* self = (TPatchListener*)inputStream;
    for (i = 10; i != 0; i--)
    {
        if(boot.file_read(self->diff_file_addr, out_data, *data_size)) break;
    }
    self->diff_file_addr += *data_size;
    return (i != 0) ? hpi_TRUE : hpi_FALSE;
}

static hpi_BOOL _do_writeNew(struct hpatchi_listener_t* listener, const hpi_byte* in_data, hpi_size_t data_size)
{
    int i;
    TPatchListener* self = (TPatchListener*)listener;
    for (i = 10; i != 0; i--)
    {
        if(boot.file_write(self->new_file_addr, (void*)in_data, data_size)) break;
    }
    self->new_file_addr += data_size;
    return (i != 0) ? hpi_TRUE : hpi_FALSE;
}
///==========================================
/** 
 * image_revert
 * @param   file_size: 差分压缩文件大小
 * @param   dataflash_addr[i]: 差分压缩文件存放在dataflash中的地址
 * @param   dataflash_addr[o]: 还原后的文件存放在dataflash中的地址
 * @return  还原后的升级文件大小
 */
uint32_t image_revert(uint32_t* dataflash_addr, uint32_t file_size)
{
    uint32_t diff_hdr[3] = {0,};

    TPatchListener      listener;
    hpi_compressType    compress_type;
    hpi_pos_t           uncompress_size, new_file_size;
    hpi_BOOL            ret;

    get_image_addr_from_hdr_info(*dataflash_addr, diff_hdr, sizeof(diff_hdr));

    memset(&listener, 0, sizeof(TPatchListener));
    listener.old_file_addr  = (const uint8*)diff_hdr[0];                  ///MCU中的旧程序地址
    listener.diff_file_addr = *dataflash_addr;                            ///差分升级包存放地址
    listener.new_file_addr  = *dataflash_addr + alignof(file_size, 4096); ///还原新程序存放地址, 放于差分文件之后(4096字节对齐)

    /* 查询是否曾经差分还原过 */
    if (diff_hdr[0] == 0x55AA55AA)
    {
        *dataflash_addr = diff_hdr[1];
        return diff_hdr[2];
    }

    ret = hpatch_lite_open(&listener, _do_readDiff, &compress_type, &new_file_size, &uncompress_size);
    if(ret == hpi_TRUE)
    {
        tuz_TStream tuzStream;
        uint8_t decompress_buf[1024 + 1024]; ///各1K用于压缩字典+解压缩缓冲
        uint8_t patch_buf[4096];             ///4K用于还原缓冲
        switch(compress_type)
        {
            case hpi_compressType_tuz: 
            { // requirements memory size: dictSize + patchCacheSize
                size_t reservedMemSize = _tuz_TStream_getReservedMemSize(&listener, _do_readDiff);
                if(reservedMemSize > 1024) return 0; ///字典暂时只支持1K以内
                tuz_TStream_open(&tuzStream, &listener, _do_readDiff, decompress_buf, 1024, 1024);
                listener.base.diff_data = &tuzStream;
                listener.base.read_diff = _tuz_TStream_decompress;
                listener.base.read_old  = _do_readOld;
                listener.base.write_new = _do_writeNew;
                break;
            }
            default: return 0;
        }
        ret = hpatch_lite_patch(&listener.base, new_file_size, patch_buf, sizeof(patch_buf));
        if(ret != hpi_TRUE) return 0;       

        /* 正确差分还原后，保存还原后的升级文件地址及长度信息，同时差分文件也被破坏 */
        diff_hdr[0] = 0x55AA55AA;
        diff_hdr[1] = *dataflash_addr + alignof(file_size, 4096);
        diff_hdr[2] = new_file_size;
        set_image_addr_with_hdr_info(*dataflash_addr, diff_hdr, sizeof(diff_hdr));
        *dataflash_addr = diff_hdr[1];
        file_size       = new_file_size;
    }

    return file_size;
}

// end of file
