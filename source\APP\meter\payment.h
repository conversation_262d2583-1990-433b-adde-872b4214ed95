/********************************************************************************
* @file    payment.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __PAYMENT_H__
#define __PAYMENT_H__
#include  "typedef.h"
#include  "..\Config\app_config.h"

/// @brief 组合数据结构
typedef struct
{
    int32_t remain;                             // 余数部分 XXXX.XXXX (与电价单位一致)
    int32_t data;                               // 整数部分 XXXXXX.XX (与Account定义的货币单位一致)
}group_data_s;

/// @brief 付费模式
typedef enum
{
    POST_PAY_MODE = 1,                          // 远程费控
    PRE_PAY_MODE,                               // 本地费控(包含本地预付费，远程预付费)
}pay_mode_t;

/// @brief 信用状态
typedef union
{
    struct
    {
        uint8_t monthly_credit        : 1;      // 月固定信用使用中
        uint8_t purchase_credit       : 1;      // 充值信用使用中
        uint8_t low_credit1           : 1;      // 低信用
        uint8_t low_credit2           : 1;      // 低信用
        uint8_t no_credit             : 1;      // 信用消耗殆尽
        uint8_t overdraft             : 1;      // 透支
    };
    uint8_t val;
}credit_status_s;

typedef union 
{
    uint8_t val;
    struct
    {
        uint8_t low_credit_alert1     : 1;      // 低信用报警1
        uint8_t low_credit_alert2     : 1;      // 低信用报警2 
    };
}pay_status_s;

/// @brief 付费控制
typedef union
{
    struct
    {
        uint16_t beep_stop1       :1;         // 蜂鸣器报警阶段1停止
        uint16_t beep_stop2       :1;         // 蜂鸣器报警阶段2停止
        uint16_t relay_off        :1;         // 继电器欠费拉闸
    };
    uint16_t word;
}pay_ctrl_s;

/// @brief 付费模块参数定义
typedef struct
{
    uint16_t crc;
    uint16_t chk;                               // 保留，CRC计算对齐

    group_data_s warning_threshold1;            // 低信用报警门限1 点亮背光，同时液晶“请购电”闪烁显示（亮 1s 灭 1s）。
    group_data_s warning_threshold2;            // 低信用报警门限2 电能表拉闸，点亮背光，跳闸灯常亮，同时液晶“请购电”闪烁显示（亮 1s 灭 1s），“拉闸”字样常显（允许插卡或按键合闸恢复）。
    group_data_s preset_credit_amount;          // 透支门限
    group_data_s connect_threshold;             // 合闸允许阈值
    int32_t max_credit;                         // 囤积门限, 固定单位为0.01
    int32_t max_vend;                           // 单次购电上限, 固定单位为0.01
}pay_para_s;

/// @brief 付费模块掉电保存数据块
typedef struct
{
    uint16_t flag;                              // 掉电保存标志
    uint16_t crc;

    credit_status_s credit_status;              // 信用状态
    pay_status_s    pay_status;                 // 付费状态

    group_data_s total_amount_paid;             // 累计消费总额，单位同信用单位，0.01
    group_data_s residual_credit;               // 剩余金额，单位同信用单位，0.01 
    
    PAY_CREDIT_DATA_TYPE total_purchase_credit; // 累计充值金额，单位同信用单位，0.01
    PAY_CREDIT_DATA_TYPE last_purchase_credit;  // 上1次充值金额，单位同信用单位，0.01
    
    ENERGY_DEF_FORMAT    lst_sec_en_bk;         // 上1秒的电能保存
    uint32_t remain_paid;                       // 剩余待扣金额, 单位为 价格单位 0.0001
    uint32_t total_purchase_cnt;                // 累计充值次数\购电次数
    uint32_t last_price;                        // 上一个阶梯电价
    pay_ctrl_s ctrl;                            // 付费控制
    uint8_t  last_step;                         // 上一个阶梯号
}pay_data_s;

typedef struct
{
    credit_status_s status;                     // 付费模块对外输出状态
    group_data_s available_credit;              // 可用信用 >=0 
    group_data_s amount_to_clear;               // 透支信用 

    group_data_s current_credit_amount_fixed;   // 当前信用额度->每月固定可用信用(每月免费信用)剩余
    group_data_s current_credit_amount;         // 当前信用额度
}pay_account_s;

/* Exported defines ----------------------------------------------------------*/
typedef uint16_t    TYPE_PAY_STUS;   
#define STUS_TOP_UP                 ((TYPE_PAY_STUS)1 << 1)    // 充值成功
#define STUS_PAY_PROGRAM            ((TYPE_PAY_STUS)1 << 2)    // 编程付费模块参数
#define STUS_SWITCH_PAY_MODE        ((TYPE_PAY_STUS)1 << 3)    // 切换付费模式
#define STUS_DEDUCT                 ((TYPE_PAY_STUS)1 << 4)    // 扣费成功

/* Exported macro ------------------------------------------------------------*/
/* Exported functions -------------------------------------------------------*/
struct payment_t
{
    void (*reset)(uint8_t type);
    bool (*state_query)(TYPE_PAY_STUS state);
    void (*state_clr)(void);
    const pay_para_s* (*para_get)(void);
    bool (*para_set)(uint16_t ofst, const void* val, uint16_t len);
    pay_data_s* (*data_get)(void);

    /**
     * @brief  预购电判断。(该函数必须提供STS模块使用)
     * @param  data - 充值金额/电量
     * @retval 返回预购电结果，true - 成功，FALSE - 失败
     */
    bool (*pre_top_up)(int32_t data);

    /**
     * @brief  购电处理。
     * @param  data - 充值金额/电量
     * @retval 返回购电结果，true - 成功，false - 失败
     */
    bool (*top_up)(int32_t data);

    /**
     * @brief  扣费处理。
     * @param  data - 扣费金额/电量, 单位0.01
     * @retval 返购电结果，true - 成功，false - 失败
     */
    bool (*deduct)(int32_t data);

    /**
     * @brief  清余额
     * @param  none
     * @retval 返回结果，true - 成功，false - 失败
     */
    bool (*amount_clr)(void);

    /**
     * @brief  获取当前账户信息
     * @param  None
     * @retval 返回账户信息缓存指针
     */
    pay_account_s* (*account_get)(void);

    /**
     * @brief  获取当前电价
     * @param  None
     * @retval 返回电价
     */
    uint32_t (*current_price_get)(void);

    /**
     * @brief  获取默认日耗信用
     * @param  None
     * @retval 返回默认日耗信用
     */
    PAY_CREDIT_DATA_TYPE (*default_daily_paid_get)(void);

    PAY_CREDIT_DATA_TYPE (*avg_hour_consum_get)(void);
};
extern const struct payment_t pay;

#endif //__PAYMENT_H__
