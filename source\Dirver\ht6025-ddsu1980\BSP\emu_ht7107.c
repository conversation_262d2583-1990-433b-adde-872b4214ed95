/**
  ******************************************************************************
  * @file    emu_ht7107.c
  * <AUTHOR> @date    2024
  * @brief   ht7107 计量芯片驱动
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include <math.h>
#include "bsp_cfg.h"
#include "mic.h"
#include "utils.h"
#include "debug.h"
#include "eeprom.h"
#include "hal_gpio.h"
#include "hal_timer.h"
#include "hal_flash.h"

/// ht7107 register 定义
/// 属据寄存器
#define REG_R_Spl_I1        0x00    // 3 电流通道 1 的 ADC 采样数据
#define REG_R_Spl_I2        0x01    // 3 电流通道 2 的 ADC 采样数据
#define REG_R_Spl_U         0x02    // 3 电压通道的 ADC 采样数据
#define REG_R_I_Dc          0x03    // I 通道直流均值（当前计量通道）
#define REG_R_U_Dc          0x04    // U 通道直流均值
#define REG_R_Rms_I1        0x06    // 3 电流通道 1 的有效值
#define REG_R_Rms_I2        0x07    // 3 电流通道 2 的有效值
#define REG_R_Rms_U         0x08    // 3 电压通道的有效值
#define REG_R_Freq_U        0x09    // 2 电压频率
#define REG_R_PowerP1       0x0A    // 3/4 第一通道有功功率，默认 3 字节。可配 4 字节
#define REG_R_PowerQ1       0x0B    // 3/4 第一通道无功功率，默认 3 字节。可配 4 字节
#define REG_R_PowerS        0x0C    // 3/4 视在功率，默认 3 字节。可配 4 字节
#define REG_R_Energy_P      0x0D    // 3 有功能量
#define REG_R_Energy_Q      0x0E    // 3 无功能量
#define REG_R_UdetCNT       0x0F    // 3 SAG/Peak 工况持续时间计数
#define REG_R_PowerP2       0x10    // 3/4 第二通道有功功率，默认 3 字节。可配 4 字节
#define REG_R_PowerQ2       0x11    // 3/4 第二通道无功功率，默认 3 字节。可配 4 字节
#define REG_R_MAXUWAVE      0x12    // 3 电压半波波形峰值寄存器，22bit
#define REG_R_CRCChecksum   0x15    // 3 校表参数校验和寄存器(CRC16)
#define REG_R_BackupData    0x16    // 3 通讯数据备份寄存器
#define REG_R_COMChecksum   0x17    // 3 通讯校验和寄存器
#define REG_R_SUMChecksum   0x18    // 3 校表参数校验和寄存器
#define REG_R_EMUSR         0x19    // 2 EMU 状态寄存器
#define REG_R_SYSSTA        0x1A    // 1 系统状态寄存器
#define REG_R_ChipID        0x1B    // 3 ChipID，默认值为 7053B0
#define REG_R_DeviceID      0x1C    // 3 DeviceID，默认值为 705321
/// 计量参数和状态寄存器
#define REG_W_EMUIE         0x30    // 0000 2(15bit) EMU 中断使能寄存器
#define REG_W_EMUIF         0x31    // 800000 3(16bit) EMU 中断标志寄存器
#define REG_W_WPREG         0x32    // 00 1(8bit) 写保护寄存器
#define REG_W_SRSTREG       0x33    // 00 1(8bit) 软件复位寄存器
#define REG_W_EMUCFG        0x40    // 0000 2(15bit) EMU 配置寄存器
#define REG_W_FreqCFG       0x41    // 0088 2(9bit) 时钟/更新频率配置寄存器
#define REG_W_ModuleEn      0x42    // 007E 2(14bit) EMU 模块使能寄存器
#define REG_W_ANAEN         0x43    // 0003 1(7bit) ADC 开关寄存器
#define REG_W_IOCFG         0x45    // 0000 2(10bit) IO 输出配置寄存器
#define REG_W_GP1           0x50    // 0000 2(16bit) 通道 1 的有功功率校正
#define REG_W_GQ1           0x51    // 0000 2(16bit) 通道 1 的无功功率校正
#define REG_W_GS1           0x52    // 0000 2(16bit) 通道 1 的视在功率校正
#define REG_W_GP2           0x54    // 0000 2(16bit) 通道 2 的有功功率校正
#define REG_W_GQ2           0x55    // 0000 2(16bit) 通道 2 的无功功率校正
#define REG_W_GS2           0x56    // 0000 2(16bit) 通道 2 的视在功率校正
#define REG_W_QPhsCal       0x58    // FF00 2(16bit) 无功相位补偿
#define REG_W_ADCCON        0x59    // 0000 2(12bit) ADC 通道增益选择
#define REG_W_I2Gain        0x5B    // 0000 2(16bit) 电流通道 2 增益补偿
#define REG_W_I1Off         0x5C    // 0000 2(16bit) 电流通道 1 的偏置校正
#define REG_W_I2Off         0x5D    // 0000 2(16bit) 电流通道 2 的偏置校正
#define REG_W_UOff          0x5E    // 0000 2(16bit) 电压通道的偏置校正
#define REG_W_PQStart       0x5F    // 0040 2(16bit) 起动功率设置
#define REG_W_HFConst       0x61    // 0040 2(15bit) 输出脉冲频率设置
#define REG_W_CHK           0x62    // 0010 1(8bit) 窃电阈值设置
#define REG_W_IPTAMP        0x63    // 0020 2(16bit) 窃电检测电流域值
#define REG_W_Dec_Shift     0x64    // 00 1(8bit) 通道 1 的相位校正(移采样点方式)
#define REG_W_P1OFFSETH     0x65    // 00 1(8bit)/2（可配置）通道 1 有功功率偏置校正参数高 8 位，和P1OFFSETL 组成为 16bit 补码
#define REG_W_P2OFFSETH     0x66    // 00 1(8bit) /2（可配置）通道 2 有功功率偏置校正参数高 8 位，和P2OFFSETL 组成为 16bit 补码
#define REG_W_Q1OFFSETH     0x67    // 00 1(8bit) /2（可配置）通道 1 无功功率偏置校正参数高 8 位，和Q1OFFSETL 组成为 16bit 补码
#define REG_W_Q2OFFSETH     0x68    // 00 1(8bit) /2（可配置）通道 2 无功功率偏置校正参数高 8 位，和Q2OFFSETL 组成为 16bit 补码
#define REG_W_I1RMSOFFSET   0x69    // 0000 2(16bit) 通道 1 有效值补偿寄存器，为 16bit 无符号数
#define REG_W_I2RMSOFFSET   0x6A    // 0000 2(16bit) 通道 2 有效值补偿寄存器，为 16bit 无符号数
#define REG_W_URMSOFFSET    0x6B    // 0000 2(16bit) 通道 U 有效值补偿寄存器,为 16bit 无符号数
#define REG_W_ZCrossCurrent 0x6C    // 0004 2(16bit) 电流过零阈值设置寄存器
#define REG_W_GPhs1         0x6D    //  0000 2(16bit) 通道 1 的相位校正（PQ 方式）
#define REG_W_GPhs2         0x6E    // 0000 2(16bit) 通道 2 的相位校正（PQ 方式）
#define REG_W_PFCnt         0x6F    // 0000 2(16bit) 快速有功脉冲计数
#define REG_W_QFCnt         0x70    // 0000 2(16bit) 快速无功脉冲计数
#define REG_W_ANACON        0x72    // 0031 2(16bit) 模拟控制寄存器
#define REG_W_SUMCHECKL     0x73    //  0000 2(16bit) 校验和低 16 位，由用户写入，使能比较功能后，芯片比较给出标志
#define REG_W_SUMCHECKH     0x74    // 00 1(8bit) 检验和高 8 位，由用户写入，使能比较功能后，芯片比较给出标志
#define REG_W_MODECFG       0x75    //  00 1(8bit) 模式配置寄存器
#define REG_W_P1OFFSETL     0x76    // 00 1(8bit) 通道 1 有功功率偏置校正参数低 8 位，和P1OFFSETH 组成 16bit 补码
#define REG_W_P2OFFSETL     0x77    // 00 1(8bit) 通道 2 有功功率偏置校正参数低 8 位，和P2OFFSETH 组成 16bit 补码
#define REG_W_Q1OFFSETL     0x78    //  00 1(8bit) 通道 1 无功功率偏置校正参数低 8 位，和Q1OFFSETH 组成 16bit 补码
#define REG_W_Q2OFFSETL     0x79    // 00 1(8bit) 通道 2 无功功率偏置校正参数低 8 位，和Q2OFFSETH 组成 16bit 补码
#define REG_W_UPeakLvl      0x7A    // 0000 2(16bit) UPEAK 阈值寄存器，16 位无符号数，与 ADC绝对值的高位对齐
#define REG_W_USagLvl       0x7B    // 0000 2(16bit) USAG 阈值寄存器，16 位无符号数，与 ADC绝对值的高位对齐
#define REG_W_UCycLen       0x7C    // 0000 2(16bit) PEAK SAG 检测周期设置寄存器，16bit

#define EMUCFG_DEFAULT      0x2000  // EMU 配置寄存器默认值 ,能量寄存器读后清 0

// 常数定义
#define DATA_2EXP12             4096ul          // 2 ^ 12
#define DATA_2EXP13             8192ul          // 2 ^ 13
#define DATA_2EXP14             16384ul         // 2 ^ 14 
#define DATA_2EXP15             32768ul         // 2 ^ 15
#define DATA_2EXP16             65536ul         // 2 ^ 16
#define DATA_2EXP20             1048576ul       // 2 ^ 20
#define DATA_2EXP23             8388608ul       // 2 ^ 23
#define DATA_2EXP24             16777216ul      // 2 ^ 24
#define DATA_2EXP31             0x80000000ul    // 2 ^ 31

//电压通道采样参数
#define Rt_v                    1.2             // kΩ 采样电阻
#define Ra_v                    (220*7)         // kΩ 分压电阻
#define Gv                      1               // 电压通道增益
#define Vu                      ((INPUT_VOLTAGE/(Rt_v + Ra_v))*Rt_v*Gv*1000)  // 电压采样电压 mV

//电流通道采样参数
#define Ra_i                    (200)           // uΩ 锰铜电阻
#define Gi                      32              // 电流通道增益
#define Vi                      (BASE_CURRENT*Ra_i*Gi/1000)  // 电流采样电压 mV

//零线电流采样参数
#define Gi_n                    1               // 零线电流通道增益
#define CT_n                    1000            // 零线电流互感器变比
#define vi_n                    (BASE_CURRENT*1000/CT_n*Gi_n)  // 零线电流采样电压 mV

#define HF_CONST                (uint16_t)(7.12*Vu*Vi*10000/(METER_CONST * BASE_CURRENT * INPUT_VOLTAGE))   // 高频脉冲常数 7.12*Vu*Vi*10^10/(EC*Un*Ib)

#define SAG_CYCLE               (4)       // (SGA判断半周波数)
#define CAL_CHNSEL               0.125    // 通道选择判断阀值
#define SAMPLE_NUM            	 6        // 最大采样次数
#define START_P_RADIO           (START_PWR / 2) // 有功启动
#define START_Q_RADIO           START_P_RADIO   // 无功启动
#define START_REG               (uint16_t)(START_P_RADIO/cal_para[0].P_ratio)  // 有功启动,单位:


/// 计量芯片通讯口定义
#define COM_EMU                  HAL_UART0
#define COM_SEND(ch)             hal_uart.print(COM_EMU, ch)
#define COM_RECV(ch)             hal_uart.scan(COM_EMU, &ch, 20)

#define CAL_CS16(ptr,len)       crc16(0,(uint8_t*)(ptr) + 4, len - 4)
#define ee_cal_read(x,y,z)      eeprom.read(x,y,z)
#define ee_cal_write(x,y,z)     eeprom.write(x,y,z)
#define mcu_cal_read(x,y,z)     hal_flash.read((MCU_CAL_DATA_BASE + x), y, z)
#define mcu_cal_write(x,y,z)    hal_flash.write((MCU_CAL_DATA_BASE + x), y, z)
#define MEASURE_PARA_ADDR       (member_offset(cal_data_s, mic_para))  // 计量参数地址

typedef union
{
    struct
    {
        uint16_t chk;       // 
        uint16_t cs;        // 校表参数校验和

        float    U_ratio;   // 电压系数
        float    I_ratio;   // 电流系数
        float    P_ratio;   // 功率系数

        uint16_t I_gain;    // 电流增益
        uint16_t P_gain;    // 功率增益

        uint16_t Phs_ofst;  // 相位校准
        uint16_t P_ofst;    // 有功功率偏移校准
        uint16_t Q_ofst;    // 无功功率偏置校准

        uint16_t I_ofst;    // 电流偏移校准
        uint16_t HFconst;   // 高频脉冲常数
        uint16_t I_zero_reg;
	};
	uint8_t reserve[32];
}cal_para_s;

typedef struct
{
    cal_para_s    cal_para[CHANNEL_NUM];    
    MeasurePara_s mic_para;
}cal_data_s;

typedef struct
{
    SMALLOC(cal_data_s, space, 512);  /// 如果这里编译报错，需在datastore.h中修改 CAL_DATA_SIZE 的大小(修改为128的整数倍)直到编译通过，默认是 512.
} mic_store_s;

/// @brief 默认校表参数
static const cal_para_s cal_para_default = {
    .cs         = 0,
    .U_ratio    = 1.0,    
    .I_ratio    = 1.0,
    .P_ratio    = 1.0,
    .I_gain     = 0,    
    .P_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .Q_ofst     = 0,
    .I_ofst     = 0,
    .HFconst    = HF_CONST,
    .I_zero_reg = 0,
};
/// @brief 零线通道默认值
static const cal_para_s cal_para_N_default = {
    .cs         = 0,
    .U_ratio    = 1.0,    
    .I_ratio    = 1.0,
    .P_ratio    = 1.0,
    .I_gain     = 0,    
    .P_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .Q_ofst     = 0,
    .I_ofst     = 0,
    .HFconst    = HF_CONST,
    .I_zero_reg = 0,
};

static const MeasurePara_s mic_para_default =
{
    .cs         = 0,
    .acc_mode   = MIC_ALGACC_MODE,          // 默认代数和计量方式
    .wiring_mode= MIC_CHN_PHASE_MODE,
    .led_mode   = 0,
    .constant   = METER_CONST,
};

uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph);
void measure_ic_init(uint8_t pwr_down);

#if USE_EMU_AT_LOSS_VOLTAGE
static void (*power_off_running)(void);
#endif
static uint8_t led_act_remain_time;         // 有功电能脉冲LED计时器
static uint8_t led_rea_remain_time;         // 无功电能脉冲LED计时器
#if USE_LED_VAPULSE
static uint8_t led_app_remain_time;         // 视在电能脉冲LED计时器
#endif
static cal_para_s    cal_para[CHANNEL_NUM]; // att7022e-校正参数
static MeasurePara_s measure_para;          // 计量参数
InstantVal_s         instant_value;         // 瞬时计量数据块,如电流电压频率功率等等
static uint8_t       ep_pulse_cnt;          // 有功电能脉冲计数器
static bool          measure_status;        // 计量状态
static SAMPLE_CHANNEL mic_channel;          // 计量通道

/// @brief 通讯口初始化
/// @param  
static void emu_com_init(void)
{
    hal_uart.open(COM_EMU, UC_HALF, CHAR_8E1, BAUDE_4800BPS, NULL, 0);  // 打开串口, 4800波特率, 8位数据位偶校验, 4800波特率，循环接收
}

/// @brief 计量芯片寄存器读取
/// @param  reg 寄存器地址
/// @return 寄存器值
static uint32_t emu_register_read(uint8_t reg)
{
    union32 dat;

    for(uint8_t i = 0; i < 3; i++) /// 超时或校验错误重试
    {
        uint8_t cs2 = 0;
        
        dat.l = 0;
        COM_SEND(0x6A);
        COM_SEND(reg); 
        if(COM_RECV(dat.b[2])) ///连续读3个字节数据+1字节校验
        if(COM_RECV(dat.b[1]))
        if(COM_RECV(dat.b[0]))
        if(COM_RECV(cs2))
        {
            uint8_t cs3 = ~(0x6A + reg + dat.b[2] + dat.b[1] + dat.b[0]);
            if(cs2 == cs3)
            {
                return dat.l;
            }
        }
        hal_timer.msdly(20);
    }
	return 0xFFFFFFFF;
}

/// @brief 计量芯片寄存器写入
/// @param reg 寄存器地址
/// @param dat 寄存器值 
/// @return TRUE 成功 FALSE 失败
static bool emu_register_write(uint8_t reg, uint16_t data)
{
    union16 dat;
    uint8_t cs;

    dat.w = data;
    cs = ~(0x6A + (reg | 0x80) + dat.b[0] + dat.b[1]);  // 计算校验和
    for(uint8_t i = 0; i < 3; i++) /// 超时或校验错误重试
    {
        uint8_t ack;
        COM_SEND(0x6A);
        COM_SEND(reg | 0x80);
        COM_SEND(dat.b[1]);
        COM_SEND(dat.b[0]);
        COM_SEND(cs);

        if(COM_RECV(ack)) ///读1字节校验
        {
            if(ack == 0x54) { return true; }
        }
        hal_timer.msdly(20);
    }
    return false;
}

static void emu_reset(void)
{
#if USE_HDW_RST_MIC
    // 复位计量芯片
    IO_EMU_PWR_OFF();
    hal_timer.xms(2);
    IO_EMU_PWR_ON();
    hal_timer.xms(6);
#endif
    measure_status = FALSE;
}


/// @brief 校验和处理
/// @param type 0-清除校验和，1—获取校验和，2-检查校验和, 3-反转校验和
/// @return TRUE 校验和正确，校验和错误
static bool emu_reg_check_sum_pro(uint8_t type)
{
    static uint32_t emu_reg_check_sum; //40H---7CH，其中连续地址中没有分配寄存器的部分不计算之内。计算不包含 6FH-74H 寄存器Default 值为 0x 01 00 BD。
    switch(type)
    {
        case 0:
            emu_reg_check_sum  = 0;
            return TRUE;
        case 1:
            emu_reg_check_sum  = emu_register_read(REG_R_SUMChecksum);
            return TRUE;
        case 2:
            if(emu_reg_check_sum  == emu_register_read(REG_R_SUMChecksum)) return TRUE;
            break;
        case 3:
            emu_reg_check_sum  = ~emu_reg_check_sum;
            return TRUE;
        default:
            break;
    }
    return FALSE;
}

/// @brief 参数检查更新
/// @param para         参数指针
/// @param hfconst_gain 脉冲加倍数目前只支持2,4,8倍
/// @return TRUE 计量芯片正常运行，FALSE 计量芯片参数异常
static bool emu_para_refresh(const cal_para_s* para, uint8_t hfconst_gain)
{
    uint16_t freq_cfg;

    /// 校验和处理
    if(0x705321 != emu_register_read(REG_R_DeviceID)) return FALSE; // 校验设备ID 
    if(emu_reg_check_sum_pro(2)) return TRUE;

    /// 复位计量芯片
    emu_com_init();
    emu_reset();

    freq_cfg = 0x0088; //EMU时钟频率1Mhz，脉宽40ms(国网要求大于30ms)
    if(hfconst_gain == 2) { freq_cfg = 0x0288; }
    else if(hfconst_gain == 4) { freq_cfg = 0x0488; }
    else if(hfconst_gain == 8) { freq_cfg = 0x0688; }

    //1，配置40H-45H寄存器
    if(emu_register_write(REG_W_WPREG,      0x00BC))    //使能写40H-45H寄存器
    if(emu_register_write(REG_W_EMUCFG,     EMUCFG_DEFAULT))    //能量寄存器读后清零
    if(emu_register_write(REG_W_FreqCFG,    freq_cfg))  //EMU时钟频率1Mhz，脉宽40ms(国网要求大于30ms)
    if(emu_register_write(REG_W_ModuleEn,   0x007E))    //无功，有功累计，高通滤波打开
#if USE_CHANNEL_2
    if(emu_register_write(REG_W_ANAEN,      0x0007))    //打开电流通道1,2和电压通道
#else
    if(emu_register_write(REG_W_ANAEN,      0x0003))    //打开电流通道1和电压通道
#endif
    if(emu_register_write(REG_W_IOCFG,      0x0010))    //PF 为高电平有效，使能uart偶校验，数据3字节
    //2，配置50H-7CH寄存器
    if(emu_register_write(REG_W_WPREG,      0x00A6))    //使能写50H-7CH寄存器

    if(emu_register_write(REG_W_HFConst,    0x0000))    //

    if(emu_register_write(REG_W_GP1,        para[0].P_gain))    //
    if(emu_register_write(REG_W_GQ1,        cal_para[0].P_gain))    //
    if(emu_register_write(REG_W_GS1,        cal_para[0].P_gain))    //
    if(emu_register_write(REG_W_GP2,        cal_para[1].P_gain))    //
    if(emu_register_write(REG_W_GQ2,        cal_para[1].P_gain))    //
    if(emu_register_write(REG_W_GS2,        cal_para[1].P_gain))    //
    if(emu_register_write(REG_W_QPhsCal,    0xFF00))    //EMU时钟1Mhz下，50Hz固定写0xFF00，60Hz固定写0xFE98

    if(emu_register_write(REG_W_GPhs1,      cal_para[0].Phs_ofst))    //
    if(emu_register_write(REG_W_GPhs2,      cal_para[1].Phs_ofst))    //

    if(emu_register_write(REG_W_P1OFFSETH,  (uint8_t)(cal_para[0].P_ofst>>8)))      //
    if(emu_register_write(REG_W_P1OFFSETL,  (uint8_t)cal_para[0].P_ofst))           //
    if(emu_register_write(REG_W_P2OFFSETH,  (uint8_t)(cal_para[1].P_ofst>>8)))      //
    if(emu_register_write(REG_W_P2OFFSETL,  (uint8_t)cal_para[1].P_ofst))           //

    if(emu_register_write(REG_W_I1RMSOFFSET,cal_para[0].I_ofst))    //
    if(emu_register_write(REG_W_I2RMSOFFSET,cal_para[1].I_ofst))    //
    if(emu_register_write(REG_W_URMSOFFSET, 0x0000))    // 需调试

    if(emu_register_write(REG_W_PQStart,    START_REG))    // 需调试

    if(emu_register_write(REG_W_ADCCON,     0x000C))    //通道1增益为16，电压增益为0，必须最后写！！！有启动计量作用。
    if(emu_register_write(REG_W_WPREG,      0x0000))    //禁止写40H-45H寄存器 50H-7CH寄存器
    {
        hal_timer.xms(30);      // 
        emu_reg_check_sum_pro(1);
        DBG_PRINTF(P_EMU, T);
        DBG_PRINTF(P_EMU, D, "\r\nht7136 init success!......\r\n");
    }
    return FALSE;
}
/// @brief 数据检出，移植不用改
/// @param  
static void emu_calpara_checkout(void)
{
    uint8_t    chn;
    cal_para_s* ptr;

    /* 校验RAM中的计量参数, EEPROM和CODEFLASH都存放有计量参数 !!!掉电涉及电能显示，可能用到变比 */
    if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
    {
        ee_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
        if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
        {
            mcu_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
            if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
            {
                measure_para = mic_para_default;
                measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            }
        }
        DBG_PRINTF(P_EMU, D, "mesure_para check out faile!!  MEASURE_PARA_ADDR: %x\r\n", MEASURE_PARA_ADDR);
    }

    /* 校验RAM中的校表参数. EEPROM和CODEFLASH都存放有校表参数 */
    for(chn = 0, ptr = cal_para; chn < CHANNEL_NUM; chn++, ptr++)
    {
        if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
        {
            ee_cal_read(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
            if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
            {
                mcu_cal_read(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
                if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
                {
                    if(chn == CHANNEL_2)
                    {
                        *ptr = cal_para_N_default;
                    }
                    else
                    {
                        *ptr = cal_para_default;
                    }
                    instant_value.stus.uncal |= 1 << chn;
                    ptr->cs = CAL_CS16(ptr, sizeof(cal_para_s));
                }
                else
                {
                    ee_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
                }
            }
            else
            {
                cal_para_s tmp;
                mcu_cal_read(chn * sizeof(cal_para_s), &tmp, sizeof(cal_para_s));
                if(tmp.cs != CAL_CS16(&tmp, sizeof(cal_para_s)))
                {
                    mcu_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
                }
            }
        }
    }
}

/// @brief emu检测，移植不用改
/// @param  
/// @return 
static bool emu_verify(void)
{
    static uint8_t  cnt = 5;

    /* 校验RAM中的校表参数 */
    emu_calpara_checkout();

    /* 校验计量芯片内的参数 */
    if(emu_para_refresh(cal_para, 0))
    {
        cnt = 5;
        instant_value.stus.error = 0;
        return TRUE; 
    }
    else if(cnt > 0 && --cnt == 0) /* 如果计量芯片数据读写不正常 */
    {
        instant_value.stus.error = 1;
        measure_ic_init(0);
    }

    return FALSE;
}

/// @brief 获取频率
/// @param  
/// @return 
static float emu_freq_val(void)
{
    uint32_t freq = emu_register_read(REG_R_Freq_U);
    return (float)(1000000/(2.0 * freq));  // 频率 = femu/(UFREQ*2) Hz
}

/// @brief 获取功率因素，移植不用改
/// @param ph 相
static float emu_pf_val(EMU_PHASE_TYPE ph)
{
    float pf;

    if(equ(instant_value.pwr_s[ph], 0)) return 1.0;
    pf = fabs(instant_value.pwr_p[ph] / instant_value.pwr_s[ph]);
    if(pf > 1.0) return  1.0;
    return pf;
}

/// @brief 计算相角，移植不用修改
/// @param ph
/// @return
#define ANGLE_360       360.0
#define PI              3.141592
static float emu_vi_angle_val(EMU_PHASE_TYPE ph, const float* pf)
{
    float angle; // 弧度，1弧度=(180/π)°
    float u_i_angle; // 角度（°）

    angle = acos(pf[ph]);
    u_i_angle = angle * ANGLE_360 / 2 / PI;

    if(less(instant_value.pwr_p[ph], 0.0))
    {
        if(less(instant_value.pwr_q[ph], 0.0))  // 第III象限
        {
            u_i_angle = ANGLE_360 / 2 + u_i_angle;
        }
        else                                 // 第II象限
        {
            u_i_angle = ANGLE_360 / 2 - u_i_angle;
        }
    }
    else
    {
        if(less(instant_value.pwr_q[ph], 0.0))  // 第IV象限
        {
            u_i_angle = ANGLE_360 - u_i_angle;
        }
    }

    if(more(u_i_angle, ANGLE_360))  u_i_angle = ANGLE_360;
    else if(less(u_i_angle, 0.0))   u_i_angle = ANGLE_360 - 0.1 + u_i_angle;

    return u_i_angle;
}

/// @brief 获取电压值
/// @param chn 
/// @return 
static float emu_vol_val(SAMPLE_CHANNEL chn)
{
    float vrms;
    vrms = emu_register_read(REG_R_Rms_U) * cal_para[0].U_ratio; // 计算瞬时电压有效值 unit: V
    if(less(vrms, MIN_WORK_VOLTAGE))  vrms = 0;
    return vrms;
}

/// @brief 获取电流值
static float emu_cur_val(SAMPLE_CHANNEL chn)
{
    return (emu_register_read(REG_R_Rms_I1  + chn) * cal_para[chn].I_ratio); // 电流 unit: A
}

/// @brief 获取功率值
static float emu_power_val(EMU_POWER_TYPE type, SAMPLE_CHANNEL chn)
{
    uint32_t reg_pwr;

    switch(type)
	{
		case P_POWER:
            {
                reg_pwr = emu_register_read((chn == CHANNEL_1) ? REG_R_PowerP1 : REG_R_PowerP2);
                if(reg_pwr & 0x800000) reg_pwr |= 0xFF000000;    // 符号扩展
            }
            break;
		case Q_POWER:
            {
                reg_pwr = emu_register_read((chn == CHANNEL_1) ? REG_R_PowerQ1 : REG_R_PowerQ2);
                if(reg_pwr & 0x800000) reg_pwr |= 0xFF000000;    // 符号扩展
            }
            break;
		case S_POWER:
            {
                reg_pwr = emu_register_read(REG_R_PowerS);
                if(reg_pwr & 0x800000) reg_pwr |= 0xFF000000;    // 符号扩展
            }
            break;
        default: return 0;
	}
    return ((float)reg_pwr * cal_para[chn - A_PHASE].P_ratio); // unit: W
}

/// @brief 根据功率符号计算相位, 移植不用改
static PWR_QUADRANT emu_quadrant_in(EMU_PHASE_TYPE ph)
{
    if(instant_value.pwr_p[ph] >= 0)
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_1 : QUADRANT_4;
    }
    else
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_2 : QUADRANT_3;
    }
}


/// @brief 切换计量通道
/// @param
/// @return
#define CHNSEL_MIN  (2 * START_PWR)
static void emu_channel_select(float *ps)
{
    static uint8_t select_wait = 0;
    uint8_t        channel_tmp;

#if USE_CHANNEL_2
    if(measure_para.wiring_mode == MIC_CHN_AUTO_MODE)
    {
        float val[3];
        val[0] = fabs(ps[1]);
        val[1] = fabs(ps[2]);
        if(val[1] < START_PWR)
        {
            if(val[0] < START_PWR) return;
            channel_tmp = CHANNEL_1;
        }
        else if(val[0] < START_PWR) { channel_tmp = CHANNEL_2; }
        else
        {
            val[2] = max(val[1], val[0]) * CAL_CHNSEL;
            val[2] = max(val[2], CHNSEL_MIN);

            if(fabs(val[1] - val[0]) >= val[2]) { channel_tmp = (val[1] > val[0]) ? CHANNEL_2 : CHANNEL_1; }
            else
                channel_tmp = mic_channel;
        }
        DBG_PRINTF(P_EMU, D, "Vs:%8d, %8d, %8d\r\n", val[0], val[1], val[2]);
        DBG_PRINTF(P_EMU, D, "CHANNEL_%1d\r\n", (mic_channel + 1));
    }
    else { channel_tmp = (measure_para.wiring_mode == MIC_CHN_PHASE_MODE) ? CHANNEL_1 : CHANNEL_2; }
#else
    channel_tmp = CHANNEL_1;
#endif

    if(mic_channel != channel_tmp)
    {
        if(++select_wait > 3)
        {
            uint32_t reg = emu_register_read(REG_W_EMUCFG);
            
            if(reg != 0xFFFFFFFF)
            {
                if(channel_tmp == CHANNEL_2)    // 选择通道 2 参与计量
                {
                    reg |= 0x0004;    // 选择通道计量（0：选择通道 1 计量 1：选择通道 2 计量）
                }
                else
                {
                    reg &= ~0x0004;   // 选择通道 1 计量
                }
                if(emu_register_write(REG_W_WPREG,      0x00BC)) // 使能写40H-45H寄存器
                if(emu_register_write(REG_W_EMUCFG,     reg))    // 能量寄存器读后清零
                {
                    emu_reg_check_sum_pro(1);
                    select_wait = 0;
                    mic_channel = (SAMPLE_CHANNEL)channel_tmp;
                }
                emu_register_write(REG_W_WPREG,      0x0000); // 禁止写
            }
        }
    }
    else
        select_wait = 0;
}

/// @brief 排序
/// @param arr 
/// @param len 
static void select_sort(float *arr, uint32_t len)
{
    float tmp;
    uint32_t i, k, j;

    for (i = 0; i < len; i++)
    {
        k = i;                           //保存当前下标
        for (j = i + 1; j < len; j++)
        {
            if (arr[k] > arr[j])
                k = j;                  //找到最小值
        }
        if (k != i)                     //将最小值放到当前下标
        {
            tmp = arr[i];
            arr[i] = arr[k];
            arr[k] = tmp;
        }
    }
}

/* Public functions ----------------------------------------------------------*/
/// @brief 刷新实时数据
void instant_refresh(void)
{
    uint32_t tmp;

    if(emu_verify())
    {
		float pp[3], pq[3], ps[3];

        /* 更新计量状态 */
        instant_value.stus.lword &= ~0x00000FFF;
        tmp = emu_register_read(REG_R_EMUSR);
        instant_value.stus.pa_start  = !boolof(tmp & (bitmask(2)|bitmask(3))); 
        
        /* 查询当前计量通道 */
        mic_channel = (tmp & bitmask(7)) ? CHANNEL_2 : CHANNEL_1;
        instant_value.stus.main_chn = boolof(mic_channel != CHANNEL_1);

        /* 读瞬时参数 */
        instant_value.freq = emu_freq_val();
        instant_value.vrms[CHANNEL_1] = emu_vol_val(CHANNEL_1);
        pp[A_PHASE] = emu_power_val(P_POWER, CHANNEL_1);
        pq[A_PHASE] = emu_power_val(Q_POWER, CHANNEL_1);
        ps[A_PHASE] = fabs(emu_power_val(S_POWER, CHANNEL_1));
        if(less(pp[A_PHASE], 0.0)) ps[A_PHASE] = -ps[A_PHASE];
   #if USE_CHANNEL_2
        pp[B_PHASE] = emu_power_val(P_POWER, CHANNEL_2);
        pq[B_PHASE] = emu_power_val(Q_POWER, CHANNEL_2);
        ps[B_PHASE] = fabs(emu_power_val(S_POWER, CHANNEL_2));
        if(less(pp[B_PHASE], 0.0)) ps[B_PHASE] = -ps[B_PHASE];
   #endif

        emu_channel_select(ps);
	    if(instant_value.stus.main_chn)
    	{
    		pp[T_PHASE] = pp[B_PHASE], pq[T_PHASE] = pq[B_PHASE], ps[T_PHASE] = ps[B_PHASE];
    	}
    	else
    	{
    		pp[T_PHASE] = pp[A_PHASE], pq[T_PHASE] = pq[A_PHASE], ps[T_PHASE] = ps[A_PHASE];
    	}

    	for(uint8 i = 0; i <= 2; i++)
        {
		    instant_value.pwr_p[i] = more_equ(fabs(pp[i]), START_PWR) ? pp[i] : 0; ///unit:1W
		    instant_value.pwr_q[i] = more_equ(fabs(pq[i]), START_PWR) ? pq[i] : 0; ///unit:1W
		    instant_value.pwr_s[i] = more_equ(fabs(ps[i]), START_PWR) ? ps[i] : 0; ///unit:1W
        }

        instant_value.stus.pa_start = boolof(instant_value.pwr_s[T_PHASE] != 0);
        if(!instant_value.stus.pa_start)
        {
            instant_value.quadrant[T_PHASE]  = QUADRANT_1;
            instant_value.stus.pa_rev  = 0;
            instant_value.stus.qa_rev  = 0;
            instant_value.vi_angle[T_PHASE]  = 0;
            instant_value.pf[T_PHASE]  = instant_value.pf[A_PHASE] = 1.0;
        }
        else
        {
            instant_value.quadrant[T_PHASE] = emu_quadrant_in(T_PHASE);
            instant_value.stus.pa_rev = boolof(pp[0] < 0);
            instant_value.stus.qa_rev = boolof(pq[0] < 0);

            instant_value.pf[A_PHASE] = emu_pf_val(A_PHASE);
            instant_value.pf[B_PHASE] = emu_pf_val(B_PHASE);
            instant_value.pf[T_PHASE] = (mic_channel == CHANNEL_1) ? instant_value.pf[A_PHASE] : instant_value.pf[B_PHASE];

            instant_value.vi_angle[A_PHASE] = emu_vi_angle_val(A_PHASE, instant_value.pf); ///电流夹角转换相角
            instant_value.vi_angle[B_PHASE] = emu_vi_angle_val(B_PHASE, instant_value.pf); ///电流夹角转换相角
            instant_value.vi_angle[T_PHASE] = (mic_channel == CHANNEL_1) ? instant_value.vi_angle[A_PHASE] : instant_value.vi_angle[B_PHASE];
        }

        instant_value.irms[CHANNEL_1] = emu_cur_val(CHANNEL_1);
	    instant_value.irms[CHANNEL_2] = emu_cur_val(CHANNEL_2);

        /* 滤波处理 */
        if(instant_value.irms[CHANNEL_1] < START_CURRENT) instant_value.irms[CHANNEL_1] = 0;
        if(instant_value.irms[CHANNEL_2] < START_CURRENT) instant_value.irms[CHANNEL_2] = 0;
        instant_value.n_irms = instant_value.irms[CHANNEL_2];
        instant_value.v_irms = (mic_channel == CHANNEL_1) ? instant_value.irms[CHANNEL_1] : instant_value.irms[CHANNEL_2];

        DBG_PRINTF(P_EMU, D, "\r\n measure instant value: \r\n");
        DBG_PRINTF(P_EMU, D, "VOL:  %-10.4f\t\r\n", instant_value.vrms[0]);
        DBG_PRINTF(P_EMU, D, "CUR:  %-10.4f\t%-10.4f\t\r\n", instant_value.irms[0], instant_value.irms[1]);
        DBG_PRINTF(P_EMU, D, "V-I:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vi_angle[0], instant_value.vi_angle[1], instant_value.vi_angle[2]);
        DBG_PRINTF(P_EMU, D, "ACT:  %-10.4f\t%-10.4f\t%-10.4f\t\r\n", instant_value.pwr_p[1], instant_value.pwr_p[2], instant_value.pwr_p[0]);
        DBG_PRINTF(P_EMU, D, "PF :  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.pf[0], instant_value.pf[1], instant_value.pf[2]);
        DBG_PRINTF(P_EMU, D, "FREQ: %-10.4f\r\n", instant_value.freq);
        DBG_PRINTF(P_EMU, D, "\r\n");
    }
}

/// 电能获取
uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    uint8_t pulse_num = 0;
    uint8_t reg;

    switch(type)
    {
        case P_POWER:
            HAL_CRITICAL_STATEMENT
            (
                pulse_num = ep_pulse_cnt;
                ep_pulse_cnt = 0;
            );
            break;
        case Q_POWER:
            reg = REG_R_Energy_Q;
            break;
        case S_POWER:
        default:
            return 0;
    }
    pulse_num = (uint8_t)emu_register_read(reg);

    return (pulse_num < MAX_PULSE_PER_SECOND) ? pulse_num : 0;
}

/// @brief 计量芯片初始化
/// @param pwr_down 1-掉电时初始化，0-上电时初始化
void measure_ic_init(uint8_t pwr_down)
{
    DBG_PRINTF(P_EMU, D, "\r\nht7107 init : \r\n");
    emu_calpara_checkout();

    for(uint8_t i = 0; i < 4; i++){ instant_value.quadrant[i] = QUADRANT_1; }

    if(!pwr_down)
    {
        emu_com_init();
        emu_reset();

        HAL_CRITICAL_STATEMENT
        (
            ep_pulse_cnt = 0;
        );
        mic_channel = CHANNEL_1;
        emu_reg_check_sum_pro(0); // 清除校验
        memset(cal_para, 0, CHANNEL_NUM * sizeof(cal_para_s));
    }
}

/// @brief 计量芯片关闭
/// @param  
void measure_ic_off(void)
{
    emu_register_write(REG_W_SRSTREG, 0x55);
    measure_status = FALSE;
}

/// @brief 获取电压电流功率数据用于校表
/// @param pdat_samp 
void calibrate_sample(void* pdat_samp)
{
    uint8_t i, delete_num;
    float   Urms[SAMPLE_NUM], Irms1[SAMPLE_NUM], Irms2[SAMPLE_NUM];
    float   Power_P1[SAMPLE_NUM], Power_P2[SAMPLE_NUM], Power_Q1[SAMPLE_NUM], Power_Q2[SAMPLE_NUM];

	CalibrateData_s* samp = pdat_samp;
    memset(samp, 0x00, sizeof(CalibrateData_s));

    delete_num = SAMPLE_NUM / 4;

    for(i = 0; i < SAMPLE_NUM; i++)
    {
        hal_timer.msdly(320);     // 延时等待寄存器更新(14.5HZ)
        Urms[i]     = emu_vol_val(CHANNEL_1);
        Irms1[i]    = emu_cur_val(CHANNEL_1);
        Irms2[i]    = emu_cur_val(CHANNEL_2);
        Power_P1[i] = emu_power_val(P_POWER, CHANNEL_1);
        Power_P2[i] = emu_power_val(P_POWER, CHANNEL_2);
        Power_Q1[i] = emu_power_val(Q_POWER, CHANNEL_1);
        Power_Q2[i] = emu_power_val(Q_POWER, CHANNEL_2);
    }

    select_sort(Urms,     SAMPLE_NUM);     
    select_sort(Irms1,    SAMPLE_NUM);    
    select_sort(Irms2,    SAMPLE_NUM);    
    select_sort(Power_P1, SAMPLE_NUM); 
    select_sort(Power_P2, SAMPLE_NUM); 
    select_sort(Power_Q1, SAMPLE_NUM); 
    select_sort(Power_Q2, SAMPLE_NUM); 
    for (i = delete_num; i < (SAMPLE_NUM - delete_num); i++) // 去掉最小最大值
    {
        samp->Urms[0] += Urms[i];
        samp->Irms[0] += Irms1[i];
        samp->Irms[1] += Irms2[i];
        samp->power_p[0] += Power_P1[i];
        samp->power_p[1] += Power_P2[i];
        samp->power_q[0] += Power_Q1[i];
        samp->power_q[1] += Power_Q2[i];
    }

    samp->Urms[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->Irms[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->Irms[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_p[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_p[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_q[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_q[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_s[0] = sqrt(samp->power_p[0]*samp->power_p[0] + samp->power_q[0]*samp->power_q[0]);
    samp->power_s[1] = sqrt(samp->power_p[1]*samp->power_p[1] + samp->power_q[1]*samp->power_q[1]);
}

#pragma optimize=none
/// @brief 校表处理
/// @param chn  通道
/// @param step 校表步骤
/// @param pdat_std  标准数据缓冲
/// @param pdat_samp 采样数据缓冲
/// @return
uint32_t calibrate_proc(uint8_t chn, uint8_t step, void *pdat_std, void *pdat_samp)
{
    cal_para_s *ptr = cal_para + chn;
    float       err;
    // 校表标准值/采样值结构体
    CalibrateData_s data_std, data_samp;

    memcpy(&data_std,  pdat_std,  sizeof(CalibrateData_s));
    memcpy(&data_samp, pdat_samp, sizeof(CalibrateData_s));

    // 单相表
    if(chn >= CHANNEL_NUM || ((measure_para.wiring_mode == MIC_CHN_PHASE_MODE) && (chn == CHANNEL_2))) return 0;
    switch(step)
    {
        case STEP_INIT: // 校表初始化
        {
            /* 载入初始校表参数 */
            *ptr                     = cal_para_default;
            instant_value.stus.uncal |= 1 << chn;
            break;
        }

        case STEP_PHASE_INIT: 
        {
            ptr->Phs_ofst = 0;
            break;
        }

        case STEP_POFST_INIT: 
        {
            ptr->P_ofst = 0;
            break;
        }

        case STEP_QOFST_INIT: 
        {
            ptr->Q_ofst = 0;
            break;
        }

        case STEP_HFCONST:    // 高频脉冲常数调整
        {
            break;
        }

        case STEP_I2GAIN:    // I2GAIN调整
        {
            break;
        }

        case STEP_SAMPLE:    // 电表计量采样平均处理
        {
            calibrate_sample(pdat_samp);
            break;
        }

        case STEP_VOL:    // 校正电压
        {
            ptr->U_ratio = data_std.Urms[0] / data_samp.Urms[0];
            break;
        }

        case STEP_CUR:    // 校正电流
        {
            ptr->I_ratio = data_std.Irms[chn] / data_samp.Irms[chn];
            break;
        }

        case STEP_POWER:    // 校正功率增益
        {
            if(data_std.err[chn] != 0) { err = data_std.err[chn]; }
            else
            {
#if MFCCMD_DOPGAIN_ENABLE
                err = fabs(data_samp.power_p[chn]) / data_std.power_p[chn] - 1;
#else
                err = fabs(data_samp.power_s[chn]) / data_std.power_s[chn] - 1;
#endif
            }

            err = -err / (1 + err);
            err *= DATA_2EXP31;
            ptr->P_gain = (uint32_t)((int32_t)err);
            break;
        }

        case STEP_PHASE:    // 校正相角
        {
            if(data_std.err[chn] != 0) { err = data_std.err[chn]; }
            else
            {
#if MFCCMD_DOPGAIN_ENABLE
                err = fabs(data_samp.power_p[chn]) / data_std.power_p[chn] - 1;
#else
                err = fabs(data_samp.power_s[chn]) / data_std.power_s[chn] - 1;
#endif
            }
            err           = -err * 661 * 6553600 / 819200;
            ptr->Phs_ofst = (uint32_t)((int32_t)err);
            break;
        }

        case STEP_POFST:    // 功率偏置//有功
        {
            float power;
            if(data_std.err[chn] != 0) { err = data_std.err[chn]; }
            else
            {
                err = fabs(data_samp.power_p[chn]) / data_std.power_p[chn] - 1;
            }
            power       = data_std.power_p[chn];
            ptr->P_ofst = (uint32_t)((int32_t)(err * power));
            break;
        }

        case STEP_QOFST:    // 无功功率偏置
        {
            float power;
            if(data_std.err[chn] != 0) { err = data_std.err[chn]; }
            else
            {
                err = fabs(data_samp.power_q[chn]) / data_std.power_q[chn] - 1;
            }
            power       = data_std.power_q[chn];
            ptr->Q_ofst = (uint32_t)((int32_t)(err * power));
            break;
        }

        case STEP_IOFST:  // 电流偏置
        {
            break;
        }

        case STEP_CONST:    // 刷新计量参数(脉冲常数 -加倍, 只接受0,2,4,8倍)
        {
            uint32_t reg  = emu_register_read(REG_W_FreqCFG);
            uint8_t  fast = *(uint8_t*)pdat_std;
            reg &= ~0x0E00;
            if(fast == 2)      { reg |= 0x0200; }
            else if(fast == 4) { reg |= 0x0400; }
            else if(fast == 8) { reg |= 0x0600; }
            else if(fast == 0) { reg |= 0x0000; }
            else return 0;
            if(emu_register_write(REG_W_WPREG,      0x00BC)) // 使能写40H-45H寄存器
            if(emu_register_write(REG_W_FreqCFG,    reg))    // 能量寄存器读后清零
            {
                emu_reg_check_sum_pro(1); 
            }
            emu_register_write(REG_W_WPREG,      0x0000); // 禁止写
            return 0;
        }

        case STEP_REFRESH:    // 刷新计量参数，重启计量
        {
            emu_reg_check_sum_pro(3);      // 触发EMU刷新参数
            emu_para_refresh(cal_para, 0);
            // 延时等待计量稳定
            hal_timer.msdly(1300);         // 延时等待计量稳定
            return 0;
        }

        case STEP_SETPARA:    // 设置计量参数
        {
            MeasurePara_s *para = (MeasurePara_s *)pdat_std;
            if(para != &measure_para) { *para = measure_para; }
            measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            if(ee_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s)))
            {
                mcu_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
                measure_ic_init(0);    // 切换计量模式需重新初始化计量
                return 1;
            }
            return 0;
        }

        case STEP_GETPARA: 
        {
            return (uint32_t)&measure_para;    // 读计量参数
        }

        case STEP_READREG:    // 计量寄存器
        {
            uint16_t reg = get_lsbdata16(pdat_std);
            set_msbdata32(pdat_std, emu_register_read(reg));
            return 4;
        }

#if MIC_SUPPORT_SET_CAL_PARA
        case STEP_REDCAL: 
        {
            memcpy(pdat_std, &measure_para[chn], sizeof(MeasurePara_s));
            return sizeof(MeasurePara_s);

            case STEP_SETCAL:
                memcpy(&measure_para[chn], pdat_std, sizeof(MeasurePara_s));
                ptr = &measure_para[chn];
                if(ptr->cs != CAL_CS16(ptr, sizeof(MeasurePara_s))) return 1;
                regChecksum = 0;
        }
#endif

        case STEP_SAVE:    // 保存校表参数
        {
            mcu_cal_write(chn * sizeof(MeasurePara_s), ptr, sizeof(MeasurePara_s));
            ee_cal_write(chn * sizeof(MeasurePara_s),  ptr, sizeof(MeasurePara_s));
            instant_value.stus.uncal &= ~(1 << chn);
            return 0;
        }

        default:    // 失败
            return 0;
    }

    /* 计算校表参数校验和 */
    ptr->cs = CAL_CS16(ptr, sizeof(cal_para_s));

    return 0;
}
#if 0
///相角获取
#define ANGLE_360       360.0
#define PI              3.141592
float measure_phase_angle_get(tAngleVector angle)
{
    float angle_val[2] = {0,0};
    tVector* vector = &angle.ref;
    float* vv_angle = instant_value.vv_angle;
    float* vi_angle = instant_value.vi_angle;

    for(uint8_t i = 0; i < 2; i++)
    {
        if(vector->type == VOL_VECTOR)
        {
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = ANGLE_360 - vv_angle[vector->chn - CHANNEL_2];
            }
        }
        else if(vector->type == CUR_VECTOR)
        {
            if(vector->chn == CHANNEL_1)
            {
                angle_val[i] = -(*vi_angle);
            }
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = -vv_angle[vector->chn - CHANNEL_2] - vi_angle[vector->chn - CHANNEL_1];
            }
        }
        vector++;
    }
    angle_val[0] = angle_val[1] - angle_val[0];

    if(more(angle_val[0], ANGLE_360)) angle_val[0] = ANGLE_360;
    else if(less(angle_val[0], 0.0)) angle_val[0] = ANGLE_360 - 0.1 + angle_val[0];

    return angle_val[0];
}
#endif

#if USE_EMU_AT_LOSS_VOLTAGE
void mic_power_off_running(void)
{
    if(power_off_running != NULL) 
    {
        //刷新电流

        //判断全失压
        power_off_running();

        //
    }
}

void mic_power_off_callback(void func(void))
{
    power_off_running = func;
}
#endif
/// @brief 计量芯片接口
const struct mic_s mic = {
    .ins                  = &instant_value,
    .init                 = measure_ic_init,
    .off                  = measure_ic_off,
    .ins_val_refresh      = instant_refresh,
#if USE_EMU_AT_LOSS_VOLTAGE
    .poweroff_run         = mic_power_off_running,
    .poweroff_callback    = mic_power_off_callback,
#else
    .poweroff_run         = NULL,
    .poweroff_callback    = NULL,
#endif
    .pulse                = measure_pulse_get,
    .cali                 = calibrate_proc,
};

///
