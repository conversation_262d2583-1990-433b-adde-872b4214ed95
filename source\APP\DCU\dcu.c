/**
 ******************************************************************************
 * @file    dcu.c
 * <AUTHOR> @date    2025
 * @brief   dcu单元处理远程数据交互
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include <string.h>
#include "utils.h"
#include "status.h"
#include "dcu.h"
#include "power_event.h"
#include "pt.h"
#include "DLT645_2007.h"
#include "module_para.h"
#include "bsp.h"
#include "debug.h"
#include "api.h"
#include "..\protocol\QGWD10376\QGDW10376.h"
#include "comm_phy.h"
#include "image_transfer_api.h"

#define logd(...) DBG_PRINTF(P_DCU, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_DCU, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_DCU, T, __VA_ARGS__)    // 时间戳打印

typedef struct dcu_pt_struct
{
    struct pt mpt;
    struct pt sub;
    struct pt wait;    // 等待PT线程
} dcu_pt_s;

/// variables
static dcu_state_s      dcu_state;                                  // DCU状态
static uint8_t          dcu_buf[DCU_DATA_BUF_SIZE + 32];            // DCU数据缓存
static uint8_t          dcu_forward_buf[DCU_DATA_BUF_SIZE + 16];    // 透明转发数据缓存
static SwTimer_s        dcu_timer;                                  // DCU定时器
static TYPE_DCU_EVT_OUT dcu_evt_out;                                // 事件输出状态字
static dcu_pt_s         dcu_pt;                                     // DCU处理线程

transparent_forward_s dcu_forward;    // 透明转发数据结构

CM_STATUS_s cm_status;    // 通讯模块状态

void dcu_init(void)
{
    PT_INIT(&dcu_pt.mpt);                       // 初始化DCU处理线程
    PT_INIT(&dcu_pt.sub);                       // 初始化子线程
    PT_INIT(&dcu_pt.wait);                      // 初始化等待线程
    remote_m.init(dcu_buf, sizeof(dcu_buf));    // 初始化远程模块
    module_para.init();                         // 初始化模块参数
    gdw376.init(0, dcu_buf);                    // 初始化376协议
}

static PT_THREAD(thread_delay(struct pt *pt, uint32_t ms))
{
    static SwTimer_s pt_tmr;    // 获取PT定时器

    PT_BEGIN(pt);
    hal_timer.interval(&pt_tmr, ms);                  // 设置定时器
    PT_WAIT_UNTIL(pt, hal_timer.expired(&pt_tmr));    // 等待定时器到期
    PT_END(pt);
}

static void dcu_login_request(void)
{
    logt();
    logd("DCU login request...\r\n");                                 // 打印登录请求日志
    remote_m.send(dcu_buf, gdw376.get_linktest_frame(dcu_buf, 1));    // 执行登录请求

    dcu_state.login_req = false;    // 清除登录请求状态
}

static void dcu_heartbeat_request(void)
{
    logt();
    logd("DCU heartbeat request...\r\n");                             // 打印登录请求日志
    remote_m.send(dcu_buf, gdw376.get_linktest_frame(dcu_buf, 0));    // 执行登录请求
}

PT_THREAD(dcu_module_is_idle(struct pt *pt))
{
    static SwTimer_s tmr;    // 定义定时器

    PT_BEGIN(pt);

    if(remote_m.send_over_query() == false)    // 如果模块发送未完成
    {
        hal_timer.interval(&tmr, 5000);    // 5秒超时，初始化模块
        PT_WAIT_UNTIL(pt, remote_m.send_over_query() || hal_timer.expired(&tmr));
        if(remote_m.send_over_query() == false)    // 如果模块发送仍未完成
        {
            logd("\r\nDCU module send timeout!\r\n");
            remote_m.init(dcu_buf, sizeof(dcu_buf));    // 初始化远程模块
            PT_EXIT(pt);
        }
        PT_EXIT(pt);    // 退出PT线程
    }

    PT_END(pt);
}

PT_THREAD(dcu_transparent_forward(struct pt *pt))
{
    static SwTimer_s tmr;             // 定义定时器
    uint16_t         send_len = 0;    // 发送数据长度
    uint16_t         msg_len;
    uint8_t          ack;
    uint8_t          fe_num = 0;

    PT_BEGIN(pt);

    if(dcu_forward.len == 0) { PT_EXIT(pt); }    // 如果透明转发数据长度为0，退出PT线程

    comm_phy.init(dcu_forward.com, dcu_forward_buf, DCU_DATA_BUF_SIZE, dcu_forward.baud, CHAR_8E1);
    DLT645.init(dcu_forward.com, dcu_forward_buf);    // 初始化DLT645协议

    send_len = DLT645.msg_process(dcu_forward.com, &ack, dcu_forward.len);
    if(!send_len && ack == ACK_NULL)    // 不是此表的数据，直接转发，并等待回应
    {
        comm_phy.send(dcu_forward.com, dcu_forward.buf, dcu_forward.len);
        hal_timer.interval(&tmr, dcu_forward.timeout);    // 5秒超时，初始化模块
        PT_WAIT_UNTIL(pt, (send_len = comm_phy.recv(dcu_forward.com)) || hal_timer.expired(&tmr));
    }

    // 主站不认前导FE
    if(send_len)
    {
        for(uint16_t i = 0; i < send_len; i++)    // 遍历发送数据
        {
            if(dcu_forward.buf[i] == 0xFE) { fe_num++; }    // 统计0xFE字节数量
        }
    }

    msg_len = gdw376.transparent_forward(dcu_buf, dcu_forward.buf + fe_num, send_len - fe_num, dcu_forward.com);    // 组装透明转发数据

    logt();
    logd("DCU transparent forward %d bytes", send_len);
    logm(dcu_forward.buf, send_len);

    logd("DCU send data: bytes=%d", msg_len);
    logm(dcu_buf, msg_len);
    remote_m.send(dcu_buf, msg_len);    // 发送透明转发数据

    PT_END(pt);
}

#define HEARTBEAT_INTERVAL_DEF 240    // 心跳间隔240秒
#define DATA_SYNC_INTERVAL 200        // 数据同步间隔60秒
#define RTC_SYNC_INTERVAL 3600        // RTC同步间隔3600秒
/// @brief 连接状态监控
/// @note 该函数用于监控DCU连接状态，包括登录、心跳、数据同步等操作。
static void dcu_connection_monitor(void)
{
    static SwTimer_s tmr                = {0, 1000};                 // 定义定时器
    static uint8_t   heartbeat_interval = HEARTBEAT_INTERVAL_DEF;    // 心跳间隔
    static uint8_t   login_err_cnt      = 0;
    static uint8_t   login_timeout      = 0;
    static uint8_t   heatbeat_timeout   = 0;
    static uint8_t   heartbeat_err_cnt  = 0;
    static uint8_t   data_sync_tmr      = DATA_SYNC_INTERVAL - 2;    // 数据同步标志
    static uint16_t  rtc_sync_tmr       = RTC_SYNC_INTERVAL - 1;     // RTC同步标志
    static uint16_t  http_tmr_limit     = 0;                         // http下载计时
    static bool      login              = false;

    if(hal_timer.expired(&tmr))    // 秒定时
    {
        hal_timer.interval(&tmr, 1000);    // 重置定时器

        if(dcu_state.http_download)
        {
            if(!http_tmr_limit) { http_tmr_limit = 1800; }    // 开始计时3分钟没有完成下载放弃升级
            http_tmr_limit--;
            if(http_tmr_limit == 0)    // 如果http下载超时
            {
                dcu_state.http_download = false;              // 清除http下载状态
                remote_m.access_request(REQUEST_RTC_SYNC);    // 设置访问请求状态为RTC同步,用于跳出http下载状态
                logd("DCU http download timeout!\r\n");
            }
            return;
        }

        if(remote_m.state->tcp0_state == false)
        {
            login = true;
            return;
        }
        if(login)
        {
            login               = false;
            dcu_state.login_req = true;
            login_err_cnt       = 0;
            login_timeout       = 0;
            heatbeat_timeout    = 0;
            heartbeat_err_cnt   = 0;
            return;
        }

        if(!dcu_state.login_ok)
        {
            if(login_timeout++ >= 20)    // 如果登录超时超过20秒
            {
                logd("DCU login time out!\r\n");
                login_timeout = 0;    // 重置登录超时计数
                if(login_err_cnt++ >= 6)
                {
                    logd("DCU reset!\r\n");
                    login_err_cnt   = 0;
                    dcu_state.reset = true;    // 设置复位通讯模块标志
                    return;
                }
                dcu_state.login_req = true;    // 设置登录重试标志
                return;
            }
        }
        else
        {
            login_timeout = 0;    // 重置登录超时计数
            login_err_cnt = 0;
            if(!dcu_state.heartbeat_ok)    // 如果心跳包未正确响应
            {
                heartbeat_interval = HEARTBEAT_INTERVAL_DEF;
                if(heatbeat_timeout++ >= 60)    // 如果心跳超时超过60秒
                {
                    logd("DCU heartbeat time out!\r\n");
                    heatbeat_timeout = 0;    // 重置心跳超时计数
                    if(heartbeat_err_cnt++ >= 3)
                    {
                        logd("DCU reset!\r\n");
                        heartbeat_err_cnt = 0;
                        dcu_state.reset   = true;    // 设置复位通讯模块标志
                        return;
                    }
                    dcu_state.heartbeat_req = true;    // 设置心跳包发送需求u
                    return;
                }
            }
            else
            {
                heatbeat_timeout  = 0;    // 重置心跳超时计数
                heartbeat_err_cnt = 0;
                if(!heartbeat_interval)
                {
                    heartbeat_interval      = HEARTBEAT_INTERVAL_DEF;
                    dcu_state.heartbeat_req = true;
                }
                heartbeat_interval--;

                if(data_sync_tmr++ >= DATA_SYNC_INTERVAL)    // 每60秒同步一次数据
                {
                    data_sync_tmr = 0;    // 重置数据同步计时器
                    remote_m.access_request(REQUEST_DATA_SYNC);
                }

                if(rtc_sync_tmr++ >= RTC_SYNC_INTERVAL)    // 每3600秒同步
                {
                    rtc_sync_tmr = 0;                             // 重置RTC同步计时器
                    remote_m.access_request(REQUEST_RTC_SYNC);    // 设置访问请求状态为RTC同步
                }
            }
        }
    }
}

#if HTTP_UPGRADE_ENABLE
/// @brief 这里设定网络连接正常时即进行http下载升级，实际已模组为准，
static void dcu_meter_update_monitor(void)
{
    // //测试
    // static uint8_t t = 1;
    // if(t)
    // {
    //     char str[] = "\"URL\":\"http://hardware-package.oss-cn-beijing.aliyuncs.com/1/DDDD1980(5-60A)-T4-app(V0.30-20250704).bin\",\"Len\":\"87252\"";
    //     fwm_upgrade.http_init(str, strlen(str));    // 初始化http下载
    //     t = 0;
    // }

    if(dcu_state.http_download == false && remote_m.state->net_state == true && image.transfer_status_get() == http_download_initiated)
    {
        dcu_state.http_download = true;                // 设置http下载状态
        remote_m.access_request(REQUEST_FILE_RECV);    // 设置访问请求状态为表计更新
    }

    // if(dcu_state.http_download && remote_m.task_state() != M_TASK_FILE_RECV)
    if(dcu_state.http_download)
    {
        if(remote_m.task_state() != M_TASK_FILE_RECV && image.transfer_status_get() == http_download_initiated)
        {
            remote_m.access_request(REQUEST_FILE_RECV);    // 设置访问请求状态为表计更新
        }
        if(image.transfer_status_get() != http_download_initiated)
        {
            dcu_state.http_download = false;    // 清除http下载状态
        }
    }
}
#endif

/// @brief 停电上报，先把串口改为
/// @param
void dcu_lastgasp(uint8_t typ)
{
    if(typ == 0)
    {
        remote_m.lastgasp_send(NULL, 0, 0);    //  关闭串口，再打开，polling方式发送停电上报
    }
    else
    {
        remote_m.lastgasp_send(dcu_buf, gdw376.get_lastgasp_frame(dcu_buf), 1);    // 发送停电上报数据
    }
}

char dcu_process(void)
{
    uint16_t len = 0;

    PT_BEGIN(&dcu_pt.mpt);    // 开始PT线程

    if(dcu_state.forward) { dcu_state.forward = false; }    // 清除透明转发标志
    dcu_connection_monitor();                               // 检查连接重试状态
#if HTTP_UPGRADE_ENABLE
    dcu_meter_update_monitor();    // 检查升级状态
#endif

    PT_SPAWN(&dcu_pt.mpt, &dcu_pt.sub, dcu_module_is_idle(&dcu_pt.sub));    // 数据没有发完不处理新数据。

    if(dcu_state.reset)
    {
        // 设置访问请求状态为TCP连接,tcp连接失败后会重启模块
        dcu_state.reset = false;
        remote_m.access_request(REQUEST_TCP_OPEN);
    }

    len = remote_m.recv();    // 接收远程模块数据

    // 如果TCP连接状态为false
    if(remote_m.state->tcp0_state == false || remote_m.task_state() != M_TASK_NORMAL) { PT_EXIT(&dcu_pt.mpt); }

    if(dcu_state.login_req && !len)    // 有收到有效数据，直接去解析协议
    {
        dcu_login_request();            // 发送登录请求
        dcu_state.login_req = false;    // 清除登录请求状态
        dcu_state.login_ok  = false;    // 清除登录状态
        PT_EXIT(&dcu_pt.mpt);
    }
    else if(dcu_state.login_ok && dcu_state.heartbeat_req && !len)    // 有收到有效数据，直接去解析协议
    {
        dcu_heartbeat_request();            // 发送心跳请求
        dcu_state.heartbeat_req = false;    // 清除心跳包发送需求
        dcu_state.heartbeat_ok  = false;    // 清除心跳成功状态
        PT_EXIT(&dcu_pt.mpt);
    }

    if(!len) { PT_EXIT(&dcu_pt.mpt); }    // 如果没有接收到数据，退出PT线程

    logt();                                                 // 打印时间戳
    logd("DCU recv data %hu: ", len);                       // 打印接收数据日志
    logm(dcu_buf, len);                                     // 打印接收数据内容
    memset(&dcu_forward, 0, sizeof(dcu_forward));           // 清空透明转发数据结构
    if((len = gdw376.msg_process(0, dcu_buf, len)) != 0)    // 处理接收到的数据
    {
        // 如果处理后的数据长度不为0，表示有数据需要发送
        logt();
        logd("DCU send data %hu :", len);
        logm(dcu_buf, len);
        remote_m.send(dcu_buf, len);
    }

    if(dcu_state.forward)
    {
        PT_SPAWN(&dcu_pt.mpt, &dcu_pt.sub, dcu_transparent_forward(&dcu_pt.sub));    // 数据没有发完不处理新数据。
        dcu_state.forward = false;
    }

    PT_END(&dcu_pt.mpt);    // PT线程结束，退出线程
}

void dcu_link_ack(uint8_t typ)
{
    if(typ)
    {
        dcu_state.login_ok      = true;    // 设置登录状态为true
        dcu_state.heartbeat_req = true;    //
    }
    else
    {
        dcu_state.heartbeat_ok = true;    // 设置心跳状态为true
    }
}

void dcu_transparent_set(uint8_t *buf, uint16_t len, uint8_t com, uint8_t format, uint8_t baud, uint16_t timeout)
{
    if(len > DCU_DATA_BUF_SIZE) { return; }    // 如果数据长度超过缓冲区大小，直接返回

    memcpy(dcu_forward_buf, buf, len);    // 复制数据到透明转发缓冲区

    dcu_forward.buf     = dcu_forward_buf;    // 设置数据缓冲区
    dcu_forward.len     = len;                // 设置数据长度
    dcu_forward.timeout = timeout;            // 设置等待超时时间
    dcu_forward.com     = com;                // 设置通讯端口
    dcu_forward.format  = format;             // 设置数据格式
    dcu_forward.baud    = baud;               // 设置波特率
    dcu_state.forward   = true;               // 设置透明转发标志
}

const struct dcu_s dcu = {
    .state           = (const dcu_state_s *)&dcu_state,    // DCU状态
    .init            = dcu_init,                           // 初始化函数
    .process         = dcu_process,                        // 处理函数
    .lastgasp        = dcu_lastgasp,                       // 停电上报函数
    .link_ack        = dcu_link_ack,                       // 链接确认函数
    .transparent_set = dcu_transparent_set,                // 透明转发设置函数
};

// file end
