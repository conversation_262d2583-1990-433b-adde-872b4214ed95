#ifndef __KEY_H__
#define __KEY_H__

#include "bsp_cfg.h"
#include "typedef.h"


/* 按键类型定义 */
typedef enum
{
#if USE_FCOVER
    TYPE_BTN_FCOVER,       // 用于面盖的按键
#endif
#if USE_TCOVER
    TYPE_BTN_TCOVER,       // 用于端盖的按键
#endif
#if USE_BTN_DISP_UP
    TYPE_BTN_DISP_UP,      // 用于上翻显示的按键
#endif

#if USE_BTN_DISP_DN
    TYPE_BTN_DISP_DN,      // 用于下翻显示的按键
#endif
    BTN_NUM,
} BTN_TYPE_t;

/* 按键捕获的状态定义 */
typedef enum
{
    STA_BTN_NULL = 0,      // 未触发过按键
    STA_BTN_SHORT,         // 短按键
    STA_BTN_LONG,          // 长按键
    STA_BTN_START,         // 开始沿
    STA_BTN_END,           // 结束沿
    STA_BTN_YES,           // 有效确认
    STA_BTN_NO,            // 无效确认
} BTN_STATE_t;

typedef enum
{
  TYPE_KEY_NONE = 0,
  TYPE_KEY_0 = '0',
  TYPE_KEY_1 = '1',
  TYPE_KEY_2 = '2',
  TYPE_KEY_3 = '3',
  TYPE_KEY_4 = '4',
  TYPE_KEY_5 = '5',
  TYPE_KEY_6 = '6',
  TYPE_KEY_7 = '7',
  TYPE_KEY_8 = '8',
  TYPE_KEY_9 = '9',
  TYPE_KEY_BACKSPACE = 0x08, // '退格'
  TYPE_KEY_ENTER     = 0x0d, // '回车'
} KEYBOARD_CHAR_TYPE_t;


typedef enum
{
  TYPE_KEY_SHORT = 0,        // 短键
  TYPE_KEY_LONG              // 长键
} KEYBOARD_MODE_TYPE;


typedef struct
{
  KEYBOARD_CHAR_TYPE_t value;
  KEYBOARD_MODE_TYPE mode;
} keyboard_type_s;


/* Export macro -------------------------------------------------------------*/
/* Exported functions -------------------------------------------------------*/
struct key_s
{
    void (*init)(void);
    void (*wakeup_scan)(BTN_TYPE_t btn, uint8_t filter_cnt);
    BTN_STATE_t (*state_get)(BTN_TYPE_t btn);
    BTN_STATE_t (*action_get)(BTN_TYPE_t btn, uint16_t filter_ms);
#if USE_BTN_DISP_DN
    keyboard_type_s (*indent)(void);
    void (*disp_btn_pwrdn_scan)(void);
#endif
};
extern const struct key_s key;

#endif
