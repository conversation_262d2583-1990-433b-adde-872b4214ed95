/**
 ******************************************************************************
* @file    hal_uart.h
* <AUTHOR> @date    2024
* @brief   /// 有远红外功能需选择通道 HAL_IR_CHANNL_NUM
*
******************************************************************************
*
* @note
* Copyright (C) 2024  JiShe Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifdef __cplusplus
 extern "C" {
#endif


#ifndef __HAL_UART_H
#define __HAL_UART_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"


/* Exported types ------------------------------------------------------------*/
#define HAL_UART0_ENABLE             true      
#define HAL_UART1_ENABLE             true
#define HAL_UART2_ENABLE             true      
#define HAL_UART3_ENABLE             true      
#define HAL_UART4_ENABLE             false     
#define HAL_UART5_ENABLE             false     

#define HAL_IR38K_ENABLE             true      
#define HAL_IR_CHANNL_NUM            HAL_UART2    /// IR通道选择

typedef enum
{
	#if  HAL_UART0_ENABLE
	HAL_UART0,
	#endif
	#if  HAL_UART1_ENABLE
	HAL_UART1,
	#endif
	#if  HAL_UART2_ENABLE
	HAL_UART2,
	#endif
    #if  HAL_UART3_ENABLE
	HAL_UART3,
	#endif
	#if  HAL_UART4_ENABLE
	HAL_UART4,
	#endif
	#if  HAL_UART5_ENABLE
	HAL_UART5,
	#endif

	HAL_UART_NUM
} HAL_UART_TYPE;

/* @brief  串口控制类型定义 */
typedef enum
{
    UC_NONE,            // 全双工收发模式
    UC_HALF,            // 半双工收发模式
    UC_RS485_DE,        // 带485控制的收发模式
    UC_RS4852_DE,       // 带485控制的收发模式2
    UC_IR_38K,          // 38KHz调制红外收发模式
    UC_7816,            // 7816
} HAL_UART_CTRL;

/* @brief  串口收发模式定义 */
typedef enum { RxTx_POLLING, RxTx_INT } HAL_UART_TRANS_MODE;

/* @brief  串口字符类型定义 */
typedef enum { CHAR_8E1 = 0, CHAR_8D1, CHAR_8N1, CHAR_7E1} HAL_UART_CHAR_TYPE;

/* @brief  串口波特率类型定义 */
typedef enum {
	BAUDE_300BPS = '0', BAUDE_600BPS, BAUDE_1200BPS, BAUDE_2400BPS, BAUDE_4800BPS,
	BAUDE_9600BPS, BAUDE_19200BPS, BAUDE_38400BPS, BAUDE_57600BPS, BAUDE_115200BPS
}HAL_UART_BAUDE_TYPE;


/* @brief  串口应用超时, 单位毫秒 */
#define HAL_UART_APP_TIMEOUT          3000

struct hal_uart_t
{
/**
  * @brief  打开UART串口
  * @param  [in]  com-指串口号
  * @param  [in]  com-串口控制类型
  * @param  [in]  format-指通讯数据格式, 可以为以下参数之一:
  *               UART_7E1:  7 bits data, 1 stop bit, Even parity
  *               UART_8N1:  8 bits data, 1 stop bit, no parity
  *               UART_8E1:  8 bits data, 1 stop bit, Even parity
  * @param  [in]  baude-指波特率值
  * @param  [in]  rxbuf-中断方式接收缓冲. 如果为NULL，则配置串口为polling方式
  * @param  [in]  size-接收缓冲大小
  * @retval none
  */
    void (*open)(HAL_UART_TYPE com, HAL_UART_CTRL ctrl, HAL_UART_CHAR_TYPE format, HAL_UART_BAUDE_TYPE baude,
                 uint8_t* rxbuf, uint16_t size);
/**
  * @brief  关闭UART串口
  * @param  [in]  com-指串口号
  * @retval none
  */
    void (*close)(HAL_UART_TYPE com);
/**
  * @brief  重新打开UART串口
  * @param  [in]  com-指串口号
  * @retval none
  */
    void (*reopen)(HAL_UART_TYPE com);
/**
  * @brief  串口打印输出一个字符(Polling方式)
  * @param  [in]  com-指串口号
  * @param  [in]  ch-待打印字符
  * @retval none
  */
    void (*print)(HAL_UART_TYPE com, char ch);
/**
  * @brief  串口扫描输入一个字符(Polling方式)
  * @param  [in]  com-指串口号
  * @param  [in]  msWait-指超时计数单位ms， =0表示不启动超时直到接收到字符为止
  * @param  [out] ch-接收字符指针
  * @retval 0-超时未收到字符
  * @retval 1-收到字符
  */
    bool (*scan)(HAL_UART_TYPE com, uint8_t* ch, uint16_t msWait);
/**
  * @brief  查询是否有串口数据串接收到(中断方式)
  * @param  [in]  com-指串口号
  * @retval =0-未收到数据
  * @retval >0-有收到数据的数据长度
  */
    uint16_t (*recv)(HAL_UART_TYPE com);
/**
  * @brief  启动串口数据串发送(中断方式)
  * @param  [in]  com-指串口号
  * @param  [in]  str-待发送数据缓冲
  * @param  [in]  size-发送数据长度
  * @retval none
  */
    void (*send)(HAL_UART_TYPE com, const void* str, uint16_t size);
/**
  * @brief  查询串口数据串是否发送完成(中断方式)
  * @param  [in]  com-指串口号
  * @retval true -发送完成
  * @retval FALSE-发送中
  */
    bool (*send_over_query)(HAL_UART_TYPE com);
 /**
  * @brief  串口中断收发模式下的软件定时器运行处理
  * @param  none
  * @retval none
  */
    void (*timer)(void);

    /// @brief 帧解析（func函数指针）方式接收
    uint16_t (*frame_recv)(HAL_UART_TYPE com, uint16_t func(void*, const uint8_t*, uint16_t), void* para);

    /// @brief 全双工接收数据
    uint16_t (*full_duplex_recv)(HAL_UART_TYPE com, void* buf);
};
extern const struct hal_uart_t hal_uart;


#endif /* __HAL_UART_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

