/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      dlt645_c7.c
 *    Describe:  DLT645-2007协议，ef类数据部分，本地费控金额部分
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/

/// @brief vscode里面的define，代码提示不报错的作用，和iar工程没关系
#ifdef __INTELLISENSE__

#include "DLT645_2007.h"
#include "DLT645_2007_id.h"
#include "..\\..\\interface\\api.h"

typedef enum
{
    CMD_NONE           = 0x00,    /// 00000：无效命令
    CMD_CUSTOM         = 0x03,    /// 自定义
    CMD_BROADCAST      = 0x08,    /// 01000：广播命令
    CMD_READ_DATA      = 0x11,    /// 10001：读取数据命令
    CMD_READ_NEXT_DATA = 0x12,    /// 10010：读取后续数据
    CMD_READ_ADDR      = 0x13,    /// 10011：读通信地址
    CMD_WRITE_PARA     = 0x14,    /// 10100：写数据
    CMD_SET_ADDR       = 0x15,    /// 10101：写通信地址
    CMD_FROZEN         = 0x16,    /// 10110：冻结命令
    CMD_SET_BAUD       = 0x17,    /// 10111：更改通信速率
    CMD_SET_PASSWORD   = 0x18,    /// 11000：修改密码
    CMD_CLEAN_MD       = 0x19,    /// 11001：最大需量清零
    CMD_CLEAN_METER    = 0x1A,    /// 11010：电表清零
    CMD_CLEAN_EVENT    = 0x1B,    /// 11011：事件清零
    CMD_RELAY_CTRL     = 0x1C,    /// 11100：继电器控制
    CMD_FACTORY        = 0x1F
} CMD_TYPE_t;

typedef enum
{
    ADDR_TYPE_NULL      = 0x00,
    ADDR_TYPE_BROADCAST = 0x01,
    ADDR_TYPE_SUPER     = 0x02,
    ADDR_TYPE_SELF      = 0x03,
} ADDR_TYPE_t;

typedef struct
{
    uint8_t    *recv;
    uint8_t    *data;
    uint8_t    *send;
    uint8_t    *snd_dat;
    uint32_t    id;
    uint32_t    id_last;
    uint16_t    timeout_tmr;
    uint16_t    err_code;
    uint8_t     data_len;
    uint8_t     data_len_max;
    uint8_t     addr[6];
    CMD_TYPE_t  ctrl;
    ADDR_TYPE_t addr_type;
    uint8_t     frame_no;
    bool        frame_f;
    bool        err_f;
    bool        is_97;
} DLT645_2007_MSG_S;

/// 错误信息字ERR：
#define ERR_CODE_REV (1 << 7)           /// 保留
#define ERR_CODE_TARIFF_OVR (1 << 6)    /// 费率数超
#define ERR_CODE_SCH_OVR (1 << 5)       /// 日时段数超
#define ERR_CODE_ZONE_OVR (1 << 4)      /// 年时区数超
#define ERR_CODE_BAUD_W_ERR (1 << 3)    /// 通信速率不能更改
#define ERR_CODE_PSW_ERR (1 << 2)       /// 密码错/未授权
#define ERR_CODE_NO_DATA (1 << 1)       /// 无请求数据
#define ERR_CODE_OTHER (1 << 0)         /// 其他错误
#define ERR_CODE_NONE 0                 /// 没有错误

#define PROTOCOL_645_DES_ENCRYPT 0    /// 是否启用DES加密，1-启用，0-不启用

#endif

// *********************** 上面的代码和工程无关，用来防止vscode报错 *****************************

#if SW_PAYMENT_EN == 1 && SHE_WEI_PAYMENT_PROTOCOL == 1

#include "des.h"
#include "timeapp.h"
#include "payment.h"
#include "datastore.h"
#include "api.h"

/// @brief 适配原有代码，但是不去影响别的代码
typedef struct
{
    uint32_t run_type;      // 普通，编程，工厂
    uint32_t work_times;    // 生效时间
    bool     verify_f;      // 身份认证
} dlt645_c7_status_s;
static dlt645_c7_status_s dlt645_c7_status = {C8_ENTRY_FACTORY, 0, false};

// *********************** define *****************************
#define C7_GET_SYSTICK() hal_timer.systick_cnt()
#define C7_RAND_16BIT() ((uint16_t)(C7_GET_SYSTICK() * 40961))

#define C7_FACTORY_RUN_TIME (60 * 5)     // 工厂模式运行时间，单位s
#define C7_PROGRAM_RUN_TIME (60 * 30)    // 编程模式运行时间，单位s

#define C7_DES_KEY_DEFAULT 0x829052EF8BE94987
#define C7_PASSWORD_DEFAULT 0x1234
#define C7_ACCOUNT_CODE_DEFAULT 0xffffffff

// *********************** define *****************************
#define DEFINE_UNION_FIELD(type, name)      \
    union                                   \
    {                                       \
        type    name;                       \
        uint8_t name##_bytes[sizeof(type)]; \
    }

/**
 * @description: c7类数据特用crc
 * @param {uint8_t} *data
 * @param {uint16_t} len
 * @return {*}
 */
static uint16_t c7_crc16(uint8_t *data, uint16_t len)
{
    uint16_t crc = 0xFFFF;
    uint8_t  i;
    while(len--)
    {
        crc ^= *data++;
        for(i = 0; i < 8; i++)
        {
            if(crc & 0x0001) { crc = (crc >> 1) ^ 0xA001; }
            else { crc = crc >> 1; }
        }
    }
    return crc;
}

/**
 * @description: 传入数据需要传入标识符，由标识符来判定是否需要加密解密
 * @param {uint8_t} *data
 * @param {uint16_t} len
 * @return {*}
 */
#if PROTOCOL_645_DES_ENCRYPT == 1
__STATIC_INLINE int is_data_need_dec_unriddle(uint8_t *data, uint16_t len, int is_encrypt)
{
    uint8_t des_key_temp[8] = {0};
    uint8_t cache_data[128] = {0};

    /// @brief 07类数据需要解密
    if(data[3] != 0x07) return 0;

    /// @brief 跳过标识符和操作码
    if(is_encrypt) { data += sizeof(uint32_t) * 2, len -= sizeof(uint32_t) * 2; }

    api.product_info_get(PRODUCT_INFO(des_key), des_key_temp);

    des_process(data, cache_data, len, des_key_temp, is_encrypt);

    memcpy(data, cache_data, len);

    return 0;
}
#endif
/**
 * @description:身份认证的数据格式以及解析函数
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_verify_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);
    DEFINE_UNION_FIELD(uint32_t, operation_code);
    DEFINE_UNION_FIELD(uint32_t, password);
    DEFINE_UNION_FIELD(uint16_t, rand_number);
    DEFINE_UNION_FIELD(uint16_t, loss_fuction_time);
    DEFINE_UNION_FIELD(uint16_t, crc);
};
#pragma pack(pop)

static int c7_verify_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t            rand_number = 0;
    uint16_t            len         = p_info->data_len;
    uint32_t            password    = 0;
    uint16_t            cal_crc     = 0;
    struct c7_verify_s *p_verify    = (struct c7_verify_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    /// @brief crc从password开始计算
    cal_crc = c7_crc16((uint8_t *)p_verify + offsetof(struct c7_verify_s, password), len - offsetof(struct c7_verify_s, password) - sizeof(uint16_t));

    /// @brief 配合原程序特殊的错误码
    if(len != sizeof(struct c7_verify_s)) { return ERR_CODE_BAUD_W_ERR; }
    if(cal_crc != p_verify->crc) { return ERR_CODE_BAUD_W_ERR; }
    if(password != p_verify->password) { return ERR_CODE_BAUD_W_ERR; }

    // 记录开始时间，后续直接和这个时间戳比较，掉电后无效
    dlt645_c7_status.verify_f   = true;
    uint32_t times_tmp          = mclock.calendar_to_seconds(&(mclock.datetime->cale));    // 获取当前时间戳
    dlt645_c7_status.work_times = times_tmp + p_verify->loss_fuction_time * 60;

    rand_number = C7_RAND_16BIT();

    /// @brief 回复数据的长度
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    memcpy(buff, &rand_number, 2);
    buff += 2, p_info->data_len += 2;

    return ERR_CODE_NONE;
}

/**
 * @description: 取消身份认证
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_verify_unvolid_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);    // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);
};
#pragma pack(pop)

static int c7_verify_unvolid_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    memset(&dlt645_c7_status, 0, sizeof(dlt645_c7_status_s));
    return ERR_CODE_NONE;
}

/**
 * @description: 开户
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_open_account_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);         // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);    // 操作者代码
    DEFINE_UNION_FIELD(uint32_t, password);          // 密码
    DEFINE_UNION_FIELD(uint16_t, rand_number_1);     // 随机数1
    DEFINE_UNION_FIELD(uint16_t, rand_number_2);     // 随机数2
    DEFINE_UNION_FIELD(uint32_t, pay_much);          // 购电金额
    DEFINE_UNION_FIELD(uint32_t, pay_count);         // 购电次数
    DEFINE_UNION_FIELD(uint32_t, account_code);      // 客户编号
    uint16_t crc;
};
#pragma pack(pop)

static int c7_open_account_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t                  len      = p_info->data_len;
    uint32_t                  time_tmp = 0;
    uint32_t                  password = 0;
    uint16_t                  cal_crc  = 0;
    struct c7_open_account_s *p_open   = (struct c7_open_account_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    cal_crc = c7_crc16((uint8_t *)p_open + offsetof(struct c7_open_account_s, password), len - offsetof(struct c7_open_account_s, password) - sizeof(uint16_t));

    /// @brief 返回的错误码配合原有小程序，不要修改
    if(password != p_open->password) { return ERR_CODE_BAUD_W_ERR; }
    if(cal_crc != p_open->crc) { return ERR_CODE_BAUD_W_ERR; }
    if((len != sizeof(struct c7_open_account_s)) || (dlt645_c7_status.verify_f == false) || (p_open->pay_count != 1)) { return ERR_CODE_BAUD_W_ERR; }

    /// @brief 判断是否在有效期内
    time_tmp = mclock.calendar_to_seconds(&(mclock.datetime->cale));
    if(time_tmp > dlt645_c7_status.work_times) { return ERR_CODE_BAUD_W_ERR; }

    /// @brief 保存客户编号，后续查询使用
    api.product_info_set(PRODUCT_INFO(account_code), p_open->account_code_bytes, sizeof(p_open->account_code));

    /// @brief 充钱
    pay.top_up(p_open->pay_much);

    /// @brief 回复数据处理
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    /// @brief 回复客户编号
    memcpy(buff, p_open->account_code_bytes, 4), buff += 4, p_info->data_len += 4;

    /// @brief 购电金额
    memcpy(buff, p_open->pay_much_bytes, 4), buff += 4, p_info->data_len += 4;

    /// @brief 购电次数
    memcpy(buff, p_open->pay_count_bytes, 4), buff += 4, p_info->data_len += 4;

    return ERR_CODE_NONE;
}

static int c7_purchase_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t                  len          = p_info->data_len;
    uint32_t                  account_code = 0;
    uint32_t                  password     = 0;
    uint16_t                  cal_crc      = 0;
    pay_data_s                pay_data     = {0x00};
    struct c7_open_account_s *p_open       = (struct c7_open_account_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(account_code), &account_code);
    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    cal_crc = c7_crc16((uint8_t *)p_open + offsetof(struct c7_open_account_s, password), len - offsetof(struct c7_open_account_s, password) - sizeof(uint16_t));

    /// @brief 返回的错误码配合原有小程序，不要修改
    if(password != p_open->password) { return ERR_CODE_PSW_ERR; }
    if(cal_crc != p_open->crc) { return ERR_CODE_PSW_ERR; }
    if((len != sizeof(struct c7_open_account_s)) || (dlt645_c7_status.verify_f == false) || (p_open->account_code != account_code)) { return ERR_CODE_PSW_ERR; }

    /// @bug (模块里面没有对购电次数自增) 购电次数不对，返回错误码，不进行充值操作
    pay_data = *(pay.data_get());
    if(pay_data.total_purchase_cnt + 1 != p_open->pay_count) { return ERR_CODE_PSW_ERR; }

    /// @brief 充钱
    pay.top_up(p_open->pay_much);

    /// @brief 正确回复数据处理
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    /// @brief 回复客户编号
    memcpy(buff, p_open->account_code_bytes, 4), buff += 4, p_info->data_len += 4;

    /// @brief 购电金额
    memcpy(buff, p_open->pay_much_bytes, 4), buff += 4, p_info->data_len += 4;

    /// @brief 购电次数
    memcpy(buff, p_open->pay_count_bytes, 4), buff += 4, p_info->data_len += 4;

    return ERR_CODE_NONE;
}

/**
 * @description: 修改密码
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_ctrl_key_update_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);         // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);    // 操作者代码
    DEFINE_UNION_FIELD(uint32_t, old_password);      // 旧密码
    DEFINE_UNION_FIELD(uint16_t, rand_number1);      // 随机数
    DEFINE_UNION_FIELD(uint16_t, rand_number2);      // 随机数
    DEFINE_UNION_FIELD(uint32_t, new_password);      // 新密码
    uint16_t crc;
};
#pragma pack(pop)

static int c7_ctrl_key_update_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t                     len      = p_info->data_len;
    uint32_t                     password = 0;
    uint16_t                     cal_crc  = 0;
    struct c7_ctrl_key_update_s *p_update = (struct c7_ctrl_key_update_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    cal_crc = c7_crc16((uint8_t *)p_update + offsetof(struct c7_ctrl_key_update_s, old_password), len - offsetof(struct c7_ctrl_key_update_s, old_password) - sizeof(uint16_t));

    /// @brief 返回的错误码配合原有小程序，不要修改
    if(password != p_update->old_password) { return ERR_CODE_PSW_ERR; }
    if(cal_crc != p_update->crc) { return ERR_CODE_PSW_ERR; }
    if((len != sizeof(struct c7_ctrl_key_update_s)) || (dlt645_c7_status.verify_f == false)) { return ERR_CODE_PSW_ERR; }

    /// @brief 修改密码
    api.product_info_set(PRODUCT_INFO(payment_password), p_update->new_password_bytes, sizeof(p_update->new_password));

    /// @brief 回复新密码
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    memcpy(buff, p_update->new_password_bytes, 4), buff += 4, p_info->data_len += 4;

    return ERR_CODE_NONE;
}

/**
 * @description: 查询状态
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_check_status_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);         // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);    // 操作者代码
    DEFINE_UNION_FIELD(uint32_t, password);          // 密码
    DEFINE_UNION_FIELD(uint16_t, rand_number1);      // 随机数
    DEFINE_UNION_FIELD(uint16_t, rand_number2);      // 随机数
    uint16_t crc;
};
#pragma pack(pop)

static int c7_check_status_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t                  len          = p_info->data_len;
    pay_data_s                pay_data     = {0x00};
    uint32_t                  account_code = 0;
    uint32_t                  password     = 0;
    uint16_t                  cal_crc      = 0;
    struct c7_check_status_s *p_check      = (struct c7_check_status_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(account_code), &account_code);
    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    cal_crc = c7_crc16((uint8_t *)p_check + offsetof(struct c7_check_status_s, password), len - offsetof(struct c7_check_status_s, password) - sizeof(uint16_t));

    /// @brief 返回的错误码配合原有小程序，不要修改
    if(password != p_check->password) { return ERR_CODE_PSW_ERR; }
    if(cal_crc != p_check->crc) { return ERR_CODE_PSW_ERR; }
    if((len != sizeof(struct c7_check_status_s)) || (dlt645_c7_status.verify_f == false)) { return ERR_CODE_PSW_ERR; }

    /// @brief 回复数据处理
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    memcpy(buff, &account_code, 4), buff += 4, p_info->data_len += 4;

    pay_data = *(pay.data_get());
    memcpy(buff, (uint8_t *)&pay_data.remain_paid, sizeof(pay_data.remain_paid));
    buff += sizeof(pay_data.remain_paid), p_info->data_len += sizeof(pay_data.remain_paid);

    memcpy(buff, (uint8_t *)&pay_data.total_purchase_cnt, sizeof(pay_data.total_purchase_cnt));
    buff += sizeof(pay_data.total_purchase_cnt), p_info->data_len += sizeof(pay_data.total_purchase_cnt);

    return ERR_CODE_NONE;
}

/**
 * @description: 销户
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_close_account_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);         // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);    // 操作者代码
    DEFINE_UNION_FIELD(uint32_t, password);          // 密码
    DEFINE_UNION_FIELD(uint16_t, rand_number1);      // 随机数
    DEFINE_UNION_FIELD(uint16_t, rand_number2);      // 随机数
    DEFINE_UNION_FIELD(uint32_t, account_code);      // 客户编号
    uint16_t crc;
};
#pragma pack(pop)

static int c7_close_account_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t                   len          = p_info->data_len;
    uint32_t                   account_code = 0;
    uint32_t                   password     = 0;
    uint16_t                   cal_crc      = 0;
    struct c7_close_account_s *p_close      = (struct c7_close_account_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(account_code), &account_code);
    api.product_info_get(PRODUCT_INFO(payment_password), &password);
    cal_crc = c7_crc16((uint8_t *)p_close + offsetof(struct c7_close_account_s, password), len - offsetof(struct c7_close_account_s, password) - sizeof(uint16_t));

    /// @brief 返回的错误码配合原有小程序，不要修改
    if(password != p_close->password) { return ERR_CODE_PSW_ERR; }
    if(cal_crc != p_close->crc) { return ERR_CODE_PSW_ERR; }
    if((len != sizeof(struct c7_close_account_s)) || (dlt645_c7_status.verify_f == false) || (p_close->account_code != account_code)) { return ERR_CODE_PSW_ERR; }

    /// @brief 销户
    pay.reset(SYS_GLOBAL_RESET);

    /// @brief 回复客户编号
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    memcpy(buff, p_close->account_code_bytes, 4), buff += 4, p_info->data_len += 4;

    return 0;
}

/**
 * @description: 退费
 * @return {*}
 */
#pragma pack(push, 1)
struct c7_refund_s
{
    DEFINE_UNION_FIELD(uint32_t, data_flag);         // 数据标识
    DEFINE_UNION_FIELD(uint32_t, operation_code);    // 操作者代码
    DEFINE_UNION_FIELD(uint32_t, password);          // 密码
    DEFINE_UNION_FIELD(uint16_t, rand_number1);      // 随机数
    DEFINE_UNION_FIELD(uint16_t, rand_number2);      // 随机数
    DEFINE_UNION_FIELD(uint32_t, refund_much);       // 退费金额
    DEFINE_UNION_FIELD(uint32_t, pay_count);         // 购电次数
    DEFINE_UNION_FIELD(uint32_t, account_code);      // 客户编号
    uint16_t crc;
};
#pragma pack(pop)

static int c7_refund_data_parse(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint16_t            len          = p_info->data_len;
    uint32_t            account_code = 0;
    struct c7_refund_s *p_refund     = (struct c7_refund_s *)(p_info->data);

    api.product_info_get(PRODUCT_INFO(account_code), &account_code);

    /// @brief crc校验，数据长度，密码判断，认证判断,匹配客户编号
    if((len != sizeof(struct c7_refund_s)) || (dlt645_c7_status.verify_f == false) || (p_refund->account_code != account_code)) { return ERR_CODE_OTHER; }

    /// @brief 退费
    pay.deduct(p_refund->refund_much);

    /// @brief 回复客户编号, 退费金额, 购电次数
    p_info->data_len = 0;

    memcpy(buff, &p_info->id, sizeof(p_info->id));
    buff += sizeof(p_info->id), p_info->data_len += sizeof(p_info->id);

    memcpy(buff, p_refund->account_code_bytes, 4), buff += 4;
    memcpy(buff, p_refund->refund_much_bytes, 4), buff += 4;
    memcpy(buff, p_refund->pay_count_bytes, 4), buff += 4;

    return 0;
}

/**
 * @description:
 * @param {DLT645_2007_MSG_S} *p_info
 * @param {uint8_t} *buff
 * @return {*}
 */
static uint16_t dlt_645_read_7(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;

    /// @brief 修改回复数据指针
    if(buff == NULL)
    {
        p_data = p_info->snd_dat;
        memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    }

    /// @brief 数据处理
    switch(p_info->id)
    {
        case C7_ID_VERIFY:    // 0x070000ff 身份认证
            break;

        case C7_ID_VERIFY_UNVOLID_FF:    // 0x070002ff 取消身份认证
            break;

        case C7_OPEN_ACCOUNT_FF:    // 0x070101ff 开户
            break;

        case C7_PURCHASE_FF:    // 0x070102ff 充值
            break;

        case C7_CTRL_KEY_UPDATE_FF:    // 0x070201ff 修改密码
            break;

        case C7_CHECK_STATUS:    // 0x078102ff 检查ESAM状态
            break;

        case C7_CLOSE_ACCOUNT:    // 0x070103ff 销户
            break;

        case C7_REFUND:    // 0x070104ff 退费
            break;
    }

    /// @brief 返回数据长度
    if(buff == NULL)
    {
        if((p_data - p_info->snd_dat) == 4)
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE;
            return 1;
        }    // 无数据
        return (uint16_t)(p_data - p_info->snd_dat);
    }
    return (uint16_t)(p_data - buff);
}

/**
 * @description:
 * @param {DLT645_2007_MSG_S} *p_info
 * @param {uint8_t} *buff
 * @return {*}
 */
static uint16_t dlt_645_write_7(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;
    uint8_t  ret    = ERR_CODE_NONE;

    /// @brief 判断模式，充钱一个单独的模式，适配以前的电表
    if(dlt645_c7_status.run_type != C8_ENTRY_FACTORY && dlt645_c7_status.run_type != C8_ENTRY_PROGRAM) { return ERR_CODE_PSW_ERR; }

    /// @brief 较为特殊，后续还是需要完整的收数据包，所以需要单独开一个空间
    if(buff == NULL) { p_data = p_info->snd_dat; }

    switch(p_info->id)
    {
        case C7_ID_VERIFY:    // 0x070000ff 身份认证
            ret = c7_verify_data_parse(p_info, p_data);
            break;

        case C7_ID_VERIFY_UNVOLID_FF:    // 0x070002ff 取消身份认证
            ret = c7_verify_unvolid_data_parse(p_info, p_data);
            break;

        case C7_OPEN_ACCOUNT_FF:    // 0x070101ff 开户
            ret = c7_open_account_data_parse(p_info, p_data);
            break;

        case C7_PURCHASE_FF:    // 0x070102ff 充值
            ret = c7_purchase_data_parse(p_info, p_data);
            break;

        case C7_CTRL_KEY_UPDATE_FF:    // 0x070201ff 修改密码
            ret = c7_ctrl_key_update_data_parse(p_info, p_data);
            break;

        case C7_CHECK_STATUS:    // 0x078102ff 检查ESAM状态
            ret = c7_check_status_data_parse(p_info, p_data);
            break;

        case C7_CLOSE_ACCOUNT:    // 0x070103ff 销户
            ret = c7_close_account_data_parse(p_info, p_data);
            break;

        case C7_REFUND:    // 0x070104ff 退费
            ret = c7_refund_data_parse(p_info, p_data);
            break;
    }

    /// @brief 返回数据长度
    if(buff == NULL)
    {
        if(ret != ERR_CODE_NONE)
        {
            /// @brief 配合协议两位错误码，不要修改
            uint16_t err = ret;
            memcpy(p_info->snd_dat, (uint8_t *)&err, sizeof(uint16_t));
            p_info->data_len = sizeof(uint16_t), p_info->err_f = TRUE;
        }
        return 0;
    }
    return (uint16_t)(p_data - buff);
}

#endif