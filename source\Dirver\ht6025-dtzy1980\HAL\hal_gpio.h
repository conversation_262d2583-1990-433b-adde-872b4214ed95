/**
  ******************************************************************************
  * @file    hal_gpio.h
  * <AUTHOR> 
  * @version V1.0
  * @date    2024-08-05
  * @brief   This file contains all the functions prototypes for the GPIO
  * @note  
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
  *
  ******************************************************************************/
// #ifdef __cplusplus
//  extern "C" {
// #endif

#ifndef __HAL_GPIO_H
#define __HAL_GPIO_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"


/*
 * @brief 系统引脚功能定义:
   -o  输出
   -i  输入
   -ii 中断输入
   -io 双向IO
   -fa 摸拟输入
   -fi 外设功能输入
   -fo 外设功能输出
   -ff 外设功能双向输入/出
   -fw PWM输出
*/
#define PIN_PLC_EVE                 HT_GPIOA,0     // -o PLC EVE 
#define PIN_PLC_RST                 HT_GPIOA,1     // -o PLC RST
#define PIN_PLC_SET                 HT_GPIOA,2     // -o PLC SET
#define PIN_PLC_STA                 HT_GPIOA,3     // -i PLC STA
#define PIN_VLCD_CTRL               HT_GPIOA,4     // -o LCD电源控制 
#define PIN_METER_COVER             HT_GPIOA,5     // -ii 电表盖检测
#define PIN_JTAG                    HT_GPIOA,6     // -io JTAG
#define PIN_CF1_IN                  HT_GPIOA,7     // -ii CF1脉冲输入
#define PIN_CF2_IN                  HT_GPIOA,8     // -ii CF2脉冲输入
#define PIN_KEY_DWN                 HT_GPIOA,9     // -ii 下翻键
#define PIN_KEY_UP                  HT_GPIOA,10    // -ii 上翻键
#define PIN_TEM_COVER               HT_GPIOA,11    // -ii 开尾盖检测
#define PIN_BATT_EXT                HT_GPIOA,12    // -fa 外置6V电池测压
#define PIN_BATT_IN                 HT_GPIOA,13    // -fa 内置3.6V电池测压
#define PIN_A_14                HT_GPIOA,14    // --
#define PIN_A_15                HT_GPIOA,15    // --


#define PIN_B_0                 HT_GPIOB,0     // --
#define PIN_EE_POW                  HT_GPIOB,1     // -o  EEPROM电源控制
#define PIN_RLY_CHK                 HT_GPIOB,2     // -i  继电器检测
#define PIN_LCD_SDA                 HT_GPIOB,3     // -io LCD iic 数据线
#define PIN_LCD_SCL                 HT_GPIOB,4     // -o LCD iic 时钟线
#define PIN_B_5                 HT_GPIOB,5     // --
#define PIN_FLASH_CS                HT_GPIOB,6     // -i  FLASH CS
#define PIN_FLASH_SCLK              HT_GPIOB,7     // -o  FLASH SCLK
#define PIN_FLASH_MISO              HT_GPIOB,8     // -i  FLASH MISO
#define PIN_FLASH_MOSI              HT_GPIOB,9     // -o  FLASH MOSI
#define PIN_IR_CTRL                 HT_GPIOB,10    // -o  红外控制输出
#define PIN_CARD_KEY                HT_GPIOB,11    // -i  卡片检测口
#define PIN_CARD_CTRL               HT_GPIOB,12    // -fi 卡片控制信号
#define PIN_SWIO                    HT_GPIOB,13    // -io 仿真口
#define PIN_B_14                HT_GPIOB,14    // --
#define PIN_SWCLK                   HT_GPIOB,15    // -io 仿真口

#define PIN_UART1_TXD               HT_GPIOC,0     // -fo  PLC串口输出
#define PIN_UART1_RXD               HT_GPIOC,1     // -fi  PLC串口输入
#define PIN_UART0_RXD               HT_GPIOC,2     // -fo  蓝牙串口输入
#define PIN_UART0_TXD               HT_GPIOC,3     // -fi  蓝牙串口输出
#define PIN_ESAM_MOSI               HT_GPIOC,4     // -o   ESAM SPI MOSI
#define PIN_HT_DOUT                 HT_GPIOC,4     // -o 计量芯片数据输出 与ESAM_SCLK共用
#define PIN_ESAM_MISO               HT_GPIOC,5     // -i  ESAM SPI MISO 
#define PIN_HT_DIN                  HT_GPIOC,5     // -i 计量芯片数据输入 与ESAM_SCLK共用
#define PIN_HT_CLK                  HT_GPIOC,6    // -o 计量芯片时钟输出 与ESAM_SCLK共用
#define PIN_ESAM_SCLK               HT_GPIOC,6     // -i  ESAM SPI SCLK  
#define PIN_HT_CS                   HT_GPIOC,7     // -o 计量芯片数据输出 
#define PIN_SECOND_TOUT             HT_GPIOC,8     // -o  秒脉冲输出
#define PIN_IN_MODULE_RST           HT_GPIOC,9     // -o  蓝牙复位
#define PIN_LCD_BG                  HT_GPIOC,10    // -o  LCD背光灯
#define PIN_UART2_TXD               HT_GPIOC,11    // -fo LCD SEG
#define PIN_UART2_RXD               HT_GPIOC,12    // -o  键盘扫描输出第4行
#define PIN_EE_SCL                  HT_GPIOC,13    // -o  EEPROM I2C SCL
#define PIN_EE_SDA                  HT_GPIOC,14    // -io EEPROM I2C SDA
//#define PIN_LCD_SCL                 HT_GPIOC,15    // -io LCD iic 时钟线

#define PIN_HALL_CHK                HT_GPIOD,0     // -i  磁场检测
#define PIN_RELAY_OFF               HT_GPIOD,1     // -o  继电器拉闸控制信号
#define PIN_RELAY_ON                HT_GPIOD,2     // -o  继电器合闸控制信号
#define PIN_RELAY_LED               HT_GPIOD,3     // -fo 继电器状态灯
#define PIN_EXT_RELAY               HT_GPIOD,4     // -o  外部继电器控制信号i
#define PIN_ESAM_CS                 HT_GPIOD,5     // -o  ESAM片选
#define PIN_ALARM_RELAY             HT_GPIOD,6     // -o  告警继电器控制信号
#define PIN_EMU_CTL                 HT_GPIOD,7     // -o  计量芯片控制信号
#define PIN_CS_1                    HT_GPIOD,8     // -o  计量芯片片选1
#define PIN_CS_2                    HT_GPIOD,9     // -o  计量芯片片选2    
#define PIN_D_10                HT_GPIOD,10    // -ff 
#define PIN_D_11                HT_GPIOD,11    // -
#define PIN_D_12                HT_GPIOB,12    // -
#define PIN_D_13                HT_GPIOD,13    // -
#define PIN_POWER_CTRL              HT_GPIOD,14      // -o  外设电源控制
#define PIN_D_15                HT_GPIOD,15    // -

#define PIN_BEEP                    HT_GPIOE,0     ///-o  蜂鸣器控制信号
#define PIN_UART4_TXD               HT_GPIOE,1     // -fo 2路RS485通讯模块串口输出-卡片串口输出
#define PIN_UART4_RXD               HT_GPIOE,2     // -fi 2路RS485通讯模块串口输入-卡片串口输入
#define PIN_4851_CTRL               HT_GPIOE,3     // -o  1路RS485通讯控制
#define PIN_UART3_RXD               HT_GPIOE,4     // -fi 1路RS485通讯模块串口输入
#define PIN_UART3_TXD               HT_GPIOE,5     // -fo 1路RS485通讯模块串口输出
#define PIN_CARD_RST                HT_GPIOE,6     // -o  卡片复位控制
#define PIN_LVDIN0                  HT_GPIOE,7     // -o  LVDIN0检测输入
#define PIN_4852_CTRL               HT_GPIOE,8     // -o  2路RS485通讯控制
#define PIN_E_10                HT_GPIOE,10    // -
#define PIN_E_11                HT_GPIOE,11    // -
#define PIN_E_12                HT_GPIOE,12    // -
#define PIN_E_13                HT_GPIOE,13    // -
#define PIN_E_14                HT_GPIOE,14    // -
#define PIN_E_15                HT_GPIOE,15    // -

/* Exported macro ------------------------------------------------------------*/
///以下根据芯片手册寄存器描述进行配置。当没有相应功能时，应定义为空，不能直接注释掉
/* @brief 寄存器操作方式配置GPIO输入 */
#define HAL_GPIO_DIR_IN(port,pin)     ((port->PTDIR) &= ~(1 << (pin))) 
/* @brief 寄存器操作方式配置GPIO输出 */
#define HAL_GPIO_DIR_OUT(port,pin)    ((port->PTDIR) |= (1 << (pin)))

/* @brief 寄存器操作方式配置获取GPIO输入电平 */
#define HAL_GPIO_IN_GET(port,pin)     ((port->PTDAT) & (1 << pin))
/* @brief 寄存器操作方式配置获取GPIO输出电平 */
#define HAL_GPIO_OUT_GET(port,pin)    ((port->PTDAT) & (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出低电平 */
#define HAL_GPIO_OUT_RST(port,pin)    ((port->PTCLR) = (1 << pin))
/* @brief 寄存器操作方式配置GPIO输出高电平 */
#define HAL_GPIO_OUT_SET(port,pin)    ((port->PTSET) = (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出翻转电平 */
#define HAL_GPIO_OUT_REV(port,pin)    ((port->PTTOG) = (1 << pin))
/* @brief 寄存器操作方式配置GPIO上拉使能 */
#define HAL_GPIO_PTUP_EN(port,pin)    ((port->PTUP) &= ~(1 << pin))

/* @brief 寄存器操作方式配置GPIO上拉关闭 */
#define HAL_GPIO_PTUP_DIS(port,pin)   ((port->PTUP) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO开漏关闭 */
#define HAL_GPIO_PTOD_DIS(port,pin)   ((port->PTOD) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO开漏开启 */
#define HAL_GPIO_PTOD_EN(port,pin)   ((port->PTOD) &= ~(1 << pin))
/* @brief 获取GPIO寄存器名称及管脚编号 */
#define HAL_GPIO_PORT(port,pin)       (port)
#define HAL_GPIO_PIN(port,pin)        (pin)

///以下为宏参数分解函数，无须更改。供底层驱动调用。
/* @brief 获取管脚的端口及编号分配 */
#define gpio_port(x)        HAL_GPIO_PORT(x)
#define gpio_pin(x)         HAL_GPIO_PIN(x)
#define gpio_pin_mask(x)    (1 << HAL_GPIO_PIN(x))
/* @brief 设置管脚输入模式 */
#define gpio_set_input(x)   HAL_GPIO_DIR_IN(x)
/* @brief 设置管脚输出模式 */
#define gpio_set_output(x)  HAL_GPIO_DIR_OUT(x)
/* @brief 获取管脚输入电平 */
#define gpio_input_get(x)   boolof(HAL_GPIO_IN_GET(x))
/* @brief 获取管脚输出电平 */
#define gpio_output_get(x)  boolof(HAL_GPIO_OUT_GET(x))
/* @brief 管脚置高电平 */
#define gpio_out_H(x)       HAL_GPIO_OUT_SET(x)
/* @brief 管脚置低电平 */
#define gpio_out_L(x)       HAL_GPIO_OUT_RST(x)
/* @brief 管脚反转电平 */
#define gpio_out_rev(x)     HAL_GPIO_OUT_REV(x)
/* @brief 管脚打开上拉 */
#define gpio_up_en(x)       HAL_GPIO_PTUP_EN(x)
/* @brief 管脚关闭上拉 */
#define gpio_up_dis(x)      HAL_GPIO_PTUP_DIS(x)
/* @brief 管脚打开开漏 */
#define gpio_od_en(x)       HAL_GPIO_PTOD_EN(x)
/* @brief 管脚关闭开漏 */
#define gpio_od_dis(x)      HAL_GPIO_PTOD_DIS(x)

/* @brief 外部中断服务函数枚举 */
typedef enum
{
    TYPE_EXTI0 = 0,
    TYPE_EXTI1,
    TYPE_EXTI2,
    TYPE_EXTI3,
    TYPE_EXTI4,
    TYPE_EXTI5,
    TYPE_EXTI6,
    TYPE_EXTI7,
    TYPE_EXTI8,
    TYPE_EXTI9,
    TYPE_EXTI_NUM,
} GPIO_EXTI_TYPE;

typedef enum
{
    GPIO_OPEN,
    GPIO_CLOSE,
    GPIO_MONITOR,
}GPIO_INIT_TYPE_t;

/* Exported functions ------------------------------------------------------- */
struct hal_gpio_t
{
    /// @brief  串口GPIO配置
    void (*uart_init)(uint8_t com);

    /// @brief  GPIO配置
    void (*init)(void);

    /// @brief  GPIO低功耗下初始化
    void (*init_nopower)(void);

    /// @brief  输出脉冲模式设置
    void (*pulse_out_mode)(uint8_t mode);
    
    /// @brief IO口扫描，typ=0只扫描电源检测IO口，typ=1扫描所有IO口
    void (*monitor)(uint8_t typ);

    /// @brief 外部中断服务函数设置
    void (*exti_set)(uint8_t irq, void func(void));

    /// @brief lcd GPIO初始化
    void (*ext_lcd)(GPIO_INIT_TYPE_t typ);

    /// @brief FLASH GPIO初始化
    void (*data_flash)(GPIO_INIT_TYPE_t type);

    /// @brief eeprom GPIO初始化
    void (*ext_eeprom)(GPIO_INIT_TYPE_t type);

    /// @brief 计量芯片GPIO初始化
    void (*mic_init)(GPIO_INIT_TYPE_t type);
};
extern const struct hal_gpio_t hal_gpio;


#endif /* __HAL_GPIO_H */

/** @} */
/** @} */
// #ifdef __cplusplus
// }
// #endif

