/**
 ******************************************************************************
* @file    datastore.c
* <AUTHOR> @date    2024
* @brief   存储模块，主要负责MCU和外部存储器的数据读写，地址索引管理等功能
* note:    eeprom 前128字节预留用于检测，128+384字节用于校表数据，剩余空间app可使用
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include <string.h>
#include "typedef.h"
#include "boot_cfg.h"
#include "bsp_cfg.h"
#include "eeprom.h"
#include "ext_flash.h"
#include "hal_flash.h"
#include "datastore.h"
#include "hal_mcu.h"
#include "status.h"
#include "debug.h"
#include "..\meter\energy.h"
#include "timeapp.h"
#include "local_port.h"
#include "comm_phy.h"
#include "tariff.h"
#include "step_tariff.h"
#include "payment.h"
#include "dispApp.h"
#include "api.h"
#include "demand.h"
#include "../config/app_config.h"
#include "event.h"
#include "profile_capture_obj.h"
#include "power_event.h"
#include "billing.h"
#include "loadcurve.h"
#include "energy.h"
#include "RelayApp.h"
#include "module_para.h"
#include "image_transfer_api.h"

#define TEST_STR_LEN 10    //不超过16字节
static const uint8_t eeprom_test[TEST_STR_LEN] = "SHEWEI2025";

/// @brief 保存在MCU的数据
/// 数据顺序非必要不要调整；每个模块基本都有预留空间，可以根据需要增加或减少空间。 ！！！注意逻辑页对齐，一个模块的参数最好不要跨页，影响效率。
typedef struct
{
    /// uint8_t cal_data[alignof(CAL_DATA_SIZE, MCU_FLASH_PAGE_PSIZE)];     ///校表数据已经预留，这里不用再给空间
    /// 默认14K空间，如果空间不够，修改icf文件和boot_cfg.h文件
    SMALLOC(EnergyValue_s,              energy,             alignof(sizeof(EnergyValue_s), MCU_FLASH_PAGE_PSIZE));///掉电电能存储 ，单独区域 三相表开分相分费率，电能占用1200字节。
    SMALLOC(tariff_para_s,              tariff1,            alignof(sizeof(tariff_para_s), MCU_FLASH_PAGE_PSIZE));///当前套
    SMALLOC(tariff_para_s,              tariff2,            alignof(sizeof(tariff_para_s), MCU_FLASH_PAGE_PSIZE));///当前套

    SMALLOC(pay_data_s,                 payment_data,       alignof(512, MCU_FLASH_PAGE_PSIZE));                  ///预付费模块预留1页或者512字节
    SMALLOC(pwrdn_rcd_s,                pwrdn_rcd,          alignof(512, MCU_FLASH_PAGE_PSIZE));                  ///低功耗下开盖检测预留512字节或者一页
    SMALLOC(pe_pwrdn_s,                 pwr_evt_pd,         alignof(512, MCU_FLASH_PAGE_PSIZE));                  ///电源事件检测预留512字节或者一页

    /// @brief 1K
    SMALLOC(product_info_s,             product_info,       256);
    SMALLOC(energy_para_s,              energy_para,        64);
    SMALLOC(private_info_s,             private_info,       64);  
    SMALLOC(pay_para_s,                 payment_para,       128);
    SMALLOC(step_tariff_para_s,         step_tariff,        512);   

    /// @brief 1K
    SMALLOC(pe_para_s,                  pwr_evt,            256); // 512
    SMALLOC(ctrl_para_s,                control,            256); 
    SMALLOC(ClockPara_s,                clock,              128);
    SMALLOC(StatusPara_s,               status,             128);
    SMALLOC(billing_para_s,             billing,            64);
    SMALLOC(demand_para_s,              demand,             64);
    SMALLOC(lc_para_s,                  load_curve,         64); //
    SMALLOC(LocalPortPara_s,            port[PHY_CHN_NUM],  64);

    /// @brief 1K
    SMALLOC(module_para_s,              module_param,       512);
#if (MCU_FLASH_PAGE_PSIZE > 512)
    uint8_t reserved[MCU_FLASH_PAGE_PSIZE - 512]; ///预留空间，MCU_FLASH_PAGE_PSIZE为MCU的页大小
#endif
    ///
    SMALLOC(disp_para_s,                disp,               alignof(sizeof(disp_para_s) + sizeof(disp_list_s) * DISP_LIST_NUM, MCU_FLASH_PAGE_PSIZE));
}McuEESpase_t;

/// @brief 事件记录、曲线指针等信息区
struct log_head_in_eeprom
{
    log_head_s step_bill;
    log_head_s mon_frozen;   
    log_head_s day_frozen;
#if LC1_ENABLE
    log_head_s lc1_head;
#endif
#if LC2_ENABLE
    log_head_s lc2_head;
#endif
    log_head_s event[EVENT_TYPE_NUM];
};

/// 结算空间定义
#define BLM_BLK_DATA_SIZE       sizeof(bl_monthly_s)  
#define BLD_BLK_DATA_SIZE       alignof(sizeof(bl_daily_s), 256)
#define BLS_BLK_DATA_SIZE       sizeof(bl_step_s)

#define BLM_BLK_SIZE            alignof(BLM_BLK_DATA_SIZE*BILLING_MONTH_LOG_NUM, EE_PAGE_SIZE)
#define BLD_BLK_SIZE            alignof(BLD_BLK_DATA_SIZE*BILLING_DAILY_LOG_NUM, DATAFLASH_PAGE_LEN)
#define BLS_BLK_SIZE            alignof(BLS_BLK_DATA_SIZE*BILLING_STEP_LOG_NUM,  EE_PAGE_SIZE)

#define DF_LC1_SIZE             alignof(LC1_RCD_SIZE*LC1_ENTRIES_NUM,     DATAFLASH_PAGE_LEN)    //负荷曲线1记录空间大小
#define DF_LC2_SIZE             alignof(LC2_RCD_SIZE*LC2_ENTRIES_NUM,     DATAFLASH_PAGE_LEN)    //负荷曲线2记录空间大小

typedef struct
{
    // uint8_t cal_data[alignof(CAL_DATA_SIZE, EE_PAGE_SIZE)];                                      ///校表数据从0开始，在底层直接使用，这里只是声明空间
    uint8_t rev[EE_PAGE_SIZE];                                                                      ///预留1页,可用于测试

    SMALLOC(EnergyValue_s,             energy,              alignof(sizeof(EnergyValue_s),      EE_PAGE_SIZE));   ///电能存储
    SMALLOC(EnergyValue_s,             energy_bak,          alignof(sizeof(EnergyValue_s),      EE_PAGE_SIZE));   ///电能备份存储

    SMALLOC(struct log_head_in_eeprom, log_header,          EE_PAGE_SIZE*10);                                ///事件记录、曲线指针等信息区

    /// @brief 保存在EEPROM的运行数据
    SMALLOC(StatusData_t,              status,              alignof(sizeof(StatusData_t),       EE_PAGE_SIZE)); 
    SMALLOC(ClockData_s,               clock,               alignof(sizeof(ClockData_s),        EE_PAGE_SIZE)); 
    SMALLOC(ClockData_s,               clockpd,             alignof(sizeof(ClockData_s),        EE_PAGE_SIZE));
    SMALLOC(demand_block_s,            demand,              alignof(sizeof(demand_block_s),     EE_PAGE_SIZE));
    SMALLOC(pay_data_s,                pay_data1,           alignof(sizeof(pay_data_s),         EE_PAGE_SIZE)); 
    SMALLOC(pay_data_s,                pay_data2,           alignof(sizeof(pay_data_s),         EE_PAGE_SIZE)); 
    SMALLOC(tariff_data_s,             tariff,              alignof(sizeof(tariff_data_s),      EE_PAGE_SIZE));
    SMALLOC(LocalPortData_s,           port[PHY_CHN_NUM],   alignof(sizeof(LocalPortData_s),    EE_PAGE_SIZE));
    SMALLOC(pe_data_s,                 pwr_evt,             alignof(sizeof(pe_data_s),          EE_PAGE_SIZE));
    SMALLOC(ctrl_data_s,               control,             alignof(sizeof(ctrl_data_s),        EE_PAGE_SIZE));
    SMALLOC(image_info_s,              firmware,            alignof(sizeof(image_info_s),       EE_PAGE_SIZE));
    /// @brief 结算数据
    uint8_t blm_blk[BLM_BLK_SIZE];
    uint8_t bls_blk[BLS_BLK_SIZE];
}EepromSpase_t;

/// @brief 保存在外部flash的数据
#define STORE_EVT1_SIZE         alignof(EVT1_RCD_SIZE*EVT1_RCD_NUM,             DATAFLASH_PAGE_LEN)
#define STORE_EVT2_SIZE         alignof(EVT2_RCD_SIZE*EVT2_RCD_NUM,             DATAFLASH_PAGE_LEN)
#define STORE_EVT3_SIZE         alignof(EVT3_RCD_SIZE*EVT3_RCD_NUM,             DATAFLASH_PAGE_LEN)
#define STORE_EVT4_SIZE         alignof(EVT4_RCD_SIZE*EVT4_RCD_NUM,             DATAFLASH_PAGE_LEN)
#define STORE_EVT5_SIZE         alignof(EVT5_RCD_SIZE*EVT5_RCD_NUM,             DATAFLASH_PAGE_LEN) 
#define STORE_EVT6_SIZE         alignof(EVT6_RCD_SIZE*EVT6_RCD_NUM,             DATAFLASH_PAGE_LEN)
#define STORE_EVT7_SIZE         alignof(EVT7_RCD_SIZE*EVT7_RCD_NUM,             DATAFLASH_PAGE_LEN)

#define STORE_PWR_DOWN_SIZE     alignof(EVT_RCD_PWRDN_SIZE*EVT_RCD_PWRDN_NUM,   DATAFLASH_PAGE_LEN)
#define STORE_ADJTM_SIZE        alignof(EVT_RCD_ADJTM_SIZE*EVT_RCD_ADJTM_NUM,   DATAFLASH_PAGE_LEN)
#define STORE_BC_SIZE           alignof(EVT_RCD_BC_SIZE*EVT_RCD_BC_NUM,         DATAFLASH_PAGE_LEN)
#define STORE_METER_COVER_SIZE  alignof(EVT_RCD_MTRCV_SIZE*EVT_RCD_MTRCV_NUM,   DATAFLASH_PAGE_LEN)
#define STORE_TEM_COVER_SIZE    alignof(EVT_RCD_TEMCV_SIZE*EVT_RCD_TEMCV_NUM,   DATAFLASH_PAGE_LEN)

#define STORE_METER_CLEAN_SIZE  alignof(EVT_RCD_MTRCL_SIZE*EVT_RCD_MTRCL_NUM,   DATAFLASH_PAGE_LEN)
#define STORE_MD_CLEAN_SIZE     alignof(EVT_RCD_MDCL_SIZE*EVT_RCD_MDCL_NUM,     DATAFLASH_PAGE_LEN)
#define STORE_EVENT_CLEAN_SIZE  alignof(EVT_RCD_EVTC_SIZE*EVT_RCD_EVTC_NUM,     DATAFLASH_PAGE_LEN)

typedef struct
{
    uint8_t test[0 * DATAFLASH_PAGE_LEN + DATAFLASH_PAGE_LEN];  // 预留1页,可用于测试
    uint8_t bld_blk[BLD_BLK_SIZE + DATAFLASH_PAGE_LEN];         // 日结算数据


#if LC1_ENABLE
    uint8_t lc1_blk[DF_LC1_SIZE + DATAFLASH_PAGE_LEN];   // 负荷曲线1数据
#endif
#if LC2_ENABLE
    uint8_t lc2_blk[DF_LC2_SIZE + DATAFLASH_PAGE_LEN];   // 负荷曲线2数据
#endif

#if EVENT_LOSS_VOL_EN        
    uint8_t loss_vol_a[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 失压A记录
    uint8_t loss_vol_b[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 失压B记录
    uint8_t loss_vol_c[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 失压C记录
#endif
#if EVENT_LOW_VOL_EN
    uint8_t low_vol_a[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];    // 低压A记录
    uint8_t low_vol_b[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];    // 低压B记录
    uint8_t low_vol_c[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];    // 低压C记录
#endif
#if EVENT_OVER_VOL_EN
    uint8_t over_vol_a[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 过压A记录
    uint8_t over_vol_b[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 过压B记录
    uint8_t over_vol_c[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // 过压C记录
#endif
#if EVENT_MISS_VOL_EN
    uint8_t miss_vol_a[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // A相缺相记录
    uint8_t miss_vol_b[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // B相缺相记录
    uint8_t miss_vol_c[STORE_EVT1_SIZE + DATAFLASH_PAGE_LEN];   // C相缺相记录
#endif
#if EVENT_ALL_LOSS_VOL_EN
    uint8_t all_loss_vol[STORE_EVT7_SIZE + DATAFLASH_PAGE_LEN];  // 全失压记录
#endif
#if EVENT_V_REV_SQR_EN
    uint8_t v_rev_sqr[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];    // 电压逆相序记录
#endif
#if EVENT_I_REV_SQR_EN
    uint8_t i_rev_sqr[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];    // 电流逆相序记录
#endif
#if EVENT_V_UNB_EN     
    uint8_t v_unb[STORE_EVT4_SIZE + DATAFLASH_PAGE_LEN];     // 电压不平衡记录
#endif
#if EVENT_I_UNB_EN 
    uint8_t i_unb[STORE_EVT4_SIZE + DATAFLASH_PAGE_LEN];     // 电流不平衡记录
#endif
#if EVENT_LOS_CUR_EN 
    uint8_t los_cur_a[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // A相失流记录
    uint8_t los_cur_b[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // B相失流记录
    uint8_t los_cur_c[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // C相失流记录
#endif
#if EVENT_OVR_CUR_EN 
    uint8_t over_cur_a[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // A相过流记录
    uint8_t over_cur_b[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // B相过流记录
    uint8_t over_cur_c[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // C相过流记录
#endif
#if EVENT_MISS_CUR_EN
    uint8_t mis_cur_a[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // A相断流记录
    uint8_t mis_cur_b[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // B相断流记录
    uint8_t mis_cur_c[STORE_EVT2_SIZE + DATAFLASH_PAGE_LEN];    // C相断流记录
#endif
#if EVENT_REV_EN     
    uint8_t rev_power_a[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   //A相潮流反向记录
    uint8_t rev_power_b[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   //B相潮流反向记录
    uint8_t rev_power_c[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   //C相潮流反向记录
#endif
#if EVENT_OVR_LOAD_EN
    uint8_t over_load_a[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   // A相过载记录
    uint8_t over_load_b[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   // B相过载记录
    uint8_t over_load_c[STORE_EVT3_SIZE + DATAFLASH_PAGE_LEN];   // C相过载记录
#endif
#if EVENT_LOW_PF_EN  
    uint8_t low_pf[STORE_EVT5_SIZE + DATAFLASH_PAGE_LEN];        // 总功率因素低记录
#endif
#if EVENT_CONTROL_EN
    uint8_t disconnect[STORE_EVT6_SIZE + DATAFLASH_PAGE_LEN];   // 拉闸记录
    uint8_t reconnect[STORE_EVT6_SIZE + DATAFLASH_PAGE_LEN];    // 合闸记录
#endif
#if EVENT_PWR_DOWN_EN             
    uint8_t pwr_down[STORE_PWR_DOWN_SIZE + DATAFLASH_PAGE_LEN];         // 掉电记录
#endif
#if EVENT_SHITFT_TIME_EN     
    uint8_t shift_time[STORE_ADJTM_SIZE + DATAFLASH_PAGE_LEN];          // 校时记录
#endif
#if EVENT_BC_TIME_EN  
    uint8_t bc_time[STORE_BC_SIZE + DATAFLASH_PAGE_LEN];                // 广播校时记录
#endif

#if EVENT_METER_CLEAN_EN                      
    uint8_t meter_clean[STORE_METER_CLEAN_SIZE + DATAFLASH_PAGE_LEN];      // 电表清零记录
#endif
#if EVENT_DEMAND_CLEAN_EN                       
    uint8_t demand_clean[STORE_MD_CLEAN_SIZE + DATAFLASH_PAGE_LEN];       // 需量清零记录
#endif
#if EVENT_EVENT_CLEAN_EN                        
    uint8_t event_clean[STORE_EVENT_CLEAN_SIZE + DATAFLASH_PAGE_LEN];      // 事件清零记录
#endif
#if EVENT_METER_COVER_EN   
    uint8_t meter_cover[STORE_METER_COVER_SIZE + DATAFLASH_PAGE_LEN];   // 开表盖记录
#endif
#if EVENT_TEM_COVER_EN            
    uint8_t tem_cover[STORE_TEM_COVER_SIZE + DATAFLASH_PAGE_LEN];       // 开端盖记录
#endif
}ExtFlashSpase_t;


typedef struct
{
    SMALLOC(McuEESpase_t,    mcuspace,    MCU_FLASH_DATA_SIZE);
    SMALLOC(EepromSpase_t,   eespace,     EE_SIZE - NVM_EXTEE_RESVER_BTYES);
    SMALLOC(ExtFlashSpase_t, eflashspace, DATAFLASH_SIZE - FIRMWARE_MAX_SIZE*IAP_EXT_NVM);
}NvmSpase_t;

#define MCU_ADDR(x)   (MCUEE_BASE_ADDR + member_offset(McuEESpase_t, x))
#define EEP_ADDR(x)   (EXTEE_BASE_ADDR + member_offset(EepromSpase_t, x))
#define EDF_ADDR(x)   (EXTDF_BASE_ADDR + FIRMWARE_MAX_SIZE*IAP_EXT_NVM + member_offset(ExtFlashSpase_t, x))

/// @brief 根据索引获取数据存储地址
/// @param idx 索引
/// @return 地址
uint32_t nvm_addr(NVM_IDX_t idx)
{
    switch(idx)
    {
        // case NVM_MCU_FLAG: return MCU_ADDR(flag);
        case NVM_EE_REV:                return EEP_ADDR(rev);       //预留测试空间
        // case NVM_CAL_DATA_EE:           return EEP_ADDR(cal_data);   //eeprom中校表数据地址

        case NVM_PD_ENG_DATA:           return MCU_ADDR(energy);
        case NVM_ENG_PARA:              return MCU_ADDR(energy_para);
        case NVM_ACTIVATE_TARIFF_PARA:  return MCU_ADDR(tariff1);
        case NVM_PASSIVE_TARIFF_PARA:   return MCU_ADDR(tariff2);
        case NVM_STATUS_PARA:           return MCU_ADDR(status);
        case NVM_STATUS_PDR:            return MCU_ADDR(pwrdn_rcd);
        case NVM_CLOCK_PARA:            return MCU_ADDR(clock);
        case NVM_STEP_TARIFF_PARA:      return MCU_ADDR(step_tariff);
        case NVM_DISPLAY_PARA:          return MCU_ADDR(disp);
        case NVM_LOCAL_PORT_PARA:       return MCU_ADDR(port);
        case NVM_PRODUCT_INFO_PARA:     return MCU_ADDR(product_info);
        case NVM_DEMAND_PARA:           return MCU_ADDR(demand);
        case NVM_PWR_EVT_PARA:          return MCU_ADDR(pwr_evt);
        case NVM_PWR_EVT_PD:            return MCU_ADDR(pwr_evt_pd);
        case NVM_BILLING_PARA:          return MCU_ADDR(billing);
        case NVM_LOAD_CURVE_PARA:       return MCU_ADDR(load_curve);
        case NVM_PAYMENT_PARA:          return MCU_ADDR(payment_para);
        case NVM_PAYMENT_PD:            return MCU_ADDR(payment_data);
        case NVM_PRIVATE_INFO:          return MCU_ADDR(private_info);
        case NVM_CTRL_PARA:             return MCU_ADDR(control);
        case NVM_MODULE_PARA:           return MCU_ADDR(module_param);
        
        case NVM_TARIFF_DATA:           return EEP_ADDR(tariff);
        case NVM_CLOCK_PD:              return EEP_ADDR(clockpd);
        case NVM_CLOCK_DATA:            return EEP_ADDR(clock);
        case NVM_PAYMENT_DATA:          return EEP_ADDR(pay_data1);
        case NVM_PAYMENT_DATA_BAK:      return EEP_ADDR(pay_data2);
        case NVM_DEMAND_DATA:           return EEP_ADDR(demand);
        case NVM_ENG_DATA:              return EEP_ADDR(energy);
        case NVM_ENG_BAK_DATA:          return EEP_ADDR(energy_bak);
        case NVM_STATUS_DATA:           return EEP_ADDR(status); 
        case NVM_LOCAL_PORT_DATA:       return EEP_ADDR(port);
        case NVM_MON_FZ_HEADER:         return EEP_ADDR(log_header.mon_frozen);
        case NVM_DAY_FZ_HEADER:         return EEP_ADDR(log_header.day_frozen);
        case NVM_PWR_EVT_DATA:          return EEP_ADDR(pwr_evt);
        case NVM_CTRL_DATA:             return EEP_ADDR(control);
        case NVM_FMW_UPGRADE_DATA:      return EEP_ADDR(firmware);  // 程序升级数据存储地址
        case NVM_FIRMWARE_DOWNLOAD:
        #if IAP_EXT_NVM
            return (EXTDF_BASE_ADDR | FIRMWARE_DOWNLOAD_ADDR); // 程序下载到EXTDF存放地址
        #else
            return FIRMWARE_DOWNLOAD_ADDR; // 程序下载到MCUDF存放地址
        #endif
        default: return 0xFFFFFFFF;     // 给无效数据
    }
}

/// @brief 事件记录，负荷曲线，冻结数据地址获取。数据量少的事件可以放在EEPROM，数据量大的事件需放在外部FLASH。
/// @param idx 曲线索引
/// @return 
log_addr_s log_addr(NVM_LOG_t idx)
{
    uint32_t   total_size, max_entry = 0xFFFFFFFF;
    uint16_t   rcd_len = 0;
    log_addr_s addr;
    
    addr.hdr  = 0xFFFFFFFF; //给无效数据
    addr.ofst = 0xFFFFFFFF;
    
    switch(idx)
    {
        case NVM_LOG_MON_FROZEN: 
            addr.hdr  = EEP_ADDR(log_header.mon_frozen);
            addr.ofst = EEP_ADDR(blm_blk);
            addr.num  = BILLING_MONTH_LOG_NUM;
            addr.max  = BILLING_MONTH_LOG_NUM;
            addr.size = BLM_BLK_DATA_SIZE;
            return addr;
        case NVM_LOG_DAY_FROZEN: 
            addr.hdr  = EEP_ADDR(log_header.day_frozen);
            addr.ofst = EDF_ADDR(bld_blk) + TAG_DF_ROLL_ACCESS;
            total_size= BLD_BLK_SIZE;
            max_entry = BILLING_DAILY_LOG_NUM;
            rcd_len   = BLD_BLK_DATA_SIZE;
            addr.num  = BILLING_DAILY_LOG_NUM;
            addr.max  = BILLING_DAILY_LOG_NUM;
            addr.size = BLD_BLK_DATA_SIZE;
            return addr;
        case NVM_LOG_STEP_FROZEN:
            addr.hdr  = EEP_ADDR(log_header.step_bill);
            addr.ofst = EEP_ADDR(bls_blk);
            addr.num  = BILLING_STEP_LOG_NUM;
            addr.max  = BILLING_STEP_LOG_NUM;
            addr.size = BLS_BLK_DATA_SIZE;
            return addr;
#if LC1_ENABLE
        case NVM_LC1_PROFILE:
            addr.hdr  = EEP_ADDR(log_header.lc1_head);
            addr.ofst = EDF_ADDR(lc1_blk) + TAG_DF_ROLL_ACCESS;
            total_size= DF_LC1_SIZE;
            max_entry = LC1_ENTRIES_NUM;
            rcd_len   = LC1_RCD_SIZE;
            break;
#endif
#if LC2_ENABLE            
        case NVM_LC2_PROFILE:
            addr.hdr  = EEP_ADDR(log_header.lc2_head);
            addr.ofst = EDF_ADDR(lc2_blk) + TAG_DF_ROLL_ACCESS;
            total_size= DF_LC2_SIZE;
            max_entry = LC2_ENTRIES_NUM;
            rcd_len   = LC2_RCD_SIZE;
            return addr;
#endif

#if EVENT_LOSS_VOL_EN             
/// 失压事件
        case NVM_LOG_LOSS_VOL_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOSS_VOL_A]);
            addr.ofst = EDF_ADDR(loss_vol_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
 #if defined(POLYPHASE_METER)
        case NVM_LOG_LOSS_VOL_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOSS_VOL_B]);
            addr.ofst = EDF_ADDR(loss_vol_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        case NVM_LOG_LOSS_VOL_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOSS_VOL_C]);
            addr.ofst = EDF_ADDR(loss_vol_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
#endif
#endif
#if EVENT_LOW_VOL_EN
        case NVM_LOG_LOW_VOL_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOW_VOL_A]);
            addr.ofst = EDF_ADDR(low_vol_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
 #if defined(POLYPHASE_METER)
        case NVM_LOG_LOW_VOL_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOW_VOL_B]);
            addr.ofst = EDF_ADDR(low_vol_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        case NVM_LOG_LOW_VOL_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOW_VOL_C]);
            addr.ofst = EDF_ADDR(low_vol_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
#endif
#endif
#if EVENT_OVER_VOL_EN
        case NVM_LOG_OVR_VOL_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_VOL_A]);
            addr.ofst = EDF_ADDR(over_vol_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
#if defined(POLYPHASE_METER)
        case NVM_LOG_OVR_VOL_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_VOL_B]);
            addr.ofst = EDF_ADDR(over_vol_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        case NVM_LOG_OVR_VOL_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_VOL_C]);
            addr.ofst = EDF_ADDR(over_vol_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
#endif
#endif
#if EVENT_ALL_LOSS_VOL_EN
        case NVM_LOG_ALL_LOSS_VOL:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_ALL_LOSS_VOL]);
            addr.ofst = EDF_ADDR(all_loss_vol) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
#endif
#if EVENT_MISS_VOL_EN
        case NVM_LOG_MISS_VOL_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_VOL_A]);
            addr.ofst = EDF_ADDR(miss_vol_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_MISS_VOL_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_VOL_B]);
            addr.ofst = EDF_ADDR(miss_vol_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        case NVM_LOG_MISS_VOL_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_VOL_C]);
            addr.ofst = EDF_ADDR(miss_vol_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT1_SIZE;
            max_entry = EVT1_RCD_NUM;
            rcd_len   = EVT1_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_V_REV_SQR_EN
        case NVM_LOG_V_REV_SQR: 
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_V_REV_SQR]);
            addr.ofst = EDF_ADDR(v_rev_sqr) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
#endif
#if EVENT_I_REV_SQR_EN
        case NVM_LOG_I_REV_SQR: 
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_I_REV_SQR]);
            addr.ofst = EDF_ADDR(i_rev_sqr) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
#endif
#if EVENT_V_UNB_EN 
        case NVM_LOG_V_UNB: 
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_V_UNB]);
            addr.ofst = EDF_ADDR(v_unb) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT4_SIZE;
            max_entry = EVT4_RCD_NUM;
            rcd_len   = EVT4_RCD_SIZE;
            break;
#endif
#if EVENT_I_UNB_EN 
        case NVM_LOG_I_UNB: 
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_I_UNB]);
            addr.ofst = EDF_ADDR(i_unb) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT4_SIZE;
            max_entry = EVT4_RCD_NUM;
            rcd_len   = EVT4_RCD_SIZE;
            break;
#endif
#if EVENT_LOS_CUR_EN 
        case NVM_LOG_LOS_CUR_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOS_CUR_A]);
            addr.ofst = EDF_ADDR(los_cur_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_LOS_CUR_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOS_CUR_B]);
            addr.ofst = EDF_ADDR(los_cur_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        case NVM_LOG_LOS_CUR_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOS_CUR_C]);
            addr.ofst = EDF_ADDR(los_cur_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_OVR_CUR_EN 
        case NVM_LOG_OVR_CUR_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_CUR_A]);
            addr.ofst = EDF_ADDR(over_cur_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_OVR_CUR_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_CUR_B]);
            addr.ofst = EDF_ADDR(over_cur_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        case NVM_LOG_OVR_CUR_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_CUR_C]);
            addr.ofst = EDF_ADDR(over_cur_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_MISS_CUR_EN
        case NVM_LOG_MISS_CUR_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_CUR_A]);
            addr.ofst = EDF_ADDR(mis_cur_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_MISS_CUR_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_CUR_B]);
            addr.ofst = EDF_ADDR(mis_cur_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        case NVM_LOG_MISS_CUR_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_MISS_CUR_C]);
            addr.ofst = EDF_ADDR(mis_cur_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT2_SIZE;
            max_entry = EVT2_RCD_NUM;
            rcd_len   = EVT2_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_REV_EN   
        case NVM_LOG_REV_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_REV_A]);
            addr.ofst = EDF_ADDR(rev_power_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_REV_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_REV_B]);
            addr.ofst = EDF_ADDR(rev_power_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        case NVM_LOG_REV_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_REV_C]);
            addr.ofst = EDF_ADDR(rev_power_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_OVR_LOAD_EN
        case NVM_LOG_OVR_LOAD_A:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_LOAD_A]);
            addr.ofst = EDF_ADDR(over_load_a) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        #if defined(POLYPHASE_METER)
        case NVM_LOG_OVR_LOAD_B:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_LOAD_B]);
            addr.ofst = EDF_ADDR(over_load_b) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        case NVM_LOG_OVR_LOAD_C:      
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_OVR_LOAD_C]);
            addr.ofst = EDF_ADDR(over_load_c) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT3_SIZE;
            max_entry = EVT3_RCD_NUM;
            rcd_len   = EVT3_RCD_SIZE;
            break;
        #endif
#endif
#if EVENT_LOW_PF_EN  
        case NVM_LOG_LOW_PF:  
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_LOW_PF]);
            addr.ofst = EDF_ADDR(low_pf) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT5_SIZE;
            max_entry = EVT5_RCD_NUM;
            rcd_len   = EVT5_RCD_SIZE;
            break;
#endif
#if EVENT_CONTROL_EN
        case NVM_LOG_DISCONNECT:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_DISCONNECT]);
            addr.ofst = EDF_ADDR(disconnect) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT6_SIZE;
            max_entry = EVT6_RCD_NUM;
            rcd_len   = EVT6_RCD_SIZE;
            break;
        case NVM_LOG_RECONNECT:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_RECONNECT]);
            addr.ofst = EDF_ADDR(reconnect) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVT6_SIZE;
            max_entry = EVT6_RCD_NUM;
            rcd_len   = EVT6_RCD_SIZE;
            break;
#endif
#if EVENT_PWR_DOWN_EN             
        case NVM_LOG_PWR_DOWN:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_PWR_DOWN]);
            addr.ofst = EDF_ADDR(pwr_down) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_PWR_DOWN_SIZE;
            max_entry = EVT_RCD_PWRDN_NUM;
            rcd_len   = EVT_RCD_PWRDN_SIZE;
            break;
#endif
#if EVENT_SHITFT_TIME_EN    
        case NVM_LOG_SHITFT_TIME:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_SHITFT_TIME]);
            addr.ofst = EDF_ADDR(shift_time) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_ADJTM_SIZE;
            max_entry = EVT_RCD_ADJTM_NUM;
            rcd_len   = EVT_RCD_ADJTM_SIZE;
            break;
#endif
#if EVENT_BC_TIME_EN    
        case NVM_LOG_BC_TIME:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_BC_TIME]);
            addr.ofst = EDF_ADDR(bc_time) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_BC_SIZE;
            max_entry = EVT_RCD_BC_NUM;
            rcd_len   = EVT_RCD_BC_SIZE;
            break;
#endif
#if EVENT_METER_CLEAN_EN                      
        case NVM_LOG_METER_CLEAN:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_METER_CLEAN]);
            addr.ofst = EDF_ADDR(meter_clean) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_METER_CLEAN_SIZE;
            max_entry = EVT_RCD_MTRCL_NUM;
            rcd_len   = EVT_RCD_MTRCL_SIZE;
            break;
#endif
#if EVENT_DEMAND_CLEAN_EN                       
        case NVM_LOG_DEMAND_CLEAN:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_DEMAND_CLEAN]);
            addr.ofst = EDF_ADDR(demand_clean) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_MD_CLEAN_SIZE;
            max_entry = EVT_RCD_MDCL_NUM;
            rcd_len   = EVT_RCD_MDCL_SIZE;
            break;
#endif
#if EVENT_EVENT_CLEAN_EN                        
        case NVM_LOG_EVENT_CLEAN:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_EVENT_CLEAN]);
            addr.ofst = EDF_ADDR(event_clean) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_EVENT_CLEAN_SIZE;
            max_entry = EVT_RCD_EVTC_NUM;
            rcd_len   = EVT_RCD_EVTC_SIZE;
            break;
#endif
#if EVENT_METER_COVER_EN     
        case NVM_LOG_METER_COVER:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_METER_COVER]);
            addr.ofst = EDF_ADDR(meter_cover) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_METER_COVER_SIZE;
            max_entry = EVT_RCD_MTRCV_NUM;
            rcd_len   = EVT_RCD_MTRCV_SIZE;
            break;
#endif
#if EVENT_TEM_COVER_EN    
        case NVM_LOG_TEM_COVER:
            addr.hdr  = EEP_ADDR(log_header.event[EVENT_TYPE_TEM_COVER]);
            addr.ofst = EDF_ADDR(tem_cover) + TAG_DF_ROLL_ACCESS;
            total_size= STORE_TEM_COVER_SIZE;    
            max_entry = EVT_RCD_TEMCV_NUM;
            rcd_len   = EVT_RCD_TEMCV_SIZE;
            break;
#endif
    }
    
    ///存储在外部flash中的记录因为预留了一个sector，需要计算最大记录数和最大有效记录数
    {
        uint32_t num;  
        if(rcd_len == 0 || rcd_len > 512)
        {
            addr.size = 0;
            addr.num = 1;
            addr.max = 1;
        }
        else
        {
            addr.size = rcd_len;
            num = total_size / rcd_len;
            if(num > max_entry) addr.num = max_entry;
            else addr.num = num;
            addr.max = (total_size + DATAFLASH_PAGE_LEN) / rcd_len;
        }
    }

    return addr;
}

/// @brief 读取数据
bool nvm_data_read(uint32_t addr, void *data, uint32_t len)
{
	uint8_t type = addr >> 30;
	addr &= 0x3FFFFFFF;
	switch(type)
	{
	#if USE_EEPROM
		case EXTEE: return eeprom.read(addr, data, len);
	#endif
	#if USE_DATAFLASH
		case EXTDF: return (extflash.read(addr, data, len) && (!hal_mcu.pwrdn_query()));
	#endif
		case MCUEE: return mcu_flash_r(addr, data, len);// 
        // case MCUEE: return hal_flash.read(addr, data, len);
		default: return FALSE;
	}   
}

/// @brief 写入数据,注意外部FLASH如果发生擦除，只回写sector前面部分数据，后面部分数据丢失.
///        所以曲线类数据要多预留一个sector，非曲线类数据需secotr对齐.
/// @param addr 地址
/// @param data 数据
/// @param len 长度
bool nvm_data_write(uint32_t addr, const void *data, uint32_t len)
{
	uint8_t type = addr >> 30;
	addr &= 0x3FFFFFFF;
	switch(type)
	{
	#if USE_EEPROM
		case EXTEE: return eeprom.write(addr, data, len);
	#endif
	#if USE_DATAFLASH
		case EXTDF: return extflash.write(addr, data, len); // 注意地址为非扇区对齐时，如果发生擦除，只回写前面部分数据，后面部分数据丢失
	#endif
		// case MCUEE: return hal_flash.write(addr, data, len);
        case MCUEE: return mcu_flash_w(addr, data, len);
		default: return FALSE;
	}
}


bool nvm_data_clean(NVM_TYPE type)
{
	switch(type)
	{
	#if USE_EEPROM
		case EXTEE: 
        {
            bool ret = nvm_data_write(EXTEE_BASE_ADDR, NULL, sizeof(EepromSpase_t));
            ret = nvm_data_write(nvm_addr(NVM_EE_REV), eeprom_test, TEST_STR_LEN) ? ret : false;
            return ret;
        }
	#endif
	#if USE_DATAFLASH
		case EXTDF: return nvm_data_write(EXTDF_BASE_ADDR, NULL, sizeof(ExtFlashSpase_t));
	#endif
		case MCUEE: return true;//mcu_flash_w(addr, data, len); hal_flash.write(MCUEE_BASE_ADDR, NULL, sizeof(McuEESpase_t));
		default: return FALSE;
	}       
}

uint8_t eeprom_check(void)
{
    uint8_t buf[TEST_STR_LEN];
    uint8_t buf2[TEST_STR_LEN];
    uint8_t i;
    
    for(i = 0; i < 3; i++)
    {
        if(hal_mcu.pwrdn_query()) return 1;
        if(nvm_data_read(nvm_addr(NVM_EE_REV), buf, TEST_STR_LEN))
        {    
            if(!nvm_data_read(nvm_addr(NVM_EE_REV), buf2, TEST_STR_LEN)) continue;
            if(memcmp(buf, buf2, TEST_STR_LEN) != 0) continue;       
            if(memcmp(buf, eeprom_test, TEST_STR_LEN) != 0) 
            {
                if(!nvm_data_write(nvm_addr(NVM_EE_REV), eeprom_test, TEST_STR_LEN)) nvm_data_write(nvm_addr(NVM_EE_REV), eeprom_test, TEST_STR_LEN);
                return 2; //新EEPROM
            }
            return 1; //eeprom正常
        }
    }
    return 0; //eeprom异常
}

uint8_t nvm_check(uint32_t addr)
{
	uint8_t type = addr >> 30;
	addr &= 0x3FFFFFFF;

	switch(type)
	{
	#if USE_EEPROM
		case EXTEE: 
        {
            DBG_PRINTF(P_NVM, D, "\r\n eeprom use space:%d. \r\n eeprom remain space:%d", sizeof(EepromSpase_t) + NVM_EXTEE_RESVER_BTYES, 
                       EE_SIZE - sizeof(EepromSpase_t) - NVM_EXTEE_RESVER_BTYES);
            return eeprom_check();
        }
	#endif
	#if USE_DATAFLASH
		case EXTDF: 
        { 
            DBG_PRINTF(P_NVM, D, "\r\n extflash use space:%d. \r\n extflash remain space:%d", sizeof(ExtFlashSpase_t) , DATAFLASH_SIZE - sizeof(ExtFlashSpase_t));
            return extflash.check();
        }
    #endif
		case MCUEE:  
        {
            DBG_PRINTF(P_NVM, D, "\r\n mcu use space:%d. \r\n mcu remain space:%d", sizeof(McuEESpase_t) , MCU_FLASH_DATA_SIZE - sizeof(McuEESpase_t));
            return TRUE;
        }
        default: return FALSE;
	}      
}

/// @brief 声明nvm对象
const struct nvm_s nvm =
{
    .read       = nvm_data_read,
    .write      = nvm_data_write,
    .clean      = nvm_data_clean,
    .check      = nvm_check,
};
