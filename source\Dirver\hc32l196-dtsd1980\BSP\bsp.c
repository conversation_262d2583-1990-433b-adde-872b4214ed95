/*****************************************************************************************
*    (c) Copyright 2024, Jishe Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      ../BSP/bsp.c
*    Device:
*    Compiler:
*    Describe:      本文件主要包括底层的硬件驱动初始化调用。
*
*    Created on:    2024
*    Modify record:
*
*****************************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "bsp.h"
#include "key.h"
#include "beep.h"
#include "led.h"
#include "relay.h"
#include "lcd.h"
#include "hal_timer.h"
#include "mic.h"

static BSP_STUS     bsp_ini_stus;
static WAKEUP_STUS  wakeup_stus;

/* Private functions ---------------------------------------------------------*/
/*
*   Action:    系统节拍任务处理, 节拍中断调用
*   Input:     no
*   Output:    no
*/
static void syatick_proc(void)
{
    /* 串口超时扫描 */
    hal_uart.timer();
    __NOP();
}

static void wakeup_second_isr(void)
{
    wakeup_stus |= SECOND_WAKEUP;
}

static void wakeup_top_cov_wakeup(void)
{
#if USE_FCOVER
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_FCOVER_OPEN())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_FCOVER_OPEN())  return;
    wakeup_stus |= TOP_COV_WAKEUP;
#endif
}
static void wakeup_bot_cov_wakeup(void)
{
#if USE_TCOVER
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_TCOVER_OPEN())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_TCOVER_OPEN())  return;
    wakeup_stus |= BOT_COV_WAKEUP;
#endif
}
static void wakeup_disp_up_btn_wakeup(void)
{
#if USE_BTN_DISP_UP
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_DISP_UP_PRESS())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_DISP_UP_PRESS())  return;
    wakeup_stus |= KEY_DISP_UP_WAKEUP;
#endif
}

static void wakeup_disp_dn_btn_wakeup(void)
{
#if USE_BTN_DISP_DN
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_DISP_DN_PRESS())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_DISP_DN_PRESS())  return;
    wakeup_stus |= KEY_DISP_DN_WAKEUP;
#endif
}

static void wakeup_back_btn_wakeup(void)
{
#if USE_BTN_BACK
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_BACK_PRESS())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_BACK_PRESS())  return;
    wakeup_stus |= KEY_BACK_WAKEUP;
#endif
}

static void wakeup_enter_btn_wakeup(void)
{
#if USE_BTN_ENTER
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_ENTER_PRESS())  return;
    hal_mcu.wait_us(1);
    if(!IS_IO_BTN_ENTER_PRESS())  return;
    wakeup_stus |= KEY_ENTER_WAKEUP;
#endif
}


/* Public functions ----------------------------------------------------------*/
/// @brief 底层硬件驱动初始化
/// @param  
void bsp_init(void)
{
    McuCoreStus_s core_stus;
    HAL_RTC_STUS rtc_stus;

    HAL_WDG_START();
    HAL_WDG_RESET();
    HAL_DISABLE_INTERRUPTS(); // 关闭所有中断

    /* 首先执行内核初始化，根据返回状态决定进行正常供电下的初始化还是辅助电源供电下的初始化 */
    core_stus = hal_mcu.init();
    if(core_stus.abnormal_rst)   bsp_ini_stus |= STUS_BSP_WDG_RST;
    if(core_stus.clk_src_extosc) bsp_ini_stus |= STUS_BSP_CLK_HSE;
    if(core_stus.por_bor_rst)    bsp_ini_stus |= STUS_BSP_POR_BOR;
    if(core_stus.power_on_rst)
    {
        bsp_ini_stus |= STUS_BSP_PWR_ON;

        /* 正常供电下的HAL层初始化 */
        hal_adc.close(); //如果使用到ADC，需要先关闭ADC，再初始化IO，再打开ADC,HT6025特定
        hal_gpio.init();
        hal_gpio.data_flash(GPIO_OPEN);
    #if USE_SAG_TYPE == SAG_PIN
        hal_mcu.is_sag_callback(is_power_off);
    #endif
        hal_adc.open();
    #if USE_DATAFLASH
        hal_spi_open(COM_DATAFLASH, 2000);
    #endif
    #if EMU_HT7136
        hal_spi_open(COM_MEASURE_IC, 2000);
    #endif

        rtc_stus = hal_rtc.init();
        hal_rtc.irq_set(SEC_ALARM, NULL); // RTC秒中断使能
        if(rtc_stus.rtc_clksrc == 0) bsp_ini_stus |= STUS_BSP_RTC_LSE;
        if(rtc_stus.rtc_init)        bsp_ini_stus |= STUS_BSP_RTC_RST;
        if(rtc_stus.rtc_ppm_err)     bsp_ini_stus |= STUS_BSP_PPM_ERR;

        /* 正常供电下的BSP层初始化 */
        print_open();
    #if USE_LCD
        lcd.ctrl(LCD_PWRON_OPEN);
    #endif
    #if USE_BTN_DISP_UP||USE_BTN_DISP_DN||USE_FCOVER||USE_TCOVER||USE_BTN_BACK||USE_BTN_ENTER
        key.init();
    #endif
    #if USE_RLY
        relay.init();
    #endif
        mic.init(0);
    #if USE_LED_BACK||USE_LED_RELAY
        led.init();
    #endif
    #if USE_BUZZER
        beep.init();
    #endif

        /* 启动系统节拍定时器 */
        hal_timer.systick_start(syatick_proc); // 设置系统节拍中断调用的函数指针
    }
    else
    {
        /* 辅助电源供电下的初始化 */
        hal_adc.close_nopower();
        hal_gpio.init_nopower();

    // #if USE_EEPROM
    //     halI2cOpen(COM_EEPROM);
    // #endif
        hal_rtc.init_nopower();
        hal_rtc.irq_set(SEC_ALARM, wakeup_second_isr); // RTC秒中断使能用于唤醒
        #if USE_BTN_DISP_UP||USE_BTN_DISP_DN||USE_FCOVER||USE_TCOVER||USE_BTN_BACK||USE_BTN_ENTER
        key.init();
        #endif
        mic.init(1); // 掉电初始化计量参数，如变比

        /* 设置按键中断唤醒服务函数 */

    #if defined(EXTI_PIN_FCOVER)
        hal_gpio.exti_set(EXTI_PIN_FCOVER, wakeup_top_cov_wakeup);
    #endif
    #if defined(EXTI_PIN_TCOVER)
        hal_gpio.exti_set(EXTI_PIN_TCOVER, wakeup_bot_cov_wakeup);
    #endif
    #if defined(EXTI_PIN_KEY_DN_BTN)
        hal_gpio.exti_set(EXTI_PIN_KEY_DN_BTN, wakeup_disp_dn_btn_wakeup);
    #endif
    #if defined(EXTI_PIN_KEY_UP_BTN)
        hal_gpio.exti_set(EXTI_PIN_KEY_UP_BTN, wakeup_disp_up_btn_wakeup);
    #endif
    #if defined(EXTI_PIN_KEY_BACK_BTN)
        hal_gpio.exti_set(EXTI_PIN_KEY_BACK_BTN, wakeup_disp_dn_btn_wakeup);
    #endif
    #if defined(EXTI_PIN_KEY_ENTER_BTN)
        hal_gpio.exti_set(EXTI_PIN_KEY_ENTER_BTN, wakeup_disp_up_btn_wakeup);
    #endif
    }
}

/// @brief 系统重启
/// @param is_wait  0：正常重启  1.掉电后重启
void bsp_restart(bool is_wait)
{
    HAL_DISABLE_INTERRUPTS();
    if(is_wait)
    {
        uint16_t cnt = 4;  ///如果掉电检测电压下降慢，适当调整该值
        while(hal_mcu.pwron_query() && cnt != 0) // 等待放电，使SVS降下来, 防止掉电后再次上电
        {
            hal_mcu.wait_us(2000);
            cnt--;
        }
    }
    HAL_SYSTEM_RESET();
}

/// @brief 检测系统供电状态
/// @param mode 0-电池供电模式  1-AC供电模式
/// @return 0-掉电状态  1-上电状态
bool bsp_montior(uint8_t mode)
{
    HAL_WDG_RESET();
    hal_rtc.cali_reg_wr(hal_rtc.ppm_rd());  // 温度补偿
    if(mode == 1)
    {
        HAL_ENABLE_INTERRUPTS(); // 始终打开中断
        if(!hal_mcu.pwrdn_query()) return TRUE;
        HAL_DISABLE_INTERRUPTS();

        /* 以下掉电前需紧急关闭的一些硬件外设 */
    #if USE_LED_BACK
        LED_BACK_OFF();
    #endif
    #if USE_HDW_RST_MIC&&(!USE_EMU_AT_LOSS_VOLTAGE) 
        IO_EMU_PWR_OFF();
    #endif
         IO_MOD_RST_RST(); // 复位通讯模块
         IO_MOD_SLEEP();   // 
         LED_ACTEGY_PULSE_OFF();
         LED_REAEGY_PULSE_OFF();
    #if USE_LED_RELAY
        LED_RELAY_OFF();
    #endif
    #if USE_BUZZER
        beep.stop();
    #endif
    #if USE_LCD
        lcd.ctrl(LCD_CLOSE);
    #endif
    }
    else
    {
        /* 低功耗睡眠 */
        STOP();
        
        ///唤醒执行低功耗下任务，此处可加底层任务，应用层不在此处处理
        ///
    }

    return FALSE;
}

/// @brief 查询系统状态
bool bsp_state_query(BSP_STUS mask)
{
    return boolof(bsp_ini_stus & mask);
}

/// @brief 查询唤醒源状态
bool bsp_wakeup_state_query(WAKEUP_STUS mask)
{
    return boolof(wakeup_stus & mask);
}

/// @brief 唤醒后，任务初始化
/// @param mask 唤醒源
void bsp_wakeup_work_init(WAKEUP_STUS mask)
{
    wakeup_stus |= mask;
}

/// @brief 唤醒后，任务关闭
/// @param mask 要关闭的唤醒任务
void bsp_wakeup_work_close(WAKEUP_STUS mask)
{
    wakeup_stus &= ~mask;
}
#if USE_MAG_DST
bool bsp_magnetic_state(void)
{
    return IS_IO_MG_ACT();
}
#endif

/// @brief 关闭模块复位
/// @param mod 0-外部模块GPRS/PLC，1-内部模块-BLE
void bsp_module_on(MODULE_TYPE mod)
{
    if(mod == EXT_MOD)
    {
        IO_MOD_RST_SET();
    }
    else if(mod == INT_MOD)
    {
        IO_INT_MOD_RST_SET();
    }
}

/// @brief 使能模块复位
/// @param mod 0-外部模块GPRS/PLC，1-内部模块-BLE
void bsp_module_off(MODULE_TYPE mod)
{
    if(mod == EXT_MOD)
    {
        IO_MOD_RST_RST();
    }
    else if(mod == INT_MOD)
    {
        IO_INT_MOD_RST_RST();
    }
}

/// @brief 远程模块控制
/// @param typ 0 供电 1-复位 2-开机
/// @param mode 0-起始/关闭 1-结束/开启
void bsp_remote_module(uint8_t typ, uint8_t mode)
{
    if(typ == 0) // 供电
    {
        mode ? IO_4G_POWER_ON() : IO_4G_POWER_OFF();
    }
    else if(typ == 1) // 复位
    {
        mode ? IO_4G_RST_END() : IO_4G_RST_START();
    }
    else  // 开机
    {
        mode ? IO_4G_PWRKEY_END() : IO_4G_PWRKEY_START();
    }
}

/// @brief 模块电源开启
/// @param 
void bsp_module_run(void)
{
    IO_MOD_RUNNING();
}
/// @brief 模块电源关闭
/// @param  
void bsp_module_sleep(void)
{
    IO_MOD_SLEEP();
}
/// @brief 模块电源上电
/// @param  
void bsp_pow3v3_on(void)
{
    // IO_POW3V3_ON();
}
/// @brief 模块电源掉电
/// @param  
void bsp_pow3v3_off(void)
{
    // IO_POW3V3_OFF();
}

/// @brief 声明bsp子模块对象
const struct bsp_t bsp =
{
    .init               = bsp_init,
    .monitor            = bsp_montior,
    .restart            = bsp_restart,
    .state_query        = bsp_state_query,
    .wakeup_init        = bsp_wakeup_work_init,
    .wakeup_state       = bsp_wakeup_state_query,
    .wakeup_close       = bsp_wakeup_work_close,
#if USE_MAG_DST
    .magnetic_state     = bsp_magnetic_state,
#endif
    .module_on          = bsp_module_on,
    .module_off         = bsp_module_off,
    .remote_module      = bsp_remote_module,
    .pow3v3_on          = bsp_pow3v3_on,
    .pow3v3_off         = bsp_pow3v3_off,
};
