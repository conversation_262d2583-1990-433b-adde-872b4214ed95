/********************************************************************************
* @file    profile_capture_obj.h
* <AUTHOR> @date    2024
* @brief   事件，曲线，冻结捕获对象定义
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __PROFILE_CAPTURE_OBJ_H__
#define __PROFILE_CAPTURE_OBJ_H__

#include "typedef.h"


/// @brief 失压，过压，欠压，断相捕获对象结构体定义
typedef struct {
    uint8_t s_time     [6];     /// 6 发生时刻
    uint8_t s_0pkWh    [4];     /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];     /// 4 发生时刻反向有功总电能
    uint8_t s_0pkvarh  [4];     /// 4 发生时刻组合无功 1 总电能
    uint8_t s_0nkvarh  [4];     /// 4 发生时刻组合无功 2 总电能
    uint8_t s_1pkWh    [4];     /// 4 发生时刻 A 相正向有功电能
    uint8_t s_1nkWh    [4];     /// 4 发生时刻 A 相反向有功电能
    uint8_t s_1pkvarh  [4];     /// 4 发生时刻 A 相组合无功 1 电能
    uint8_t s_1nkvarh  [4];     /// 4 发生时刻 A 相组合无功 2 电能
    uint8_t s_1v       [2];     /// 2 发生时刻 A 相电压
    uint8_t s_1i       [3];     /// 3 发生时刻 A 相电流
    uint8_t s_1kW4     [3];     /// 3 发生时刻 A 相有功功率
    uint8_t s_1kvar    [3];     /// 3 发生时刻 A 相无功功率
    uint8_t s_1pf      [2];     /// 2 发生时刻 A 相功率因数
    uint8_t s_2pkWh    [4];     /// 4 发生时刻 B 相正向有功电能
    uint8_t s_2nkWh    [4];     /// 4 发生时刻 B 相反向有功电能
    uint8_t s_2pkvarh  [4];     /// 4 发生时刻 B 相组合无功 1 电能
    uint8_t s_2nkvarh  [4];     /// 4 发生时刻 B 相组合无功 2 电能
    uint8_t s_2v       [2];     /// 2 发生时刻 B 相电压
    uint8_t s_2i       [3];     /// 3 发生时刻 B 相电流
    uint8_t s_2kW      [3];     /// 3 发生时刻 B 相有功功率
    uint8_t s_2kvar    [3];     /// 3 发生时刻 B 相无功功率
    uint8_t s_2pf      [2];     /// 2 发生时刻 B 相功率因数
    uint8_t s_3pkWh    [4];     /// 4 发生时刻 C 相正向有功电能
    uint8_t s_3nkWh    [4];     /// 4 发生时刻 C 相反向有功电能
    uint8_t s_3pkvarh  [4];     /// 4 发生时刻 C 相组合无功 1 电能
    uint8_t s_3nkvarh  [4];     /// 4 发生时刻 C 相组合无功 2 电能
    uint8_t s_3v       [2];     /// 2 发生时刻 C 相电压
    uint8_t s_3i       [3];     /// 3 发生时刻 C 相电流
    uint8_t s_3kW      [3];     /// 3 发生时刻 C 相有功功率
    uint8_t s_3kvar    [3];     /// 3 发生时刻 C 相无功功率
    uint8_t s_3pf      [2];     /// 2 发生时刻 C 相功率因数

    uint8_t e_0ah      [4];     /// 4 期间总安时数
    uint8_t e_1ah      [4];     /// 4 期间 A 相安时数
    uint8_t e_2ah      [4];     /// 4 期间 B 相安时数
    uint8_t e_3ah      [4];     /// 4 期间 C 相安时数
    uint8_t e_time     [6];     /// 6 结束时刻
    uint8_t e_0pkWh    [4];     /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];     /// 4 结束时刻反向有功总电能
    uint8_t e_0pkvarh  [4];     /// 4 结束时刻组合无功 1 总电能
    uint8_t e_0nkvarh  [4];     /// 4 结束时刻组合无功 2 总电能
    uint8_t e_1pkWh    [4];     /// 4 结束时刻 A 相正向有功电能
    uint8_t e_1nkWh    [4];     /// 4 结束时刻 A 相反向有功电能
    uint8_t e_1pkvarh  [4];     /// 4 结束时刻 A 相组合无功 1 电能
    uint8_t e_1nkvarh  [4];     /// 4 结束时刻 A 相组合无功 2 电能
    uint8_t e_2pkWh    [4];     /// 4 结束时刻 B 相正向有功电能
    uint8_t e_2nkWh    [4];     /// 4 结束时刻 B 相反向有功电能
    uint8_t e_2pkvarh  [4];     /// 4 结束时刻 B 相组合无功 1 电能
    uint8_t e_2nkvarh  [4];     /// 4 结束时刻 B 相组合无功 2 电能
    uint8_t e_3pkWh    [4];     /// 4 结束时刻 C 相正向有功电能
    uint8_t e_3nkWh    [4];     /// 4 结束时刻 C 相反向有功电能
    uint8_t e_3pkvarh  [4];     /// 4 结束时刻 C 相组合无功 1 电能
    uint8_t e_3nkvarh  [4];     /// 4 结束时刻 C 相组合无功 2 电能
}evt1_s;


/// @brief 失流、过流、断流捕获对象结构体定义
typedef struct {
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_0pkvarh  [4];  /// 4 发生时刻组合无功 1 总电能
    uint8_t s_0nkvarh  [4];  /// 4 发生时刻组合无功 2 总电能
    uint8_t s_1pkWh    [4];  /// 4 发生时刻 A 相正向有功电能
    uint8_t s_1nkWh    [4];  /// 4 发生时刻 A 相反向有功电能
    uint8_t s_1pkvarh  [4];  /// 4 发生时刻 A 相组合无功 1 电能
    uint8_t s_1nkvarh  [4];  /// 4 发生时刻 A 相组合无功 2 电能
    uint8_t s_1v       [2];  /// 2 发生时刻 A 相电压
    uint8_t s_1i       [3];  /// 3 发生时刻 A 相电流
    uint8_t s_1kW      [3];  /// 3 发生时刻 A 相有功功率
    uint8_t s_1kvar    [3];  /// 3 发生时刻 A 相无功功率
    uint8_t s_1pf      [2];  /// 2 发生时刻 A 相功率因数
    uint8_t s_2pkWh    [4];  /// 4 发生时刻 B 相正向有功电能
    uint8_t s_2nkWh    [4];  /// 4 发生时刻 B 相反向有功电能
    uint8_t s_2pkvarh  [4];  /// 4 发生时刻 B 相组合无功 1 电能
    uint8_t s_2nkvarh  [4];  /// 4 发生时刻 B 相组合无功 2 电能
    uint8_t s_2v       [2];  /// 2 发生时刻 B 相电压
    uint8_t s_2i       [3];  /// 3 发生时刻 B 相电流
    uint8_t s_2kW      [3];  /// 3 发生时刻 B 相有功功率
    uint8_t s_2kvar    [3];  /// 3 发生时刻 B 相无功功率
    uint8_t s_2pf      [2];  /// 2 发生时刻 B 相功率因数
    uint8_t s_3pkWh    [4];  /// 4 发生时刻 C 相正向有功电能
    uint8_t s_3nkWh    [4];  /// 4 发生时刻 C 相反向有功电能
    uint8_t s_3pkvarh  [4];  /// 4 发生时刻 C 相组合无功 1 电能
    uint8_t s_3nkvarh  [4];  /// 4 发生时刻 C 相组合无功 2 电能
    uint8_t s_3v       [2];  /// 2 发生时刻 C 相电压
    uint8_t s_3i       [3];  /// 3 发生时刻 C 相电流
    uint8_t s_3kW      [3];  /// 3 发生时刻 C 相有功功率
    uint8_t s_3kvar    [3];  /// 3 发生时刻 C 相无功功率
    uint8_t s_3pf      [2];  /// 2 发生时刻 C 相功率因数

    uint8_t e_time     [6];  /// 6 结束时刻
    uint8_t e_0pkWh    [4];  /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];  /// 4 结束时刻反向有功总电能
    uint8_t e_0pkvarh  [4];  /// 4 结束时刻组合无功 1 总电能
    uint8_t e_0nkvarh  [4];  /// 4 结束时刻组合无功 2 总电能
    uint8_t e_1pkWh    [4];  /// 4 结束时刻 A 相正向有功电能
    uint8_t e_1nkWh    [4];  /// 4 结束时刻 A 相反向有功电能
    uint8_t e_1pkvarh  [4];  /// 4 结束时刻 A 相组合无功 1 电能
    uint8_t e_1nkvarh  [4];  /// 4 结束时刻 A 相组合无功 2 电能
    uint8_t e_2pkWh    [4];  /// 4 结束时刻 B 相正向有功电能
    uint8_t e_2nkWh    [4];  /// 4 结束时刻 B 相反向有功电能
    uint8_t e_2pkvarh  [4];  /// 4 结束时刻 B 相组合无功 1 电能
    uint8_t e_2nkvarh  [4];  /// 4 结束时刻 B 相组合无功 2 电能
    uint8_t e_3pkWh    [4];  /// 4 结束时刻 C 相正向有功电能
    uint8_t e_3nkWh    [4];  /// 4 结束时刻 C 相反向有功电能
    uint8_t e_3pkvarh  [4];  /// 4 结束时刻 C 相组合无功 1 电能
    uint8_t e_3nkvarh  [4];  /// 4 结束时刻 C 相组合无功 2 电能
}evt2_s;    

/// @brief 电压 电流逆相序, 潮流反向，过载捕获对象结构体定义
typedef struct 
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_0pkvarh  [4];  /// 4 发生时刻组合无功 1 总电能
    uint8_t s_0nkvarh  [4];  /// 4 发生时刻组合无功 2 总电能
    uint8_t s_1pkWh    [4];  /// 4 发生时刻 A 相正向有功电能
    uint8_t s_1nkWh    [4];  /// 4 发生时刻 A 相反向有功电能
    uint8_t s_1pkvarh  [4];  /// 4 发生时刻 A 相组合无功 1 电能
    uint8_t s_1nkvarh  [4];  /// 4 发生时刻 A 相组合无功 2 电能
    uint8_t s_2pkWh    [4];  /// 4 发生时刻 B 相正向有功电能
    uint8_t s_2nkWh    [4];  /// 4 发生时刻 B 相反向有功电能
    uint8_t s_2pkvarh  [4];  /// 4 发生时刻 B 相组合无功 1 电能
    uint8_t s_2nkvarh  [4];  /// 4 发生时刻 B 相组合无功 2 电能
    uint8_t s_3pkWh    [4];  /// 4 发生时刻 C 相正向有功电能
    uint8_t s_3nkWh    [4];  /// 4 发生时刻 C 相反向有功电能
    uint8_t s_3pkvarh  [4];  /// 4 发生时刻 C 相组合无功 1 电能
    uint8_t s_3nkvarh  [4];  /// 4 发生时刻 C 相组合无功 2 电能

    uint8_t e_time     [6];  /// 6 结束时刻
    uint8_t e_0pkWh    [4];  /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];  /// 4 结束时刻反向有功总电能
    uint8_t e_0pkvarh  [4];  /// 4 结束时刻组合无功 1 总电能
    uint8_t e_0nkvarh  [4];  /// 4 结束时刻组合无功 2 总电能
    uint8_t e_1pkWh    [4];  /// 4 结束时刻 A 相正向有功电能
    uint8_t e_1nkWh    [4];  /// 4 结束时刻 A 相反向有功电能
    uint8_t e_1pkvarh  [4];  /// 4 结束时刻 A 相组合无功 1 电能
    uint8_t e_1nkvarh  [4];  /// 4 结束时刻 A 相组合无功 2 电能
    uint8_t e_2pkWh    [4];  /// 4 结束时刻 B 相正向有功电能
    uint8_t e_2nkWh    [4];  /// 4 结束时刻 B 相反向有功电能
    uint8_t e_2pkvarh  [4];  /// 4 结束时刻 B 相组合无功 1 电能
    uint8_t e_2nkvarh  [4];  /// 4 结束时刻 B 相组合无功 2 电能
    uint8_t e_3pkWh    [4];  /// 4 结束时刻 C 相正向有功电能
    uint8_t e_3nkWh    [4];  /// 4 结束时刻 C 相反向有功电能
    uint8_t e_3pkvarh  [4];  /// 4 结束时刻 C 相组合无功 1 电能
    uint8_t e_3nkvarh  [4];  /// 4 结束时刻 C 相组合无功 2 电能 
}evt3_s;

/// @brief 电压电流不平衡捕获对象结构体定义
typedef struct 
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_0pkvarh  [4];  /// 4 发生时刻组合无功 1 总电能
    uint8_t s_0nkvarh  [4];  /// 4 发生时刻组合无功 2 总电能
    uint8_t s_1pkWh    [4];  /// 4 发生时刻 A 相正向有功电能
    uint8_t s_1nkWh    [4];  /// 4 发生时刻 A 相反向有功电能
    uint8_t s_1pkvarh  [4];  /// 4 发生时刻 A 相组合无功 1 电能
    uint8_t s_1nkvarh  [4];  /// 4 发生时刻 A 相组合无功 2 电能
    uint8_t s_2pkWh    [4];  /// 4 发生时刻 B 相正向有功电能
    uint8_t s_2nkWh    [4];  /// 4 发生时刻 B 相反向有功电能
    uint8_t s_2pkvarh  [4];  /// 4 发生时刻 B 相组合无功 1 电能
    uint8_t s_2nkvarh  [4];  /// 4 发生时刻 B 相组合无功 2 电能
    uint8_t s_3pkWh    [4];  /// 4 发生时刻 C 相正向有功电能
    uint8_t s_3nkWh    [4];  /// 4 发生时刻 C 相反向有功电能
    uint8_t s_3pkvarh  [4];  /// 4 发生时刻 C 相组合无功 1 电能
    uint8_t s_3nkvarh  [4];  /// 4 发生时刻 C 相组合无功 2 电能

    uint8_t e_max_per  [3];  /// 3 最大不平衡率 
    uint8_t e_time     [6];  /// 6 结束时刻
    uint8_t e_0pkWh    [4];  /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];  /// 4 结束时刻反向有功总电能
    uint8_t e_0pkvarh  [4];  /// 4 结束时刻组合无功 1 总电能
    uint8_t e_0nkvarh  [4];  /// 4 结束时刻组合无功 2 总电能
    uint8_t e_1pkWh    [4];  /// 4 结束时刻 A 相正向有功电能
    uint8_t e_1nkWh    [4];  /// 4 结束时刻 A 相反向有功电能
    uint8_t e_1pkvarh  [4];  /// 4 结束时刻 A 相组合无功 1 电能
    uint8_t e_1nkvarh  [4];  /// 4 结束时刻 A 相组合无功 2 电能
    uint8_t e_2pkWh    [4];  /// 4 结束时刻 B 相正向有功电能
    uint8_t e_2nkWh    [4];  /// 4 结束时刻 B 相反向有功电能
    uint8_t e_2pkvarh  [4];  /// 4 结束时刻 B 相组合无功 1 电能
    uint8_t e_2nkvarh  [4];  /// 4 结束时刻 B 相组合无功 2 电能
    uint8_t e_3pkWh    [4];  /// 4 结束时刻 C 相正向有功电能
    uint8_t e_3nkWh    [4];  /// 4 结束时刻 C 相反向有功电能
    uint8_t e_3pkvarh  [4];  /// 4 结束时刻 C 相组合无功 1 电能
    uint8_t e_3nkvarh  [4];  /// 4 结束时刻 C 相组合无功 2 电能 
}evt4_s;


/// @brief 低功率因素捕获对象结构体定义
typedef struct 
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_0pkvarh  [4];  /// 4 发生时刻组合无功 1 总电能
    uint8_t s_0nkvarh  [4];  /// 4 发生时刻组合无功 2 总电能

    uint8_t e_time     [6];  /// 6 结束时刻
    uint8_t e_0pkWh    [4];  /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];  /// 4 结束时刻反向有功总电能
    uint8_t e_0pkvarh  [4];  /// 4 结束时刻组合无功 1 总电能
    uint8_t e_0nkvarh  [4];  /// 4 结束时刻组合无功 2 总电能

}evt5_s;

/// @brief 拉闸合闸捕获对象结构体定义
typedef struct 
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t opt_code   [4];  /// 4 操作者代码
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_q1_kvarh [4];  /// 4 发生时刻 Q1 无功电能
    uint8_t s_q2_kvarh [4];  /// 4 发生时刻 Q2 无功电能
    uint8_t s_q3_kvarh [4];  /// 4 发生时刻 Q3 无功电能
    uint8_t s_q4_kvarh [4];  /// 4 发生时刻 Q4 无功电能
}evt6_s;

/// @brief 全失压捕获对象结构体定义
typedef struct
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t s_i        [3];  /// 3 发生时刻电流
    uint8_t e_time     [6];  /// 6 结束时刻
}evt7_s;

/// @brief 掉电捕获对象结构体定义
typedef struct
{
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t e_time     [6];  /// 6 结束时刻
}evt8_s;

/// @brief 开盖事件捕获对象结构体定义
typedef struct {
    uint8_t s_time     [6];  /// 6 发生时刻
    uint8_t e_time     [6];  /// 6 结束时刻
    uint8_t s_0pkWh    [4];  /// 4 发生时刻正向有功总电能
    uint8_t s_0nkWh    [4];  /// 4 发生时刻反向有功总电能
    uint8_t s_q1_kvarh [4];  /// 4 发生时刻 Q1 无功电能
    uint8_t s_q2_kvarh [4];  /// 4 发生时刻 Q2 无功电能
    uint8_t s_q3_kvarh [4];  /// 4 发生时刻 Q3 无功电能
    uint8_t s_q4_kvarh [4];  /// 4 发生时刻 Q4 无功电能
    
    uint8_t e_0pkWh    [4];  /// 4 结束时刻正向有功总电能
    uint8_t e_0nkWh    [4];  /// 4 结束时刻反向有功总电能
    uint8_t e_q1_kvarh [4];  /// 4 结束时刻 Q1 无功电能
    uint8_t e_q2_kvarh [4];  /// 4 结束时刻 Q2 无功电能
    uint8_t e_q3_kvarh [4];  /// 4 结束时刻 Q3 无功电能
    uint8_t e_q4_kvarh [4];  /// 4 结束时刻 Q4 无功电能
}evt_cov_s;


/// @brief 显示列表对象声明
extern const uint32_t disp_list1_id[];
extern const uint8_t  disp_list1_id_num;
extern const uint32_t disp_list2_id[];
extern const uint8_t  disp_list2_id_num;
extern const uint32_t disp_list3_id[];
extern const uint8_t  disp_list3_id_num;


///分配空间要大于等于实际空间，使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048

/// @brief 事件1(失压，欠压，过压，断相)捕获对象列表声明
extern const uint32_t evt1_str_objlist[];
extern const uint8_t  evt1_str_objlist_num;
extern const uint32_t evt1_end_objlist[];
extern const uint8_t  evt1_end_objlist_num;
#define EVT1_RCD_SIZE 256   //分配空间大于等于实际空间,使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048
#define EVT1_RCD_NUM  10    //最大记录数
/// @失流、过流、断流捕获对象结构体定义
#define EVT2_RCD_SIZE 256   //
#define EVT2_RCD_NUM  10    //最大记录数
/// @brief 电压 电流逆相序, 潮流反向，过载
#define EVT3_RCD_SIZE 256   //
#define EVT3_RCD_NUM  10    //最大记录数
/// @brief 电压 电流不平衡
#define EVT4_RCD_SIZE 256   //
#define EVT4_RCD_NUM  10    //最大记录数
/// @brief 低功率因素
#define EVT5_RCD_SIZE 64    //
#define EVT5_RCD_NUM  10    //最大记录数
/// @brief 拉闸 合闸
#define EVT6_RCD_SIZE 64    //
#define EVT6_RCD_NUM  10    //最大记录数
/// @brief 全失压
#define EVT7_RCD_SIZE 16    //
#define EVT7_RCD_NUM  10    //最大记录数

extern const uint32_t evt_pwr_down_objlist[];
extern const uint8_t  evt_pwr_down_objlist_num;
#define EVT_RCD_PWRDN_SIZE 16    //分配空间大于等于实际空间,使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048
#define EVT_RCD_PWRDN_NUM  10    //最大记录数

extern const uint32_t evt_ovr_dm_pos_kW_objlist[];
extern const uint8_t  evt_ovr_dm_pos_kW_objlist_num;

extern const uint32_t evt_program_objlist[];
extern const uint8_t  evt_program_objlist_num;

extern const uint32_t evt_meter_clean_objlist[];
extern const uint8_t  evt_meter_clean_objlist_num;

extern const uint32_t evt_demand_clean_objlist[];
extern const uint8_t  evt_demand_clean_objlist_num;

extern const uint32_t evt_event_clean_objlist[];
extern const uint8_t  evt_event_clean_objlist_num;

extern const uint32_t evt_shift_time_objlist[];
extern const uint8_t  evt_shift_time_objlist_num;
#define EVT_RCD_ADJTM_SIZE 16   //分配空间大于等于实际空间,使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048
#define EVT_RCD_ADJTM_NUM  10   //最大记录数

extern const uint32_t evt_bc_time_objlist[];
extern const uint8_t  evt_bc_time_objlist_num;
#define EVT_RCD_BC_SIZE 16      //分配空间大于等于实际空间
#define EVT_RCD_BC_NUM  100     //最大记录数

extern const uint32_t evt_schedule_objlist[];
extern const uint8_t  evt_schedule_objlist_num;

extern const uint32_t evt_zone_tab_objlist[];
extern const uint8_t  evt_zone_tab_objlist_num;

extern const uint32_t evt_weekends_pgm_objlist[];
extern const uint8_t  evt_weekends_pgm_objlist_num;

extern const uint32_t evt_holiday_pgm_objlist[];
extern const uint8_t  evt_holiday_pgm_objlist_num;

extern const uint32_t evt_comb_kWh_pgm_objlist[];
extern const uint8_t  evt_comb_kWh_pgm_objlist_num;

extern const uint32_t evt_comb1_kvarh_pgm_objlist[];
extern const uint8_t  evt_comb1_kvarh_pgm_objlist_num;

extern const uint32_t evt_comb2_kvarh_pgm_objlist[];
extern const uint8_t  evt_comb2_kvarh_pgm_objlist_num;

extern const uint32_t evt_bl_day_pgm_objlist[];
extern const uint8_t  evt_bl_day_pgm_objlist_num;

///电表清零
#define EVT_RCD_MTRCL_SIZE  128
#define EVT_RCD_MTRCL_NUM   10
///需量清零
#define EVT_RCD_MDCL_SIZE   256
#define EVT_RCD_MDCL_NUM    10
///事件清零
#define EVT_RCD_EVTC_SIZE   16
#define EVT_RCD_EVTC_NUM    10


extern const uint32_t evt_meter_cover_objlist[];
extern const uint8_t  evt_meter_cover_objlist_num;
#define EVT_RCD_MTRCV_SIZE 64   //分配空间大于等于实际空间,使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048
#define EVT_RCD_MTRCV_NUM  10   //最大记录数

extern const uint32_t evt_tem_cover_objlist[];
extern const uint8_t  evt_tem_cover_objlist_num;
#define EVT_RCD_TEMCV_SIZE 64   //分配空间大于等于实际空间,使逻辑页对齐，2，4，8，16，32，64，128，256，512，1024，2048
#define EVT_RCD_TEMCV_NUM  10   //最大记录数


#define LC1_RCD_SIZE       128
#define LC2_RCD_SIZE       256

#endif //__PROFILE_CAPTURE_OBJ_H__

