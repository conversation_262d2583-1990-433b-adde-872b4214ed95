/********************************************************************************
 * @file    loadcurve.h
 * <AUTHOR> @date    2024
 * @brief   负荷记录
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __LOADCURVE_H
#define __LOADCURVE_H
#include "app.h"
#include "typedef.h"
#include "app_config.h"
#include "timeApp.h"
#include "mic.h"

#define LC_ENG_POS_ACT TYPE_ENERGY_POS_ACT
#define LC_ENG_NEG_ACT TYPE_ENERGY_NEG_ACT
#define LC_ENG_CM1_REA TYPE_ENERGY_POS_REA
#define LC_ENG_CM2_REA TYPE_ENERGY_NEG_REA

typedef enum
{
    TYPE_LC1,    // 负荷曲线(block load profile)
#if LC2_ENABLE
    TYPE_LC2,    // 负荷曲线(daily load profile)
#endif
    LC_TYPE_NUM
} LC_TYPE_t;

typedef union
{
    struct
    {
        uint8_t ins : 1;         // 电压电流频率
        uint8_t pwr : 1;         // 功率
        uint8_t pf : 1;          // 功率因数
        uint8_t eng : 1;         // 有功无功电能
        uint8_t quadrant : 1;    // 四象限电能
        uint8_t md : 1;          // 需量
    };
    uint8_t value;
} lc_mode_s;

typedef struct
{
    uint16_t  crc;
    uint16_t  chk;
    uint32_t  period[LC_TYPE_NUM];    // 捕获周期, 单位秒
    clock_s   start_time;             // 负荷记录开始时间
    lc_mode_s mode;                   // 负荷记录模式
} lc_para_s;

typedef union
{
    struct
    {
        uint8_t circal_error : 1;     // 严重错误
        uint8_t clock_invalid : 1;    // 时钟无效
        uint8_t data_invalid : 1;     // 数据无效
        uint8_t clock_adjust : 1;     // 时钟调整(超过同步范围)
        uint8_t buffer_clr : 1;       // 缓冲清空
        uint8_t power_down : 1;       // 掉电
    };
    uint8_t value;
} ls_status_s;

typedef enum lc1_member_enum
{
    LC1_TIMESTAMP,            // 时间戳
    LC1_TIME_STATUS,          // 时间状态
    LC1_STATUS,               // 状态字
    LC1_VRMS_A,               // A相电压
    LC1_VRMS_B,               // B相电压
    LC1_VRMS_C,               // C相电压
    LC1_IRMS_A,               // A相电流
    LC1_IRMS_B,               // B相电流
    LC1_IRMS_C,               // C相电流
    LC1_FREQ,                 // 频率
    LC1_PWR_P,                // 有功功率
    LC1_PWR_P_A,              // A相有功功率
    LC1_PWR_P_B,              // B相有功功率
    LC1_PWR_P_C,              // C相有功功率
    LC1_PWR_Q,                // 无功功率
    LC1_PWR_Q_A,              // A相无功功率
    LC1_PWR_Q_B,              // B相无功功率
    LC1_PWR_Q_C,              // C相无功功率
    LC1_PF,                   // 功率因数
    LC1_PF_A,                 // A相功率因数
    LC1_PF_B,                 // B相功率因数
    LC1_PF_C,                 // C相功率因数
    LC1_ACTIVE_ENERGY_POS,    // 有功正向电能
    LC1_ACTIVE_ENERGY_NEG,    // 有功反向电能
    LC1_COMB1_KVARH,          // 组合表计1无功电能
    LC1_COMB2_KVARH,          // 组合表计2无功电能
    LC1_Q1_KVARH,             // 象限1无功电能
    LC1_Q2_KVARH,             // 象限2无功电能
    LC1_Q3_KVARH,             // 象限3无功电能
    LC1_Q4_KVARH,             // 象限4无功电能
    LC1_MD_KW,                // 需量有功
    LC1_MD_KVAR               // 需量无功
}lc1_member_t;

/* 固定负荷曲线捕获BUFFER */
typedef struct
{
    uint8_t timestamp[4];
    uint8_t time_status;
    uint8_t status;

    uint8_t vrms[3][2];
    uint8_t irms[3][4];
    uint8_t freq[2];

    uint8_t pwr_p[4][4];
    uint8_t pwr_q[4][4];

    uint8_t pf[4][2];

    uint8_t tpkWh[4];
    uint8_t tnkWh[4];
    uint8_t comb1_kvarh[4];
    uint8_t comb2_kvarh[4];

    uint8_t qx_kvarh[4][4];

    uint8_t md_kW[4];
    uint8_t md_kvar[4];
} lc1_blk_s;

/* Exported defines ----------------------------------------------------------*/
typedef uint16_t LC_STUS;
#define STUS_LC_PRG_PERIOD ((LC_STUS)1 << 1)     // 负荷曲线捕获周期修改
#define STUS_LC_CLR_PROFILE ((LC_STUS)1 << 2)    // 清零负荷曲线

/* Exported macro ------------------------------------------------------------*/
#define LC1_ENTRIES_NUM (LC1_DAYS * (86400L / LC1_PERIOD))
#define LC2_ENTRIES_NUM (LC2_DAYS * (86400L / LC2_PERIOD))

struct loadcurve_s
{
    void (*reset)(uint8_t type);
    void (*rcd_clr)(LC_TYPE_t lc_type);
    const lc_para_s *(*para_get)(void);
    bool (*para_set)(uint16_t ofst, void *para, uint16_t len);
    bool (*state_query)(LC_STUS state);
    void (*state_clr)(void);
};

extern const struct loadcurve_s loadcurve;
extern const struct app_task_t  loadcurve_task;
#endif
