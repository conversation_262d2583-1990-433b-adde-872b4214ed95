/**
 ******************************************************************************
* @file    bsp_lcd.c
* <AUTHOR> @date    2024
* @brief   BL55080 驱动。
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include <string.h>
#include "hal_mcu.h"
#include "ext_lcd_driver.h"
#include "hal_gpio.h"
#include "typedef.h"


/// @brief I2C IO 定义  PIN_VLCD_CTRL
#define LCD_SDA_INPUT()     gpio_set_input(PIN_LCD_SDA)
#define LCD_SDA_OUTPUT()    gpio_set_output(PIN_LCD_SDA)
#define LCD_SDA_READ()      gpio_input_get(PIN_LCD_SDA)

#define LCD_SDA_1()         gpio_out_H(PIN_LCD_SDA)
#define LCD_SDA_0()         gpio_out_L(PIN_LCD_SDA)

#define LCD_SCL_1()         gpio_out_H(PIN_LCD_SCL)
#define LCD_SCL_0()         gpio_out_L(PIN_LCD_SCL)

/// @brief 延时函数 
#define lcd_Delay()         hal_mcu.wait_us(2)


/// BL55080 IIC地址 
#define  SLAVE_ADDR         0x7c
#define  READ_BL55080       0x01
#define  WRITE_BL55080      0x00

///     
#define  NEXT_DATA          0x00 // 后面发送的字节是显示数据
#define  NEXT_COMMAND       0x80 // 后面发送的字节是指令

#define  ADSET              0x00 // 地址设置
#define  SEG_CRL            0x23 

// 电压选择 
#define  EVRSET             0x40  
#define  EVR_LCD_5V         0x00  //可选0-12，对应3.3V-2.5V
#define  EVR_LCD_4V8        0x01
#define  EVR_LCD_4V6        0x02
#define  EVR_LCD_2V5        0x1E  //30-2.5V

// 显示控制 
#define  DISCTL             0x60
#define  DISCTL_80HZ        0x00
#define  DISCTL_71HZ        0x04
#define  DISCTL_64HZ        0x08
#define  DISCTL_50HZ        0x0C
#define  DISCTL_MODE_1      0x00
#define  DISCTL_MODE_2      0x01
#define  DISCTL_NORMAL      0x02
#define  DISCTL_HPOWER      0x03
// 芯片设置 
#define  ICSET              0x70
#define  ICSET_LINE         0x00  // 线
#define  ICSET_FRAME        0x04
#define  ICSET_SW_RESET     0x02  // 软复位
#define  ICSET_DISOFF       0x00  // 显示关闭
#define  ICSET_DISON        0x01  // 显示开启
// 显示控制2    
#define  APCTL              0x78
#define  APCTL_NORMAL       0x00  // 正常
#define  APCTL_OFF          0x01  // 全灭
#define  APCTL_ON           0x02  // 全亮


#define LCD_COM_NUM         8   // COM数
#define MCU_LCD_MEM_LEN     32  // LCD显示内存长度,填使用到的

static uint8_t lcd_buff[MCU_LCD_MEM_LEN];   // LCD显示缓存

/***** 以下为 LCD 指令 *****/
///lcd初始化指令
static const uint8_t LCD_reset_Cmd[] = 
{
    (SLAVE_ADDR | WRITE_BL55080),
    (NEXT_COMMAND | ICSET | ICSET_SW_RESET | ICSET_LINE),
    (NEXT_COMMAND | DISCTL | DISCTL_64HZ | DISCTL_NORMAL),
    (NEXT_COMMAND | EVRSET | EVR_LCD_4V8),
    (NEXT_DATA | ADSET | 0x00)
};
/// 正常刷新显示指令，为了避免驱动RAM被修改，每次刷新时配置所有指令
static const uint8_t LCD_refresh_Cmd[] = 
{
    (SLAVE_ADDR | WRITE_BL55080),
    (NEXT_COMMAND | ICSET | ICSET_LINE | ICSET_DISON),
    (NEXT_COMMAND | DISCTL | DISCTL_64HZ | DISCTL_NORMAL),
    (NEXT_COMMAND | EVRSET | EVR_LCD_4V8),
    (NEXT_DATA | ADSET | 0x00)
};
///lcd显示开启指令
static const uint8_t LCD_disp_on_Cmd[] = 
{
    (SLAVE_ADDR | WRITE_BL55080),
    (NEXT_COMMAND | ICSET | ICSET_LINE | ICSET_DISON)
};
///lcd显示关闭指令
static const uint8_t LCD_disp_off_Cmd[] = 
{
    (SLAVE_ADDR | WRITE_BL55080),
    (NEXT_COMMAND | ICSET | ICSET_DISOFF)
};
/***** 以下为 IIC 总线 *****/
/// @brief I2C start condition
static void lcd_stop(void)
{
    LCD_SCL_0();
    LCD_SDA_0();
    lcd_Delay();
    LCD_SCL_1();
    lcd_Delay();
    LCD_SDA_1();
}

/// @brief I2C start condition
/// @param  
static void lcd_start(void)
{
    LCD_SCL_0();
    lcd_Delay();
    LCD_SDA_1();
    lcd_Delay();
    LCD_SCL_1();
    lcd_Delay();
    LCD_SDA_0();
    lcd_Delay();
    LCD_SCL_0();
    lcd_Delay();
}

/// @brief I2C write one byte to slave
/// @param  _ucByte: the byte to be written
static uint8_t lcd_send_byte(uint8_t _ucByte)
{
    register uint8_t i;

    for(i = 0; i < 8; i++)
    {
        LCD_SCL_0();
        if(_ucByte & 0x80) { LCD_SDA_1(); }
        else { LCD_SDA_0(); }
        _ucByte <<= 1;
        lcd_Delay();
        LCD_SCL_1();
        lcd_Delay();
    }
	LCD_SCL_0();
	lcd_Delay();
    /// 读取ACK
    LCD_SDA_INPUT();
    LCD_SCL_1();
    lcd_Delay();
    _ucByte = LCD_SDA_READ();
    LCD_SCL_0();
    LCD_SDA_OUTPUT();
    return _ucByte;
}

/// @brief I2C read one byte from slave
/// @return the read byte
static uint8_t lcd_read_byte(void)
{
    uint8_t i;
    uint8_t value;

    LCD_SDA_INPUT();
    value = 0;
    for(i = 0; i < 8; i++)
    {
        LCD_SCL_1();
        lcd_Delay();
        value <<= 1;
        if(LCD_SDA_READ()) { value++; }
        LCD_SCL_0();
        lcd_Delay();
    }
    return value;
}

/// @brief I2C write one byte to slave with NACK
static void lcd_ack(void)
{
    LCD_SDA_OUTPUT();
    LCD_SDA_0();
    __NOP();__NOP();__NOP();__NOP();
    LCD_SCL_1();
    lcd_Delay();
    LCD_SCL_0();
    lcd_Delay();
    LCD_SDA_1();
}
/// @brief I2C write one byte to slave with NACK
static void lcd_nack(void)
{
    //LCD_SDA_OUTPUT();
    LCD_SDA_1();
    __NOP();__NOP();__NOP();__NOP();
    LCD_SCL_1();
    lcd_Delay();
    LCD_SCL_0();
    lcd_Delay();
}

/// @brief 往液晶驱动写入指令、数据
/// @param cmd      指令指针
/// @param cmd_len  指令长度
/// @param typ      1-写显存，0-只写指令
static void lcd_write_cmd_data(const void *cmd, uint8_t cmd_len, uint8_t typ)  
{
    uint8_t *ptr  = (uint8_t *)cmd;
    uint8_t *dptr = (uint8_t *)lcd_buff;
    uint8_t i;

    lcd_stop();
    lcd_start();
    for(i = 0; i < cmd_len; i++)
    {
        lcd_send_byte(ptr[i]);
    }
    if(typ)
    {
        for(i = 0; i < sizeof(lcd_buff); i++)
        {
            lcd_send_byte(*dptr++);
        }
    }
    lcd_stop();
}

/***** 以下为 LCD驱动 *****/
/// @brief 正常供电下LCD初始化
bool ext_lcd_open(void)
{
    hal_gpio.ext_lcd(GPIO_OPEN);
    lcd_write_cmd_data(LCD_reset_Cmd,   sizeof(LCD_reset_Cmd),   1);
    lcd_write_cmd_data(LCD_disp_on_Cmd, sizeof(LCD_disp_on_Cmd), 0);
    return TRUE;
}
/// @brief 辅助电源唤醒显示，LCD初始化
bool ext_lcd_open_nopower(void)
{
    return ext_lcd_open();
}
/// @brief 关闭LCD
bool ext_lcd_close(void)
{
    lcd_write_cmd_data(LCD_disp_off_Cmd, sizeof(LCD_disp_off_Cmd), 0);
    hal_gpio.ext_lcd(GPIO_CLOSE);
    return TRUE;
}

/***** 以下函数移植无需修改 *****/

/// @brief 显示所有段
void ext_lcd_all_seg_set(void)
{
    memset(lcd_buff, 0xFF, sizeof(lcd_buff));
}
/// @brief 清除所有段
void ext_lcd_all_seg_clr(void)
{
    memset(lcd_buff, 0x00, sizeof(lcd_buff));
}

/// @brief 设置指定段的状态
/// @param seg  段号
/// @param on_off  1-点亮，0-熄灭
void ext_lcd_light(uint16_t seg, uint8_t on_off)
{
    if(seg == 0) return;
    seg = seg - 1; // 显示段数值定义做了加1的非0处理
	uint8_t x = 1 << (seg % 8);
	uint8_t y = seg / 8;

    if(y >= sizeof(lcd_buff)) return;
	if(on_off & 0x01)
	{
		lcd_buff[y] |= x;
	}
	else
	{
		lcd_buff[y] &= ~x;
	}    
}

/// @brief 刷新LCD显存
void ext_lcd_refresh(void)
{
    lcd_write_cmd_data(LCD_refresh_Cmd, sizeof(LCD_refresh_Cmd), 1);
    lcd_write_cmd_data(LCD_disp_on_Cmd, sizeof(LCD_disp_on_Cmd), 0);
}