/**
 ******************************************************************************
 * @file    hal_lcd.h
 * <AUTHOR> @date    2024
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 ******************************************************************************/

#ifndef __HAL_LCD_H__
#define __HAL_LCD_H__
#include "hal_def.h"

/* Exported types ------------------------------------------------------------*/
/* Exported defines ----------------------------------------------------------*/
#define LCD_COM_NUM 4                        // 这里填上配置LCD COM的数量
#define MCU_LCD_MEM_PTR (HT_LCD->LCDBUF)    // 这里填上MCU的显存地址

///液晶com  -> 驱动com 映射 
#define LCD_COM1            0  
#define LCD_COM2            1
#define LCD_COM3            2
#define LCD_COM4            3
#define LCD_COM5            4
#define LCD_COM6            5
#define LCD_COM7            6
#define LCD_COM8            7   

/// 内存地址偏移计算
#if(LCD_COM_NUM == 8)
#define MCU_LCD_MEM_LEN 42    // 这里填上MCU的显存大小
#define COM0(x) (x * 8 + LCD_COM8 + 1)
#define COM1(x) (x * 8 + LCD_COM7 + 1)
#define COM2(x) (x * 8 + LCD_COM6 + 1)
#define COM3(x) (x * 8 + LCD_COM5 + 1)
#define COM4(x) (x * 8 + LCD_COM4 + 1)
#define COM5(x) (x * 8 + LCD_COM3 + 1)
#define COM6(x) (x * 8 + LCD_COM2 + 1)
#define COM7(x) (x * 8 + LCD_COM1 + 1)
#elif(LCD_COM_NUM == 6)
#define MCU_LCD_MEM_LEN 42    // 这里填上MCU的显存大小
/* LCD MEM的位索引定义 */
#define COM0(x) (x * 8 + LCD_COM6 + 1)
#define COM1(x) (x * 8 + LCD_COM5 + 1)
#define COM2(x) (x * 8 + LCD_COM4 + 1)
#define COM3(x) (x * 8 + LCD_COM3 + 1)
#define COM4(x) (x * 8 + LCD_COM2 + 1)
#define COM5(x) (x * 8 + LCD_COM1 + 1)
#else
#define MCU_LCD_MEM_LEN 36    // 这里填上MCU的显存大小
/* LCD MEM的位索引定义 */
#define COM0(x) (x * 8 + LCD_COM1 + 1)
#define COM1(x) (x * 8 + LCD_COM2 + 1)
#define COM2(x) (x * 8 + LCD_COM3 + 1)
#define COM3(x) (x * 8 + LCD_COM4 + 1)
#endif

/* Exported macro ------------------------------------------------------------*/
/* lcd seg 与 mcu seg段映射 */
#define LCD_SEG01 1
#define LCD_SEG02 2
#define LCD_SEG03 3
#define LCD_SEG04 4
#define LCD_SEG05 5
#define LCD_SEG06 6
#define LCD_SEG07 7
#define LCD_SEG08 8
#define LCD_SEG09 9
#define LCD_SEG10 10
#define LCD_SEG11 11
#define LCD_SEG12 12
#define LCD_SEG13 33
#define LCD_SEG14 14
#define LCD_SEG15 34
#define LCD_SEG16 16
#define LCD_SEG17 17
#define LCD_SEG18 18
#define LCD_SEG19 19
#define LCD_SEG20 20
#define LCD_SEG21 21
#define LCD_SEG22 22
#define LCD_SEG23 23

struct hal_lcd_s
{
    bool (*open)(void);
    bool (*open_nopower)(void);
    bool (*close)(void);
    void (*all_seg_set)(void);
    void (*all_seg_clr)(void);
    void (*light)(uint16_t com_seg, uint8_t mask);
    void (*refresh)(void);
};
extern const struct hal_lcd_s hal_lcd;

#endif /* __HAL_LCD_H__ */
