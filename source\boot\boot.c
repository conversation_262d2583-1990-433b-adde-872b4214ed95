/**
 ******************************************************************************
 * @file    boot.c
 * <AUTHOR> @date    2024
 * @brief   应用程序跳转，自编程，接受程序文件。
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>
#include <stdio.h>
#if defined(__ICCARM__)
#include <cmsis_compiler.h>
#endif
#include "..\toolkit\pt-1.4\pt.h"
#include "utils.h"
#include "ymodem.h"
#include "boot_entry.h"

const struct boot_info_t *boot_info = (const struct boot_info_t *)MCU_BOOT_DATA_BASE;

/* Private function prototypes -----------------------------------------------*/
PT_THREAD(iap_process(void));

/* Private functions ---------------------------------------------------------*/

/// @brief 启动YMODEM文件传输协议下载程序升级
/// @note  1，升级文件大小和地址合法性检查。2，升级文件校验。3，升级文件写入。4，升级文件校验。
/// @param
static void isp_process(void)
{
    modem_struct_t     mblock;
    struct boot_info_t info = {
        0,
    };
    struct image_header_t header;
    uint32_t              addr      = 0xFFFFFFFF, base_addr;
    uint32_t              crc32_val = CRC32_START_VALUE, image_size = 0;
    uint32_t              header_crc32_val = CRC32_START_VALUE;

    // 打开串口, 准备接收YMODEM协议数据
    boot.serial_open();
#if IAP_DISPLAY_EN
    boot.lcd_printf(0);
#endif
    // 超时没收到升级文件，退出YMODEM模式
    if(ymodem_init(&mblock, boot.serial_send, boot.serial_recv) != 0)
    {
        if(boot_info->mode == MODE_IAP_ERR)
        {
            // IAP 失败，原程序无法运行
            ymodem_print("\r\nNo file selected! \r\n");
            return;
        }

        info.mode = MODE_RUNING_APP;                               /// 超时没收到升级文件，退出YMODEM模式，回到应用程序
        boot.program((uint32_t)boot_info, &info, sizeof(info));    /// 保存信息
        return;
    }
    while(1)
    {
#if IAP_DISPLAY_EN
        boot.lcd_printf(1, mblock.nxt_num * 10);
#endif
        switch(ymodem_recv(&mblock))    /// 接收数据,内有超时机制
        {
            case 0:                       // 一帧数据接收完毕
                if(addr == 0xFFFFFFFF)    // 首帧数据
                {
#if USE_PATCH_DECOMPRESS
                    base_addr = FIRMWARE_DOWNLOAD_ADDR;    /// 差分文件地址，外部FLASH
#else
                    base_addr = ((struct image_header_t *)mblock.buf)->base_addr;    /// 取出升级文件的固件起始地址
#endif
                    addr = base_addr;
                    if(mblock.filelen < SIGNATURE_MAX_LEN || mblock.filelen > FIRMWARE_MAX_SIZE || addr == 0xFFFFFFFF
#if !USE_PATCH_DECOMPRESS
                       || (addr >= BOOT_BASE_ADDR && addr < BOOT_BASE_ADDR + BOOT_SIZE)
#endif
                    )
                    {
                        ymodem_cancle();
                        ymodem_print("\r\nFile unmatched!\r\n");
                        return;
                    }
                    memcpy((uint8 *)&header, mblock.buf, sizeof(header));
#if !USE_PATCH_DECOMPRESS
                    // 完整包升级需要判断升级文件是否合法
                    header_crc32_val = crc32(header_crc32_val, (uint8_t *)&header, sizeof(header) - 4);
                    if(header_crc32_val != header.verify_tag)
                    {
                        ymodem_cancle();
                        ymodem_print("\r\nFile unmatched!\r\n");
                        return;
                    }
#endif
                    image_size = mblock.filelen - SIGNATURE_MAX_LEN;    /// 真实升级文件大小
                }

#if USE_PATCH_DECOMPRESS
                if(!boot.file_write(addr, mblock.buf, mblock.len))    /// 保存差分文件
                {
                    ymodem_cancle();
                    ymodem_print("\r\nDataFlash error!");    /// 外部FLASH写入失败
                    return;
                }
                /// @note 计算CRC32校验，这里需要注意，如果使用外部FLASH，需要计算的是差分文件，而不是实际的升级文件
                if(image_size >= mblock.len)
                {
                    crc32_val = crc32(crc32_val, mblock.buf, mblock.len);    // file_write中已经进行校验判断，认为mblock_buf和实际内存中的数据已经一致
                    image_size -= mblock.len;
                }
                else
                {
                    crc32_val  = crc32(crc32_val, mblock.buf, image_size);
                    image_size = 0;
                }
#else
                // 非差分升级，直接编程CODEFLASH
                if(!boot.program(addr, mblock.buf, mblock.len))    /// 编程CODEFLASH
                {
                    ymodem_cancle();
                    ymodem_print("\r\nCodeFlash error!");
                    return;
                }
                // 计算CRC32校验
                if(image_size >= mblock.len)
                {
                    crc32_val = crc32(crc32_val, (uint8_t *)addr, mblock.len);
                    image_size -= mblock.len;
                }
                else
                {
                    crc32_val  = crc32(crc32_val, (uint8_t *)addr, image_size);
                    image_size = 0;
                }
#endif
                addr += mblock.len;
                break;

            case 1:    // 单个文件传送结束
#if USE_PATCH_DECOMPRESS
                boot.file_read((base_addr + mblock.filelen - SIGNATURE_MAX_LEN + 1), mblock.buf, 4);
#else
                memcpy(mblock.buf, (void *)(base_addr + mblock.filelen - SIGNATURE_MAX_LEN + 1), 4);
#endif
                if(crc32_val != get_lsbdata32(mblock.buf))    /// 升级文件签名是小端格式存放的
                {
#if !USE_PATCH_DECOMPRESS
                    info.mode = MODE_IAP_ERR;
                    boot.program((uint32_t)boot_info, &info, sizeof(info));
#endif
                    ymodem_print("\r\ncrc failure!");    /// 校验错误
                    return;
                }
                info.image_size = mblock.filelen - SIGNATURE_MAX_LEN;    /// 保存升级文件长度
                strcpy(info.image_identifier, mblock.filename);          /// 保存升级文件名，也即固件标识
#if USE_PATCH_DECOMPRESS
                boot.program((uint32_t)boot_info, &info, sizeof(info));    /// 保存信息
                while(PT_SCHEDULE(iap_process())) {}                       /// 执行IAP
                if(boot_info->mode == MODE_IAP_ERR)
                {
                    ymodem_print("\r\nIAP failure!");
                    return;
                }
#endif
                {
                    uint32_t base_addr = header.base_addr;
                    // 判断传输下来的文件是否与表型相符，记录的地址，必须编程完才能判断。
                    if(strncmp((*(const struct image_header_t *)base_addr).identifier, APP_IDENTIFIER, strlen(APP_IDENTIFIER)))
                    {
                        info.mode = MODE_IAP_ERR;
                        boot.program((uint32_t)boot_info, &info, sizeof(info));
                    }
#if !USE_PATCH_DECOMPRESS
                    else
                    {
                        boot.time_get(info.stamp);                                 /// 记录升级时间
                        info.mode = MODE_RUNING_APP;                               /// 初始到跳转APP模式
                        boot.program((uint32_t)boot_info, &info, sizeof(info));    /// 保存信息
                    }
#endif

                    if(boot_info->mode == MODE_IAP_ERR)
                        ymodem_print("\r\nISP failure!");
                    else
                        ymodem_print("\r\nSuccess!");
                }
                return;

            default:    // 超时或错误，取消传输
                ymodem_cancle();
                return;
        }
    }
}

#if IAP_SUPPORT
/// @brief 读取已下载的文件，进行IAP，主要还原差分文件，从外部flash中下载程序，并进行校验
/// @note  多线程处理，写一次就跳出，下次继续写。
/// @param iap_process
PT_THREAD(iap_process(void))
{
    static uint32_t  df_addr = FIRMWARE_DOWNLOAD_ADDR;
    static uint32_t  cf_addr = 0xFFFFFFFF;
    static uint32_t  size;
    static struct pt pt_iap;

    struct boot_info_t info;
    uint16_t           tmp_len;

    PT_BEGIN(&pt_iap);
#if IAP_DISPLAY_EN
    boot.lcd_printf(2);
#endif

#if USE_PATCH_DECOMPRESS
    extern uint32_t image_revert(uint32_t *dataflash_addr, uint32_t file_size);
    size = image_revert(&df_addr, boot_info->image_size);    /// 差分文件还原
#else
    size = boot_info->image_size;
#endif
    while(size != 0)
    {
        PT_YIELD(&pt_iap);    /// 暂时退出线程，下次接着运行
#if IAP_DISPLAY_EN
        boot.lcd_printf(3, df_addr / 1024);
#endif
        uint8_t buf[MCU_FLASH_PAGE_PSIZE];
        tmp_len = (size >= sizeof(buf)) ? sizeof(buf) : size;
        if(!boot.file_read(df_addr, buf, tmp_len)) continue;    /// 保证升级文件读正确
        if(cf_addr == 0xFFFFFFFF)
        {
            cf_addr = ((struct image_header_t *)buf)->base_addr;    /// 取出升级文件起始地址
            if(size > FIRMWARE_MAX_SIZE || (cf_addr >= BOOT_BASE_ADDR && cf_addr < BOOT_BASE_ADDR + BOOT_SIZE))
            {
                cf_addr = 0xFFFFFFFF;
                break;    /// 文件大小或地址非法, 结束升级
            }
        }
        if(!boot.program(cf_addr, buf, tmp_len)) continue;    /// 保证程序编程正确
        df_addr += tmp_len, cf_addr += tmp_len, size -= tmp_len;
    }
    memcpy(&info, boot_info, sizeof(info));    /// 保留升级文件大小和固件标识等
    boot.time_get(info.stamp);                 /// 记录升级时间

    info.mode = (cf_addr == 0xFFFFFFFF) ? MODE_IAP_ERR : MODE_RUNING_APP;    /// 初始到跳转APP模式

    boot.program((uint32_t)boot_info, &info, sizeof(info));    /// 保存信息

    PT_END(&pt_iap);
}
#endif

/// @brief  boot 主函数
/// @note   1，如果没有升级任务，应尽快直接跳转到应用程序。2，为了升级稳定性以及避免电池消耗，在电池供电时，进入低功耗模式，不执行升级。
/// @param
/// @return
int main(void)
{
    volatile uint32_t mode = boot_info->mode;

    if(mode != MODE_RUNING_ISP && mode != MODE_RUNING_IAP && mode != MODE_IAP_ERR)
    {
        /// 正常启动，跳转到应用程序
        const uint32_t *app = (const uint32_t *)APP_RSTVEC_ADDR;
        __set_MSP(*app++);
        ((void (*)())(*app))();
    }
    /// @note boot 运行环境初始化
    boot.init();
    /// @note BOOT 引导程序更新
#if IAP_SUPPORT
    if(mode == MODE_RUNING_IAP)
    {
        // 进入IAP升级模式,主要还原差分文件，从外部flash中下载程序，并进行校验
        while(PT_SCHEDULE(iap_process())) {}
    }
    else
#endif
    {
        // 进入YMODEM升级模式。
        isp_process();
    }

    boot.reset();
}

extern bool hal_flash_read(uint32_t ofst, void *pdat, uint16_t num);
extern bool hal_flash_write(uint32_t ofst, const void *pdat, uint16_t num);

/// boot api
#pragma location = ".boot_api"

__root static const struct boot_api_s boot_entry = {
    .boot_identifier = BOOT_IDENTIFIER,
    .boof_info       = (const struct boot_info_t *)MCU_BOOT_DATA_BASE,
    .intvec_set      = boot_intvec_set,
    .mcu_flash_read  = hal_flash_read,
    .mcu_flash_write = hal_flash_write,
};
