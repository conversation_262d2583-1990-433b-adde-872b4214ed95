
/**
 ******************************************************************************
* @file    ext_lcd_driver.c
* <AUTHOR> @date    2024
* @brief   lcd -> lcd驱动映射
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __EXT_LCD_DRIVER_H__
#define __EXT_LCD_DRIVER_H__


///液晶com  -> 驱动com 映射 
#define LCD_COM1            7  
#define LCD_COM2            6
#define LCD_COM3            5
#define LCD_COM4            4
#define LCD_COM5            3
#define LCD_COM6            2
#define LCD_COM7            1
#define LCD_COM8            0   

/* lcd seg -> 驱动seg段 映射 */
#define LCD_SEG01           0
#define LCD_SEG02           1
#define LCD_SEG03           2
#define LCD_SEG04           3
#define LCD_SEG05           4
#define LCD_SEG06           5
#define LCD_SEG07           6
#define LCD_SEG08           7
#define LCD_SEG09           8
#define LCD_SEG10           9
#define LCD_SEG11           10
#define LCD_SEG12           11
#define LCD_SEG13           12
#define LCD_SEG14           13
#define LCD_SEG15           14
#define LCD_SEG16           15
#define LCD_SEG17           16
#define LCD_SEG18           17
#define LCD_SEG19           18
#define LCD_SEG20           19
#define LCD_SEG21           20
#define LCD_SEG22           21
#define LCD_SEG23           22
#define LCD_SEG24           23
#define LCD_SEG25           24
#define LCD_SEG26           25
#define LCD_SEG27           26
#define LCD_SEG28           27
#define LCD_SEG29           28
#define LCD_SEG30           29
#define LCD_SEG31           30
#define LCD_SEG32           31
#define LCD_SEG33           32
#define LCD_SEG34           33
#define LCD_SEG35           34

/// 内存地址偏移计算
#define COM0(x)             (x * 8 + LCD_COM1 + 1)
#define COM1(x)             (x * 8 + LCD_COM2 + 1)
#define COM2(x)             (x * 8 + LCD_COM3 + 1)
#define COM3(x)             (x * 8 + LCD_COM4 + 1)
#define COM4(x)             (x * 8 + LCD_COM5 + 1)
#define COM5(x)             (x * 8 + LCD_COM6 + 1)
#define COM6(x)             (x * 8 + LCD_COM7 + 1)
#define COM7(x)             (x * 8 + LCD_COM8 + 1)


#endif //__EXT_LCD_DRIVER_H__
