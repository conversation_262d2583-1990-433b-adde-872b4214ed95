#ifndef _DELAY_H
#define _DELAY_H

#include "hal_def.h"
#define SYS_CLK_PLL

/* @brief PWM定时器选择 */
#define PWM_TIMER_EN         true
#define PWM_TIMER0_EN        true
#define PWM_TIMER1_EN        false
#define PWM_TIMER2_EN        false

/* @brief 枚举所有支持PWM的定时器 */
typedef enum
{
#if PWM_TIMER0_EN
    PWM_TIMER0,
#endif
#if PWM_TIMER1_EN
    PWM_TIMER1,
#endif
#if PWM_TIMER2_EN
    PWM_TIMER2,
#endif
    PWM_TIMER_NUM
} HAL_PWM_TIMER_TYPE;

/* @brief 回调函数链定义 */
typedef struct sCallChain
{
    struct sCallChain* next;
    void (*func)(void);
}CallChain_s;

/// @brief 软件定时器定义 
typedef struct
{
    uint32_t start;
    uint32_t interval;
}SwTimer_s;

/// @brief 系统节拍调用函数的声明与实体，相当于中断处理函数
#define SYSTICKCALL_NAME(name) extern tCallChain name;
#define SYSTICKCALL(name) \
void systick_call_##name(void); \
CallChain_s name = {NULL, systick_call_##name,}; \
void systick_call_##name(void)

/* Exported functions ------------------------------------------------------- */
struct hal_delay_s
{
    void (*systick_start)(void func(void));
    void (*systick_insert)(CallChain_s* call);
    void (*systick_remove)(CallChain_s* call);
    uint32_t (*systick_cnt)(void);
/**
 * @brief  系统精确毫秒延时
 * @param  [in]  ms-延时毫秒数
 * @retval None
 */
    void (*msdly)(uint32_t ms);

/**
 * @brief  软定时器定时间隔设置
 * @param  [in]  t-软定时器
 * @param  [in]  interval-定时间隔，单位MS
 * @retval None
 */
    void (*interval)(SwTimer_s* t, uint32_t interval);

/**
 * @brief  软定时器定时重新开始
 * @param  [in]  t-软定时器
 * @retval None
 */
    void (*restart)(SwTimer_s* t);

/**
 * @brief  查询软定时器是否定时到期
 * @param  [in]  t-软定时器
 * @retval 0-定时未到期
 * @retval 1-定时已到期
 */
    bool (*expired)(const SwTimer_s* t);

/**
 * @brief  毫秒延时
 * @param  [in]  ms-延时微秒数
 * @retval None
 */
    void (*xms)(uint16_t ms); 

    /// @brief  PWM输出设置
    void (*pwm_out)(HAL_PWM_TIMER_TYPE ch, uint32_t hz);
};
extern const struct hal_delay_s hal_timer;


#endif
