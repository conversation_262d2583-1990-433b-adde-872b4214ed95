/**
 ******************************************************************************
* @file    crc.h
* <AUTHOR> @date    2024
* @brief   crc32算法头
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include <stdint.h>

static const uint32_t crc32revtab[256] =
{
    /* 0x00 */ 0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA,
    /* 0x04 */ 0x076DC419, 0x706AF48F, 0xE963A535, 0x9E6495A3,
    /* 0x08 */ 0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988,
    /* 0x0C */ 0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91,
    /* 0x10 */ 0x1DB71064, 0x6AB020F2, 0xF3B97148, 0x84BE41DE,
    /* 0x14 */ 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
    /* 0x18 */ 0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC,
    /* 0x1C */ 0x14015C4F, 0x63066CD9, 0xFA0F3D63, 0x8D080DF5,
    /* 0x20 */ 0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172,
    /* 0x24 */ 0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B,
    /* 0x28 */ 0x35B5A8FA, 0x42B2986C, 0xDBBBC9D6, 0xACBCF940,
    /* 0x2C */ 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
    /* 0x30 */ 0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116,
    /* 0x34 */ 0x21B4F4B5, 0x56B3C423, 0xCFBA9599, 0xB8BDA50F,
    /* 0x38 */ 0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924,
    /* 0x3C */ 0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D,
    /* 0x40 */ 0x76DC4190, 0x01DB7106, 0x98D220BC, 0xEFD5102A,
    /* 0x44 */ 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
    /* 0x48 */ 0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818,
    /* 0x4C */ 0x7F6A0DBB, 0x086D3D2D, 0x91646C97, 0xE6635C01,
    /* 0x50 */ 0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E,
    /* 0x54 */ 0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457,
    /* 0x58 */ 0x65B0D9C6, 0x12B7E950, 0x8BBEB8EA, 0xFCB9887C,
    /* 0x5C */ 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
    /* 0x60 */ 0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2,
    /* 0x64 */ 0x4ADFA541, 0x3DD895D7, 0xA4D1C46D, 0xD3D6F4FB,
    /* 0x68 */ 0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0,
    /* 0x6C */ 0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9,
    /* 0x70 */ 0x5005713C, 0x270241AA, 0xBE0B1010, 0xC90C2086,
    /* 0x74 */ 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
    /* 0x78 */ 0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4,
    /* 0x7C */ 0x59B33D17, 0x2EB40D81, 0xB7BD5C3B, 0xC0BA6CAD,
    /* 0x80 */ 0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A,
    /* 0x84 */ 0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683,
    /* 0x88 */ 0xE3630B12, 0x94643B84, 0x0D6D6A3E, 0x7A6A5AA8,
    /* 0x8C */ 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
    /* 0x90 */ 0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE,
    /* 0x94 */ 0xF762575D, 0x806567CB, 0x196C3671, 0x6E6B06E7,
    /* 0x98 */ 0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC,
    /* 0x9C */ 0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5,
    /* 0xA0 */ 0xD6D6A3E8, 0xA1D1937E, 0x38D8C2C4, 0x4FDFF252,
    /* 0xA4 */ 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
    /* 0xA8 */ 0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60,
    /* 0xAC */ 0xDF60EFC3, 0xA867DF55, 0x316E8EEF, 0x4669BE79,
    /* 0xB0 */ 0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236,
    /* 0xB4 */ 0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F,
    /* 0xB8 */ 0xC5BA3BBE, 0xB2BD0B28, 0x2BB45A92, 0x5CB36A04,
    /* 0xBC */ 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
    /* 0xC0 */ 0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A,
    /* 0xC4 */ 0x9C0906A9, 0xEB0E363F, 0x72076785, 0x05005713,
    /* 0xC8 */ 0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38,
    /* 0xCC */ 0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21,
    /* 0xD0 */ 0x86D3D2D4, 0xF1D4E242, 0x68DDB3F8, 0x1FDA836E,
    /* 0xD4 */ 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
    /* 0xD8 */ 0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C,
    /* 0xDC */ 0x8F659EFF, 0xF862AE69, 0x616BFFD3, 0x166CCF45,
    /* 0xE0 */ 0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2,
    /* 0xE4 */ 0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB,
    /* 0xE8 */ 0xAED16A4A, 0xD9D65ADC, 0x40DF0B66, 0x37D83BF0,
    /* 0xEC */ 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
    /* 0xF0 */ 0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6,
    /* 0xF4 */ 0xBAD03605, 0xCDD70693, 0x54DE5729, 0x23D967BF,
    /* 0xF8 */ 0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94,
    /* 0xFC */ 0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D,
};

static const uint32_t crc32tab[256] =
{
    /* 0x00 */ 0x00000000, 0x04c11db7, 0x09823b6e, 0x0d4326d9,
    /* 0x04 */ 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005,
    /* 0x08 */ 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61,
    /* 0x0C */ 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd,
    /* 0x10 */ 0x4c11db70, 0x48d0c6c7, 0x4593e01e, 0x4152fda9,
    /* 0x14 */ 0x5f15adac, 0x5bd4b01b, 0x569796c2, 0x52568b75,
    /* 0x18 */ 0x6a1936c8, 0x6ed82b7f, 0x639b0da6, 0x675a1011,
    /* 0x1C */ 0x791d4014, 0x7ddc5da3, 0x709f7b7a, 0x745e66cd,
    /* 0x20 */ 0x9823b6e0, 0x9ce2ab57, 0x91a18d8e, 0x95609039,
    /* 0x24 */ 0x8b27c03c, 0x8fe6dd8b, 0x82a5fb52, 0x8664e6e5,
    /* 0x28 */ 0xbe2b5b58, 0xbaea46ef, 0xb7a96036, 0xb3687d81,
    /* 0x2C */ 0xad2f2d84, 0xa9ee3033, 0xa4ad16ea, 0xa06c0b5d,
    /* 0x30 */ 0xd4326d90, 0xd0f37027, 0xddb056fe, 0xd9714b49,
    /* 0x34 */ 0xc7361b4c, 0xc3f706fb, 0xceb42022, 0xca753d95,
    /* 0x38 */ 0xf23a8028, 0xf6fb9d9f, 0xfbb8bb46, 0xff79a6f1,
    /* 0x3C */ 0xe13ef6f4, 0xe5ffeb43, 0xe8bccd9a, 0xec7dd02d,
    /* 0x40 */ 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae,
    /* 0x44 */ 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072,
    /* 0x48 */ 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16,
    /* 0x4C */ 0x018aeb13, 0x054bf6a4, 0x0808d07d, 0x0cc9cdca,
    /* 0x50 */ 0x7897ab07, 0x7c56b6b0, 0x71159069, 0x75d48dde,
    /* 0x54 */ 0x6b93dddb, 0x6f52c06c, 0x6211e6b5, 0x66d0fb02,
    /* 0x58 */ 0x5e9f46bf, 0x5a5e5b08, 0x571d7dd1, 0x53dc6066,
    /* 0x5C */ 0x4d9b3063, 0x495a2dd4, 0x44190b0d, 0x40d816ba,
    /* 0x60 */ 0xaca5c697, 0xa864db20, 0xa527fdf9, 0xa1e6e04e,
    /* 0x64 */ 0xbfa1b04b, 0xbb60adfc, 0xb6238b25, 0xb2e29692,
    /* 0x68 */ 0x8aad2b2f, 0x8e6c3698, 0x832f1041, 0x87ee0df6,
    /* 0x6C */ 0x99a95df3, 0x9d684044, 0x902b669d, 0x94ea7b2a,
    /* 0x70 */ 0xe0b41de7, 0xe4750050, 0xe9362689, 0xedf73b3e,
    /* 0x74 */ 0xf3b06b3b, 0xf771768c, 0xfa325055, 0xfef34de2,
    /* 0x78 */ 0xc6bcf05f, 0xc27dede8, 0xcf3ecb31, 0xcbffd686,
    /* 0x7C */ 0xd5b88683, 0xd1799b34, 0xdc3abded, 0xd8fba05a,
    /* 0x80 */ 0x690ce0ee, 0x6dcdfd59, 0x608edb80, 0x644fc637,
    /* 0x84 */ 0x7a089632, 0x7ec98b85, 0x738aad5c, 0x774bb0eb,
    /* 0x88 */ 0x4f040d56, 0x4bc510e1, 0x46863638, 0x42472b8f,
    /* 0x8C */ 0x5c007b8a, 0x58c1663d, 0x558240e4, 0x51435d53,
    /* 0x90 */ 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47,
    /* 0x94 */ 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b,
    /* 0x98 */ 0x0315d626, 0x07d4cb91, 0x0a97ed48, 0x0e56f0ff,
    /* 0x9C */ 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623,
    /* 0xA0 */ 0xf12f560e, 0xf5ee4bb9, 0xf8ad6d60, 0xfc6c70d7,
    /* 0xA4 */ 0xe22b20d2, 0xe6ea3d65, 0xeba91bbc, 0xef68060b,
    /* 0xA8 */ 0xd727bbb6, 0xd3e6a601, 0xdea580d8, 0xda649d6f,
    /* 0xAC */ 0xc423cd6a, 0xc0e2d0dd, 0xcda1f604, 0xc960ebb3,
    /* 0xB0 */ 0xbd3e8d7e, 0xb9ff90c9, 0xb4bcb610, 0xb07daba7,
    /* 0xB4 */ 0xae3afba2, 0xaafbe615, 0xa7b8c0cc, 0xa379dd7b,
    /* 0xB8 */ 0x9b3660c6, 0x9ff77d71, 0x92b45ba8, 0x9675461f,
    /* 0xBC */ 0x8832161a, 0x8cf30bad, 0x81b02d74, 0x857130c3,
    /* 0xC0 */ 0x5d8a9099, 0x594b8d2e, 0x5408abf7, 0x50c9b640,
    /* 0xC4 */ 0x4e8ee645, 0x4a4ffbf2, 0x470cdd2b, 0x43cdc09c,
    /* 0xC8 */ 0x7b827d21, 0x7f436096, 0x7200464f, 0x76c15bf8,
    /* 0xCC */ 0x68860bfd, 0x6c47164a, 0x61043093, 0x65c52d24,
    /* 0xD0 */ 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30,
    /* 0xD4 */ 0x029f3d35, 0x065e2082, 0x0b1d065b, 0x0fdc1bec,
    /* 0xD8 */ 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088,
    /* 0xDC */ 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654,
    /* 0xE0 */ 0xc5a92679, 0xc1683bce, 0xcc2b1d17, 0xc8ea00a0,
    /* 0xE4 */ 0xd6ad50a5, 0xd26c4d12, 0xdf2f6bcb, 0xdbee767c,
    /* 0xE8 */ 0xe3a1cbc1, 0xe760d676, 0xea23f0af, 0xeee2ed18,
    /* 0xEC */ 0xf0a5bd1d, 0xf464a0aa, 0xf9278673, 0xfde69bc4,
    /* 0xF0 */ 0x89b8fd09, 0x8d79e0be, 0x803ac667, 0x84fbdbd0,
    /* 0xF4 */ 0x9abc8bd5, 0x9e7d9662, 0x933eb0bb, 0x97ffad0c,
    /* 0xF8 */ 0xafb010b1, 0xab710d06, 0xa6322bdf, 0xa2f33668,
    /* 0xFC */ 0xbcb4666d, 0xb8757bda, 0xb5365d03, 0xb1f740b4
};

uint32_t crc32_update_revtab(uint32_t crc, const void* cp, uint32_t size)
{
    uint32_t i;
    crc ^= 0xFFFFFFFF;
    for(i = 0; i < size; i++)
    {
        crc = (crc >> 8) ^ crc32revtab[(uint8_t)crc ^ ((uint8_t*)cp)[i]];
    }
    return ~crc;
}

uint32_t crc32_update_tab(uint32_t crc, const void* cp, uint32_t size)
{
    uint32_t i;
    for(i = 0; i < size; i++)
    {
        crc = (crc << 8) ^ crc32tab[(uint8_t)(crc >> 24) ^ ((uint8_t*)cp)[i]];
    }
    return crc;
}

uint32_t crc32_update_revbit(uint32_t crc, const void* cp, uint32_t size)
{
    uint32_t i;
    crc ^= 0xFFFFFFFF;
    for(i = 0; i < size; i++)
    {
        uint8_t j;
        uint8_t tmp = ((uint8_t*)cp)[i];
        for(j = 0; j < 8; j++)
        {
            if(((uint8_t)crc ^ tmp) & 1)
            {
                crc >>= 1;
                crc ^= 0xEDB88320;
            }
            else
            {
                crc >>= 1;
            }
            tmp >>= 1;
        }
    }
    return ~crc;
}

uint32_t crc32_update_bit(uint32_t crc, const void* cp, uint32_t size)
{
    uint32_t i;
    for(i = 0; i < size; i++)
    {
        uint8_t j;
        uint8_t tmp = ((uint8_t*)cp)[i];
        for(j = 0x80; j != 0; j >>= 1)
        {
            if(crc & 0x80000000)
            {
                crc <<= 1;
                crc ^= 0x04c11db7;
            }
            else
            {
                crc <<= 1;
            }
            if(tmp & j) crc ^= 0x04c11db7;
        }
    }
    return crc;
}

/// file end
