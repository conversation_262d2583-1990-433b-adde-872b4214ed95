/**
  ******************************************************************************
  * @file    hal_adc.h
  * <AUTHOR> @date    2024
  * @brief   
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __HAL_ADC_H
#define __HAL_ADC_H

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"

/* Exported types ------------------------------------------------------------*/

/*< Defined the HAL_ADC_CHN_TYPE as enum*/
typedef enum
{
    HAL_ADC_CHN0 = 1,   // 外部输入vin1
    HAL_ADC_CHN1,       // 外部输入vin2
    HAL_ADC_CHN3,       // 外部输入vin4
    HAL_ADC_CHN4,
    HAL_ADC_CHN5,
    HAL_ADC_VCC,        // VCC
    HAL_ADC_VBAT,       // VBAT
    HAL_ADC_TEMP,       // TemperatureSensor
    HAL_ADC_CHN_NUM
} HAL_ADC_CHN_TYPE;

/* Exported defines ----------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/
struct hal_adc_t
{
/**
  * @brief  Init and Open the Adc, 单通道单次采样
  * @param  None
  * @retval None
  */
    void (*open)(void);

/**
  * @brief  Init and Open the Adc(No Power), 单通道单次采样
  * @param  None
  * @retval None
  */
    void (*open_nopower)(void);

/**
  * @brief  Close the Adc
  * @param  None
  * @retval None
  */
    void (*close)(void);

/**
  * @brief  Close the Adc(No Power)
  * @param  None
  * @retval None
  */
    void (*close_nopower)(void);

/**
  * @brief  Start a single ADC sample
  * @param  [in ] chn-ADC channel
  * @retval None
  */
    void (*start)(HAL_ADC_CHN_TYPE chn);

/**
  * @brief  Get the ADC sample value
  * @param  None
  * @retval Return ADC sample value
  */
    int32_t (*result)(HAL_ADC_CHN_TYPE chn);

/**
  * @brief  Get the ADC sample voltage
  * @param  None
  * @retval Return ADC sample voltage
  */
    float (*voltage)(HAL_ADC_CHN_TYPE chn);

/**
  * @brief  Get the current temperature value
  * @param  None
  * @retval
  */
    float (*temperature)(void);
};
extern const struct hal_adc_t hal_adc;



#endif /* __HAL_ADC_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

