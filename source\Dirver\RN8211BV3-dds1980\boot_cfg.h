 /**
  ******************************************************************************
  * @file    boot_cfg.h
  * <AUTHOR> @version V1.0
  * @date    2024-08-04
  * @brief   此文件在移植时注意修改 MCU_FLASH_PAGE_PSIZE
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/ 
#ifndef __BOOT_CFG_H__
#define __BOOT_CFG_H__

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#define BOOT_IDENTIFIER "V1.0"                  ///@BOOT版本,修改BOOT需变更版本。
#define APP_IDENTIFIER  "DDS1980-T2-RN8211BV3"     ///@定义APP程序标识符: 型号-芯片平台，不超过32字节

#define MCU_FLASH_PAGE_PSIZE  512UL

/* 在线升级:  */
#define IAP_SUPPORT                  1                ///@支持IAP升级模式
#define IAP_EXT_NVM                  0                ///@文件下载至外部DATAFLASH使能, 如果MCU FLASH足够大可以存放升级程序, 可以考虑关闭置0
#define IAP_DISPLAY_EN               0                ///@支持是否LCD显示升级信息
#define USE_PATCH_DECOMPRESS        (IAP_EXT_NVM * 1) ///@定义是否支持差分升级(需要外部DATAFLASH)
#define IDENTIFIER_MAX_LEN          (1 + 64)          ///@程序标识符传输最大长度(长度+标识符)
#define SIGNATURE_MAX_LEN           (1 + 64)          ///@程序签名最大长度(长度+签名)
#define FIRMWARE_MAX_SIZE           (256*1024UL)      ///@程序文件最大下载长度
#define CRC32_START_VALUE            12345678         ///@升级文件CRC32校验初值

#if IAP_EXT_NVM
#define EXT_NVM_MX25
#define FIRMWARE_DOWNLOAD_ADDR      0                 ///程序文件下载存放在DATAFLASH的物理起始位置定义
#else
#define FIRMWARE_DOWNLOAD_ADDR      (MCU_FLASH_APP_BASE + FIRMWARE_MAX_SIZE) ///程序文件下载存放在MCU的另一空间物理起始位置定义
#endif

extern uint32_t __checksum;

extern int __ICFEDIT_BOOT_start__;
extern int __ICFEDIT_BOOT_api__  ;
extern int __ICFEDIT_BOOT_size__ ;
extern int __ICFEDIT_APP_start__ ;
extern int __ICFEDIT_APP_api__   ;
extern int __ICFEDIT_APP_size__  ;
extern int __ICFEDIT_CRC_start__ ;
extern int __ICFEDIT_CRC_size__  ;
extern int __ICFEDIT_DATA_start__;
extern int __ICFEDIT_DATA_size__ ;
extern int __ICFEDIT_app_intvec_start__;
extern int __ICFEDIT_region_RAM_start__;

#define BOOT_BASE_ADDR              ((uint32_t)&__ICFEDIT_BOOT_start__)         ///定义BOOT 程序存储起始地址
#define BOOT_API_ADDR               ((uint32_t)&__ICFEDIT_BOOT_api__)           ///定义BOOT API地址
#define BOOT_SIZE                   ((uint32_t)&__ICFEDIT_BOOT_size__)          ///定义BOOT 程序大小，默认16K


#define MCU_FLASH_APP_BASE          ((uint32_t)&__ICFEDIT_APP_start__)          ///定义APP 程序存储起始地址
#define APP_API_ADDR                ((uint32_t)&__ICFEDIT_APP_api__)            ///定义APP API地址
#define MCU_FLASH_APP_SIZE          ((uint32_t)&__ICFEDIT_APP_size__)           ///定义APP 程序大小，默认220 K
#define APP_RSTVEC_ADDR             ((uint32_t)&__ICFEDIT_app_intvec_start__)   ///定义APP 的复位向量地址 (非ARM单片机，指cstart中的起始地址)

#define MCU_INFO_ADDR               ((uint32_t)&__ICFEDIT_DATA_start__)         ///定义MCU数据起始地址，不可直接引用，只用于区域划分
#define MCU_INFO_SIZE               (16 *1024)                                  ///定义MCU数据区域大小，默认16K

#define MCU_BOOT_DATA_BASE          (MCU_INFO_ADDR)                             ///boot信息存储地区
#define MCU_BOOT_DATA_SIZE          (MCU_FLASH_PAGE_PSIZE)                      ///boot信息存储空间大小，默认1页

#define MCU_CAL_DATA_BASE           (MCU_BOOT_DATA_BASE + MCU_BOOT_DATA_SIZE)   ///校表数据
#define MCU_CAL_DATA_SIZE           (MCU_FLASH_PAGE_PSIZE)                      ///校表数据存储空间大小，默认1页

#define MCU_FLASH_DATA_BASE         (MCU_CAL_DATA_BASE + MCU_CAL_DATA_SIZE)                                      ///定义APP数据存储地区
#define MCU_FLASH_DATA_SIZE         (MCU_INFO_SIZE - MCU_BOOT_DATA_SIZE - MCU_CAL_DATA_SIZE)                     ///定义APP数据存储空间大小，默认16K - 2页
#define MCU_FLASH_DATA_SIZE_CHK     ((uint32_t)&__ICFEDIT_DATA_size__ - MCU_BOOT_DATA_SIZE - MCU_CAL_DATA_SIZE)  ///定义APP数据存储空间大小，默认16K - 2页

#define MCU_RAM_START               ((uint32_t)&__ICFEDIT_region_RAM_start__)  ///定义MCU RAM基址
#define MCU_APB_START               0x40000000                                 ///定义MCU外设寄存器基址

/// @brief boot需要使用的函数接口
struct boot_t
{
    /// @brief 初始化BOOT运行环境，包括掉电下的低功耗运行环境
    void (*init)(void);
    /// @brief 编程FLASH
    bool (*program)(uint32_t addr, const void* dat, uint16_t len);
    /// @brief 触发系统复位
    void (*reset)(void);
    /// @brief 打开ISP需用到的串口，一般只使用扫描方式
    void (*serial_open)(void);
    /// @brief ISP串口发送
    void (*serial_send)(unsigned char ch);
    /// @brief ISP串口接收
    char (*serial_recv)(unsigned char* ch, unsigned int time_out);
    /// @brief 打开升级文件
    void (*file_open)(void);
    /// @brief 读升级文件
    bool (*file_read)(uint32_t addr, void* dat, uint16_t len);
    /// @brief 写升级文件
    bool (*file_write)(uint32_t addr, void* dat, uint16_t len);
    /// @brief 获取时间
    void (*time_get)(uint8_t time[6]);
#if IAP_DISPLAY_EN
    /// @brief 打开LCD显示
    void (*lcd_open)(void);
    /// @brief LCD打印信息
    void (*lcd_printf)(uint8_t course, ...);
#endif
};
extern const struct boot_t boot;

/// @brief 中断向量设置。BOOT里实现了所有中断入口，但需外部提供服务函数。当中断发生时，将会执行该服务函数。
/// @param irq - 中断向量类型，参照intvec_tab.h定义
/// @param vec - 中断服务函数
extern void boot_intvec_set(int irq, void (*vec)(void));

#endif /* __BOOT_CFG_H__ */
