/**
  ******************************************************************************
  * @file    tariff.c
  * <AUTHOR> @date    2024
  * @brief   费率处理模块
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "typedef.h"
#include "tariff.h"
#include "datastore.h"
#include "utils.h"
#include "debug.h"

#define TARIFF_CRC16                0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CAL_CS16(x, y)              crc16(TARIFF_CRC16, (uint8_t*)(x) + 4, y - 4)
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(TARIFF_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(TARIFF_CRC16, struct, len)

#define TF_ACTIVATE_PARA_ADDR       nvm_addr(NVM_ACTIVATE_TARIFF_PARA)
#define TF_PASSIVE_PARA_ADDR        nvm_addr(NVM_PASSIVE_TARIFF_PARA)
#define TARIFF_DATA_ADDR            nvm_addr(NVM_TARIFF_DATA)

extern const tariff_data_s  tariff_default_data;    ///默认费率数据
extern const tariff_para_s  tariff_default_para;    ///默认费率参数

static const tariff_para_s* activate;               ///激活的费率参数
static const tariff_para_s* passive;                ///备用的费率参数
static tariff_data_s tariff_data;                   ///费率数据
static TF_STUS tariff_state;                        ///费率事件状态 

/// @brief 当前套,局部操作，全取占用堆栈空间太大。
/// @param para
/// @return
static bool tariff_activate_para_checkin(uint16_t ofst, const void* para, uint16_t len)
{
    activate = (const tariff_para_s*)TF_ACTIVATE_PARA_ADDR;
    if(nvm.write((uint32_t)activate + ofst, para, len))
    {
        uint16_t crc = CAL_CS16(activate, sizeof(tariff_para_s));
        return nvm.write((uint32_t)&activate->crc, &crc, 2);
    }
    return false;
}

/// @brief 备用套,局部操作，全取占用堆栈空间太大。
/// @param para
/// @return
static bool tariff_passive_para_checkin(uint16_t ofst, const void* para, uint16_t len)
{
    passive = (const tariff_para_s*)TF_PASSIVE_PARA_ADDR;
    if(nvm.write((uint32_t)passive + ofst, para, len))
    {
        uint16_t crc = CAL_CS16(passive, sizeof(tariff_para_s));
        return nvm.write((uint32_t)&passive->crc, &crc, 2);
    }
    return false;
}

/// @brief 数据检出
/// @param
static void tariff_para_checkout(void)
{
    activate = (const tariff_para_s*)TF_ACTIVATE_PARA_ADDR;
    passive  = (const tariff_para_s*)TF_PASSIVE_PARA_ADDR;

    /* 检查activate费率是否校验正确 */
    if(activate->crc != CAL_CS16(activate, sizeof(tariff_para_s)))
    {
        activate = &tariff_default_para; ///使用默认费率模型
    }

    /* 检查passive费率是否校验正确 */
    if(passive->crc != CAL_CS16(passive, sizeof(tariff_para_s)))
    {
        passive = &tariff_default_para; ///使用默认费率模型
    }
}

static void tariff_data_store(void)
{
    CRC16_CAL(&tariff_data, sizeof(tariff_data_s));
    nvm.write((uint32_t)TARIFF_DATA_ADDR, &tariff_data, sizeof(tariff_data_s));
}

static void tariff_data_load(void)
{
    if(CRC16_CHK(&tariff_data, sizeof(tariff_data_s)) == FALSE)
    {
        nvm.read((uint32_t)TARIFF_DATA_ADDR, &tariff_data, sizeof(tariff_data_s));
        if(CRC16_CHK(&tariff_data, sizeof(tariff_data_s)) == FALSE)
        {
            tariff_data = tariff_default_data; 
            CRC16_CAL(&tariff_data, sizeof(tariff_data_s));
            nvm.write((uint32_t)TARIFF_DATA_ADDR, &tariff_data, sizeof(tariff_data_s));
        }
    }
}

/// @brief 根据当前时间，查找日表
/// @param now
/// @return
static const day_profile_s* day_profile_table_get(const clock_s* now)
{
    // static calendar_s         last_time = {0};    ///上次检查时间， 每小时检测一次
    uint8_t                   i, day_id, is_weekend, save;
    clock_s                   clock;
    const day_list_s         *day_list         = &activate->day_list;
    const week_list_s        *week_list        = &activate->week_list;
    const zone_list_s        *zone_list        = &activate->zone_list;
    const special_day_list_s *special_day_list = &activate->special_day_list; 

    // if(memcmp(&last_time, now, 5) == 0) return &day_list->day[tariff_data.cur_day_id];  // 每小时检测一次, 节省时间。 （没考虑备用套激活，屏蔽）
    // last_time = now->cale;

    day_id = 0xFF;
    save   = FALSE;
    /* 检查当前时间是否是节假日的时间 */
    for(i = 0; i < special_day_list->entry_num; i++)
    {
        uint16_t year;
        if(special_day_list->entry_num > TARIFF_HOLIDAY_NUM) break;
        if((memcmp(&now->year, &special_day_list->entry[i].date[0], 3) == 0) &&
           (special_day_list->entry[i].day_id < day_list->day_num))  break; // 找到匹配的节假日
    }
    if((i < special_day_list->entry_num) && (special_day_list->entry_num <= TARIFF_HOLIDAY_NUM))
    {
        day_id = special_day_list->entry[i].day_id;    // 找到节假日对应的day_id
        if(tariff_data.is_holiday == false) {tariff_data.is_holiday = true, save = true;}
        goto GET_DAY_ID;
    }
    else
    {
        if(tariff_data.is_holiday == true) {tariff_data.is_holiday = false, save = true;}
    }

    /// 根据周休日特征字，查找使用的日表序号
    is_weekend = false;
    if((week_list->day_id < day_list->day_num) && (week_list->week_character != 0xFF))
    {
    #if RTC_SUNDAY_IS_0
        if((week_list->sun == 0) && (now->week == 0)) is_weekend = true;
    #else
        if((week_list->sun == 0) && (now->week == 7)) is_weekend = true;
    #endif
        if((week_list->mon == 0) && (now->week == 1)) is_weekend = true;
        if((week_list->tue == 0) && (now->week == 2)) is_weekend = true;
        if((week_list->wed == 0) && (now->week == 3)) is_weekend = true;
        if((week_list->thu == 0) && (now->week == 4)) is_weekend = true;
        if((week_list->fri == 0) && (now->week == 5)) is_weekend = true;
        if((week_list->sat == 0) && (now->week == 6)) is_weekend = true;            
    }
    if(is_weekend)
    {
        day_id = week_list->day_id;
        if(tariff_data.is_weekend == false) {tariff_data.is_weekend = true, save = true;}
        goto GET_DAY_ID;
    }
    else
    {
        if(tariff_data.is_weekend == true) {tariff_data.is_weekend = false, save = true;}
    }

    /// 查找当前时间处于哪个时区，注意，时区表需按照顺序排列 !!!
    if(zone_list->zone_num)
    {
        for(i = 0; i < zone_list->zone_num; i++)
        {
            if((zone_list->zone[i].date[0] > now->month) || 
               ((zone_list->zone[i].date[0] >= now->month) && (zone_list->zone[i].date[1] > now->day))) break;
        }
        i = (i + zone_list->zone_num - 1) % zone_list->zone_num;
        if(tariff_data.cur_zone != i)
        {
            tariff_data.cur_zone = i, save = true;  
            tariff_state |= STUS_TF_ZONE_CHG;    /// 时区切换
            DBG_PRINTF(P_TARIFF, D, "\r\n STUS_TF_ZONE_CHG: %d", tariff_data.cur_zone);
        }
        if(zone_list->zone[i].day_id <= day_list->day_num) { day_id = zone_list->zone[i].day_id; }
    }

    /// 根据day_id, 找到day-profile中对应的日表
GET_DAY_ID:
    // 直接根据序号给出
    if(day_id <= day_list->day_num)
    {
        if(tariff_data.cur_day_id != day_id) 
        { 
            tariff_data.cur_day_id = day_id; save = true;  
            tariff_state |= STUS_TF_DAY_CHG;    /// 日表切换
            DBG_PRINTF(P_TARIFF, D, "\r\n STUS_TF_DAY_CHG:%d", day_id);
        }
        if(save) tariff_data_store();
        return &day_list->day[day_id?(day_id - 1):0];
    }
    if(save) {
        tariff_data_store(); 
        DBG_PRINTF(P_TARIFF, D, "\r\n is_weekend: %d, is_holiday: %d", tariff_data.is_weekend, tariff_data.is_holiday);
    }
    /* 未找到对应的日表 */
    return NULL;

    // 根据索引查找
    // for(i = 0; i < day_list->day_num; i++)
    // {
    //     if(day_id == day_list->day[i].day_id) break;
    // }
    // if(i >= day_list->day_num) return NULL;
    // return &day_list->day[i];
}

/// @brief 根据当前时间段和日表，查找对应的费率
/// @param now
/// @param day
/// @return
static uint8_t script_selector_scan(const clock_s* now, const day_profile_s* day)
{
    uint8_t  i, j, k;
    uint16_t s0, s1, s2, s3, s4;

    if(day == NULL) return tariff_default_data.cur_rate; //时段表未找到，返回默认费率

    /// 本算法可以无论时段的顺序排列查找当前费率
    s0 = get_msbdata16(&now->hour);
    s1 = 0;
    s3 = 0xFFFF;
    for(i = 0, j = 0xFF; i < day->schedule_num; i++)
    {
        s2 = get_msbdata16(day->action[i].start_time);
        if(s1 <= s2)    // 查找最大值开始时间
        {
            s1 = s2, k = i;
        }
        if(s0 >= s2)    // 查找最近的开始时间
        {
            s4 = s0 - s2;
            if(s4 <= s3) { s3 = s4, j = i; }
        }
    }
    if(j == 0xFF) j = k;
    DBG_PRINTF(P_TARIFF, D, "\r\n *** script_selector_scan: %d", j); // 打印找到的时段序号
    return day->action[j].script_selector;
}

void tariff_init(void)
{
    tariff_para_checkout();
    tariff_data_load();

    DBG_PRINTF(P_TARIFF, D, "\r\n ** tariff_init: tariff_para_s = %d",  sizeof(tariff_para_s));
    DBG_PRINTF(P_TARIFF, D, "\r\n ** tariff_init: TF_ACTIVATE_PARA_ADDR = %x",  TF_ACTIVATE_PARA_ADDR);
    DBG_PRINTF(P_TARIFF, D, "\r\n ** tariff_init: TF_PASSIVE_PARA_ADDR = %x",   TF_PASSIVE_PARA_ADDR);
    DBG_PRINTF(P_TARIFF, D, "\r\n ** tariff_init: TARIFF_DATA_ADDR = %x",       TARIFF_DATA_ADDR);
}


void tariff_refresh(void)
{
    uint8_t save = false;
    /* 检出费率参数 */
    tariff_para_checkout();
    tariff_data_load();

    /* 查询当前时钟是否有效 */
    if(!mclock.datetime->stus.invalid_value && !mclock.datetime->stus.doubtful_value)
    {
        clock_s activate_time = passive->calendar_para.zonel_activate_time;

        /// 备用套时区表激活判断
        if(mclock.is_valid(&activate_time) && mclock.compare(&activate_time) >= 0)
        {
            /// 备用套拷贝到当前套
            tariff_activate_para_checkin(member_offset(tariff_para_s, zone_list), &passive->zone_list, sizeof(zone_list_s));
           
            mclock.invalid_set(&activate_time);
            tariff_passive_para_checkin(member_offset(tariff_para_s, calendar_para.zonel_activate_time), &activate_time, sizeof(clock_s));
            tariff_state |= STUS_TF_SWITCH_ZONE;
            DBG_PRINTF(P_TARIFF, D, "\r\n *** STUS_TF_SWITCH_ZONE");
        }

        /// 备用套日时段表激活判断
        activate_time = passive->calendar_para.dayl_activate_time;
        if(mclock.is_valid(&activate_time) && mclock.compare(&activate_time) >= 0)
        {
            /// 备用套拷贝到当前套
            tariff_activate_para_checkin(member_offset(tariff_para_s, day_list), &passive->day_list, sizeof(day_list_s));
           
            mclock.invalid_set(&activate_time);
            tariff_passive_para_checkin(member_offset(tariff_para_s, calendar_para.zonel_activate_time), &activate_time, sizeof(clock_s));
            tariff_state |= STUS_TF_SWITCH_DAY_TAB;
            DBG_PRINTF(P_TARIFF, D, "\r\n *** STUS_TF_SWITCH_DAY_TAB");
        }

        /// 根据时区，周休日，节假日，日表，时段表，查找当前费率
        {
            uint8_t cur_rate = script_selector_scan(mclock.datetime, day_profile_table_get(mclock.datetime));
            if(tariff_data.cur_rate != cur_rate) 
            { 
                tariff_data.cur_rate = cur_rate; 
                tariff_state |= STUS_TF_RATE_CHG;    /// 费率切换
                save = true;
                DBG_PRINTF(P_TARIFF, D, "\r\n *** tariff_data.cur_rate:%d", tariff_data.cur_rate);
            }
        }
    }
    if(save) tariff_data_store();
}

uint8_t tariff_get_cur_rate(void)
{
    if(tariff_data.cur_rate == 0 || tariff_data.cur_rate > TARIFF_RATE_NUM) { tariff_data.cur_rate = tariff_default_data.cur_rate; }
    return tariff_data.cur_rate;
}

void tariff_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        /* 恢复Activity & Passive 费率默认配置 */
        tariff_activate_para_checkin(0, &tariff_default_para, sizeof(tariff_para_s));
        tariff_passive_para_checkin(0,  &tariff_default_para, sizeof(tariff_para_s));

        /* 清零数据 */
        tariff_data = tariff_default_data;
        CRC16_CAL(&tariff_data, sizeof(tariff_data_s));
        nvm.write((uint32_t)TARIFF_DATA_ADDR, &tariff_data, sizeof(tariff_data_s));

        tariff_state = 0;
        DBG_PRINTF(P_TARIFF, D, "\r\n *** tariff_reset");
    }
}

bool tariff_state_query(TF_STUS state)
{
    return boolof(state & tariff_state);
}

void tariff_state_clr(void)
{
    tariff_state = 0;
}

/// @brief 节假日表获取
/// @param list
/// @param is_passive
static void tariff_special_day_get(special_day_list_s *list, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(list, &para->special_day_list, sizeof(special_day_list_s));
}

/// @brief 节假日表设置
/// @param list
/// @param is_passive
/// @return
static bool tariff_special_day_set(special_day_list_s *list, uint8_t is_passive)
{
    if(is_passive)
    {
        tariff_state |= STUS_TF_PRG_SPEC_DAY;
        return tariff_passive_para_checkin(member_offset(tariff_para_s, special_day_list), list, sizeof(special_day_list_s));
    }
    else
    {
        return tariff_activate_para_checkin(member_offset(tariff_para_s, special_day_list), list, sizeof(special_day_list_s));
    }
}


/// @brief 时区表获取
/// @param list
/// @param is_passive
static void tariff_zone_profile_get(zone_list_s *list, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(list, &para->zone_list, sizeof(zone_list_s));
}

/// @brief 时区表设置
/// @param list
/// @param is_passive
/// @return
static bool tariff_zone_profile_set(zone_list_s *list, uint8_t is_passive)
{
    if(is_passive)
    {
        // tariff_state |= STUS_TF_PRG_SEASON;
        return tariff_passive_para_checkin(member_offset(tariff_para_s, zone_list), list, sizeof(zone_list_s));
    }
    else { return tariff_activate_para_checkin(member_offset(tariff_para_s, zone_list), list, sizeof(zone_list_s)); }
}

/// @brief 周休日信息获取
/// @param list
/// @param is_passive
static void tariff_week_profile_table_get(week_list_s *list, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(list, &para->week_list, sizeof(week_list_s));
}

/// @brief 周休日信息设置
/// @param list
/// @param is_passive
/// @return
static bool tariff_week_profile_table_set(week_list_s *list, uint8_t is_passive)
{
    if(is_passive)
    {
        tariff_state |= STUS_TF_PRG_WEEK;
        return tariff_passive_para_checkin(member_offset(tariff_para_s, week_list), list, sizeof(week_list_s));
    }
    else 
    { 
        return tariff_activate_para_checkin(member_offset(tariff_para_s, week_list), list, sizeof(week_list_s)); 
    }
}

/// @brief 日表获取
/// @param list
/// @param is_passive
static void tariff_day_profile_table_get(day_list_s *list, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(list, &para->day_list, sizeof(day_list_s));
}

/// @brief 获取费率数
/// @param  
/// @return 
uint8_t tariff_day_tf_num_get(void)
{
    return (activate->day_list.tf_num == 0 || activate->day_list.tf_num > TARIFF_RATE_NUM) ? TARIFF_RATE_NUM : activate->day_list.tf_num;
}

/// @brief  日表设置
/// @param list
/// @param is_passive
/// @return
static bool tariff_day_profile_table_set(day_list_s *list, uint8_t is_passive)
{
    if(is_passive)
    {
        tariff_state |= STUS_TF_PRG_DAY;
        return tariff_passive_para_checkin(member_offset(tariff_para_s, day_list), list, sizeof(day_list_s));
    }                                                              
    else { return tariff_activate_para_checkin(member_offset(tariff_para_s, day_list), list, sizeof(day_list_s)); }
}

/// @brief 备用套时区激活时间获取
/// @param time
/// @param is_passive
static void tariff_zone_activate_passive_calendar_time_get(clock_s *time, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(time, &para->calendar_para.zonel_activate_time, sizeof(clock_s));
}

/// @brief 备用套时区激活时间设置
/// @param time
/// @param is_passive
/// @return
static bool tariff_zone_activate_passive_calendar_time_set(clock_s *time, uint8_t is_passive)
{
    time->stus.invalid_value = 0;
    time->stus.doubtful_value = 0;
    time->second = 0;
    if(mclock.is_valid(time) == false) return false;
    if(is_passive)
    {
        return tariff_passive_para_checkin(member_offset(tariff_para_s, calendar_para.zonel_activate_time), time, sizeof(clock_s));
    }
    else
    {
        return tariff_activate_para_checkin(member_offset(tariff_para_s, calendar_para.zonel_activate_time), time, sizeof(clock_s));
    }
}

/// @brief 备用套日表激活时间获取
/// @param time
/// @param is_passive
static void tariff_day_activate_passive_calendar_time_get(clock_s *time, uint8_t is_passive)
{
    const tariff_para_s *para = is_passive ? passive : activate;
    memcpy(time, &para->calendar_para.dayl_activate_time, sizeof(clock_s));
}

/// @brief 备用套日表激活时间设置
/// @param time
/// @param is_passive
/// @return
static bool tariff_day_activate_passive_calendar_time_set(clock_s *time, uint8_t is_passive)
{
    time->stus.invalid_value = 0;
    time->stus.doubtful_value = 0;
    time->second = 0;
     if(mclock.is_valid(time) == false) return false;
    if(is_passive)
    {
        return tariff_passive_para_checkin(member_offset(tariff_para_s, calendar_para.dayl_activate_time), time, sizeof(clock_s));
    }
    else
    {
        return tariff_activate_para_checkin(member_offset(tariff_para_s, calendar_para.dayl_activate_time), time, sizeof(clock_s));
    }
}


/// @brief 声明电表费率子模块对象
const struct tariff_s tariff =
{
    .init            = tariff_init,
    .refresh         = tariff_refresh,
    .reset           = tariff_reset,
    .state_query     = tariff_state_query,
    .state_clr       = tariff_state_clr,
    .cur_rate_get    = tariff_get_cur_rate,

    .special_day_get                = tariff_special_day_get,
    .special_day_set                = tariff_special_day_set,
    .zone_profile_get               = tariff_zone_profile_get,
    .zone_profile_set               = tariff_zone_profile_set,
    .week_profile_table_get         = tariff_week_profile_table_get,
    .week_profile_table_set         = tariff_week_profile_table_set,
    .day_profile_table_get          = tariff_day_profile_table_get,
    .day_tf_num_get                 = tariff_day_tf_num_get,
    .day_profile_table_set          = tariff_day_profile_table_set,
    
    .zone_activate_passive_time_get = tariff_zone_activate_passive_calendar_time_get,
    .zone_activate_passive_time_set = tariff_zone_activate_passive_calendar_time_set,
    .day_activate_passive_time_get  = tariff_day_activate_passive_calendar_time_get,
    .day_activate_passive_time_set  = tariff_day_activate_passive_calendar_time_set,
};

