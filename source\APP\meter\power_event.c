/********************************************************************************
  * @file    power_event.c
  * <AUTHOR> @date    2024
  * @brief   电源，电网质量，负载事件处理
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "app.h"
#include "power_event.h"
#include "mic.h"
#include "utils.h"

    
#define PE_CRC16                    0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(PE_CRC16, struct, len)

#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(PE_CRC16, struct, len)

#define PE_PARA_ADDR         nvm_addr(NVM_PWR_EVT_PARA)
#define PE_DATA_ADDR         nvm_addr(NVM_PWR_EVT_DATA)
#define PE_PD_ADDR           nvm_addr(NVM_PWR_EVT_PD)

/* 掉电状态字 */
typedef union
{
    struct
    {
        uint8_t pwdn             :1; // 电表掉电
        uint8_t long_pwdn        :1; // 电表长掉电
        uint8_t pwdn_abnormal    :1; // 异常掉电
        uint8_t rst_abnormal     :1; // 异常重启
    };
    uint8_t byte;
}pwrdn_status_s;

extern const pe_para_s  pe_default_para; // 默认参数
static const pe_para_s *pe_para_runing;  // 运行时参数 指向code flash

static pe_data_s   pe_data;     // 运行时数据
static pe_pwrdn_s  pe_pwrdn;    // 掉电保存数据
static pe_status_s pe_ins_stus; // 瞬时状态
static TYPE_PE_EVT_OUT pe_evt_out[PE_EVT_OUT_LEN]; // 事件输出
static pwrdn_status_s  power_down_stus; // 掉电状态

#if EVENT_LOSS_VOL_EN
static uint32_t vol_loss_ah[3][3];
#endif
#if EVENT_LOW_VOL_EN
static uint32_t vol_low_ah[3][3];
#endif
#if EVENT_OVR_VOL_EN
static uint32_t vol_ovr_ah[3][3];
#endif
#if EVENT_MISS_VOL_EN
static uint32_t vol_miss_ah[3][3];
#endif


#define pe_out_set_ph(ph,mask) st(pe_evt_out[mask&0x0007] |= ((mask & 0xFFF8) << ph) + (mask & 0x0007);)
#define pe_out_set(mask)       st(pe_evt_out[mask&0x0007] |= mask;)


static void pe_para_load(void)
{
    pe_para_runing = (pe_para_s *)PE_PARA_ADDR;
    if(CRC16_CHK(pe_para_runing, sizeof(pe_para_s)) == false)
    {
        pe_para_runing = &pe_default_para; // 校验失败，使用默认参数
    }
}

static bool pe_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    pe_para_s para;
    if(ofst != 0) memcpy(&para, pe_para_runing, sizeof(pe_para_s));
    memcpy((uint8_t *)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(pe_para_s));
    pe_para_runing = (const pe_para_s*)PE_PARA_ADDR;
    return nvm.write((uint32_t)pe_para_runing, &para, sizeof(pe_para_s));
}

/// @brief 保存数据
/// @param typ 0-运行数据，1-掉电数据
/// @param wr_nvm ture-保存到nvm，false-不保存到nvm
/// @return 
static bool pe_data_save(uint8_t typ, bool wr_nvm)
{
    switch (typ)
    {
    case 0:
        CRC16_CAL(&pe_data, sizeof(pe_data_s));
        return wr_nvm ? nvm.write((uint32_t)PE_DATA_ADDR, &pe_data, sizeof(pe_data_s)) : true;
    case 1:
        CRC16_CAL(&pe_pwrdn, sizeof(pe_pwrdn_s));
        return wr_nvm ? nvm.write((uint32_t)PE_PD_ADDR, &pe_pwrdn, sizeof(pe_pwrdn_s)) : true;
    }
    return false;
}

static void pe_data_load(void)
{
    if(CRC16_CHK(&pe_data, sizeof(pe_data_s)) == false)
    {
        nvm.read((uint32_t)PE_DATA_ADDR, &pe_data, sizeof(pe_data_s));
        if(CRC16_CHK(&pe_data, sizeof(pe_data_s)) == false)
        {
            memset(&pe_data, 0, sizeof(pe_data_s)); // 读取失败，清零
            pe_data_save(0, false); // 保存到nvm
        }
    }

    if(CRC16_CHK(&pe_pwrdn, sizeof(pe_pwrdn_s)) == false)
    {
        nvm.read((uint32_t)PE_PD_ADDR, &pe_pwrdn, sizeof(pe_pwrdn_s));
        if(CRC16_CHK(&pe_pwrdn, sizeof(pe_pwrdn_s)) == false)
        {
            memset(&pe_pwrdn, 0, sizeof(pe_pwrdn_s));   // 读取失败，清零
            clock_s time;
            mclock.pdtime_get(&time);           // 掉电时钟保存异常，则获取掉电时钟备份
            pe_pwrdn.pwdn_clock = *mclock.datetime;
            power_down_stus.pwdn_abnormal = 1;  // 异常掉电数据保存恢复
            pe_data_save(1, true);
        }
    }
}

#if EVENT_ALL_LOSS_VOL_EN
/// @brief  全失压事件检测
/// @param  
/// @return 
static bool pe_all_loss_voltage_detect(void)
{
    static uint8_t pe_time = 0;
    bool   save_data = false;
    bool   save_pd   = false;

    pe_ins_stus.v.v_all_miss = 0;
    if(pe_para_runing->vol_all_mis_thd_v == 0) 
    {
        pe_data.confirm.v.v_all_miss = 0; 
        return false;
    }

    {
        if(((mic.ins->vrms[0] < pe_para_runing->vol_all_mis_thd_v / 10.0) &&          \
            (mic.ins->vrms[1] < pe_para_runing->vol_all_mis_thd_v / 10.0) &&          \
            (mic.ins->vrms[2] < pe_para_runing->vol_all_mis_thd_v / 10.0))&&          \
           ((fabs(mic.ins->irms[0]) > pe_para_runing->vol_all_mis_thd_i / 10000.0) || \
            (fabs(mic.ins->irms[1]) > pe_para_runing->vol_all_mis_thd_i / 10000.0) || \
            (fabs(mic.ins->irms[2]) > pe_para_runing->vol_all_mis_thd_i / 10000.0)))
        {
            //条件满足
            pe_ins_stus.v.v_all_miss = 1;
        }

        if(pe_ins_stus.v.v_all_miss)  
        {
            if((pe_data.confirm.v.v_all_miss) == 0) 
            {
                pe_time++;
                if(pe_time >= pe_para_runing->vol_all_mis_thd_time)  //事件开始
                {
                    float current;
                    pe_data.confirm.v.v_all_miss = 1;
                    pe_pwrdn.all_miss_time_s = mclock.datetime->u32datetime;
                    pe_pwrdn.all_miss_cnt++;
                    current = max(fabs(mic.ins->irms[0]), fabs(mic.ins->irms[1]));  // 取最大电流
                    current = max(current, fabs(mic.ins->irms[2]));
                    pe_pwrdn.all_miss_i = (uint32_t)(current * 1000); // 0.001A
                    pe_time   = 0;
                    save_data = true;
                    save_pd   = true;
                    pe_out_set(EVT_ALL_LOS_V_S);
                }
            }else pe_time = 0;
        }
        else
        {
            if(pe_data.confirm.v.v_all_miss)
            {
                pe_time++;
                if(pe_time >= pe_para_runing->vol_all_mis_thd_time)  //事件结束
                {
                    pe_data.confirm.v.v_all_miss  = 0;
                    pe_pwrdn.all_miss_time_e = mclock.datetime->u32datetime;
                    pe_time   = 0;
                    save_data = true;
                    save_pd   = true;
                    pe_out_set(EVT_ALL_LOS_V_E);
                }
            }
            else pe_time = 0;
        }
    }

    if(pe_data.confirm.v.v_all_miss)
    {
        pe_pwrdn.all_miss_time_cnt++;
        save_pd   = true;
    }

    // if(save_data) pe_data_save(0, true);
    if(save_pd)  pe_data_save(1, false);
    return save_data;
}

static void pe_all_loss_voltage_detect_pwroff(void)
{
        if(((fabs(mic.ins->irms[0]) > pe_para_runing->vol_all_mis_thd_i / 10000.0) || \
            (fabs(mic.ins->irms[1]) > pe_para_runing->vol_all_mis_thd_i / 10000.0) || \
            (fabs(mic.ins->irms[2]) > pe_para_runing->vol_all_mis_thd_i / 10000.0)))
        {
            //条件满足
            float current;
            pe_pwrdn.all_miss_pwroff = 1;
            pe_pwrdn.all_miss_time_s = mclock.datetime->u32datetime;
            pe_pwrdn.all_miss_cnt++;
            current = max(fabs(mic.ins->irms[0]), fabs(mic.ins->irms[1]));  // 取最大电流
            current = max(current, fabs(mic.ins->irms[2]));
            pe_pwrdn.all_miss_i = (uint32_t)(current * 1000); // 0.001A
            pe_data_save(1, true);
        }
}

/// @brief 全失压事件上电初始化
/// @param pwrdn_time 掉电持续时间，单位秒
/// @return 
static void pe_all_loss_voltage_init(int32_t pwrdn_time)
{
    if(!pe_data.confirm.v.v_all_miss && pe_pwrdn.all_miss_pwroff)
    {
        pe_data.confirm.v.v_all_miss = 1;
        pe_pwrdn.all_miss_time_cnt += (pwrdn_time > 0) ? pwrdn_time : 0; /// 全失压事件持续时间累加
        pe_out_set(EVT_ALL_LOS_V_S);
        pe_data_save(0, true);
    }
    pe_pwrdn.all_miss_pwroff = 0;
    pe_data_save(1, false);
}
#endif

#if EVENT_LOSS_VOL_EN  
/// @brief 分相失压事件检测, 全失压发生时，分相失压事件结束
/// @param 
/// @return true - 保存数据到nvm，false - 不保存数据到nvm
static bool pe_loss_voltage_detect(void)
{
    static uint8_t pe_time[3] = {0};
    uint8_t ph;
    bool    save_data = false;
    bool    save_pd   = false;

    /// 全失压发生时，分相失压事件结束
    if(pe_data.confirm.v.v_all_miss)
    {
        if(pe_data.confirm.v.vol_loss)
        {
            for(ph = 0; ph < 3; ph++)
            {
                if(pe_data.confirm.v.vol_loss & bitmask(ph))
                {
                    pe_data.confirm.v.vol_loss &= ~bitmask(ph);
                    pe_pwrdn.vol_loss_time_e = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_loss_ah, 0, sizeof(vol_loss_ah));
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_LOS_V_A_E);
                }
            }
            if(save_pd)   pe_data_save(1, false);
        }
        return save_data;
    }
    /// 分相失压事件检测
    pe_ins_stus.v.vol_loss = 0;
    if(pe_para_runing->vol_loss_thd_v_h == 0) 
    {
        pe_data.confirm.v.vol_loss = 0; 
        return false;
    }
#if defined(POLYPHASE_METER)
    for(ph = 0; ph < 3; ph++)
#else
    ph = 0;
#endif
    {
        if((mic.ins->vrms[ph] < pe_para_runing->vol_loss_thd_v_h / 10.0) && mic.ins->irms[ph] > pe_para_runing->vol_loss_thd_i / 10000.0)
        {
            //失压条件满足
            pe_ins_stus.v.vol_loss |= bitmask(ph);
        }

        if(pe_ins_stus.v.vol_loss & bitmask(ph))  
        {
            if((pe_data.confirm.v.vol_loss & bitmask(ph)) == 0) 
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_loss_thd_time)  //事件开始
                {
                    pe_data.confirm.v.vol_loss |= bitmask(ph);
                    pe_pwrdn.vol_loss_time_s = mclock.datetime->u32datetime;
                    pe_pwrdn.vol_loss_cnt[ph]++;
                    pe_pwrdn.vol_loss_cnt[0]++;
                    memset(vol_loss_ah, 0, sizeof(vol_loss_ah));
                    memset(pe_pwrdn.vol_loss_ah, 0, sizeof(pe_pwrdn.vol_loss_ah));//清除上一次记录的值
                    pe_time[ph] = 0;
                    save_data   = true;
                    save_pd     = true;
                    pe_out_set_ph(ph, EVT_LOS_V_A_S);
                }
            }else pe_time[ph] = 0; 
        }
        else
        {
            if(pe_data.confirm.v.vol_loss & bitmask(ph))
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_loss_thd_time)  //事件结束
                {
                    pe_data.confirm.v.vol_loss &= ~bitmask(ph);
                    pe_pwrdn.vol_loss_time_e = mclock.datetime->u32datetime;
                    memset(vol_loss_ah, 0, sizeof(vol_loss_ah));
                    pe_time[ph] = 0;
                    save_data   = true;
                    save_pd     = true;
                    pe_out_set_ph(ph, EVT_LOS_V_A_E);
                }
            }
            else pe_time[ph] = 0;
        }

        if(pe_data.confirm.v.vol_loss & bitmask(ph)) //事件持续
        {
            pe_pwrdn.vol_loss_time_cnt[ph + 1]++;
            for(uint8_t i = 0; i < 3; i++)
            {
                vol_loss_ah[ph][i] += (uint32_t)(fabs(mic.ins->irms[i]) * 1000);   // 0.001A
                while(vol_loss_ah[ph][i] >= (3600 * 1000 / 100)) // 计算安时，保留两位小数 0.01A.S 积分
                {
                    vol_loss_ah[ph][i] -= (3600 * 1000 / 100);
                    pe_pwrdn.vol_loss_ah[ph][i]++;
                }
            }
            save_pd = true;
        }
    }

    if(pe_data.confirm.v.vol_loss)
    {
        pe_pwrdn.vol_loss_time_cnt[0]++;
        save_pd = true;
    }

    // if(save_data) pe_data_save(0, true);
    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif
#if EVENT_LOW_VOL_EN 
/// @brief 欠压事件检测
/// @param 
/// @return true - 保存数据到nvm，false - 不保存数据到nvm
static bool pe_low_voltage_detect(void)
{
    static uint8_t pe_time[3] = {0};
    uint8_t ph;
    bool    save_data = false;
    bool    save_pd   = false;

    pe_ins_stus.v.vol_low = 0;
    if(pe_para_runing->vol_low_thd_v == 0) 
    {
        pe_data.confirm.v.vol_low = 0; 
        return false;
    }
#if defined(POLYPHASE_METER)
    for(ph = 0; ph < 3; ph++)
#else
    ph = 0;
#endif
    {
        if((mic.ins->vrms[ph] < pe_para_runing->vol_low_thd_v / 10.0))
        {
            //条件满足
            pe_ins_stus.v.vol_low |= bitmask(ph);
        }
        if(pe_ins_stus.v.vol_low & bitmask(ph))  
        {
            if((pe_data.confirm.v.vol_low & bitmask(ph)) == 0) 
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_low_thd_time)  //事件开始
                {
                    pe_data.confirm.v.vol_low |= bitmask(ph);
                    pe_pwrdn.vol_low_time_s = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_low_ah, 0, sizeof(vol_low_ah));
                    memset(pe_pwrdn.vol_low_ah, 0, sizeof(pe_pwrdn.vol_low_ah));//清除上一次记录的值
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_LOW_V_A_S);
                }
            }else pe_time[ph] = 0;
        }
        else
        {
            if(pe_data.confirm.v.vol_low & bitmask(ph))
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_low_thd_time)  //事件结束
                {
                    pe_data.confirm.v.vol_low &= ~bitmask(ph);
                    pe_pwrdn.vol_low_time_e = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_low_ah, 0, sizeof(vol_low_ah));
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_LOW_V_A_E);
                }
            }
            else pe_time[ph] = 0;
        }

        if(pe_data.confirm.v.vol_low & bitmask(ph)) //事件持续
        {
            pe_pwrdn.vol_low_time_cnt[ph + 1]++;
            for(uint8_t i = 0; i < 3; i++)
            {
                vol_low_ah[ph][i] += (uint32_t)(fabs(mic.ins->irms[i]) * 1000);   // 0.001A
                while(vol_low_ah[ph][i] >= (3600 * 1000 / 100)) // 计算安时，保留两位小数
                {
                    vol_low_ah[ph][i] -= (3600 * 1000 / 100);
                    pe_pwrdn.vol_low_ah[ph][i]++;
                }
            }
            save_pd   = true;
        }
    }

    if(pe_data.confirm.v.vol_low)
    {
        pe_pwrdn.vol_low_time_cnt[0]++;
        save_pd   = true;
    }

    // if(save_data) pe_data_save(0, true);
    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif
#if EVENT_OVR_VOL_EN 
/// @brief 过压事件检测
/// @param 
/// @return true - 保存数据到nvm，false - 不保存数据到nvm
static bool pe_over_voltage_detect(void)
{
    static uint8_t pe_time[3] = {0};
    uint8_t ph;
    bool    save_data = false;
    bool    save_pd   = false;

    pe_ins_stus.v.vol_ovr = 0;
    if(pe_para_runing->vol_ovr_thd_v == 0) 
    {
        pe_data.confirm.v.vol_ovr = 0; 
        return false;
    }
#if defined(POLYPHASE_METER)
    for(ph = 0; ph < 3; ph++)
#else
    ph = 0;
#endif
    {
        if((mic.ins->vrms[ph] > pe_para_runing->vol_ovr_thd_v / 10.0))
        {
            //条件满足
            pe_ins_stus.v.vol_ovr |= bitmask(ph);
        }
        if(pe_ins_stus.v.vol_ovr & bitmask(ph))  
        {
            if((pe_data.confirm.v.vol_ovr & bitmask(ph)) == 0) 
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_ovr_thd_time)  //事件开始
                {
                    pe_data.confirm.v.vol_ovr |= bitmask(ph);
                    pe_pwrdn.vol_ovr_time_s = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_ovr_ah, 0, sizeof(vol_ovr_ah));
                    memset(pe_pwrdn.vol_ovr_ah, 0, sizeof(pe_pwrdn.vol_ovr_ah));//清除上一次记录的值
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_OVR_V_A_S);
                }
            }else pe_time[ph] = 0;
        }
        else
        {
            if(pe_data.confirm.v.vol_ovr & bitmask(ph))
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_ovr_thd_time)  //事件结束
                {
                    pe_data.confirm.v.vol_ovr &= ~bitmask(ph);
                    pe_pwrdn.vol_ovr_time_e = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_ovr_ah, 0, sizeof(vol_ovr_ah));
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_OVR_V_A_E);
                }
            }
            else pe_time[ph] = 0;
        }

        if(pe_data.confirm.v.vol_ovr & bitmask(ph)) //事件持续
        {
            pe_pwrdn.vol_ovr_time_cnt[ph + 1]++;
            for(uint8_t i = 0; i < 3; i++)
            {
                vol_ovr_ah[ph][i] += (uint32_t)(fabs(mic.ins->irms[i]) * 1000);   // 0.001A
                while(vol_ovr_ah[ph][i] >= (3600 * 1000 / 100)) // 计算安时，保留两位小数
                {
                    vol_ovr_ah[ph][i] -= (3600 * 1000 / 100);
                    pe_pwrdn.vol_ovr_ah[ph][i]++;
                }
            }
            save_pd   = true;
        }
    }

    if(pe_data.confirm.v.vol_ovr)
    {
        pe_pwrdn.vol_ovr_time_cnt[0]++;
        save_pd   = true;
    }

    // if(save_data) pe_data_save(0, true);
    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif
#if EVENT_MISS_VOL_EN
/// @brief 断相 事件检测
/// @param 
/// @return true - 保存数据到nvm，false - 不保存数据到nvm
static bool pe_ph_miss_detect(void)
{
    static uint8_t pe_time[3] = {0};
    uint8_t ph;
    bool    save_data = false;
    bool    save_pd   = false;

    pe_ins_stus.v.ph_miss = 0;
    if(pe_para_runing->vol_miss_thd_v == 0) 
    {
        pe_data.confirm.v.ph_miss = 0; 
        return false;
    }
#if defined(POLYPHASE_METER)
    for(ph = 0; ph < 3; ph++)
#else
    ph = 0;
#endif
    {
        if((mic.ins->vrms[ph] < pe_para_runing->vol_miss_thd_v / 10.0) && (mic.ins->irms[ph] < pe_para_runing->vol_miss_thd_i / 10000.0))
        {
            //条件满足
            pe_ins_stus.v.ph_miss |= bitmask(ph);
        }
        if(pe_ins_stus.v.ph_miss & bitmask(ph))  
        {
            if((pe_data.confirm.v.ph_miss & bitmask(ph)) == 0) 
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_miss_thd_time)  //事件开始
                {
                    pe_data.confirm.v.ph_miss |= bitmask(ph);
                    pe_pwrdn.ph_miss_time_s = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_miss_ah, 0, sizeof(vol_miss_ah));
                    memset(pe_pwrdn.ph_miss_ah, 0, sizeof(pe_pwrdn.ph_miss_ah));//清除上一次记录的值
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_MIS_PH_A_S);
                }
            }else pe_time[ph] = 0;
        }
        else
        {
            if(pe_data.confirm.v.ph_miss & bitmask(ph))
            {
                pe_time[ph]++;
                if(pe_time[ph] >= pe_para_runing->vol_ovr_thd_time)  //事件结束
                {
                    pe_data.confirm.v.ph_miss &= ~bitmask(ph);
                    pe_pwrdn.ph_miss_time_e = mclock.datetime->u32datetime;
                    pe_time[ph] = 0;
                    memset(vol_miss_ah, 0, sizeof(vol_miss_ah));
                    save_data = true;
                    save_pd   = true;
                    pe_out_set_ph(ph, EVT_MIS_PH_A_E);
                }
            }
            else pe_time[ph] = 0;
        }

        if(pe_data.confirm.v.ph_miss & bitmask(ph)) //事件持续
        {
            pe_pwrdn.ph_miss_time_cnt[ph + 1]++;
            for(uint8_t i = 0; i < 3; i++)
            {
                vol_miss_ah[ph][i] += (uint32_t)(fabs(mic.ins->irms[i]) * 1000);   // 0.001A
                while(vol_miss_ah[ph][i] >= (3600 * 1000 / 100)) // 计算安时，保留两位小数
                {
                    vol_miss_ah[ph][i] -= (3600 * 1000 / 100);
                    pe_pwrdn.ph_miss_ah[ph][i]++;
                }
            }
            save_pd   = true;
        }
    }

    if(pe_data.confirm.v.ph_miss)
    {
        pe_pwrdn.ph_miss_time_cnt[0]++;
        save_pd   = true;
    }

    // if(save_data) pe_data_save(0, true);
    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif

#if EVENT_V_REV_SQR_EN
/// @brief 电压逆向序事件检测
/// @param  
/// @return 
static bool pe_v_rev_sqr_detect(void)
{
    static uint8_t pe_time = 0;
    bool    save_data = false;
    bool    save_pd   = false;

    pe_ins_stus.v.vol_rev_seq = mic.ins->stus.seq_rev;

    if(pe_ins_stus.v.vol_rev_seq)
    {
        if((pe_data.confirm.v.vol_rev_seq == 0)) 
        {
            pe_time++;
            if(pe_time >= pe_para_runing->vol_rev_sqr_thd_time)  //事件开始
            {
                pe_data.confirm.v.vol_rev_seq = 1;
                pe_pwrdn.vol_rev_sqr_time_s = mclock.datetime->u32datetime;
                pe_time = 0;
                save_data = true;
                save_pd   = true;
                pe_out_set(EVT_REV_V_SEQ_S);
            }
        }
        else pe_time = 0;
    }
    else
    {
        if(pe_data.confirm.v.vol_rev_seq)
        {
            pe_time++;
            if(pe_time >= pe_para_runing->vol_rev_sqr_thd_time)  //事件结束
            {
                pe_data.confirm.v.vol_rev_seq = 0;
                pe_pwrdn.vol_rev_sqr_time_e = mclock.datetime->u32datetime;
                pe_time = 0;
                save_data = true;
                save_pd   = true;
                pe_out_set(EVT_REV_V_SEQ_E);
            }
            else pe_time = 0;
        }
    }
    if(pe_data.confirm.v.vol_rev_seq) //事件持续
    {
        pe_pwrdn.vol_rev_sqr_time_cnt++;
        save_pd   = true;
    }

    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif


#if EVENT_I_REV_SQR_EN
/// @brief 电流逆向序事件检测
/// @param  
/// @return 
static bool pe_i_rev_sqr_detect(void)
{
    static uint8_t pe_time = 0;
    bool    save_data = false;
    bool    save_pd   = false;

    pe_ins_stus.i.i_rev_seq = mic.ins->stus.i_seq_rev;

    if(pe_ins_stus.i.i_rev_seq)
    {
        if((pe_data.confirm.i.i_rev_seq == 0)) 
        {
            pe_time++;
            if(pe_time >= pe_para_runing->i_rev_sqr_thd_time)  //事件开始
            {
                pe_data.confirm.i.i_rev_seq = 1;
                pe_pwrdn.i_rev_sqr_time_s = mclock.datetime->u32datetime;
                pe_time = 0;
                save_data = true;
                save_pd   = true;
                pe_out_set(EVT_REV_I_SEQ_S);
            }
        }
        else pe_time = 0;
    }
    else
    {
        if(pe_data.confirm.i.i_rev_seq)
        {
            pe_time++;
            if(pe_time >= pe_para_runing->i_rev_sqr_thd_time)  //事件结束
            {
                pe_data.confirm.i.i_rev_seq = 0;
                pe_pwrdn.i_rev_sqr_time_e   = mclock.datetime->u32datetime;
                pe_time = 0;
                save_data = true;
                save_pd   = true;
                pe_out_set(EVT_REV_I_SEQ_E);
            }
            else pe_time = 0;
        }
    }
    if(pe_data.confirm.i.i_rev_seq) //事件持续
    {
        pe_pwrdn.i_rev_sqr_time_cnt++;
        save_pd   = true;
    }

    if(save_pd)   pe_data_save(1, false);
    return save_data;
}
#endif


/// @brief 掉电上电事件处理
/// @param  
static void pe_power_down_event_process(void)
{
    int32_t pwdn_duration = PQ_PWDN_TIME_THD + 1;

    if(pe_pwrdn.pwdn_state != STUS_PWR_DOWN_CNF) // 没有进入掉电保存状态
    {
        pe_pwrdn.pwdn_state = STUS_PWR_ON_CNF;
        if(!power_down_stus.pwdn_abnormal) // 掉电保存数据正常，非掉电保存异常引起
        {
            power_down_stus.rst_abnormal = 1;
            pe_data.confirm.v.pwdn = false;
            pe_out_set(EVT_PWDN_ABNORMAL); //异常重启
            pe_data_save(0, true);
            pe_data_save(1, false);
            return;
        }
    }
    else
    {
        pe_pwrdn.pwdn_state = STUS_PWR_ON_CNF;
        pwdn_duration = mclock.diff_value(mclock.datetime, &pe_pwrdn.pwdn_clock);
    }

    /* 掉电事件检测 */
    {
        power_down_stus.pwdn = true;
        pe_data.confirm.v.pwdn = true;
        pe_pwrdn.pwon_clock = *mclock.datetime;
        // pe_data_save(1, false);
    }

    /* 记录掉电事件 */
    if(power_down_stus.pwdn)
    {
        pe_out_set(EVT_PWR_DOWN_S);
        pe_out_set(EVT_PWR_DOWN_E);
        pe_pwrdn.cum_pwdn_time += (pwdn_duration > 0) ? pwdn_duration : 0;
        pe_pwrdn.cum_pwdn_cnt++;       // 累计掉电次数

        /* 记录掉电事件后，置位掉电时标为非法制，用于判断下次掉电是否异常 */
        pe_pwrdn.pwdn_clock.stus.value = 0xFF;
    }
    
    /// 这里放置一些事件上电初始化的操作,主要累加时间
#if EVENT_ALL_LOSS_VOL_EN
    pe_all_loss_voltage_init(pwdn_duration);
#endif
    power_down_stus.byte = 0;
    pe_data_save(0, true);
    pe_data_save(1, true);
}

/// @brief 事件检测
/// @param  
static void pe_event_detect(void)
{
    bool save_data = false;
#if EVENT_ALL_LOSS_VOL_EN
    save_data |= pe_all_loss_voltage_detect();
#endif
#if EVENT_LOSS_VOL_EN
    save_data |= pe_loss_voltage_detect(); ///放在全失压检测后面
#endif
#if EVENT_LOW_VOL_EN
    save_data |= pe_low_voltage_detect();
#endif
#if EVENT_OVR_VOL_EN
    save_data |= pe_over_voltage_detect();
#endif
#if EVENT_MISS_VOL_EN
    save_data |= pe_ph_miss_detect();
#endif
    if(save_data) pe_data_save(0, true);
}


/// 以下为接口函数

void pe_init(void)
{
    pe_para_load();
    pe_data_load();
}

void pe_second_run(void)
{
    static uint16_t pwron_time;
    bool save_data = false;

    /// 参数加载
    pe_para_load();
    pe_data_load();

    if(pwron_time < 10) pwron_time++;
    if(pwron_time == PQ_PWON_TIME_THD)
    {
        // 上电事件处理放这里
        pe_power_down_event_process();
    }

    if(pwron_time > 3) // 上电延时，防止因上电时计量值不准导致记录错误
    {
        pe_pwrdn.cum_pwon_time++; /// 累计上电时间
        /// 事件检测
        pe_event_detect();
        pe_data_save(1, false);
    }

    // if(save_data) pe_data_save(1, false);
}

bool pe_state_query(TYPE_PE_EVT_OUT state)
{
    uint8_t idx = (uint8_t)state & 0x07;
    state &= 0xFFF8;
    return boolof(pe_evt_out[idx] & state);
}

void pe_state_clr(void)
{
    memset(pe_evt_out, 0, sizeof(pe_evt_out));
}

/// @brief 掉电保存
/// @param  
void pe_power_down_save(void)
{
    if(pe_pwrdn.pwdn_state != STUS_PWR_ON_CNF) return; // 非上电状态直接不用保存

    if((boot_information)->mode != MODE_RUNING_ISP &&
       (boot_information)->mode != MODE_RUNING_IAP) // 升级触发的数据保存不记录掉电事件
    {
        pe_pwrdn.pwdn_state = STUS_PWR_DOWN_CNF;
        pe_pwrdn.pwdn_clock = *mclock.datetime;
    }
    pe_data_save(1, true);
}

void pe_power_off_init(void)
{
#if EVENT_ALL_LOSS_VOL_EN
    pe_para_load();
    pe_data_load();

    if((pe_para_runing->vol_all_mis_thd_v != 0) && (pe_data.confirm.v.v_all_miss == 0))
    {
        mic.poweroff_callback(pe_all_loss_voltage_detect_pwroff);
    }
    else
    {
        mic.poweroff_callback(NULL);
    }
#endif    
}

/// @brief 获取掉电时间
void pe_power_down_time_get(clock_s *time)
{
    *time = pe_pwrdn.pwdn_clock;
}

/// @brief  获取上电时间
void pe_power_on_time_get(clock_s* time)
{
    *time = pe_pwrdn.pwon_clock;
}

/// @brief 获取掉电数据
pe_pwrdn_s *pe_pwrdn_get(void)
{
    return &pe_pwrdn;
}

/// @brief 获取运行数据
/// @param  
/// @return 
pe_data_s *pe_data_get(void)
{
    return &pe_data;
}

/// @brief 设置参数
/// @param  
/// @return 
bool pe_para_set(uint16_t ofst, const void* val, uint16_t len)
{
    return pe_para_save(ofst, val, len);
}

/// @brief 获取参数
/// @param  
/// @return 
const pe_para_s *pe_para_get(void)
{
    return pe_para_runing;
}

/// @brief 获取状态
/// @param mode 0:ins_stus 1:confirm
/// @return 
pe_status_s *pe_status_get(uint8_t mode)
{
    if (mode == 0)
    {
        return &pe_ins_stus;
    }
    else
    {
        return &pe_data.confirm;
    }
}

void pe_reset(uint8 type)
{
    if(type & SYS_PARA_RESET)
    {
        pe_para_save(0, &pe_default_para, sizeof(pe_para_s));
    }

    if(type & SYS_DATA_RESET)
    {
        memset(&pe_data, 0, sizeof(pe_data_s));
        pe_data_save(0, boolof(type != SYS_GLOBAL_RESET));

        memset(&pe_pwrdn, 0, sizeof(pe_pwrdn_s));
        pe_pwrdn.pwdn_clock.stus.value = 0x01;
        pe_pwrdn.pwdn_state = STUS_PWR_ON_CNF;
        pe_data_save(1, boolof(type != SYS_GLOBAL_RESET));

        memset(pe_evt_out, 0, sizeof(pe_evt_out));
        memset(&pe_ins_stus, 0, sizeof(pe_ins_stus));

        power_down_stus.byte = 0;
    }
}


///@ 电量事件检测模块接口
const struct power_event_s power_event = 
{
    .reset              = pe_reset,
    .para_set           = pe_para_set,
    .para_get           = pe_para_get,
    .data_get           = pe_data_get,
    .status_get         = pe_status_get,
    .pwrdn_data_get     = pe_pwrdn_get,
    .state_query        = pe_state_query,
    .state_clr          = pe_state_clr,
    .power_down_time_get= pe_power_down_time_get,
    .power_on_time_get  = pe_power_on_time_get,
};


///@ 电量事件检测模块任务
const struct app_task_t power_event_task = 
{
   .init            = pe_init,
   .second_run      = pe_second_run,
   .power_down_save = pe_power_down_save,
   .power_off_init  = pe_power_off_init,
};


//end of file
