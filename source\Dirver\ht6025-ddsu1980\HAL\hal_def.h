/*
 * @Author: 
 * @Date: 2024-12-17 08:58:06
 * @LastEditTime: 2025-01-06 17:04:56
 * @Description: 
 */
/********************************************************************************
  * @file    hal_def.h
  * <AUTHOR> 
  * @version V1.0
  * @date    2024-08-05
  * @brief   硬件抽象层定义. 包含设备、中断向量头文件。定义复位系统及看门狗宏，定义临界开关中断宏等。
  * @note  
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
  *
  ******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

/** @addtogroup HAL-HT6025
  * @{
  */

/** @addtogroup Hardware-Abstract-define
  * @{
  */

#ifndef __HAL_DEF_H
#define __HAL_DEF_H

/* Includes ------------------------------------------------------------------*/
#include "ht6xxx_lib.h"
#include "typedef.h"
#include "boot_api.h"
#include "intvec_tab.h" 
     
/* ---------------------- IAR Compiler ---------------------- */
#ifdef __IAR_SYSTEMS_ICC__
#include <intrinsics.h>
#define HAL_COMPILER_IAR

/* ---------------------- ARM Compiler ---------------------- */
#elif defined ( __CC_ARM )
typedef unsigned long __istate_t;
#define __enable_interrupt()        __enable_irq()
#define __disable_interrupt()       __disable_irq()
#define __get_interrupt_state()     __get_PRIMASK()
#define __set_interrupt_state(x)    __set_PRIMASK(x)
#define __no_operation()            __nop()
#define __root
#define __INLINE                    __attribute__((always_inline)) static __inline
#define __no_init                   __attribute__((zero_init))
/* ------------------ Unrecognized Compiler ------------------ */
#else
#pragma message("ERROR: Unrecognized compiler.")
#endif

//========================================================

/* 定义看门狗溢出时间周期 */
#define HAL_WDG_TIMEOUT_MS          5000  /* watch dog periods, unit:ms */

/* 定义开关中断以及临界代码宏 */
#define HAL_ENABLE_INTERRUPTS()      __enable_interrupt()
#define HAL_DISABLE_INTERRUPTS()     st( __disable_interrupt(); __no_operation();)
#define HAL_INTERRUPTS_ARE_ENABLED() !__get_interrupt_state()
#define HAL_ENTER_CRITICAL_SECTION(x) \
   st( x = __get_interrupt_state();  HAL_DISABLE_INTERRUPTS(); )
#define HAL_EXIT_CRITICAL_SECTION(x) \
   st( __set_interrupt_state(x); )
#define HAL_CRITICAL_STATEMENT(x) \
   st( __istate_t s; HAL_ENTER_CRITICAL_SECTION(s); x; HAL_EXIT_CRITICAL_SECTION(s); )

/* 定义软件复位指令 */
#define HAL_SYSTEM_RESET()          (NVIC_SystemReset())   /// 强制系统复位

/* 定义看门狗使能及喂狗函数接口 */
extern __weak void hal_dwt_enable(void);
extern __weak void hal_dwt_reset(void);
#define HAL_WDG_START()              do{hal_dwt_enable();}while(0)
#define HAL_WDG_RESET()              do{hal_dwt_reset();}while(0)  /* clear watch dog counter */

/* 定义寄存器写保护设置(如果有的话) */
#define HAL_SAFETY_WR(x)    st(HT_CMU->WPREG = 0xA55A; x; HT_CMU->WPREG = 0x0000;)
#define HAL_VRTC_ACCESS(x)  st(x)

/* Exported gloable variable ------------------------------------------------ */
/* @brief  系统内核时钟频率 */
extern uint32_t SystemCoreClock;

#endif /* __HAL_DEF_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

