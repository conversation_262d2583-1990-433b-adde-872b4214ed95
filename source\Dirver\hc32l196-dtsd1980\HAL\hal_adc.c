/**
 ******************************************************************************
 * @file    hal_adc.c
 * <AUTHOR> @version V1.0.0
 * @date    2024
 * @brief   本模块完成对MCU的AD采样驱动(单次单通道).
 * @note    单次采样时间: 采样时间 + 转换时间;
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_adc.h"
#include "hal_mcu.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */
/**
 * @brief  多通道采样初始化
 * @param  None
 * @retval None
 */
// Step3：设置 BGR_CR.BGR_EN 为 1，使能 BGR 模块。
// Step4：设置 ADC_CR0.En 为 1，使能 ADC 模块。
// Step5：延时 20us，等待 ADC 及 BGR 模块启动完成。
// Step6：设置 ADC_CR1.Mode 为 0，选择单次转换模式。
// Step7：配置 ADC_CR0.Ref，选择 ADC 的参考电压。
// Step8：设置 ADC_CR0.InRefEn 为 1，使能 ADC 内部参考电压。
// Step9：配置 ADC_CR0.SAM 及 ADC_CR0.CkDiv，设置 ADC 的转换速度。
// Step10：配置 ADC_CR0.SGLMux，选择待转换的通道。
// Step11：设置 ADC_ICR.SGLIC 为 0，清除 ADC_IFR.SGLIF 标志。
// Step12：设置 ADC_SglStart.Start 为 1，启动 ADC 单次转换。
// Step13：等待 ADC_IFR. SGLIF 变为 1，读取 ADC_Result 寄存器以获取 ADC 转换结果。
// Step14：如需对其它通道进行转换，重复执行 Step10~Step13。
// Step15：设置 ADC_CR0.En 及 BGR_CR.BGR_EN 为 0，关闭 ADC 模块、BGR

// 通过 ADC 测量环境温度操作流程：
// Step1：设置 BGR_CR 为 3，使能 BGR 模块和温度传感器模块。
// Step2：设置 ADC_CR0.En 为 1，使能 ADC 模块。
// Step3：延时 20us，等待 ADC 及 BGR 模块启动完成。
// Step4：设置 ADC_CR1.Mode 为 0，选择单次转换模式。
// Step5: 设置 ADC_CR0.InRefEn 为 1，使能 ADC 内部参考电压。
// Step6：配置 ADC_CR0.Ref，选择 ADC 的参考电压为内部 1.5V 或内部 2.5V。
// Step7：配置 ADC_CR0.SAM 及 ADC_CR0.CkDiv，设置 ADC 的转换速度。
// Step8：设置 ADC_CR0.SGLMux 为 0x1C，选择待转换的通道为温度传感器的输出。
// Step9：设置 ADC_CR0.Buf 为 1，使能输入信号放大器。
// Step10：设置 ADC_ICR.SGLIC 为 0，清除 ADC_IFR.SGLIF 标志。
// Step11：设置 ADC_SglStart.Start 为 1，启动 ADC 单次转换。
// Step12：等待 ADC_IFR.SGLIF 变为 1，读取 ADC_Result 寄存器以获取 ADC 转换结果。
// Step13：设置 ADC_CR0.En 及 BGR_CR 为 0，关闭 ADC 模块、BGR 模块、温度传感器模块。
// Step14：读取温度传感器校准值，根据公式计算当前的环境温度。
void hal_adc_open(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.ADC = TRUE;    // 使能ADC&BGR外设时钟

    M0P_BGR->CR_f.BGR_EN = 0x1u;    // 开启BGR
    M0P_BGR->CR_f.TS_EN  = 0x1u;    // 开启温度传感器

    hal_mcu.wait_us(40);

    M0P_ADC->CR0 = 0x1u;    // ADC 使能
    hal_mcu.wait_us(40);

    M0P_ADC->CR0 |= (uint32_t)AdcMskClkDiv8 |              // 采样分频-8
                    (uint32_t)AdcMskRefVolSelInBgr1p5 |    // 参考电压选择-1.5V ，修改时注意修改温度计算公式
                    (uint32_t)AdcMskBufDisable |           // OP BUF配置-关
                    (uint32_t)AdcMskSampCycle12Clk |       // 采样周期
                    (uint32_t)AdcMskInRefEnable;           // 内部参考电压使能-开
    M0P_ADC->CR1_f.MODE  = AdcSglMode;                     // 采样模式-单次
    M0P_ADC->CR1_f.ALIGN = AdcAlignRight;                  // 转换结果对齐方式-右
}

/**
 * @brief  Init and Open the Adc(No Power), 单通道单次采样
 * @param  None
 * @retval None
 */
void hal_adc_open_nopower(void)
{
    hal_adc_open();
}

/**
 * @brief  Close the Adc
 * @param  None
 * @retval None
 */
void hal_adc_close(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.ADC = FALSE;    // 关闭AdcBgr时钟
    M0P_BGR->CR_f.BGR_EN           = 0;        // 关闭BGR
    M0P_BGR->CR_f.TS_EN            = 0;        // 关闭温度传感器
    M0P_ADC->CR0_f.EN              = 0;        // 关闭ADC
}

/**
 * @brief  Close the Adc(No Power)
 * @param  None
 * @retval None
 */
void hal_adc_close_nopower(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.ADC = FALSE;    // 关闭AdcBgr时钟
    M0P_BGR->CR_f.BGR_EN           = 0;        // 关闭BGR
    M0P_BGR->CR_f.TS_EN            = 0;        // 关闭温度传感器
    M0P_ADC->CR0_f.EN              = 0;        // 关闭ADC
}

/**
 * @brief  Start Adc Conversion
 * @param  chn-ADC channel
 * @retval None
 */
void hal_adc_start_conversion(HAL_ADC_CHN_TYPE chn)
{
    uint16_t wait         = 1000;
    M0P_ADC->CR0_f.SGLMUX = chn;    // 选择通道
    if(chn == HAL_ADC_TEMP)
    {
        M0P_ADC->CR0_f.BUF   = 1;                      //
        M0P_ADC->ICR_f.SGLIC = 0;                      // 温度采2次
        M0P_ADC->SGLSTART    = 1u;                     //
        while(M0P_ADC->IFR_f.SGLIF == 0 && wait--);    //
    }
    else
    {
        M0P_ADC->CR0_f.BUF = 0;    // 其他通道BUF关闭
    }
    M0P_ADC->ICR_f.SGLIC = 0;                      // 清除单次转换完成标志
    M0P_ADC->SGLSTART    = 1u;                     // 单次转换开始转换
    while(M0P_ADC->IFR_f.SGLIF == 0 && wait--);    // 等待采样完成
}

/// @brief Get the ADC sample value
/// @param chn-ADC channel
/// @return
int32_t hal_adc_result_get(HAL_ADC_CHN_TYPE chn)
{
    return M0P_ADC->RESULT;    // 获取采样值
}

/// @brief Get the current channel voltage value
/// @param chn-ADC channel
/// @return 电压值单位V
float hal_adc_voltage_get(HAL_ADC_CHN_TYPE chn)
{
    volatile float vol;

    hal_adc_start_conversion(chn);    // 启动ADC采样
    vol = (float)hal_adc_result_get(chn);
    return (vol / 4096 * 1.5f);    // 计算电压值，假设ADC分辨率为12位，参考电压为3.3V
}

/// @brief hc32l196 环境温度 = 25 + 0.0795 × Vref × ( AdcValue – Trim )
/// @note  Vref 为当前 ADC 模块的参考电压，取值为 1.5 或 2.5。
/// @param
/// @return
#define ADC_REF_VOLTAGE 1    // 1-1.5V;  2-2.5V   选择参考电压 ！！

#if ADC_REF_VOLTAGE == 1
#define ADC_TEMP_REF_VOLTAGE 1.5              // 参考电压1.5
#define ADC_TRIM *((uint16_t *)0x00100C34)    // 读取温度传感器校准值
#else
#define ADC_TEMP_REF_VOLTAGE 2.5    // 参考电压2.5
#define ADC_TRIM *((uint16_t *)0x00100C36)
#endif
float hal_adc_temperature_get(void)
{
    float    temp;
    int32_t  adc;
    uint16_t trim = ADC_TRIM;    // 读取温度传感器校准值

    hal_adc_start_conversion(HAL_ADC_TEMP);     // 启动温度传感器采样
    adc  = hal_adc_result_get(HAL_ADC_TEMP);    // 获取温度传感器采样值
    temp = 25 + 0.0795 * ADC_TEMP_REF_VOLTAGE * (adc - trim);
    return temp;
}

/// @brief 声明hal_adc子模块对象
const struct hal_adc_t hal_adc = {
    .open          = hal_adc_open,
    .open_nopower  = hal_adc_open_nopower,
    .close         = hal_adc_close,
    .close_nopower = hal_adc_close_nopower,
    .start         = hal_adc_start_conversion,
    .result        = hal_adc_result_get,
    .voltage       = hal_adc_voltage_get,
    .temperature   = hal_adc_temperature_get,
};

/** @} */
