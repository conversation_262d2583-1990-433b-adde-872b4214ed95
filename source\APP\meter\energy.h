/********************************************************************************
  * @file    energy.h
  * <AUTHOR> @date    2024
  * @brief    
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
 
#ifndef __ENERGY_M_H__
#define __ENERGY_M_H__

#include "typedef.h"
#include "app_config.h"
#include "mic.h"

typedef enum
{
    TYPE_ENERGY_POS_ACT,    // 正向有功电能
    TYPE_ENERGY_NEG_ACT,    // 反向有功电能
// #if ENERGY_REACTIVE_ENABLE
    TYPE_ENERGY_Q1_REA,     // 一象限无功电能
    TYPE_ENERGY_Q2_REA,     // 二象限无功电能
    TYPE_ENERGY_Q3_REA,     // 三象限无功电能
    TYPE_ENERGY_Q4_REA,     // 四象限无功电能
// #endif
#if ENERGY_APP_ENABLE
    TYPE_ENERGY_POS_APP,    // 正向视在电能
    TYPE_ENERGY_NEG_APP,    // 反向视在电能
#endif
    ENERGY_TYPE_NUM,

    /* 以下定义为电能类型扩展, 必须放于ENERGY_TYPE_NUM之后!! */
    TYPE_ENERGY_POS_REA,    // 正向无功电能(Lag) 对应组合无功1
    TYPE_ENERGY_NEG_REA,    // 反向无功电能(Lead)对应组合无功2
    TYPE_ENERGY_ADD_ACT,    // 正向+反向有功组合电能
    TYPE_ENERGY_SUB_ACT,    // 正向-反向有功组合电能
    TYPE_ENERGY_ADD_REA,    // 正向+反向无功组合电能
    TYPE_ENERGY_ADD_APP,     // 正向+反向视在组合电能

    TYPE_ENERGY_NUM
} ENERGY_TYPE;              // 电能类型定义

/* 电能数据结构，映射在内存中一份，EEPROM中两份(包括备份), 掉电保存MCU FLASH一份 */
#define EN_CHN_NUM       (1 + ENERGY_PHASE_ENABLE * PHASE_NUM)
typedef struct
{
	uint16_t flag;          // 掉电保存标志
	uint16_t crc;           // CRC校验
    uint32_t cnt;           // 存储次数计数器
	uint32_t fast_cnt[4];   // 快速脉冲计数器(脉冲余数)
    int64_t value[1 + TARIFF_RATE_NUM][ENERGY_TYPE_NUM]; // 总以及分费率累计电能脉冲
#if ENERGY_PHASE_ENABLE
    int64_t a_value[1 + ENERGY_PHASE_TARIFF_EN*TARIFF_RATE_NUM][ENERGY_TYPE_NUM];
    int64_t b_value[1 + ENERGY_PHASE_TARIFF_EN*TARIFF_RATE_NUM][ENERGY_TYPE_NUM];
    int64_t c_value[1 + ENERGY_PHASE_TARIFF_EN*TARIFF_RATE_NUM][ENERGY_TYPE_NUM];
#endif
} EnergyValue_s;

typedef struct
{
    uint16_t crc;
    uint16_t chk;

    uint8_t comb_kWh_code;
    uint8_t comb1_kvarh_code;
    uint8_t comb2_kvarh_code;
}energy_para_s;

/* Exported defines ----------------------------------------------------------*/
/* 每秒最大可能产生电能数 */
#define EN_MAX_ENERGY_PER_SECOND   (MAX_PULSE_PER_SECOND / (METER_CONST * ENERGY_DEF_SCALER) + 1) 

/* Exported macro ------------------------------------------------------------*/
/* Exported variables --------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
struct energy_s
{
    /// @初始化
    void (*init)(void);
    /// @计量，放置于秒级任务
    void (*refresh)(void);
    /// @掉电保存
    void (*pwr_down_save)(void);
    /// @数据重置
    void (*reset)(uint8_t type);

    /// @预付费模块数据存储回调函数，跟电能同时存储
    void (*checkin_callback)(void func(void));

    /// 返回T/A/B/C相秒电量脉冲
    int16 (*phs_sec_pulse_get)(uint8_t ph, ENERGY_TYPE type);

    /* 返回T/A/B/C相总以及分费率累计电量脉冲 */
    int64 (*phs_cum_pulse_get)(uint8_t ph, ENERGY_TYPE type, uint8_t rate);

    /* 返回T/A/B/C相总以及分费率累计电能, 单位取决于 ENERGY_DEF_SCALER 定义 */
    ENERGY_DEF_FORMAT (*phs_cum_value_get)(uint8_t ph, ENERGY_TYPE type, uint8_t rate);

    /// @brief 电能参数设置
    bool (*para_set)(uint16_t ofst, const void* val, uint16_t len);
    /// @brief 电能参数获取
    const energy_para_s* (*para_get)(void);
};
extern const struct energy_s energy;







#endif /* __ENERGY_H__ */
