/**
 ******************************************************************************
 * @file    RN8211_B_V3
 * <AUTHOR> @date    2024
 * @brief   SOC计量
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include <math.h>
#include <stdlib.h>    // For abs()
#include "bsp_cfg.h"
#include "mic.h"
#include "utils.h"
#include "debug.h"
#include "eeprom.h"
#include "hal_gpio.h"
#include "hal_timer.h"
#include "hal_flash.h"
#include "rn821x_rn721x_soc.h"

/// @brief rn8211计量芯片寄存器地址
#define RN8211_CAL_BASE EMU

/// @brief 各寄存器默认值

// 常数定义
#define DATA_2EXP12 4096ul          // 2 ^ 12
#define DATA_2EXP13 8192ul          // 2 ^ 13
#define DATA_2EXP14 16384ul         // 2 ^ 14
#define DATA_2EXP15 32768ul         // 2 ^ 15
#define DATA_2EXP16 65536ul         // 2 ^ 16
#define DATA_2EXP20 1048576ul       // 2 ^ 20
#define DATA_2EXP23 8388608ul       // 2 ^ 23
#define DATA_2EXP24 16777216ul      // 2 ^ 24
#define DATA_2EXP31 0x80000000ul    // 2 ^ 31

// 电压通道采样参数
#define Rt (1.0)               // kΩ 电阻分压电阻
#define Ra (270.0 * 6 + Rt)    // kΩ 总电阻
#define Upga 2                 // 电压通道增益
// Kv = Ra / (Rt * Upga * DATA_2EXP23) = 1621.0 / (1.0 * 1 * 8388608) ≈ 0.0001932
#define Kv (Ra / (Rt * Upga * DATA_2EXP23))    // 电压转换系数

// 电流通道采样参数
#define Ri (200.0)    // uΩ 锰铜电阻
#define Ipga 16       // 电流通道增益
// Ki = 1.0 * 1000000 / (Ri * Ipga * DATA_2EXP23) = 1000000 / (200.0 * 1 * 8388608) ≈ 0.000595
#define Ki (1.0 * 1000000 / (Ri * Ipga * DATA_2EXP23))    // 电流采样系数

// 零线电流采样参数
#define Gi_n 1                                      // 零线电流通道增益
#define CT_n 1000                                   // 零线电流互感器变比
#define vi_n (BASE_CURRENT * 1000 / CT_n * Gi_n)    // 零线电流采样电压 mV

// 功率转换系数
#define Kp (Ra * 1000000 / ((Ri * Ipga) * (Rt * Upga) * DATA_2EXP31))    // 功率转换系数 0.0037743
#define I_start (20 * 0.001)                                             // 20mA
#define P_start (0.7 * I_start * INPUT_VOLTAGE / Kp / 256 + 0.5)         // 启动潜动寄存器
#define Q_start (P_start)                                                // 启动潜动寄存器
#define m_PI (3.1415928)                                                 //
#define _50HZ_LSB (0.00976560)                                           // 50HZ，PHSA/B 有 0.00976560/LSB 的关系

// 高压常数
#define HF_CONST (uint16_t)(1.8 * 921600 * Ri * Ipga * Rt * Upga / Ra / METER_CONST + 0.5)

#define SAG_CYCLE (4)                                                // (SGA判断半周波数)
#define CAL_CHNSEL 0.125                                             // 通道选择判断阀值
#define SAMPLE_NUM 10                                                // 最大采样次数
#define SAMPLE_1P0_NUM 3                                             // 最大采样次数
#define _14HZ (80)                                                   // 14.5HZ
#define _1P7HZ (600)                                                 // 1.7HZ
#define START_P_RADIO (START_PWR / 2)                                // 有功启动
#define START_Q_RADIO START_P_RADIO                                  // 无功启动
#define START_REG (uint16_t)(START_P_RADIO / cal_para[0].P_ratio)    // 有功启动,单位:

/// 计量芯片通讯口定义
#define CAL_CS16(ptr, len) crc16(0, (uint8_t *)(ptr) + 4, len - 4)
#define ee_cal_read(x, y, z) eeprom.read(x, y, z)
#define ee_cal_write(x, y, z) eeprom.write(x, y, z)
#define mcu_cal_read(x, y, z) hal_flash.read((MCU_CAL_DATA_BASE + x), y, z)
#define mcu_cal_write(x, y, z) hal_flash.write((MCU_CAL_DATA_BASE + x), y, z)
#define MEASURE_PARA_ADDR (member_offset(cal_data_s, mic_para))    // 计量参数地址

typedef union
{
    struct
    {
        uint16_t chk;           //
        uint16_t cs;            // 校表参数校验和
        float    U_ratio;       // 电压系数
        float    I_ratio;       // 电流系数
        float    P_ratio;       // 功率系数
        uint16_t I_gain;        // 电流增益
        uint16_t U_gain;        // 电压增益
        uint16_t Phs_ofst;      // 相位校准
        uint16_t P_ofst;        // 有功功率偏移校准
        uint16_t Q_ofst;        // 无功功率偏置校准
        uint16_t I_ofst;        // 电流偏移校准
        uint16_t HFconst;       // 高频脉冲常数
        uint16_t I_zero_reg;    // 零线电流寄存器
    };
    uint8_t reserve[32];
} cal_para_s;

typedef struct
{
    cal_para_s    cal_para[CHANNEL_NUM];
    MeasurePara_s mic_para;
} cal_data_s;

typedef struct
{
    SMALLOC(cal_data_s, space, 512);    /// 如果这里编译报错，需在datastore.h中修改 CAL_DATA_SIZE 的大小(修改为128的整数倍)直到编译通过，默认是 512.
} mic_store_s;

/// @brief 默认校表参数
static const cal_para_s cal_para_default = {
    .cs         = 0,
    .U_ratio    = 1.0,
    .I_ratio    = 1.0,
    .P_ratio    = 1.0,
    .I_gain     = 0,
    .U_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .Q_ofst     = 0,
    .I_ofst     = 0,
    .HFconst    = HF_CONST,
    .I_zero_reg = 0,
};
/// @brief 零线通道默认值
static const cal_para_s cal_para_N_default = {
    .cs         = 0,
    .U_ratio    = 1.0,
    .I_ratio    = 1.0,
    .P_ratio    = 1.0,
    .I_gain     = 0,
    .U_gain     = 0,
    .Phs_ofst   = 0,
    .P_ofst     = 0,
    .Q_ofst     = 0,
    .I_ofst     = 0,
    .HFconst    = HF_CONST,
    .I_zero_reg = 0,
};

static const MeasurePara_s mic_para_default = {
    .cs          = 0,                     // 校验和
    .acc_mode    = MIC_ALGACC_MODE,       // 默认代数和计量方式
    .wiring_mode = MIC_CHN_PHASE_MODE,    // 默认接线方式
    .led_mode    = 0,                     // LED模式
    .constant    = METER_CONST,           // 计量常数
};

void measure_ic_init(uint8_t pwr_down);

#if USE_EMU_AT_LOSS_VOLTAGE
static void (*power_off_running)(void);
#endif
static uint8_t led_act_remain_time;    // 有功电能脉冲LED计时器
static uint8_t led_rea_remain_time;    // 无功电能脉冲LED计时器
#if USE_LED_VAPULSE
static uint8_t led_app_remain_time;    // 视在电能脉冲LED计时器
#endif
static cal_para_s     cal_para[CHANNEL_NUM];    // att7022e-校正参数
static MeasurePara_s  measure_para;             // 计量参数
InstantVal_s          instant_value;            // 瞬时计量数据块,如电流电压频率功率等等
static uint8_t        ep_pulse_cnt;             // 有功电能脉冲计数器
static bool           measure_status;           // 计量状态
static SAMPLE_CHANNEL mic_channel;              // 计量通道

/// @attention 直接操作寄存器define**************************************************
#define RN8211_CAL_REG(reg) (RN8211_CAL_BASE->reg)
#define RN8211_CAL_SET(reg, value) (RN8211_CAL_REG(reg) = (uint32_t)value)
#define RN8211_CAL_SETBIT(reg, value) (RN8211_CAL_REG(reg) |= value)
#define RN8211_CAL_CLRBIT(reg, value) (RN8211_CAL_REG(reg) &= ~value)
#define RN8211_CAL_CHECK(reg, bitmask) ((RN8211_CAL_REG(reg) & bitmask) == bitmask)
#define RN8211_CAL_WRITE_ENABLE() (RN8211_CAL_REG(SPCMD) = 0xE5)
#define RN8211_CAL_WRITE_CLOSE() (RN8211_CAL_REG(SPCMD) = 0xDC)
#define RN8211_CAL_WAIT_32NOP() \
    for(size_t i = 0; i < 32; i++) { __NOP(); }

/// @brief 计量芯片初始化，且设置电流通道为A通道（B没有接）
/// @param
static void emu_com_init(void)
{
    /// @attention 下面外设时钟初始化是放到hal层和底层相关比较好，还是放在这里
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
    LL_SYSC_ApbClkCtrl(LL_SYSC_EMU_ID, ERN_ENABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI1, ERN_ENABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI2, ERN_ENABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCU, ERN_ENABLE);
    /// @brief 电流通道adc增益设置
    LL_SYSC_ADCPGACtrl(LL_SYSC_ADCCHN_I1, LL_SYSC_ADCPGA_16);
    LL_SYSC_ADCPGACtrl(LL_SYSC_ADCCHN_U, LL_SYSC_ADCPGA_2);

    /// @attention 设置的是寄存器，是否存在写不成功导致卡死，不能写看门狗
    while(RN8211_CAL_CHECK(EMUStatus, EMU_EMUSTATUS_CHNSEL) == true)
    {
        HAL_WDG_RESET();
        RN8211_CAL_WRITE_ENABLE();
        RN8211_CAL_REG(SPCMD) = 0x5A;
        RN8211_CAL_WRITE_CLOSE();
        RN8211_CAL_WAIT_32NOP();
    }

    /// @brief 配置cf引脚
    hal_gpio.mic_init(GPIO_OPEN);
}

static void emu_reset(void)
{
#if USE_HDW_RST_MIC
    // 复位计量芯片
    IO_EMU_PWR_OFF();
    hal_timer.xms(2);
    IO_EMU_PWR_ON();
    hal_timer.xms(6);
#endif
    measure_status = FALSE;
}

/// @brief 校验和处理
/// @param type 0-清除校验和，1—获取校验和，2-检查校验和, 3-反转校验和
/// @return TRUE 校验和正确，校验和错误
#define CS_CLR 0
#define CS_SET 1
#define CS_CHECK 2
#define CS_REVERSE 3
#define CS_DEFAULT_VALUE (0xE33378U)
static uint32_t emu_reg_check_sum_1;    /// 写在函数外部，高亮需要
static uint32_t emu_reg_check_sum_2;    /// 写在函数外部，高亮需要
static bool     emu_reg_check_sum_pro(uint8_t type)
{
    switch(type)
    {
        case CS_CLR:
            emu_reg_check_sum_1 = CS_DEFAULT_VALUE;
            return TRUE;

        case CS_SET:
            RN8211_CAL_WAIT_32NOP();
            emu_reg_check_sum_1 = RN8211_CAL_REG(EMUStatus) & EMU_EMUSTATUS_CHKSUM1;
            return TRUE;

        case CS_CHECK:
            if((emu_reg_check_sum_1 & EMU_EMUSTATUS_CHKSUM1) == (RN8211_CAL_REG(EMUStatus) & EMU_EMUSTATUS_CHKSUM1)) return TRUE;
            break;

        case CS_REVERSE:
            emu_reg_check_sum_1 = ~emu_reg_check_sum_1;
            return TRUE;

        default:
            break;
    }
    return FALSE;
}

static int emu_set_para(uint8_t chn)
{
    cal_para_s *cal = &cal_para[chn];

    RN8211_CAL_WRITE_ENABLE();

    // 关闭通道B的高通滤波
    RN8211_CAL_SETBIT(EMUCON, EMU_EMUCON_HPFIBOFF);

    /// @brief 脉冲增加倍数 2^（CFSU[1:0]＋1）
    // RN8211_CAL_SETBIT(EMUCON, 1<<3);
    // RN8211_CAL_SETBIT(EMUCON, EMU_EMUCON_CFSUEN);

    // 脉冲频率写
    uint32_t temp = HF_CONST;
    RN8211_CAL_SET(HFConst, temp);

    // 有功潜动与启动阈值
    temp = (uint16_t)START_P_RADIO;
    RN8211_CAL_SET(PStart, temp);

    // 无功潜动与启动阈值
    temp = (uint16_t)START_Q_RADIO;
    RN8211_CAL_SET(QStart, temp);

    // 电压增益校正寄存器
    temp = cal[0].U_gain;
    RN8211_CAL_SET(UGAIN, temp);

    // 电流增益校正寄存器
    temp = cal[0].I_gain;
    RN8211_CAL_SET(IAGAIN, temp);

    // 相位校正寄存器
    temp = cal[0].Phs_ofst;
    RN8211_CAL_SET(PhsA, temp);

    // 有功偏置寄存器
    temp = cal[0].P_ofst;
    RN8211_CAL_SET(APOSA, temp);

    // 无功偏置寄存器
    temp = cal[0].Q_ofst;
    RN8211_CAL_SET(RPOSA, temp);

    // 电流偏置寄存器
    temp = cal[0].I_ofst;
    RN8211_CAL_SET(IARMSOS, temp);

    RN8211_CAL_WRITE_CLOSE();

    return 0;
}

/// @brief 参数检查更新
/// @param para         参数指针
/// @param hf_const_gain 脉冲加倍数目前只支持2,4,8倍
/// @return TRUE 计量芯片正常运行，FALSE 计量芯片参数异常
static bool emu_para_refresh(const cal_para_s *para, uint8_t hf_const_gain)
{
    uint16_t freq_cfg;
    (void)(hf_const_gain);

    /// 校验和处理
    if(emu_reg_check_sum_pro(CS_CHECK)) return TRUE;

    /// 复位计量芯片
    emu_com_init();
    emu_reset();
    emu_set_para(CHANNEL_1);
    emu_reg_check_sum_pro(CS_SET);

    return FALSE;
}
/// @brief 数据检出，移植不用改
/// @param
static void emu_calpara_checkout(void)
{
    uint8_t     chn;
    cal_para_s *ptr;

    /* 校验RAM中的计量参数, EEPROM和CODEFLASH都存放有计量参数 !!!掉电涉及电能显示，可能用到变比 */
    if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
    {
        ee_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
        if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
        {
            mcu_cal_read(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
            if(measure_para.cs != CAL_CS16(&measure_para, sizeof(MeasurePara_s)))
            {
                measure_para    = mic_para_default;
                measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            }
        }
    }

    /* 校验RAM中的校表参数. EEPROM和CODEFLASH都存放有校表参数 */
    for(chn = 0, ptr = cal_para; chn < CHANNEL_NUM; chn++, ptr++)
    {
        if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
        {
            ee_cal_read(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
            if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
            {
                mcu_cal_read(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
                if(ptr->cs != CAL_CS16(ptr, sizeof(cal_para_s)))
                {
                    if(chn == CHANNEL_2) { *ptr = cal_para_N_default; }
                    else { *ptr = cal_para_default; }
                    instant_value.stus.uncal |= 1 << chn;
                    ptr->cs = CAL_CS16(ptr, sizeof(cal_para_s));
                }
                else { ee_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s)); }
            }
            else
            {
                cal_para_s tmp;
                mcu_cal_read(chn * sizeof(cal_para_s), &tmp, sizeof(cal_para_s));
                if(tmp.cs != CAL_CS16(&tmp, sizeof(cal_para_s))) { mcu_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s)); }
            }
        }
    }
}

/// @brief emu检测，移植不用改
/// @param
/// @return
static bool emu_verify(void)
{
    static uint8_t cnt = 5;

    /* 校验RAM中的校表参数 */
    emu_calpara_checkout();

    /* 校验计量芯片内的参数 */
    if(emu_para_refresh(cal_para, 0))
    {
        cnt                      = 5;
        instant_value.stus.error = 0;
        return TRUE;
    }
    else if(cnt > 0 && --cnt == 0) /* 如果计量芯片数据读写不正常 */
    {
        instant_value.stus.error = 1;
        measure_ic_init(0);
    }

    return FALSE;
}

/// @brief 获取频率
/// @param
/// @return
static float emu_freq_val(void)
{
    return (1843200.0 / 4 / RN8211_CAL_REG(Ufreq));
}

/// @brief 获取功率因素，移植不用改s
/// @param ph 相
static float emu_pf_val(EMU_PHASE_TYPE ph)
{
    float pf;

    if(equ(instant_value.pwr_s[ph], 0)) return 1.0;
    pf = fabs(instant_value.pwr_p[ph] / instant_value.pwr_s[ph]);
    if(pf > 1.0) return 1.0;
    return pf;
}

/// @brief 计算相角，移植不用修改
/// @param ph
/// @return
#define ANGLE_360 360.0
#define PI 3.141592
static float emu_vi_angle_val(EMU_PHASE_TYPE ph, const float *pf)
{
    float angle;        // 弧度，1弧度=(180/π)°
    float u_i_angle;    // 角度（°）

    angle     = acos(pf[ph]);
    u_i_angle = angle * ANGLE_360 / 2 / PI;

    if(less(instant_value.pwr_p[ph], 0.0))
    {
        if(less(instant_value.pwr_q[ph], 0.0))    // 第III象限
        {
            u_i_angle = ANGLE_360 / 2 + u_i_angle;
        }
        else    // 第II象限
        {
            u_i_angle = ANGLE_360 / 2 - u_i_angle;
        }
    }
    else
    {
        if(less(instant_value.pwr_q[ph], 0.0))    // 第IV象限
        {
            u_i_angle = ANGLE_360 - u_i_angle;
        }
    }

    if(more(u_i_angle, ANGLE_360))
        u_i_angle = ANGLE_360;
    else if(less(u_i_angle, 0.0))
        u_i_angle = ANGLE_360 - 0.1 + u_i_angle;

    return u_i_angle;
}

/// @brief 获取电压值
/// @param chn
/// @return
static float emu_vol_val(SAMPLE_CHANNEL chn)
{
    float vrms;

    vrms = (RN8211_CAL_REG(URMS) & (~(1 << 24))) * Kv * cal_para[0].U_ratio;

    if(less(vrms, MIN_VOLTAGE)) vrms = 0;

    return vrms;
}

/// @brief 获取电流值
static float emu_cur_val(SAMPLE_CHANNEL chn)
{
    float    irms;
    bool     sign;
    uint32_t reg;

    reg  = RN8211_CAL_REG(IARMS);
    sign = (reg & (1 << 24)) != 0;    // 电流符号位

    irms = (reg & (~(1 << 24))) * Ki * cal_para[0].I_ratio;
    irms = (sign) ? -irms : irms;

    return irms;
}

/// @brief 获取功率值
static float emu_power_val(EMU_POWER_TYPE type, SAMPLE_CHANNEL chn)
{
    uint32_t reg_pwr;
    float    value = 0x00;

    switch(type)
    {
        case P_POWER: {
            reg_pwr = (chn == CHANNEL_1) ? RN8211_CAL_REG(PowerPA) : RN8211_CAL_REG(PowerPA2);
            // 功率寄存器是4字节（32位）带符号数，直接强制转换为int32_t即可完成符号扩展
            value = ((float)(int32_t)reg_pwr) * Kp / 1000;
        }
        break;
        case Q_POWER: {
            reg_pwr = (chn == CHANNEL_1) ? RN8211_CAL_REG(PowerQA) : RN8211_CAL_REG(PowerQA);
            // 功率寄存器是4字节（32位）带符号数，直接强制转换为int32_t即可完成符号扩展
            value = ((float)(int32_t)reg_pwr) * Kp / 1000;
        }
        break;
        case S_POWER: {
            reg_pwr = (chn == CHANNEL_1) ? RN8211_CAL_REG(PowerSA) : RN8211_CAL_REG(PowerSA);
            // 功率寄存器是4字节（32位）带符号数，直接强制转换为int32_t即可完成符号扩展
            value = ((float)(int32_t)reg_pwr) * Kp / 1000;
        }
        break;
        default:
            return 0;
    }
    return (value * cal_para[chn].P_ratio);    // unit: W
}

/// @brief 根据功率符号计算相位, 移植不用改
static PWR_QUADRANT emu_quadrant_in(EMU_PHASE_TYPE ph)
{
    if(instant_value.pwr_p[ph] >= 0) { return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_1 : QUADRANT_4; }
    else { return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_2 : QUADRANT_3; }
}

/// @brief 切换计量通道
/// @param
/// @return
#define CHNSEL_MIN (2 * START_PWR)
static void emu_channel_select(float *ps)
{
    static uint8_t select_wait = 0;
    uint8_t        channel_tmp;
    (void)(select_wait);
    (void)(channel_tmp);

#if USE_CHANNEL_2
    if(measure_para.wiring_mode == MIC_CHN_AUTO_MODE)
    {
        float val[3];
        val[0] = fabs(ps[1]);
        val[1] = fabs(ps[2]);
        if(val[1] < START_PWR)
        {
            if(val[0] < START_PWR) return;
            channel_tmp = CHANNEL_1;
        }
        else if(val[0] < START_PWR) { channel_tmp = CHANNEL_2; }
        else
        {
            val[2] = max(val[1], val[0]) * CAL_CHNSEL;
            val[2] = max(val[2], CHNSEL_MIN);

            if(fabs(val[1] - val[0]) >= val[2]) { channel_tmp = (val[1] > val[0]) ? CHANNEL_2 : CHANNEL_1; }
            else
                channel_tmp = mic_channel;
        }
        DBG_PRINTF(P_EMU, D, "Vs:%8d, %8d, %8d\r\n", val[0], val[1], val[2]);
        DBG_PRINTF(P_EMU, D, "CHANNEL_%1d\r\n", (mic_channel + 1));
    }
    else { channel_tmp = (measure_para.wiring_mode == MIC_CHN_PHASE_MODE) ? CHANNEL_1 : CHANNEL_2; }
#else
    channel_tmp = CHANNEL_1;
#endif

    // if(mic_channel != channel_tmp)
    // {
    //     if(++select_wait > 3)
    //     {
    //         uint32_t reg = emu_register_read(REG_W_EMUCFG);

    //         if(reg != 0xFFFFFFFF)
    //         {
    //             if(channel_tmp == CHANNEL_2)    // 选择通道 2 参与计量
    //             {
    //                 reg |= 0x0004;    // 选择通道计量（0：选择通道 1 计量 1：选择通道 2 计量）
    //             }
    //             else
    //             {
    //                 reg &= ~0x0004;    // 选择通道 1 计量
    //             }
    //             if(emu_register_write(REG_W_WPREG, 0x00BC))      // 使能写40H-45H寄存器
    //                 if(emu_register_write(REG_W_EMUCFG, reg))    // 能量寄存器读后清零
    //                 {
    //                     emu_reg_check_sum_pro(1);
    //                     select_wait = 0;
    //                     mic_channel = (SAMPLE_CHANNEL)channel_tmp;
    //                 }
    //             emu_register_write(REG_W_WPREG, 0x0000);    // 禁止写
    //         }
    //     }
    // }
    // else
    //     select_wait = 0;
}

/// @brief 排序
/// @param arr
/// @param len
static void select_sort(float *arr, uint32_t len)
{
    float    tmp;
    uint32_t i, k, j;

    for(i = 0; i < len; i++)
    {
        k = i;    // 保存当前下标
        for(j = i + 1; j < len; j++)
        {
            if(arr[k] > arr[j]) k = j;    // 找到最小值
        }
        if(k != i)    // 将最小值放到当前下标
        {
            tmp    = arr[i];
            arr[i] = arr[k];
            arr[k] = tmp;
        }
    }
}

/// @brief 掉电判断函数
/// @param
/// @return
static bool emu_power_down(void)
{
    static bool is_power_on = false;

    if(is_power_on == false && (instant_value.vrms[0] > MIN_WORK_VOLTAGE)) { is_power_on = true; }

    if(is_power_on != false && (instant_value.vrms[0] < MIN_WORK_VOLTAGE)) { return true; }

    return false;
}

/* Public functions ----------------------------------------------------------*/
/// @brief 刷新实时数据
void instant_refresh(void)
{
    uint32_t tmp = 0x00;

    if(emu_verify() != true) return;

    float pp[3], pq[3], ps[3];

    /* 更新计量状态 */
    instant_value.stus.lword &= ~0x00000FFF;
    tmp                         = RN8211_CAL_REG(EMUStatus);
    instant_value.stus.pa_start = !boolof(tmp & (bitmask(2) | bitmask(3)));

    /* 查询当前计量通道 CHNSEL */
    mic_channel = (tmp & bitmask(29)) ? CHANNEL_2 : CHANNEL_1;

    /* 读瞬时参数 */
    instant_value.freq            = emu_freq_val();
    instant_value.vrms[CHANNEL_1] = emu_vol_val(CHANNEL_1);
    pp[A_PHASE]                   = emu_power_val(P_POWER, CHANNEL_1);
    pq[A_PHASE]                   = emu_power_val(Q_POWER, CHANNEL_1);
    ps[A_PHASE]                   = fabs(emu_power_val(S_POWER, CHANNEL_1));
    if(less(pp[A_PHASE], 0.0)) ps[A_PHASE] = -ps[A_PHASE];
    /// @brief 功率符号与有功无功符号一致

#if USE_CHANNEL_2
    pp[B_PHASE] = emu_power_val(P_POWER, CHANNEL_2);
    pq[B_PHASE] = emu_power_val(Q_POWER, CHANNEL_2);
    ps[B_PHASE] = fabs(emu_power_val(S_POWER, CHANNEL_2));
    if(less(pp[B_PHASE], 0.0)) ps[B_PHASE] = -ps[B_PHASE];
#endif

    emu_channel_select(ps);
    if(instant_value.stus.main_chn) { pp[T_PHASE] = pp[B_PHASE], pq[T_PHASE] = pq[B_PHASE], ps[T_PHASE] = ps[B_PHASE]; }
    else { pp[T_PHASE] = pp[A_PHASE], pq[T_PHASE] = pq[A_PHASE], ps[T_PHASE] = ps[A_PHASE]; }

    for(uint8 i = 0; i <= 2; i++)
    {
        instant_value.pwr_p[i] = more_equ(fabs(pp[i]), START_PWR) ? pp[i] : 0;    /// unit:1W
        instant_value.pwr_q[i] = more_equ(fabs(pq[i]), START_PWR) ? pq[i] : 0;    /// unit:1W
        instant_value.pwr_s[i] = more_equ(fabs(ps[i]), START_PWR) ? ps[i] : 0;    /// unit:1W
    }

    instant_value.stus.pa_start = boolof(instant_value.pwr_s[T_PHASE] != 0);
    if(!instant_value.stus.pa_start)
    {
        instant_value.quadrant[T_PHASE] = QUADRANT_1;
        instant_value.stus.pa_rev       = 0;
        instant_value.stus.qa_rev       = 0;
        instant_value.vi_angle[T_PHASE] = 0;
        instant_value.pf[T_PHASE] = instant_value.pf[A_PHASE] = 1.0;
    }
    else
    {
        instant_value.quadrant[T_PHASE] = emu_quadrant_in(T_PHASE);
        instant_value.stus.pa_rev       = boolof(pp[0] < 0);
        instant_value.stus.qa_rev       = boolof(pq[0] < 0);

        instant_value.pf[A_PHASE] = emu_pf_val(A_PHASE);
        instant_value.pf[B_PHASE] = emu_pf_val(B_PHASE);
        instant_value.pf[T_PHASE] = (mic_channel == CHANNEL_1) ? instant_value.pf[A_PHASE] : instant_value.pf[B_PHASE];

        instant_value.vi_angle[A_PHASE] = emu_vi_angle_val(A_PHASE, instant_value.pf);    /// 电流夹角转换相角
        instant_value.vi_angle[B_PHASE] = emu_vi_angle_val(B_PHASE, instant_value.pf);    /// 电流夹角转换相角
        instant_value.vi_angle[T_PHASE] = (mic_channel == CHANNEL_1) ? instant_value.vi_angle[A_PHASE] : instant_value.vi_angle[B_PHASE];
    }

    instant_value.irms[CHANNEL_1] = emu_cur_val(CHANNEL_1);
    instant_value.irms[CHANNEL_2] = emu_cur_val(CHANNEL_2);

    /* 滤波处理 */
    if(instant_value.irms[CHANNEL_1] < START_CURRENT) instant_value.irms[CHANNEL_1] = 0;
    if(instant_value.irms[CHANNEL_2] < START_CURRENT) instant_value.irms[CHANNEL_2] = 0;
    instant_value.n_irms = instant_value.irms[CHANNEL_2];
    instant_value.v_irms = (mic_channel == CHANNEL_1) ? instant_value.irms[CHANNEL_1] : instant_value.irms[CHANNEL_2];
}

/// 电能获取
static uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    uint8_t pulse_num = 0;
    uint8_t reg;

    switch(type)
    {
        case P_POWER:
            pulse_num = RN8211_CAL_REG(PFCnt);
            break;
        case Q_POWER:
            pulse_num = RN8211_CAL_REG(QFCnt);
            break;
        case S_POWER:
            pulse_num = RN8211_CAL_REG(SFCnt);
            break;
        default:
            return 0;
    }

    return (pulse_num < MAX_PULSE_PER_SECOND) ? pulse_num : 0;
}

/// @brief 计量芯片初始化
/// @param pwr_down 1-掉电时初始化，0-上电时初始化
void measure_ic_init(uint8_t pwr_down)
{
    emu_calpara_checkout();

    for(uint8_t i = 0; i < 4; i++) { instant_value.quadrant[i] = QUADRANT_1; }

    if(!pwr_down)
    {
        emu_com_init();
        emu_reset();

        HAL_CRITICAL_STATEMENT(ep_pulse_cnt = 0;);
        mic_channel = CHANNEL_1;
        emu_set_para(CHANNEL_1);
        emu_reg_check_sum_pro(CS_SET);

        /// @brief 掉电判断函数注册
        hal_mcu.is_sag_callback(emu_power_down);

        /// @bug 为什么要清除掉校验参数，cal_para是后续生成的么？
        // memset(cal_para, 0, CHANNEL_NUM * sizeof(cal_para_s));
    }
}

/// @brief 计量芯片关闭
/// @param
void measure_ic_off(void)
{
    LL_SYSC_ApbClkCtrl(LL_SYSC_EMU_ID, ERN_DISABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI1, ERN_DISABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI2, ERN_DISABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCU, ERN_DISABLE);

    measure_status = FALSE;
}

/// @brief 获取电压电流功率数据用于校表
/// @param pdat_samp
void calibrate_sample(void *pdat_samp)
{
    uint8_t i, delete_num;
    float   Urms[SAMPLE_NUM], Irms1[SAMPLE_NUM], Irms2[SAMPLE_NUM];
    float   Power_P1[SAMPLE_NUM], Power_P2[SAMPLE_NUM], Power_Q1[SAMPLE_NUM], Power_Q2[SAMPLE_NUM];

    CalibrateData_s *samp = pdat_samp;
    memset(samp, 0x00, sizeof(CalibrateData_s));

    delete_num = SAMPLE_NUM / 4;

    for(i = 0; i < SAMPLE_NUM; i++)
    {
        hal_timer.msdly(320);    // 延时等待寄存器更新(14.5HZ)
        Urms[i]     = emu_vol_val(CHANNEL_1);
        Irms1[i]    = emu_cur_val(CHANNEL_1);
        Irms2[i]    = emu_cur_val(CHANNEL_2);
        Power_P1[i] = emu_power_val(P_POWER, CHANNEL_1);
        Power_P2[i] = emu_power_val(P_POWER, CHANNEL_2);
        Power_Q1[i] = emu_power_val(Q_POWER, CHANNEL_1);
        Power_Q2[i] = emu_power_val(Q_POWER, CHANNEL_2);
    }

    select_sort(Urms, SAMPLE_NUM);
    select_sort(Irms1, SAMPLE_NUM);
    select_sort(Irms2, SAMPLE_NUM);
    select_sort(Power_P1, SAMPLE_NUM);
    select_sort(Power_P2, SAMPLE_NUM);
    select_sort(Power_Q1, SAMPLE_NUM);
    select_sort(Power_Q2, SAMPLE_NUM);
    for(i = delete_num; i < (SAMPLE_NUM - delete_num); i++)    // 去掉最小最大值
    {
        samp->Urms[0] += Urms[i];
        samp->Irms[0] += Irms1[i];
        samp->Irms[1] += Irms2[i];
        samp->power_p[0] += Power_P1[i];
        samp->power_p[1] += Power_P2[i];
        samp->power_q[0] += Power_Q1[i];
        samp->power_q[1] += Power_Q2[i];
    }

    samp->Urms[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->Irms[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->Irms[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_p[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_p[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_q[0] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_q[1] /= (SAMPLE_NUM - delete_num * 2);
    samp->power_s[0] = sqrt(samp->power_p[0] * samp->power_p[0] + samp->power_q[0] * samp->power_q[0]);
    samp->power_s[1] = sqrt(samp->power_p[1] * samp->power_p[1] + samp->power_q[1] * samp->power_q[1]);
}

//     float Urms[3];               ///A,B,C相电压
//     float Irms[4];               ///A,B,C,N相电流
//     float power_p[3];            ///A,B,C相有功功率
//     float power_q[3];            ///A,B,C相无功功率
//     float power_s[3];            ///A,B,C相视在功率
//     float err[3];

/**
 * @description: 台体加 Un、Ib、功率因数 1.0，电压电流功率的增益校验
 * @param {void} *pdat_std
 * @param {void} *pdat_samp
 * @return {*}
 */
static int calibrate_Un_Ib_1p0(cal_para_s *_para, CALIBRATE_STEP_TYPE type, float rms)
{
    if(type != STEP_VOL && type != STEP_CUR) return -1;
    /// @brief 避免校验的时候按错
    if(type == STEP_VOL && rms < INPUT_VOLTAGE * 0.9) return -1;
    if(type == STEP_CUR && rms < BASE_CURRENT * 0.9) return -1;

    uint16_t Gain     = 0;
    float    data0    = 0.0;
    float    data_reg = 0.0;

    data0 = rms / ((type == STEP_VOL) ? Kv : Ki);

    // 寄存器的数值
    for(size_t i = 0; i < SAMPLE_1P0_NUM; i++)
    {
        if(i != 0) hal_timer.msdly(_14HZ);    // 14.0625Hz
        data_reg += (float)(((type == STEP_VOL) ? EMU->URMS : EMU->IARMS) / SAMPLE_1P0_NUM);
    }

    Gain = (uint16_t)(((data0 / data_reg - 1) * DATA_2EXP15) + ((data0 > data_reg) ? 0 : DATA_2EXP16));

    // 先暂存校验系数，到时候统一写入内存
    RN8211_CAL_WRITE_ENABLE();
    EMU->HFConst   = HF_CONST;
    _para->HFconst = HF_CONST;
    if(type == STEP_VOL)
    {
        _para->U_gain = Gain;
        RN8211_CAL_SET(UGAIN, Gain);
    }
    else
    {
        _para->I_gain = Gain;
        RN8211_CAL_SET(IAGAIN, Gain);
    }
    RN8211_CAL_WRITE_CLOSE();
    RN8211_CAL_WAIT_32NOP();
    return 0;
}

/**
 * @description: 台体加 Un、Ib、功率因数 0.5L，相位校正
 * @param {void} *pdat_std
 * @param {void} *pdat_samp
 * @return {*}
 */
static int calibrate_Un_Ib_0p5l(cal_para_s *_para, float p0)
{
    if(p0 < INPUT_VOLTAGE * BASE_CURRENT * 0.4) return -1;

    float p_reg = 0.0;
    float err   = 0.0;

    for(size_t i = 0; i < SAMPLE_1P0_NUM; i++)
    {
        if(i != 0) hal_timer.msdly(_1P7HZ);    // 1.7578125Hz
        p_reg += (float)EMU->PowerPA / SAMPLE_1P0_NUM;
    }

    /// @brief 计算误差
    err = (p_reg - (p0 / Kp)) / (p0 / Kp);

    double theta = asin(-err / sqrt(3.0));
    double deg   = (theta * 180.0) / m_PI / _50HZ_LSB;

    int16_t phs_val_signed = (int16_t)round(deg);

    // （8位有效）
    if(phs_val_signed > 255) { phs_val_signed = 255; }
    else if(phs_val_signed < -255) { phs_val_signed = -255; }

    uint16_t phs_val = (uint16_t)(phs_val_signed + ((phs_val_signed < 0) ? 512 : 0));

    phs_val &= 0x1FF;    // 9位有效数据

    /// @brief 相位偏移,暂存，后续统一写入
    _para->Phs_ofst = phs_val;

    RN8211_CAL_WRITE_ENABLE();
    RN8211_CAL_SET(PhsA, phs_val);
    RN8211_CAL_WRITE_CLOSE();
    RN8211_CAL_WAIT_32NOP();

    return 0;
}

/**
 * @description: 有功偏置 OFFSET 校正： 台体加 Un、10%Ib 或 5%Ib、功率因数 1.0
 * @param {void} *pdat_std
 * @param {void} *pdat_samp
 * @return {*}
 */
static int calibrate_Un_0p1Ib_1p0(cal_para_s *_para, float p0)
{
    /// @brief 有功偏置 OFFSET 校正： 台体加 Un、10%Ib 或 5%Ib、功率因数 1.0
    int32_t p_reg[SAMPLE_NUM + 1] = {0x00};
    int32_t q_reg[SAMPLE_NUM + 1] = {0x00};

    /// 等待数据稳定
    hal_timer.msdly(300);

    /// 读取多次P，注意数据位数
    for(size_t i = 0; i < SAMPLE_NUM; i++)
    {
        p_reg[i] = RN8211_CAL_REG(PowerPA);
        q_reg[i] = RN8211_CAL_REG(PowerQA);
        hal_timer.msdly(_1P7HZ);
        hal_mcu.wdg_clr();
    }

    /// 计算平均值
    for(size_t i = 0; i < SAMPLE_NUM; i++)
    {
        p_reg[SAMPLE_NUM] += p_reg[i];
        q_reg[SAMPLE_NUM] += q_reg[i];
    }
    p_reg[SAMPLE_NUM] /= SAMPLE_NUM;
    q_reg[SAMPLE_NUM] /= SAMPLE_NUM;

    /// 计算误差
    float p_temp = p0 / Kp;
    float err    = (p_reg[SAMPLE_NUM] - p_temp) / (p_temp);

    uint16_t pAPOSA = 0x00;
    if(err > 0) { pAPOSA = (uint16_t)(DATA_2EXP16 + (-err) * p_temp); }
    else { pAPOSA = (uint16_t)((-err) * p_temp); }

    /// 计算无功功率误差的补码
    int32_t  q_offset = -(int32_t)q_reg[SAMPLE_NUM];
    uint16_t qAQOSA;
    if(q_offset >= 0) { qAQOSA = (uint16_t)q_offset; }
    else { qAQOSA = (uint16_t)(q_offset + DATA_2EXP16); }

    /// @brief 功率偏移,暂存，后续统一写入
    _para->P_ofst = pAPOSA;
    // _para->Q_ofst = qAQOSA;    // 暂存无功偏置
    qAQOSA = 0x00;

    // 一次性写入所有校准参数
    RN8211_CAL_WRITE_ENABLE();
    RN8211_CAL_SET(APOSA, pAPOSA);
    RN8211_CAL_SET(RPOSA, qAQOSA);    // RPOSA 是无功功率A相偏置寄存器
    RN8211_CAL_WRITE_CLOSE();
    RN8211_CAL_WAIT_32NOP();
    return 0;
}

/**
 * @description: 电流有效值 OFFSET 校正： 台体加 Un 空载
 * @param {void} *pdat_std
 * @param {void} *pdat_samp
 * @return {*}
 */
static int calibrate_Un_0Ib(cal_para_s *_para, float i0)
{
    if(i0 > 0.2) return -1;

    /// @brief 电流有效值 OFFSET 校正： 台体加 Un 空载
    /// 状态机读取十次电流（间隔大于100ms），平方后取反
    uint32_t i_Armsos[SAMPLE_NUM + 1] = {0x00};

    // 值会很小，直接除以10大概率直接等于0
    for(size_t i = 0; i < SAMPLE_NUM; i++)
    {
        i_Armsos[i] = RN8211_CAL_REG(IARMS);
        hal_timer.msdly(_14HZ * 2);
        hal_mcu.wdg_clr();
    }
    for(size_t i = 0; i < SAMPLE_NUM; i++) { i_Armsos[SAMPLE_NUM] += i_Armsos[i]; }
    i_Armsos[SAMPLE_NUM] /= SAMPLE_NUM;

    uint16_t iarmsos = (DATA_2EXP24 - i_Armsos[SAMPLE_NUM] * i_Armsos[SAMPLE_NUM]) / 256;

    _para->I_ofst = iarmsos;

    RN8211_CAL_WRITE_ENABLE();
    RN8211_CAL_SET(IARMSOS, iarmsos);
    RN8211_CAL_WRITE_CLOSE();
    RN8211_CAL_WAIT_32NOP();
    return 0;
}

const sEmuCheckConfigReg_TypeDef emu_reg[] = {
    /*效验寄存器指针			寄存器默认值*/
    {(uint32_t *)&EMU->EMUCON, 0x1C0007},               //
    {(uint32_t *)&EMU->EMUCON2, 0x0000},                //
    {(uint32_t *)&EMU->HFConst, (uint32_t)HF_CONST},    //
    {(uint32_t *)&EMU->PStart, (uint32_t)P_start},      //
    {(uint32_t *)&EMU->QStart, (uint32_t)Q_start},      //
    {(uint32_t *)&EMU->GPQA, 0x0000},                   //
    {(uint32_t *)&EMU->GPQB, 0x0000},                   //
    {(uint32_t *)&EMU->PhsA, 0x0000},                   //
    {(uint32_t *)&EMU->PhsB, 0x0000},                   //
    {(uint32_t *)&EMU->QPhsCal, 0x0000},                //
    {(uint32_t *)&EMU->APOSA, 0x0000},                  //
    {(uint32_t *)&EMU->APOSB, 0x0000},                  //
    {(uint32_t *)&EMU->RPOSA, 0x0000},                  //
    {(uint32_t *)&EMU->RPOSB, 0x0000},                  //
    {(uint32_t *)&EMU->IARMSOS, 0x0000},                //
    {(uint32_t *)&EMU->IBRMSOS, 0x0000},                //
    {(uint32_t *)&EMU->URMSOS, 0x0000},                 //
    {(uint32_t *)&EMU->IAGAIN, 0x0000},                 //
    {(uint32_t *)&EMU->IBGAIN, 0x0000},                 //
    {(uint32_t *)&EMU->UGAIN, 0x0000},                  //
    {(uint32_t *)&EMU->IADCOS, 0x0000},                 //
    {(uint32_t *)&EMU->IBDCOS, 0x0000},                 //
    {(uint32_t *)&EMU->UDCOS, 0x0000},                  //
    {(uint32_t *)&EMU->UADD, 0x0000},                   //
    {(uint32_t *)&EMU->USAG, 0x0000},                   //
    {(uint32_t *)&EMU->IAPEAK, 0x0000},                 //
    {(uint32_t *)&EMU->IBPEAK, 0x0000},                 //
    {(uint32_t *)&EMU->UPEAK, 0x0000},                  //
    {(uint32_t *)&EMU->D2FP, 0x0000},                   //
};

static void rn8211_emu_clr(void)
{
    // 使能 EMU 写操作
    RN8211_CAL_WRITE_ENABLE();

    for(size_t i = 0; i < sizeof(emu_reg) / sizeof(sEmuCheckConfigReg_TypeDef); i++)
    {
        // 逐个写
        *(emu_reg[i].RegAd) = emu_reg[i].DefaultValue;
    }

    // 关闭 EMU 写操作
    RN8211_CAL_WRITE_CLOSE();
}

static void save_cal_para(uint8_t chn)
{
    cal_para_s *ptr = cal_para + chn;
    /* 等待芯片内部校验和完整 */
    emu_reg_check_sum_pro(CS_SET);
    ptr->chk = RN8211_CAL_REG(EMUStatus) & EMU_EMUSTATUS_CHKSUM1;
    ptr->cs  = CAL_CS16(ptr, sizeof(cal_para_s));

    mcu_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
    ee_cal_write(chn * sizeof(cal_para_s), ptr, sizeof(cal_para_s));
}

#pragma optimize = none
/// @brief 校表处理
/// @param chn  通道
/// @param step 校表步骤
/// @param pdat_std  标准数据缓冲
/// @param pdat_samp 采样数据缓冲
/// @return
static uint32_t calibrate_proc(uint8_t chn, uint8_t step, void *pdat_std, void *pdat_samp)
{
    cal_para_s *ptr = cal_para + chn;
    (void)(pdat_samp);

    // 单相表
    if(chn >= CHANNEL_NUM || ((measure_para.wiring_mode == MIC_CHN_PHASE_MODE) && (chn == CHANNEL_2))) return 0;
    switch(step)
    {
        case STEP_INIT:    // 校表初始化
        {
            rn8211_emu_clr();
            // 等待数据写生效
            hal_timer.msdly(500);
            /* 载入初始校表参数 */
            memcpy(ptr, &cal_para_default, sizeof(cal_para_s));
            instant_value.stus.uncal |= 1 << chn;
            break;
        }

        case STEP_PHASE_INIT: {
            ptr->Phs_ofst = 0;
            break;
        }

        case STEP_POFST_INIT: {
            ptr->P_ofst = 0;
            break;
        }

        case STEP_QOFST_INIT: {
            ptr->Q_ofst = 0;
            break;
        }

        case STEP_HFCONST:    // 高频脉冲常数调整
        {
            break;
        }

        case STEP_I2GAIN:    // I2GAIN调整
        {
            break;
        }

        case STEP_SAMPLE:    // 电表计量采样平均处理
        {
            calibrate_sample(pdat_samp);
            break;
        }

        case STEP_VOL:    // 校正电压
        {
            calibrate_Un_Ib_1p0(ptr, STEP_VOL, ((CalibrateData_s *)pdat_std)->Urms[0]);
            break;
        }

        case STEP_CUR:    // 校正电流
        {
            calibrate_Un_Ib_1p0(ptr, STEP_CUR, ((CalibrateData_s *)pdat_std)->Irms[0]);
            break;
        }

        case STEP_POWER:    // 校正功率增益，可忽略
        {
            break;
        }

        case STEP_PHASE:    // 校正相角
        {
            calibrate_Un_Ib_0p5l(ptr, ((CalibrateData_s *)pdat_std)->power_p[0]);
            break;
        }

        case STEP_POFST:    // 功率偏置//有功
        {
            calibrate_Un_0p1Ib_1p0(ptr, ((CalibrateData_s *)pdat_std)->power_p[0]);
            break;
        }

        case STEP_QOFST:    // 无功功率偏置，同上不需要
        {
            break;
        }

        case STEP_IOFST:    // 电流偏置
        {
            calibrate_Un_0Ib(ptr, ((CalibrateData_s *)pdat_std)->Irms[0]);
            break;
        }

        case STEP_CONST:    // 刷新计量参数(脉冲常数 -加倍, 只接受0,2,4,8倍)
        {
        }

        case STEP_REFRESH:    // 刷新计量参数，重启计量
        {
            emu_para_refresh(cal_para, 0);
            hal_timer.msdly(1300);    // 延时等待计量稳定
            return 0;
        }

        case STEP_SETPARA:    // 设置计量参数
        {
            MeasurePara_s *para = (MeasurePara_s *)pdat_std;
            if(para != &measure_para) { *para = measure_para; }
            measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
            if(ee_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s)))
            {
                mcu_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
                measure_ic_init(0);    // 切换计量模式需重新初始化计量
                return 1;
            }
            return 0;
        }

        case STEP_GETPARA: {
            return (uint32_t)&measure_para;    // 读计量参数
        }

        case STEP_READREG:    // 计量寄存器
        {
            uint16_t reg = get_lsbdata16(pdat_std);
            //            set_msbdata32(pdat_std, emu_register_read(reg));
            return 4;
        }

#if MIC_SUPPORT_SET_CAL_PARA
        case STEP_REDCAL: {
            memcpy(pdat_std, &measure_para[chn], sizeof(MeasurePara_s));
            return sizeof(MeasurePara_s);

            case STEP_SETCAL:
                memcpy(&measure_para[chn], pdat_std, sizeof(MeasurePara_s));
                ptr = &measure_para[chn];
                if(ptr->cs != CAL_CS16(ptr, sizeof(MeasurePara_s))) return 1;
                regChecksum = 0;
        }
#endif

        case STEP_SAVE:    // 保存校表参数
        {
            save_cal_para(chn);
            instant_value.stus.uncal &= ~(1 << chn);
            return 0;
        }

        default:    // 失败
            return 0;
    }
    emu_reg_check_sum_pro(CS_SET);
    ptr->chk = RN8211_CAL_REG(EMUStatus) & EMU_EMUSTATUS_CHKSUM1;
    ptr->cs  = CAL_CS16(ptr, sizeof(cal_para_s));

    return 0;
}
#pragma optimize =    // 恢复优化设置

#if 0
///相角获取
#define ANGLE_360 360.0
#define PI 3.141592
float measure_phase_angle_get(tAngleVector angle)
{
    float angle_val[2] = {0,0};
    tVector* vector = &angle.ref;
    float* vv_angle = instant_value.vv_angle;
    float* vi_angle = instant_value.vi_angle;

    for(uint8_t i = 0; i < 2; i++)
    {
        if(vector->type == VOL_VECTOR)
        {
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = ANGLE_360 - vv_angle[vector->chn - CHANNEL_2];
            }
        }
        else if(vector->type == CUR_VECTOR)
        {
            if(vector->chn == CHANNEL_1)
            {
                angle_val[i] = -(*vi_angle);
            }
            if(vector->chn == CHANNEL_2 || vector->chn == CHANNEL_3)
            {
                angle_val[i] = -vv_angle[vector->chn - CHANNEL_2] - vi_angle[vector->chn - CHANNEL_1];
            }
        }
        vector++;
    }
    angle_val[0] = angle_val[1] - angle_val[0];

    if(more(angle_val[0], ANGLE_360)) angle_val[0] = ANGLE_360;
    else if(less(angle_val[0], 0.0)) angle_val[0] = ANGLE_360 - 0.1 + angle_val[0];

    return angle_val[0];
}
#endif

/// @brief 读取计量参数
/// @param tag 偏移地址
/// @return    返回数据
uint32_t mic_measure_para_get(uint16_t tag)
{
    uint32_t val = 0;

    switch(tag)
    {
        case member_offset(MeasurePara_s, acc_mode):
            val = measure_para.acc_mode;
            break;

        case member_offset(MeasurePara_s, wiring_mode):
            val = measure_para.wiring_mode;
            break;

        case member_offset(MeasurePara_s, led_mode):
            val = measure_para.led_mode;
            break;

        case member_offset(MeasurePara_s, constant):
            val = measure_para.constant;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, pt_numerator)):
            val = measure_para.ratio.pt_numerator;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, pt_denominator)):
            val = measure_para.ratio.pt_denominator;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, ct_numerator)):
            val = measure_para.ratio.ct_numerator;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, ct_denominator)):
            val = measure_para.ratio.ct_denominator;
            break;

        default:
            return 0;
    }
    return val;
}

/// @brief 设置计量参数
/// @param tag 偏移地址
/// @param in  输入缓冲区
/// @param len 输入数据长度
/// @return    返回true表示成功，false表示失败
bool mic_measure_para_set(uint16_t tag, uint32_t val)
{
    uint8_t is_init = 1;
    switch(tag)
    {
        case member_offset(MeasurePara_s, acc_mode):
            measure_para.acc_mode = val;
            break;

        case member_offset(MeasurePara_s, wiring_mode):
            measure_para.wiring_mode = val;
            break;

        case member_offset(MeasurePara_s, led_mode):
            measure_para.led_mode = val;
            break;

        case member_offset(MeasurePara_s, constant):
            measure_para.constant = val;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, pt_numerator)):
            measure_para.ratio.pt_numerator = val;
            is_init                         = 0;    // 修改变比不重新初始化计量芯片
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, pt_denominator)):
            measure_para.ratio.pt_denominator = val;
            is_init                           = 0;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, ct_numerator)):
            measure_para.ratio.ct_numerator = val;
            is_init                         = 0;
            break;

        case(member_offset(MeasurePara_s, ratio) + member_offset(pt_ct_s, ct_denominator)):
            measure_para.ratio.ct_denominator = val;
            is_init                           = 0;
            break;

        default:
            return false;
    }

    measure_para.cs = CAL_CS16(&measure_para, sizeof(MeasurePara_s));
    if(ee_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s)))
    {
        mcu_cal_write(MEASURE_PARA_ADDR, &measure_para, sizeof(MeasurePara_s));
        if(is_init) measure_ic_init(0);    // 切换计量模式需重新初始化计量
        return true;
    }
    return false;
}

#if USE_EMU_AT_LOSS_VOLTAGE
void mic_power_off_running(void)
{
    if(power_off_running != NULL)
    {
        // 刷新电流
        // 判断全失压
        power_off_running();
    }
}

void mic_power_off_callback(void func(void))
{
    power_off_running = func;
}

#endif
/// @brief 计量芯片接口
const struct mic_s mic = {
    .ins             = &instant_value,
    .init            = measure_ic_init,
    .off             = measure_ic_off,
    .ins_val_refresh = instant_refresh,
#if USE_EMU_AT_LOSS_VOLTAGE
    .poweroff_run      = mic_power_off_running,
    .poweroff_callback = mic_power_off_callback,
#else
    .poweroff_run      = NULL,
    .poweroff_callback = NULL,
#endif
    .pulse            = measure_pulse_get,
    .cali             = calibrate_proc,
    .measure_para_get = mic_measure_para_get,
    .measure_para_set = mic_measure_para_set,
};

///
