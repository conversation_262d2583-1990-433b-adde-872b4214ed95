/**
 ******************************************************************************
* @file    lcd.h
* <AUTHOR> @date    2024
* @brief   液晶屏选型及图标映射关系头文件
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __LCD_H
#define __LCD_H

/* Includes -----------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "bsp_lcd.h"

/// @brief 应用层图标显示映射定义
struct icon_map_s
{
    ICON_TYPE_t app_icon;    /// 应用层图标
    uint8_t     seg_num;     /// 图标的SEG数量
    const void *seg_tab;     /// 图标的SEG组合表指针
};

#if (LCD_TYPE == LCD_JSH209105Y)  ///国网三相表液晶
///LCD_JSH209105Y 图标定义
#include "lcd_jsh209105y.c"
/// @brief 应用层图标显示映射表，只需要定义液晶屏支持的图标
static const struct icon_map_s icon_map_list[] =
{
    {.app_icon = TYPE_ICON_TIME,            .seg_tab = unit_time,            .seg_num = eleof(unit_time)},          // 时间指示符号 hh:mm:ss
    {.app_icon = TYPE_ICON_DATE,            .seg_tab = unit_date,            .seg_num = eleof(unit_date)},          // 日期指示符号 YY.MM.DD

    {.app_icon = TYPE_ICON_SIGN,            .seg_tab = unit_sign,            .seg_num = eleof(unit_sign)},          // 正负'-'符号
    {.app_icon = TYPE_ICON_KWH,             .seg_tab = unit_kWh,             .seg_num = eleof(unit_kWh)},           // 有功电能单位符号
    {.app_icon = TYPE_ICON_KVARH,           .seg_tab = unit_kvarh,           .seg_num = eleof(unit_kvarh)},         // 无功电能单位符号
    {.app_icon = TYPE_ICON_KVAH,            .seg_tab = unit_kVAh,            .seg_num = eleof(unit_kVAh)},          // 视在电能单位符号

    {.app_icon = TYPE_ICON_KW,              .seg_tab = unit_kW,              .seg_num = eleof(unit_kW)},            // 有功功率单位符号,KW
    {.app_icon = TYPE_ICON_KVAR,            .seg_tab = unit_kvar,            .seg_num = eleof(unit_kvar)},          // 无功功率单位符号,KVAR
    {.app_icon = TYPE_ICON_KVA,             .seg_tab = unit_kVA,             .seg_num = eleof(unit_kVA)},           // 视在功率单位符号,KVA

    {.app_icon = TYPE_ICON_V,               .seg_tab = unit_V,               .seg_num = eleof(unit_V)},             // 电压单位符号 
    {.app_icon = TYPE_ICON_A,               .seg_tab = unit_A,               .seg_num = eleof(unit_A)},             // 电流单位符号 
    {.app_icon = TYPE_ICON_Hz,              .seg_tab = unit_Hz,              .seg_num = eleof(unit_Hz)},            // 频率单位符号

    {.app_icon = TYPE_ICON_YUAN,            .seg_tab = icon_money,           .seg_num = eleof(icon_money)},         // 元单位符号

    {.app_icon = TYPE_ICON_P_START,         .seg_tab = icon_arrow_pq,        .seg_num = eleof(icon_arrow_pq)},      // 象限坐标
    {.app_icon = TYPE_ICON_Q1,              .seg_tab = icon_arrow_q1,        .seg_num = eleof(icon_arrow_q1)},      // 功率象限I符号
    {.app_icon = TYPE_ICON_Q2,              .seg_tab = icon_arrow_q2,        .seg_num = eleof(icon_arrow_q2)},      // 功率象限II符号
    {.app_icon = TYPE_ICON_Q3,              .seg_tab = icon_arrow_q3,        .seg_num = eleof(icon_arrow_q3)},      // 功率象限III符号
    {.app_icon = TYPE_ICON_Q4,              .seg_tab = icon_arrow_q4,        .seg_num = eleof(icon_arrow_q4)},      // 功率象限IV符号

    {.app_icon = TYPE_ICON_DANG_QIAN,       .seg_tab = icon_dang_qian,       .seg_num = eleof(icon_dang_qian)},     // 当前 符号
    {.app_icon = TYPE_ICON_SHANG_1,         .seg_tab = icon_shang_1yue,      .seg_num = eleof(icon_shang_1yue)},    // 上1月
    {.app_icon = TYPE_ICON_SHANG_2,         .seg_tab = icon_shang_2yue,      .seg_num = eleof(icon_shang_2yue)},    // 上2月
    {.app_icon = TYPE_ICON_SHANG_3,         .seg_tab = icon_shang_3yue,      .seg_num = eleof(icon_shang_3yue)},    // 上3月
    {.app_icon = TYPE_ICON_SHANG_4,         .seg_tab = icon_shang_4yue,      .seg_num = eleof(icon_shang_4yue)},    // 上4月
    {.app_icon = TYPE_ICON_SHANG_5,         .seg_tab = icon_shang_5yue,      .seg_num = eleof(icon_shang_5yue)},    // 上5月
    {.app_icon = TYPE_ICON_SHANG_6,         .seg_tab = icon_shang_6yue,      .seg_num = eleof(icon_shang_6yue)},    // 上6月
    {.app_icon = TYPE_ICON_SHANG_7,         .seg_tab = icon_shang_7yue,      .seg_num = eleof(icon_shang_7yue)},    // 上7月
    {.app_icon = TYPE_ICON_SHANG_8,         .seg_tab = icon_shang_8yue,      .seg_num = eleof(icon_shang_8yue)},    // 上8月
    {.app_icon = TYPE_ICON_SHANG_9,         .seg_tab = icon_shang_9yue,      .seg_num = eleof(icon_shang_9yue)},    // 上9月
    {.app_icon = TYPE_ICON_SHANG_10,        .seg_tab = icon_shang_10yue,     .seg_num = eleof(icon_shang_10yue)},   // 上10月
    {.app_icon = TYPE_ICON_SHANG_11,        .seg_tab = icon_shang_11yue,     .seg_num = eleof(icon_shang_11yue)},   // 上11月
    {.app_icon = TYPE_ICON_SHANG_12,        .seg_tab = icon_shang_12yue,     .seg_num = eleof(icon_shang_12yue)},   // 上12月

    {.app_icon = TYPE_ICON_ZU_HE,           .seg_tab = icon_zu_he,           .seg_num = eleof(icon_zu_he)},         // 组合
    {.app_icon = TYPE_ICON_NEG,             .seg_tab = icon_neg,             .seg_num = eleof(icon_neg)},           // 反向
    {.app_icon = TYPE_ICON_POS,             .seg_tab = icon_pos,             .seg_num = eleof(icon_pos)},           // 正向
    {.app_icon = TYPE_ICON_REACT,           .seg_tab = icon_react,           .seg_num = eleof(icon_react)},         // 无功
    {.app_icon = TYPE_ICON_ACT,             .seg_tab = icon_act,             .seg_num = eleof(icon_act)},           // 有功
    {.app_icon = TYPE_ICON_TOTAL,           .seg_tab = icon_total,           .seg_num = eleof(icon_total)},         // 总
    {.app_icon = TYPE_ICON_TARIFF,          .seg_tab = icon_tariff,          .seg_num = eleof(icon_tariff)},        // 费率

    {.app_icon = TYPE_ICON_Q_I,             .seg_tab = icon_react_q1,        .seg_num = eleof(icon_react_q1)},      // 无功I
    {.app_icon = TYPE_ICON_Q_II,            .seg_tab = icon_react_q2,        .seg_num = eleof(icon_react_q2)},      // 无功II
    {.app_icon = TYPE_ICON_Q_III,           .seg_tab = icon_react_q3,        .seg_num = eleof(icon_react_q3)},      // 无功III
    {.app_icon = TYPE_ICON_Q_IV,            .seg_tab = icon_react_q4,        .seg_num = eleof(icon_react_q4)},      // 无功IV

    {.app_icon = TYPE_ICON_TF1,             .seg_tab = icon_tariff_1,        .seg_num = eleof(icon_tariff_1)},      // 费率'1'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF2,             .seg_tab = icon_tariff_2,        .seg_num = eleof(icon_tariff_2)},      // 费率'2'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF3,             .seg_tab = icon_tariff_3,        .seg_num = eleof(icon_tariff_3)},      // 费率'3'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF4,             .seg_tab = icon_tariff_4,        .seg_num = eleof(icon_tariff_4)},      // 费率'4'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF5,             .seg_tab = icon_tariff_5,        .seg_num = eleof(icon_tariff_5)},      // 费率'5'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF6,             .seg_tab = icon_tariff_6,        .seg_num = eleof(icon_tariff_6)},      // 费率'6'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF7,             .seg_tab = icon_tariff_7,        .seg_num = eleof(icon_tariff_7)},      // 费率'7'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF8,             .seg_tab = icon_tariff_8,        .seg_num = eleof(icon_tariff_8)},      // 费率'8'符号 (指示显示内容)

    {.app_icon = TYPE_ICON_POWER,           .seg_tab = icon_power,           .seg_num = eleof(icon_power)},         // 功率
    {.app_icon = TYPE_ICON_POWER_A,         .seg_tab = icon_power_A,         .seg_num = eleof(icon_power_A)},       // A相功率
    {.app_icon = TYPE_ICON_POWER_B,         .seg_tab = icon_power_B,         .seg_num = eleof(icon_power_B)},       // B相功率
    {.app_icon = TYPE_ICON_POWER_C,         .seg_tab = icon_power_C,         .seg_num = eleof(icon_power_C)},       // C相功率

    {.app_icon = TYPE_ICON_VOLTAGE_A,       .seg_tab = icon_voltage_A,       .seg_num = eleof(icon_voltage_A)},     // A相电压
    {.app_icon = TYPE_ICON_VOLTAGE_B,       .seg_tab = icon_voltage_B,       .seg_num = eleof(icon_voltage_B)},     // B相电压
    {.app_icon = TYPE_ICON_VOLTAGE_C,       .seg_tab = icon_voltage_C,       .seg_num = eleof(icon_voltage_C)},     // C相电压

    {.app_icon = TYPE_ICON_CURRENT_A,       .seg_tab = icon_current_A,       .seg_num = eleof(icon_current_A)},     // A相电流
    {.app_icon = TYPE_ICON_CURRENT_B,       .seg_tab = icon_current_B,       .seg_num = eleof(icon_current_B)},     // B相电流
    {.app_icon = TYPE_ICON_CURRENT_C,       .seg_tab = icon_current_C,       .seg_num = eleof(icon_current_C)},     // C相电流
    {.app_icon = TYPE_ICON_CURRENT_N,       .seg_tab = icon_current_N,       .seg_num = eleof(icon_current_N)},     // N路电流

    {.app_icon = TYPE_ICON_PF_A,            .seg_tab = icon_pf_A,            .seg_num = eleof(icon_pf_A)},          // A相功率因素
    {.app_icon = TYPE_ICON_PF_B,            .seg_tab = icon_pf_B,            .seg_num = eleof(icon_pf_B)},          // B相功率因素
    {.app_icon = TYPE_ICON_PF_C,            .seg_tab = icon_pf_C,            .seg_num = eleof(icon_pf_C)},          // C相功率因素

    {.app_icon = TYPE_ICON_JIE_TI,          .seg_tab = icon_jie_ti,          .seg_num = eleof(icon_jie_ti)},        // 阶梯
    {.app_icon = TYPE_ICON_ENERGY,          .seg_tab = icon_energy,          .seg_num = eleof(icon_energy)},        // 电量
    {.app_icon = TYPE_ICON_REMAIN_kWh,      .seg_tab = icon_remain_kWh,      .seg_num = eleof(icon_remain_kWh)},    // 剩余电量
    {.app_icon = TYPE_ICON_REMAIN_MONEY,    .seg_tab = icon_remain_money,    .seg_num = eleof(icon_remain_money)},  // 剩余电费
    {.app_icon = TYPE_ICON_DEMAND,          .seg_tab = icon_demand,          .seg_num = eleof(icon_demand)},        // 需量
    {.app_icon = TYPE_ICON_SHI_YA,          .seg_tab = icon_voltage_miss,    .seg_num = eleof(icon_voltage_miss)},  // 失压
    {.app_icon = TYPE_ICON_SHI_LIU,         .seg_tab = icon_current_miss,    .seg_num = eleof(icon_current_miss)},  // 失流
    {.app_icon = TYPE_ICON_SHI_JIAN,        .seg_tab = icon_shi_jian,        .seg_num = eleof(icon_shi_jian)},      // 时间
    {.app_icon = TYPE_ICON_SHI_DUAN,        .seg_tab = icon_shi_duan,        .seg_num = eleof(icon_shi_duan)},      // 时段

    {.app_icon = TYPE_ICON_Ua,              .seg_tab = icon_Ua,              .seg_num = eleof(icon_Ua)},            // A相电压指示符号
    {.app_icon = TYPE_ICON_Ub,              .seg_tab = icon_Ub,              .seg_num = eleof(icon_Ub)},            // B相电压指示符号
    {.app_icon = TYPE_ICON_Uc,              .seg_tab = icon_Uc,              .seg_num = eleof(icon_Uc)},            // C相电压指示符号

    {.app_icon = TYPE_ICON_Ia,              .seg_tab = icon_Ia,              .seg_num = eleof(icon_Ia)},            // A相电流指示符号
    {.app_icon = TYPE_ICON_Ib,              .seg_tab = icon_Ib,              .seg_num = eleof(icon_Ib)},            // B相电流指示符号
    {.app_icon = TYPE_ICON_Ic,              .seg_tab = icon_Ic,              .seg_num = eleof(icon_Ic)},            // C相电流指示符号

    {.app_icon = TYPE_ICON_Pa_s,            .seg_tab = icon_Pa_s,            .seg_num = eleof(icon_Pa_s)},          // A相功率反向指示符号
    {.app_icon = TYPE_ICON_Pb_s,            .seg_tab = icon_Pb_s,            .seg_num = eleof(icon_Pb_s)},          // B相功率反向指示符号
    {.app_icon = TYPE_ICON_Pc_s,            .seg_tab = icon_Pc_s,            .seg_num = eleof(icon_Pc_s)},          // C相功率反向指示符号

    {.app_icon = TYPE_ICON_CUR_PRICE_SUITE, .seg_tab = icon_cur_suite,       .seg_num = eleof(icon_cur_suite)},     // 当前套电价 三角1
    {.app_icon = TYPE_ICON_BAK_PRICE_SUITE, .seg_tab = icon_bak_suite,       .seg_num = eleof(icon_bak_suite)},     // 备用套电价 三角2

    {.app_icon = TYPE_ICON_L_1,             .seg_tab = icon_L_1,             .seg_num = eleof(icon_L_1)},           // 当前运行第1阶梯电价
    {.app_icon = TYPE_ICON_L_2,             .seg_tab = icon_L_2,             .seg_num = eleof(icon_L_2)},           // 当前运行第2阶梯电价
    {.app_icon = TYPE_ICON_L_3,             .seg_tab = icon_L_3,             .seg_num = eleof(icon_L_3)},           // 当前运行第3阶梯电价
    {.app_icon = TYPE_ICON_L_4,             .seg_tab = icon_L_4,             .seg_num = eleof(icon_L_4)},           // 当前运行第4阶梯电价
    {.app_icon = TYPE_ICON_L_5,             .seg_tab = icon_L_5,             .seg_num = eleof(icon_L_5)},           // 当前运行第5阶梯电价
    {.app_icon = TYPE_ICON_L_6,             .seg_tab = icon_L_6,             .seg_num = eleof(icon_L_6)},           // 当前运行第6阶梯电价
    {.app_icon = TYPE_ICON_L_7,             .seg_tab = icon_L_7,             .seg_num = eleof(icon_L_7)},           // 当前运行第7阶梯电价
    {.app_icon = TYPE_ICON_L_8,             .seg_tab = icon_L_8,             .seg_num = eleof(icon_L_8)},           // 当前运行第8阶梯电价

    {.app_icon = TYPE_ICON_CUR_TARIFF,      .seg_tab = icon_cur_tariff,      .seg_num = eleof(icon_cur_tariff)},    // 当前套费率 圆形1
    {.app_icon = TYPE_ICON_BAK_TARIFF,      .seg_tab = icon_bak_tariff,      .seg_num = eleof(icon_bak_tariff)},    // 备用套费率 圆形2  

    {.app_icon = TYPE_ICON_CUR_TF_1,        .seg_tab = icon_cur_tf_1,        .seg_num = eleof(icon_cur_tf_1)},      // 当前费率状态1 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_2,        .seg_tab = icon_cur_tf_2,        .seg_num = eleof(icon_cur_tf_2)},      // 当前费率状态2 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_3,        .seg_tab = icon_cur_tf_3,        .seg_num = eleof(icon_cur_tf_3)},      // 当前费率状态3 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_4,        .seg_tab = icon_cur_tf_4,        .seg_num = eleof(icon_cur_tf_4)},      // 当前费率状态4 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_5,        .seg_tab = icon_cur_tf_5,        .seg_num = eleof(icon_cur_tf_5)},      // 当前费率状态5 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_6,        .seg_tab = icon_cur_tf_6,        .seg_num = eleof(icon_cur_tf_6)},      // 当前费率状态6 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_7,        .seg_tab = icon_cur_tf_7,        .seg_num = eleof(icon_cur_tf_7)},      // 当前费率状态7 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_8,        .seg_tab = icon_cur_tf_8,        .seg_num = eleof(icon_cur_tf_8)},      // 当前费率状态8 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_9,        .seg_tab = icon_cur_tf_9,        .seg_num = eleof(icon_cur_tf_9)},      // 当前费率状态9 (指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_10,       .seg_tab = icon_cur_tf_10,       .seg_num = eleof(icon_cur_tf_10)},     // 当前费率状态10(指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_11,       .seg_tab = icon_cur_tf_11,       .seg_num = eleof(icon_cur_tf_11)},     // 当前费率状态11(指示当前运行费率)
    {.app_icon = TYPE_ICON_CUR_TF_12,       .seg_tab = icon_cur_tf_12,       .seg_num = eleof(icon_cur_tf_12)},     // 当前费率状态12(指示当前运行费率)

    {.app_icon = TYPE_ICON_SIGNALTOWER,     .seg_tab = icon_signal_tower,    .seg_num = eleof(icon_signal_tower)},  // 信号塔
    {.app_icon = TYPE_ICON_SIGNAL1,         .seg_tab = icon_signal,          .seg_num = 1},                         // 信号强度1
    {.app_icon = TYPE_ICON_SIGNAL2,         .seg_tab = icon_signal,          .seg_num = 2},                         // 信号强度2
    {.app_icon = TYPE_ICON_SIGNAL3,         .seg_tab = icon_signal,          .seg_num = 3},                         // 信号强度3
    {.app_icon = TYPE_ICON_SIGNAL4,         .seg_tab = icon_signal,          .seg_num = 4},                         // 信号强度4   

    {.app_icon = TYPE_ICON_COMM,            .seg_tab = icon_mudle_comm,      .seg_num = eleof(icon_mudle_comm)},    // 模块通信指示符号
    {.app_icon = TYPE_ICON_IR_COMM,         .seg_tab = icon_ir_comm,         .seg_num = eleof(icon_ir_comm)},       // 红外模块通信指示符号
    {.app_icon = TYPE_ICON_4851_COMM,       .seg_tab = icon_4851_comm,       .seg_num = eleof(icon_4851_comm)},     // 4851模块通信指示符号
    {.app_icon = TYPE_ICON_4852_COMM,       .seg_tab = icon_4852_comm,       .seg_num = eleof(icon_4852_comm)},     // 4852模块通信指示符号
    {.app_icon = TYPE_ICON_IR_VIRIFY,       .seg_tab = icon_ir_verify,       .seg_num = eleof(icon_ir_verify)},     // 红外认证有效
    {.app_icon = TYPE_ICON_HANG_UP,         .seg_tab = icon_hang_up,         .seg_num = eleof(icon_hang_up)},       // 电表挂起
    {.app_icon = TYPE_ICON_KEY,             .seg_tab = icon_key,             .seg_num = eleof(icon_key)},           // 测试密钥指示符号
    {.app_icon = TYPE_ICON_ALARM,           .seg_tab = icon_alarm,           .seg_num = eleof(icon_alarm)},         // 告警指示符号

    {.app_icon = TYPE_ICON_BAT1,            .seg_tab = icon_battery1_low,    .seg_num = eleof(icon_battery1_low)},  // 电池1
    {.app_icon = TYPE_ICON_BAT2,            .seg_tab = icon_battery2_low,    .seg_num = eleof(icon_battery2_low)},  // 电池2

    {.app_icon = TYPE_ICON_REV_SEQ,         .seg_tab = icon_rev_seq,         .seg_num = eleof(icon_rev_seq)},       // 逆序指示符号
    {.app_icon = TYPE_ICON_SUCCESS,         .seg_tab = icon_success,         .seg_num = eleof(icon_success)},       // 成功符号
    {.app_icon = TYPE_ICON_FAILURE,         .seg_tab = icon_fail,            .seg_num = eleof(icon_fail)},          // 失败符号
    {.app_icon = TYPE_ICON_PURCHASE,        .seg_tab = icon_purchase,        .seg_num = eleof(icon_purchase)},      // 请购电
    {.app_icon = TYPE_ICON_RLY_OFF,         .seg_tab = icon_relay_off,       .seg_num = eleof(icon_relay_off)},     // 继电器拉闸指示符号
};

#elif(LCD_TYPE == LCD_M11138AHRP)
#include "lcd_m11138ahrp.c"
static const struct icon_map_s icon_map_list[] =
{
    {.app_icon = TYPE_ICON_TIME,            .seg_tab = unit_time,            .seg_num = eleof(unit_time)},          // 时间指示符号 hh:mm:ss
    {.app_icon = TYPE_ICON_DATE,            .seg_tab = unit_date,            .seg_num = eleof(unit_date)},          // 日期指示符号 YY.MM.DD

    // {.app_icon = TYPE_ICON_SIGN,            .seg_tab = unit_sign,            .seg_num = eleof(unit_sign)},          // 正负'-'符号
    {.app_icon = TYPE_ICON_KWH,             .seg_tab = unit_kWh,             .seg_num = eleof(unit_kWh)},           // 有功电能单位符号
    // {.app_icon = TYPE_ICON_KVARH,           .seg_tab = unit_kvarh,           .seg_num = eleof(unit_kvarh)},         // 无功电能单位符号
    // {.app_icon = TYPE_ICON_KVAH,            .seg_tab = unit_kVAh,            .seg_num = eleof(unit_kVAh)},          // 视在电能单位符号

    {.app_icon = TYPE_ICON_KW,              .seg_tab = unit_kW,              .seg_num = eleof(unit_kW)},            // 有功功率单位符号,KW
    // {.app_icon = TYPE_ICON_KVAR,            .seg_tab = unit_kvar,            .seg_num = eleof(unit_kvar)},          // 无功功率单位符号,KVAR
    // {.app_icon = TYPE_ICON_KVA,             .seg_tab = unit_kVA,             .seg_num = eleof(unit_kVA)},           // 视在功率单位符号,KVA

    {.app_icon = TYPE_ICON_V,               .seg_tab = unit_V,               .seg_num = eleof(unit_V)},             // 电压单位符号 
    {.app_icon = TYPE_ICON_A,               .seg_tab = unit_A,               .seg_num = eleof(unit_A)},             // 电流单位符号 
    {.app_icon = TYPE_ICON_Hz,              .seg_tab = unit_Hz,              .seg_num = eleof(unit_Hz)},            // 频率单位符号

    {.app_icon = TYPE_ICON_YUAN,            .seg_tab = icon_money,           .seg_num = eleof(icon_money)},         // 元单位符号

    // {.app_icon = TYPE_ICON_P_START,         .seg_tab = icon_arrow_pq,        .seg_num = eleof(icon_arrow_pq)},      // 象限坐标
    // {.app_icon = TYPE_ICON_Q1,              .seg_tab = icon_arrow_q1,        .seg_num = eleof(icon_arrow_q1)},      // 功率象限I符号
    // {.app_icon = TYPE_ICON_Q2,              .seg_tab = icon_arrow_q2,        .seg_num = eleof(icon_arrow_q2)},      // 功率象限II符号
    // {.app_icon = TYPE_ICON_Q3,              .seg_tab = icon_arrow_q3,        .seg_num = eleof(icon_arrow_q3)},      // 功率象限III符号
    // {.app_icon = TYPE_ICON_Q4,              .seg_tab = icon_arrow_q4,        .seg_num = eleof(icon_arrow_q4)},      // 功率象限IV符号

    {.app_icon = TYPE_ICON_DANG_QIAN,       .seg_tab = icon_dang_qian,       .seg_num = eleof(icon_dang_qian)},     // 当前 符号
    {.app_icon = TYPE_ICON_SHANG_1,         .seg_tab = icon_shang_1yue,      .seg_num = eleof(icon_shang_1yue)},    // 上1月
    {.app_icon = TYPE_ICON_SHANG_2,         .seg_tab = icon_shang_2yue,      .seg_num = eleof(icon_shang_2yue)},    // 上2月
    {.app_icon = TYPE_ICON_SHANG_3,         .seg_tab = icon_shang_3yue,      .seg_num = eleof(icon_shang_3yue)},    // 上3月
    {.app_icon = TYPE_ICON_SHANG_4,         .seg_tab = icon_shang_4yue,      .seg_num = eleof(icon_shang_4yue)},    // 上4月
    {.app_icon = TYPE_ICON_SHANG_5,         .seg_tab = icon_shang_5yue,      .seg_num = eleof(icon_shang_5yue)},    // 上5月
    {.app_icon = TYPE_ICON_SHANG_6,         .seg_tab = icon_shang_6yue,      .seg_num = eleof(icon_shang_6yue)},    // 上6月
    {.app_icon = TYPE_ICON_SHANG_7,         .seg_tab = icon_shang_7yue,      .seg_num = eleof(icon_shang_7yue)},    // 上7月
    {.app_icon = TYPE_ICON_SHANG_8,         .seg_tab = icon_shang_8yue,      .seg_num = eleof(icon_shang_8yue)},    // 上8月
    {.app_icon = TYPE_ICON_SHANG_9,         .seg_tab = icon_shang_9yue,      .seg_num = eleof(icon_shang_9yue)},    // 上9月
    {.app_icon = TYPE_ICON_SHANG_10,        .seg_tab = icon_shang_10yue,     .seg_num = eleof(icon_shang_10yue)},   // 上10月
    {.app_icon = TYPE_ICON_SHANG_11,        .seg_tab = icon_shang_11yue,     .seg_num = eleof(icon_shang_11yue)},   // 上11月
    {.app_icon = TYPE_ICON_SHANG_12,        .seg_tab = icon_shang_12yue,     .seg_num = eleof(icon_shang_12yue)},   // 上12月

    // {.app_icon = TYPE_ICON_ZU_HE,           .seg_tab = icon_zu_he,           .seg_num = eleof(icon_zu_he)},         // 组合
    // {.app_icon = TYPE_ICON_NEG,             .seg_tab = icon_neg,             .seg_num = eleof(icon_neg)},           // 反向
    // {.app_icon = TYPE_ICON_POS,             .seg_tab = icon_pos,             .seg_num = eleof(icon_pos)},           // 正向
    // {.app_icon = TYPE_ICON_REACT,           .seg_tab = icon_react,           .seg_num = eleof(icon_react)},         // 无功
    // {.app_icon = TYPE_ICON_ACT,             .seg_tab = icon_act,             .seg_num = eleof(icon_act)},           // 有功
    {.app_icon = TYPE_ICON_TOTAL,           .seg_tab = icon_total,           .seg_num = eleof(icon_total)},         // 总
    {.app_icon = TYPE_ICON_TARIFF,          .seg_tab = icon_tariff,          .seg_num = eleof(icon_tariff)},        // 费率

    // {.app_icon = TYPE_ICON_Q_I,             .seg_tab = icon_react_q1,        .seg_num = eleof(icon_react_q1)},      // 无功I
    // {.app_icon = TYPE_ICON_Q_II,            .seg_tab = icon_react_q2,        .seg_num = eleof(icon_react_q2)},      // 无功II
    // {.app_icon = TYPE_ICON_Q_III,           .seg_tab = icon_react_q3,        .seg_num = eleof(icon_react_q3)},      // 无功III
    // {.app_icon = TYPE_ICON_Q_IV,            .seg_tab = icon_react_q4,        .seg_num = eleof(icon_react_q4)},      // 无功IV

    {.app_icon = TYPE_ICON_TF1,             .seg_tab = icon_tariff_1,        .seg_num = eleof(icon_tariff_1)},      // 费率'1'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF2,             .seg_tab = icon_tariff_2,        .seg_num = eleof(icon_tariff_2)},      // 费率'2'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF3,             .seg_tab = icon_tariff_3,        .seg_num = eleof(icon_tariff_3)},      // 费率'3'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF4,             .seg_tab = icon_tariff_4,        .seg_num = eleof(icon_tariff_4)},      // 费率'4'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF5,             .seg_tab = icon_tariff_5,        .seg_num = eleof(icon_tariff_5)},      // 费率'5'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF6,             .seg_tab = icon_tariff_6,        .seg_num = eleof(icon_tariff_6)},      // 费率'6'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF7,             .seg_tab = icon_tariff_7,        .seg_num = eleof(icon_tariff_7)},      // 费率'7'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF8,             .seg_tab = icon_tariff_8,        .seg_num = eleof(icon_tariff_8)},      // 费率'8'符号 (指示显示内容)

    // {.app_icon = TYPE_ICON_POWER,           .seg_tab = icon_power,           .seg_num = eleof(icon_power)},         // 功率
    // {.app_icon = TYPE_ICON_POWER_A,         .seg_tab = icon_power_A,         .seg_num = eleof(icon_power_A)},       // A相功率
    // {.app_icon = TYPE_ICON_POWER_B,         .seg_tab = icon_power_B,         .seg_num = eleof(icon_power_B)},       // B相功率
    // {.app_icon = TYPE_ICON_POWER_C,         .seg_tab = icon_power_C,         .seg_num = eleof(icon_power_C)},       // C相功率

    // {.app_icon = TYPE_ICON_VOLTAGE_A,       .seg_tab = icon_voltage_A,       .seg_num = eleof(icon_voltage_A)},     // A相电压
    // {.app_icon = TYPE_ICON_VOLTAGE_B,       .seg_tab = icon_voltage_B,       .seg_num = eleof(icon_voltage_B)},     // B相电压
    // {.app_icon = TYPE_ICON_VOLTAGE_C,       .seg_tab = icon_voltage_C,       .seg_num = eleof(icon_voltage_C)},     // C相电压

    // {.app_icon = TYPE_ICON_CURRENT_A,       .seg_tab = icon_current_A,       .seg_num = eleof(icon_current_A)},     // A相电流
    // {.app_icon = TYPE_ICON_CURRENT_B,       .seg_tab = icon_current_B,       .seg_num = eleof(icon_current_B)},     // B相电流
    // {.app_icon = TYPE_ICON_CURRENT_C,       .seg_tab = icon_current_C,       .seg_num = eleof(icon_current_C)},     // C相电流
    // {.app_icon = TYPE_ICON_CURRENT_N,       .seg_tab = icon_current_N,       .seg_num = eleof(icon_current_N)},     // N路电流

    // {.app_icon = TYPE_ICON_PF_A,            .seg_tab = icon_pf_A,            .seg_num = eleof(icon_pf_A)},          // A相功率因素
    // {.app_icon = TYPE_ICON_PF_B,            .seg_tab = icon_pf_B,            .seg_num = eleof(icon_pf_B)},          // B相功率因素
    // {.app_icon = TYPE_ICON_PF_C,            .seg_tab = icon_pf_C,            .seg_num = eleof(icon_pf_C)},          // C相功率因素

    // {.app_icon = TYPE_ICON_JIE_TI,          .seg_tab = icon_jie_ti,          .seg_num = eleof(icon_jie_ti)},        // 阶梯
    {.app_icon = TYPE_ICON_ENERGY,          .seg_tab = icon_energy,          .seg_num = eleof(icon_energy)},        // 电量
    // {.app_icon = TYPE_ICON_REMAIN_kWh,      .seg_tab = icon_remain_kWh,      .seg_num = eleof(icon_remain_kWh)},    // 剩余电量
    // {.app_icon = TYPE_ICON_REMAIN_MONEY,    .seg_tab = icon_remain_money,    .seg_num = eleof(icon_remain_money)},  // 剩余电费
    // {.app_icon = TYPE_ICON_DEMAND,          .seg_tab = icon_demand,          .seg_num = eleof(icon_demand)},        // 需量
    // {.app_icon = TYPE_ICON_SHI_YA,          .seg_tab = icon_voltage_miss,    .seg_num = eleof(icon_voltage_miss)},  // 失压
    // {.app_icon = TYPE_ICON_SHI_LIU,         .seg_tab = icon_current_miss,    .seg_num = eleof(icon_current_miss)},  // 失流
    // {.app_icon = TYPE_ICON_SHI_JIAN,        .seg_tab = icon_shi_jian,        .seg_num = eleof(icon_shi_jian)},      // 时间
    // {.app_icon = TYPE_ICON_SHI_DUAN,        .seg_tab = icon_shi_duan,        .seg_num = eleof(icon_shi_duan)},      // 时段

    // {.app_icon = TYPE_ICON_Ua,              .seg_tab = icon_Ua,              .seg_num = eleof(icon_Ua)},            // A相电压指示符号
    // {.app_icon = TYPE_ICON_Ub,              .seg_tab = icon_Ub,              .seg_num = eleof(icon_Ub)},            // B相电压指示符号
    // {.app_icon = TYPE_ICON_Uc,              .seg_tab = icon_Uc,              .seg_num = eleof(icon_Uc)},            // C相电压指示符号

    // {.app_icon = TYPE_ICON_Ia,              .seg_tab = icon_Ia,              .seg_num = eleof(icon_Ia)},            // A相电流指示符号
    // {.app_icon = TYPE_ICON_Ib,              .seg_tab = icon_Ib,              .seg_num = eleof(icon_Ib)},            // B相电流指示符号
    // {.app_icon = TYPE_ICON_Ic,              .seg_tab = icon_Ic,              .seg_num = eleof(icon_Ic)},            // C相电流指示符号

    // {.app_icon = TYPE_ICON_Pa_s,            .seg_tab = icon_Pa_s,            .seg_num = eleof(icon_Pa_s)},          // A相功率反向指示符号
    // {.app_icon = TYPE_ICON_Pb_s,            .seg_tab = icon_Pb_s,            .seg_num = eleof(icon_Pb_s)},          // B相功率反向指示符号
    // {.app_icon = TYPE_ICON_Pc_s,            .seg_tab = icon_Pc_s,            .seg_num = eleof(icon_Pc_s)},          // C相功率反向指示符号

    // {.app_icon = TYPE_ICON_CUR_PRICE_SUITE, .seg_tab = icon_cur_suite,       .seg_num = eleof(icon_cur_suite)},     // 当前套电价 三角1
    // {.app_icon = TYPE_ICON_BAK_PRICE_SUITE, .seg_tab = icon_bak_suite,       .seg_num = eleof(icon_bak_suite)},     // 备用套电价 三角2

    // {.app_icon = TYPE_ICON_L_1,             .seg_tab = icon_L_1,             .seg_num = eleof(icon_L_1)},           // 当前运行第1阶梯电价
    // {.app_icon = TYPE_ICON_L_2,             .seg_tab = icon_L_2,             .seg_num = eleof(icon_L_2)},           // 当前运行第2阶梯电价
    // {.app_icon = TYPE_ICON_L_3,             .seg_tab = icon_L_3,             .seg_num = eleof(icon_L_3)},           // 当前运行第3阶梯电价
    // {.app_icon = TYPE_ICON_L_4,             .seg_tab = icon_L_4,             .seg_num = eleof(icon_L_4)},           // 当前运行第4阶梯电价
    // {.app_icon = TYPE_ICON_L_5,             .seg_tab = icon_L_5,             .seg_num = eleof(icon_L_5)},           // 当前运行第5阶梯电价
    // {.app_icon = TYPE_ICON_L_6,             .seg_tab = icon_L_6,             .seg_num = eleof(icon_L_6)},           // 当前运行第6阶梯电价
    // {.app_icon = TYPE_ICON_L_7,             .seg_tab = icon_L_7,             .seg_num = eleof(icon_L_7)},           // 当前运行第7阶梯电价
    // {.app_icon = TYPE_ICON_L_8,             .seg_tab = icon_L_8,             .seg_num = eleof(icon_L_8)},           // 当前运行第8阶梯电价

    // {.app_icon = TYPE_ICON_CUR_TARIFF,      .seg_tab = icon_cur_tariff,      .seg_num = eleof(icon_cur_tariff)},    // 当前套费率 圆形1
    // {.app_icon = TYPE_ICON_BAK_TARIFF,      .seg_tab = icon_bak_tariff,      .seg_num = eleof(icon_bak_tariff)},    // 备用套费率 圆形2  

    // {.app_icon = TYPE_ICON_CUR_TF_1,        .seg_tab = icon_cur_tf_1,        .seg_num = eleof(icon_cur_tf_1)},      // 当前费率状态1 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_2,        .seg_tab = icon_cur_tf_2,        .seg_num = eleof(icon_cur_tf_2)},      // 当前费率状态2 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_3,        .seg_tab = icon_cur_tf_3,        .seg_num = eleof(icon_cur_tf_3)},      // 当前费率状态3 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_4,        .seg_tab = icon_cur_tf_4,        .seg_num = eleof(icon_cur_tf_4)},      // 当前费率状态4 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_5,        .seg_tab = icon_cur_tf_5,        .seg_num = eleof(icon_cur_tf_5)},      // 当前费率状态5 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_6,        .seg_tab = icon_cur_tf_6,        .seg_num = eleof(icon_cur_tf_6)},      // 当前费率状态6 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_7,        .seg_tab = icon_cur_tf_7,        .seg_num = eleof(icon_cur_tf_7)},      // 当前费率状态7 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_8,        .seg_tab = icon_cur_tf_8,        .seg_num = eleof(icon_cur_tf_8)},      // 当前费率状态8 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_9,        .seg_tab = icon_cur_tf_9,        .seg_num = eleof(icon_cur_tf_9)},      // 当前费率状态9 (指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_10,       .seg_tab = icon_cur_tf_10,       .seg_num = eleof(icon_cur_tf_10)},     // 当前费率状态10(指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_11,       .seg_tab = icon_cur_tf_11,       .seg_num = eleof(icon_cur_tf_11)},     // 当前费率状态11(指示当前运行费率)
    // {.app_icon = TYPE_ICON_CUR_TF_12,       .seg_tab = icon_cur_tf_12,       .seg_num = eleof(icon_cur_tf_12)},     // 当前费率状态12(指示当前运行费率)

    // {.app_icon = TYPE_ICON_SIGNALTOWER,     .seg_tab = icon_signal_tower,    .seg_num = eleof(icon_signal_tower)},  // 信号塔
    // {.app_icon = TYPE_ICON_SIGNAL1,         .seg_tab = icon_signal,          .seg_num = 1},                         // 信号强度1
    // {.app_icon = TYPE_ICON_SIGNAL2,         .seg_tab = icon_signal,          .seg_num = 2},                         // 信号强度2
    // {.app_icon = TYPE_ICON_SIGNAL3,         .seg_tab = icon_signal,          .seg_num = 3},                         // 信号强度3
    // {.app_icon = TYPE_ICON_SIGNAL4,         .seg_tab = icon_signal,          .seg_num = 4},                         // 信号强度4   

    {.app_icon = TYPE_ICON_COMM,            .seg_tab = icon_mudle_comm,      .seg_num = eleof(icon_mudle_comm)},    // 模块通信指示符号
    // {.app_icon = TYPE_ICON_IR_COMM,         .seg_tab = icon_ir_comm,         .seg_num = eleof(icon_ir_comm)},       // 红外模块通信指示符号
    // {.app_icon = TYPE_ICON_4851_COMM,       .seg_tab = icon_4851_comm,       .seg_num = eleof(icon_4851_comm)},     // 4851模块通信指示符号
    // {.app_icon = TYPE_ICON_4852_COMM,       .seg_tab = icon_4852_comm,       .seg_num = eleof(icon_4852_comm)},     // 4852模块通信指示符号
    // {.app_icon = TYPE_ICON_IR_VIRIFY,       .seg_tab = icon_ir_verify,       .seg_num = eleof(icon_ir_verify)},     // 红外认证有效
    // {.app_icon = TYPE_ICON_HANG_UP,         .seg_tab = icon_hang_up,         .seg_num = eleof(icon_hang_up)},       // 电表挂起
    {.app_icon = TYPE_ICON_KEY,             .seg_tab = icon_key,             .seg_num = eleof(icon_key)},           // 测试密钥指示符号
    // {.app_icon = TYPE_ICON_ALARM,           .seg_tab = icon_alarm,           .seg_num = eleof(icon_alarm)},         // 告警指示符号

    {.app_icon = TYPE_ICON_BAT1,            .seg_tab = icon_battery1_low,    .seg_num = eleof(icon_battery1_low)},  // 电池1
    // {.app_icon = TYPE_ICON_BAT2,            .seg_tab = icon_battery2_low,    .seg_num = eleof(icon_battery2_low)},  // 电池2

    // {.app_icon = TYPE_ICON_REV_SEQ,         .seg_tab = icon_rev_seq,         .seg_num = eleof(icon_rev_seq)},       // 逆序指示符号
    // {.app_icon = TYPE_ICON_SUCCESS,         .seg_tab = icon_success,         .seg_num = eleof(icon_success)},       // 成功符号
    // {.app_icon = TYPE_ICON_FAILURE,         .seg_tab = icon_fail,            .seg_num = eleof(icon_fail)},          // 失败符号
    {.app_icon = TYPE_ICON_PURCHASE,        .seg_tab = icon_purchase,        .seg_num = eleof(icon_purchase)},      // 请购电
    {.app_icon = TYPE_ICON_RLY_OFF,         .seg_tab = icon_relay_off,       .seg_num = eleof(icon_relay_off)},     // 继电器拉闸指示符号
};
#else

#error "LCD类型不匹配!"

#endif


static const uint8_t icon_map_list_num = eleof(icon_map_list);


#endif 

/// end of file

