/**
 ******************************************************************************
 * @file    afn_0D.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 13 请求 2 类数据 (历史数据)
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"
#include "profile.h"
#include "timeapp.h"
#include "utils.h"
#include "billing.h"
#include "tariff.h"
#include "demand.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

static lc_get_s lc;

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

/// @brief  获取冻结间隔
/// @param  num
/// @return 分钟数
static uint16_t get_freeze_interval(uint8_t num)
{
    switch(num)
    {
        case 0:
            return 0;    // 1分钟
        case 1:
            return 15;    // 15分钟
        case 2:
            return 30;    // 30分钟
        case 3:
            return 60;    // 60分钟
        case 10:
            return 120;    // 120分钟
        case 11:
            return 180;
        case 12:
            return 240;    // 240分钟
        case 13:
            return 360;
        case 14:
            return 720;
        case 254:
            return 5;
        case 255:
            return 1;
        default:
            return 0;    // 无效值
    }
}

/// @brief  获取负荷曲线数据
/// @param fn 功能码
/// @return 是否成功获取
static uint16_t lc1_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *ptr = rsp->apdu;
    uint8_t *get_num_ptr;
#if LC1_ENABLE
    clock_s      start_time;
    uint8_t      get_num;
    uint8_t      cnt;
    uint32_t     midu;
    lc_get_s     lc = {0};
    uint8_t      buf[DCU_DATA_BUF_SIZE];
    lc1_member_t member;
    uint8_t      data_class;

    switch(req->fn)
    {
        case 81:    // F81 有功功率 3
            member     = LC1_PWR_P;
            data_class = 3;
            break;
        case 82:    // F82  A相有功功率
        case 83:    // F83  B相有功功率
        case 84:    // F84  C相有功功率
            member     = (lc1_member_t)(LC1_PWR_P_A + req->fn - 82);
            data_class = 3;
            break;
        case 85:    // F85  无功功率
            member     = LC1_PWR_Q;
            data_class = 3;
            break;
        case 86:    // F86  A相无功功率
        case 87:    // F87  B相无功功率
        case 88:    // F88  C相无功功率
            member     = (lc1_member_t)(LC1_PWR_Q_A + req->fn - 86);
            data_class = 3;
            break;
        case 89:    // F89  A相电压 2
        case 90:    // F90  B相电压
        case 91:    // F91  C相电压
            member     = (lc1_member_t)(LC1_VRMS_A + req->fn - 89);
            data_class = 2;
            break;
        case 92:    // F92  A相电流 2
        case 93:    // F93  B相电流
        case 94:    // F94  C相电流
            member     = (lc1_member_t)(LC1_IRMS_A + req->fn - 92);
            data_class = 3;
            break;
        case 96:    // F96  频率
            member     = LC1_FREQ;
            data_class = 2;
            break;
        case 101:    // F101  正向有功电能
            member     = LC1_ACTIVE_ENERGY_POS;
            data_class = 4;
            break;
        case 102:    // F102 正向无功电能
            member     = LC1_COMB1_KVARH;
            data_class = 4;
            break;
        case 103:    // F103  反向有功电能
            member     = LC1_ACTIVE_ENERGY_NEG;
            data_class = 4;
            break;
        case 104:    // F104  反向无功电能
            member     = LC1_COMB2_KVARH;
            data_class = 4;
        case 105:    // F105 功率因素
        case 106:    // F106 A相功率因素
        case 107:    // F107 B相功率因素
        case 108:    // F108 C相功率因素
            member     = (lc1_member_t)(LC1_PF + req->fn - 105);
            data_class = 2;
            break;
        case 145:    // F145 象限1无功电能
            member     = LC1_Q1_KVARH;
            data_class = 4;
            break;
        case 146:    // F146 象限4无功电能
            member     = LC1_Q4_KVARH;
            data_class = 4;
            break;
        case 147:    // F147 象限2无功电能
            member     = LC1_Q2_KVARH;
            data_class = 4;
            break;
        case 148:    // F148 象限3无功电能
            member     = LC1_Q3_KVARH;
            data_class = 4;
            break;
        default:
            return 0;
    }

    memset(&start_time, 0, sizeof(start_time));    // 清空起始时间
    mclock.unformat_frm645(req->req_apdu, &start_time, CLOCK_YMDhm), req->req_apdu += 5;
    // midu    = get_freeze_interval(*req->req_apdu), req->req_apdu++;
    midu = *req->req_apdu, req->req_apdu++;

    get_num = *req->req_apdu, req->req_apdu++;    // 获取冻结间隔和获取数量
    if(get_num == 0) { get_num = 1; }
    req->apdu_len -= 7;    // 减去数据单元

    lc.type      = 1;
    lc.get_num   = get_num;
    lc.time      = mclock.calendar_to_seconds(&start_time.cale);    // 将起始时间转换为全局秒数
    lc.frame_num = 0;
    profile.lc1_fetch_for_376(&lc, buf, LC1_TIMESTAMP, 0);    // 获取时间戳数据
    profile.lc1_fetch_for_376(&lc, buf, LC1_TIMESTAMP, 1);    // 获取时间戳数据

    mclock.seconds_to_calendar(&start_time.cale, *((uint32_t *)buf));

    // 曲线类数据时标 Td_c 5 + 1 + 1
    mclock.format_to376(ptr, &start_time, CLOCK_YMDhm), ptr += 5;    // 将起始时间格式化为376格式
    *ptr++      = midu;
    get_num_ptr = ptr, ptr++;    // 保存获取数量指针

    get_num = 0;
    if(lc.get_num > 20) { lc.get_num = 10; }    // 限制获取数量最大为10条

    for(lc.frame_num = 0; lc.frame_num < lc.valid_num; lc.frame_num++)
    {
        if(lc.frame_num >= lc.valid_num) { break; }    // 如果索引超过有效记录数，退出循环

        profile.lc1_fetch_for_376(&lc, buf, member, 1);    // 获取时间戳数据

        uint32_to_lsbbcd(ptr, get_lsbdata16(buf), data_class), ptr += data_class;    // 将数据转换为LSBBCD格式

        get_num++;
        if(get_num >= lc.get_num) { break; }    // 如果不是第一条记录，添加分隔符
    }

    *get_num_ptr = get_num;    // 更新获取数量

    rsp->rsp_len = ptr - rsp->apdu;
#endif

    return (ptr - rsp->apdu);    // 默认返回时间戳
}

/// @brief 获取日冻结数据
/// @param req
/// @param rsp
/// @return 返回数据长度
static uint16_t daliy_freezen_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *rsp_apdu = rsp->apdu;

    clock_s       td_d;
    uint8_t       tf = 0;         // 费率
    uint8_t       bl_lst = 0;     // 读取上N次结算的累计总电能, 0表示读当前累计总电能
    BL_EN_TYPE_t  en_type;        // 电能类型
    demand_type_t demand_type;    // 需量类型

    // 清零
    memset(&td_d, 0, sizeof(td_d));
    // 解析终端抄表时间
    mclock.unformat_frm645(req->req_apdu, &td_d, CLOCK_YMD), req->req_apdu += 3, req->apdu_len -= 3;

    // 计算bl_lst：根据当前日期和冻结日期的天数差
    uint32_t current_sec = mclock.calendar_to_seconds(&(mclock.datetime->cale));
    uint32_t freeze_sec  = mclock.calendar_to_seconds(&td_d.cale);
    if(current_sec < freeze_sec)
    {
        return 0;    // 当前日期小于冻结日期
    }
    uint32_t days_diff = (current_sec - freeze_sec) / (24 * 3600);
    bl_lst             = (uint8_t)days_diff;

    // 格式化响应数据 - 日冻结类数据时标Td_d
    mclock.format_to376(rsp_apdu, &td_d, CLOCK_YMD);
    rsp_apdu += 3;

    // 终端抄表时间
    mclock.format_to376(rsp_apdu, mclock.datetime, CLOCK_YMDhm);
    rsp_apdu += 5;

    switch(req->fn) 
    {    
        case 185:    // F185 正向有功最大需量及发生时间
            demand_type = TYPE_DEMAND_POS_ACT;
            break;
#if DEMAND_POS_REA_ENABLE
        case 186:    // F186 正向无功最大需量及发生时间
            demand_type = TYPE_DEMAND_POS_ACT;
            break;
#endif
        case 187:    // F187 反向有功最大需量及发生时间
            demand_type = TYPE_DEMAND_NEG_ACT;
            break;
#if DEMAND_NEG_REA_ENABLE
        case 188:    // F188 反向无功最大需量及发生时间
            demand_type = TYPE_DEMAND_POS_ACT;
            break;
#endif
    }

    switch(req->fn)
    {
        // 需量
        case 3:    // F3：  日冻结正向有/无功最大需量及发生时间（总、费率 1～M，1≤M≤12）
        case 4:    // F4：  日冻结反向有/无功最大需量及发生时间（总、费率 1～M，1≤M≤12）
        {

            demand_type   = (req->fn == 3) ? TYPE_DEMAND_POS_ACT : TYPE_DEMAND_NEG_ACT;

            tf = 0;

#if DEMAND_TARIFF_RATE_NUM
            tf = min(tariff.day_tf_num_get(), TARIFF_RATE_NUM);        // 需量费率
#endif
            *rsp_apdu++ = tf;
            MD_reg_s demand_all;

            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)    //正/反向有功最大需量
            {
                demand_all = billing.max_demand_get(bl_daily, bl_lst, demand_type, tf_num);
                uint32_to_lsbbcd(rsp_apdu, demand_all.value, 3), rsp_apdu += 3;
            }
            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)    //正/反向有功最大需量发生时间
            {
                mclock.format_to376(rsp_apdu, &demand_all.capture_time, CLOCK_MDhm);    //(月日时分)
                rsp_apdu += 4;
            }
#if DEMAND_POS_REA_ENABLE || DEMAND_NEG_REA_ENABLE
            demand_type = (req->fn == 3) ? TYPE_DEMAND_POS_ACT : TYPE_DEMAND_POS_ACT;
            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)    //正/反向无功最大需量
            {
                demand_all = billing.max_demand_get(bl_lst, demand_type, tf_num);
                uint32_to_lsbbcd(rsp_apdu, demand_all.value, 3), rsp_apdu += 3;
            }
            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)    //正/反向无功最大需量发生时间
            {
                mclock.format_to376(rsp_apdu, &demand_all.capture_time, CLOCK_MDhm);    //(月日时分)
                rsp_apdu += 4;
            }
#else
            memset(rsp_apdu, 0, 7), rsp_apdu += 7;
#endif
        }
        break;
        case 185:    // F185 正向有功最大需量及发生时间（总、费率 1～M ）日冻结
#if DEMAND_POS_REA_ENABLE
        case 186:    // F186 正向无功最大需量及发生时间（总、费率 1～M） 日冻结 
#endif
        case 187:    // F187 反向有功最大需量及发生时间（总、费率 1～M） 日冻结
#if DEMAND_NEG_REA_ENABLE
        case 188:    // F188 反向无功最大需量及发生时间（总、费率 1～M） 日冻结 
#endif
        {
            tf = 0;
#if DEMAND_TARIFF_RATE_NUM
            tf = min(tariff.day_tf_num_get(), TARIFF_RATE_NUM);        // 需量费率
#endif
            // 费率数M
            *rsp_apdu++ = tf;
            MD_reg_s demand;

            // 获取当前需量和各费率需量数据
            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)
            {
                demand = billing.max_demand_get(bl_daily, bl_lst, demand_type, tf_num);
                // 最大需量 (A.23, 3字节 BCD, kW单位)
                uint32_to_lsbbcd(rsp_apdu, demand.value, 3), rsp_apdu += 3;
                // 发生时间 (A.16, 5字节 分时日月年)
                mclock.format_to376(rsp_apdu, &demand.capture_time, CLOCK_MDhm);    //(月日时分)
                rsp_apdu += 4;
            }
        }
        break;

        // 电能
        case 161:    // F161：日冻结正向有功电能示值（总、费率 1～M）
        case 163:    // F163: 日冻结反向有功电能示值（总、费率 1～M）
        {
            tf = 0;
            tf = min(tariff.day_tf_num_get(), TARIFF_RATE_NUM);               // 这里用于区分需量费率和电能的费率
            // 费率数M
            *rsp_apdu++ = tf;
            // uint8_t data_len = (req->fn == 161) ? 5 : 4;
            en_type = (req->fn == 161) ? TYPE_BL_EN_ADD_ACT : TYPE_BL_EN_SUB_ACT;

            // 获取总费率电能和各费率电能数据
            ENERGY_DEF_FORMAT energy;
            for(uint8_t tf_num = 0; tf_num <= tf; tf_num++)
            {
                energy = billing.phs_cum_energy_get(bl_daily, T_PHASE, bl_lst, en_type, tf_num);
                *rsp_apdu++ = 0; 
                uint32_to_lsbbcd(rsp_apdu, energy, 4);
                rsp_apdu += 4;
            }
        }
        break;
    }
    return (rsp_apdu - rsp->apdu);    // 返回数据长度
}

rsp_err_t afn0D_verify(req_obj_s *req, rsp_obj_s *rsp)
{
    return ACK_RIGHT;    // 返回确认结果
}

rsp_err_t afn0D_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *ptr = rsp->apdu;

    if((req->fn == 3 || req->fn == 4) || req->fn == 161 || req->fn == 163 || (req->fn >= 185 && req->fn <= 188))
    {
        if(req->apdu_len < 3)
        {
            rsp->err = ACK_ERR;
            return ACK_ERR;    // 如果小于7，返回错误
        }
        rsp->rsp_len = daliy_freezen_get(req, rsp);    // 获取日冻结数据
    }

    switch(req->fn)
    {
        case 0: {
        }
        break;
        case 1: {
        }
        break;
        case 2: {
            break;
        }
        case 81:     // F81 有功功率 3
        case 82:     // F82  A相有功功率
        case 83:     // F83  B相有功功率
        case 84:     // F84  C相有功功率
        case 85:     // F85  无功功率
        case 86:     // F86  A相无功功率
        case 87:     // F87  B相无功功率
        case 88:     // F88  C相无功功率
        case 89:     // F89  A相电压 2
        case 90:     // F90  B相电压
        case 91:     // F91  C相电压
        case 92:     // F92  A相电流 2
        case 93:     // F93  B相电流
        case 94:     // F94  C相电流
        case 96:     // F96  频率
        case 101:    // F101  正向有功电能
        case 102:    // F102 正向无功电能
        case 103:    // F103  反向有功电能
        case 104:    // F104  反向无功电能
        {
            if(req->apdu_len < 7)
            {
                rsp->err = ACK_ERR;
                return ACK_ERR;    // 如果小于7，返回错误
            }
            rsp->rsp_len = lc1_get(req, rsp);    // 获取负荷曲线数据
        }
        break;
    }

    return ACK_RIGHT;
}

const gdw376_table_s afn0D_table = {
    .afn    = AFN_REQ_CLASS2_DATA,    ///< 功能码
    .reset  = NULL,                   ///< 复位函数
    .verify = NULL,                   ///< 验证函数
    .get    = afn0D_get,              ///< 获取函数
    .set    = NULL,                   ///< 设置函数
};

// end of file
