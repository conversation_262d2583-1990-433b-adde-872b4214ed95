/*
*********************************************************************************************************
*                                              HT6XXX
*                                          Library Function
*
*                                   Copyright 2013, Hi-Trend Tech, Corp.
*                                        All Rights Reserved
*
*
* Project      : HT6xxx
* File         : ht6xxx_lib.h
* By           : Hitrendtech_SocTeam
* Version      : V1.0.3
* Description  :
**********************************************************************************************************
*/

#ifndef __HT6XXX_LIB_H__
#define __HT6XXX_LIB_H__

// #ifdef __cplusplus
// extern "C" {
// #endif

#include "ht6xxx.h"
#include "ht6xxx_cmu.h"
#include "ht6xxx_crc.h"
#include "ht6xxx_exti.h"
#include "ht6xxx_flash.h"
#include "ht6xxx_gpio.h"
#include "ht6xxx_iic.h"
#include "ht6xxx_lcd.h"
#include "ht6xxx_pmu.h"
#include "ht6xxx_reset.h"
#include "ht6xxx_rtc.h"
#include "ht6xxx_spi.h"
#include "ht6xxx_tbs.h"
#include "ht6xxx_timer.h"
#include "ht6xxx_uart_7816.h"
#include "ht6xxx_tdes_rand.h"
#include "ht6xxx_aes_rand.h"
#include "ht6xxx_dma.h"
#include "ht50xx_emu.h"
#include "ht501x_keyscan.h"
#include "ht6x3x_emu.h"

/*
*********************************************************************************************************
*                                           全局宏/结构体
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                             全局变量
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                           全局函数申明
*********************************************************************************************************
*/







// #ifdef __cplusplus
// }
// #endif

#endif /* __HT6XXX_LIB_H__ */
