/**
 ******************************************************************************
 * @file    afn_0C.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 12 请求1 类数据 (实时数据)
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "DLT645_2007_id.h"
#include "dcu.h"
#include "debug.h"
#include "timeapp.h"
#include "mic.h"
#include "utils.h"
#include "tariff.h"
#include "api.h"
#include "demand.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

/// @brief A_XDR数据结构 - 电能

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t afn0C_get(req_obj_s *req, rsp_obj_s *rsp)
{
    rsp_err_t ret      = ACK_ERR;
    uint8_t  *rsp_apdu = rsp->apdu;

    rsp->rsp_len = 0;
    rsp->err     = ACK_ERR;    // 设置响应错误码为无错误
    switch(req->fn)
    {
        case 2:    // F2 时钟
        {
            mclock.format_to376(rsp_apdu, mclock.datetime, CLOCK_YMDWhms), rsp_apdu += 6;    // 将时钟格式化为376格式

            rsp->rsp_len = 6;            // 设置响应长度为6字节
            ret          = ACK_RIGHT;    // 设置返回状态为正确
            break;
        }
        case 25:    // F25：当前三相及总有/无功功率、功率因数，三相电压、电流、零序电流、视在功率
        {
            // 终端抄表时间 见附录 A.15 分时日月年 5
            mclock.format_to376(rsp_apdu, mclock.datetime, CLOCK_YMDhm), rsp_apdu += 5;    // 将时钟格式化为376格式
            // 当前总有功功率 见附录 A.9 kW 3
            // 当前 A 相有功功率 见附录 A.9 kW 3
            // 当前 B 相有功功率 见附录 A.9 kW 3
            // 当前 C 相有功功率 见附录 A.9 kW 3
            for(uint8_t i = 0; i < 4; i++) { int32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->pwr_p[i] * 10000), 3), rsp_apdu += 3; }
            // 当前总无功功率 见附录 A.9 kW 3
            // 当前 A 相无功功率 见附录 A.9 kW 3
            // 当前 B 相无功功率 见附录 A.9 kW 3
            // 当前 C 相无功功率 见附录 A.9 kW 3
            for(uint8_t i = 0; i < 4; i++) { int32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->pwr_q[i] * 10000), 3), rsp_apdu += 3; }
            // 当前总功率因数 见附录 A.5 % 2
            // 当前 A 相功率因数 见附录 A.5 % 2
            // 当前 B 相功率因数 见附录 A.5 % 2
            // 当前 C 相功率因数 见附录 A.5 % 2
            for(uint8_t i = 0; i < 4; i++) { int32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->pf[i] * 1000), 2), rsp_apdu += 2; }
            // 当前 A 相电压 见附录 A.7 V 2
            // 当前 B 相电压 见附录 A.7 V 2
            // 当前 C 相电压 见附录 A.7 V 2
            for(uint8_t i = 0; i < 3; i++) { uint32_to_lsbbcd(rsp_apdu, (uint32_t)(mic.ins->vrms[i] * 10), 2), rsp_apdu += 2; }
            // 当前 A 相电流 见附录 A.25 A 3
            // 当前 B 相电流 见附录 A.25 A 3
            // 当前 C 相电流 见附录 A.25 A 3
            for(uint8_t i = 0; i < 3; i++) { int32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->irms[i] * 1000), 3), rsp_apdu += 3; }
            // 当前零序电流 见附录 A.25 A 3
            uint32_to_lsbbcd(rsp_apdu, (uint32_t)(mic.ins->n_irms * 1000), 3), rsp_apdu += 3;
            // 当前总视在功率 见附录 A.9 kVA 3
            // 当前 A 相视在功率 见附录 A.9 kVA 3
            // 当前 B 相视在功率 见附录 A.9 kVA 3
            // 当前 C 相视在功率 见附录 A.9 kVA 3

            for(uint8_t i = 0; i < 4; i++) { int32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->pwr_s[i] * 10000), 3), rsp_apdu += 3; }

            ret          = ACK_RIGHT;    // 设置返回状态为正确
            rsp->rsp_len = rsp_apdu - rsp->apdu;
            break;
        }
        case 49:    // F25 电压、电流相位角
        {
#if defined(POLYPHASE_METER)
            for(uint8_t i = 0; i < 3; i++) { uint32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->vv_angle[i] * 10), 2), rsp_apdu += 2; }
#else
            for(uint8_t i = 0; i < 3; i++) { uint32_to_lsbbcd(rsp_apdu, 0, 2), rsp_apdu += 2; }
#endif
            for(uint8_t i = 0; i < 3; i++) { uint32_to_lsbbcd(rsp_apdu, (int32_t)(mic.ins->vi_angle[i] * 10), 2), rsp_apdu += 2; }
            rsp->rsp_len = rsp_apdu - rsp->apdu;
            ret          = ACK_RIGHT;
            break;
        }
        case 129:    // F129 当前正向有功电能示值（总、费率1-N）
        case 130:    // F130 当前正向无功电能示值（总、费率1-N）
        case 131:    // F131 当前反向有功电能示值（总、费率1-N）
        case 132:    // F132 当前反向无功电能示值（总、费率1-N）
        {
            uint32_t id = 0;
            uint8_t  tf = 0;    // 费率
            uint8_t  eng[8];    // 电能示值缓冲区

            tf = min(tariff.day_tf_num_get(), TARIFF_RATE_NUM);    // 确保费率不超过最大值

            // 终端抄表时间 见附录 A.15 分时日月年 5
            mclock.format_to376(rsp_apdu, mclock.datetime, CLOCK_YMDhm), rsp_apdu += 5;    // 将时钟格式化为376格式
            // 费率数 M BIN 个 1
            *rsp_apdu++ = tf;    // 费率数
            // 正向有功总电能示值 见附录 A.14 kWh 5
            // 费率 1 正向有功总电能示值 见附录 A.14 kWh 5 … … … …
            // 费率 M 正向有功总电能示值 见附录 A.14 kWh 5
            for(uint8_t i = 0; i <= tf; i++)
            {
                switch(req->fn)
                {
                    case 129:    // 正向有功电能示值
                        id          = C0_POS_kWh(0, i);
                        *rsp_apdu++ = 0;    // 4位小数，最后两位清零
                        break;
                    case 130:    // 正向无功电能示值
                        id = C0_CMB1_kvarh(0, i);
                        break;
                    case 131:    // 反向有功电能示值
                        id          = C0_NEG_kWh(0, i);
                        *rsp_apdu++ = 0;    // 4位小数，最后两位清零
                        break;
                    case 132:    // 反向无功电能示值
                        id = C0_CMB2_kvarh(0, i);
                        break;
                }

                api.energy_get(id, &eng);    // 获取电能示值
                uint32_to_lsbbcd(rsp_apdu, get_msbdata32(eng + 1), 4), rsp_apdu += 4;
            }
            rsp->rsp_len = rsp_apdu - rsp->apdu;
            ret          = ACK_RIGHT;
            break;
        }
        case 145:    // F145：当月正向无功最大需量及发生时间（总、费率 1～M）
#if DEMAND_POS_REA_ENABLE
        case 146:    // F146：当月正向无功最大需量及发生时间（总、费率 1～M）
#endif
        case 147:    // F147：当月反向有功最大需量及发生时间（总、费率 1～M）
#if DEMAND_NEG_REA_ENABLE
        case 148:    // F148：当月反向无功最大需量及发生时间（总、费率 1～M）
#endif
        {
            MD_reg_s      md;
            uint32_t      id = 0;
            uint8_t       tf = 0;    // 费率
            demand_type_t md_typ;

#if DEMAND_TARIFF_RATE_NUM
            tf = min(tariff.day_tf_num_get(), TARIFF_RATE_NUM);    // 确保费率不超过最大值
#endif
            mclock.format_to376(rsp_apdu, mclock.datetime, CLOCK_YMDhm), rsp_apdu += 5;    // 将时钟格式化为376格式
            // 费率数 M BIN 个 1
            *rsp_apdu++ = tf;    // 费率数

            for(uint8_t i = 0; i <= tf; i++)
            {
                switch(req->fn)
                {
                    case 145:    // 正向有功电能示值
                        md_typ = TYPE_DEMAND_POS_ACT;
                        break;
#if DEMAND_POS_REA_ENABLE
                    case 146:    // 正向无功电能示值
                        md_typ = TYPE_DEMAND_POS_ACT;
                        break;
#endif
                    case 147:    // 反向有功电能示值
                        md_typ = TYPE_DEMAND_NEG_ACT;
                        break;
#if DEMAND_NEG_REA_ENABLE
                    case 132:    // 反向无功电能示值
                        md_typ = TYPE_DEMAND_POS_ACT;
                        break;
#endif
                }

                md = demand.max_value_get(md_typ, i);    // 获取电能示值
                uint32_to_lsbbcd(rsp_apdu, md.value, 3), rsp_apdu += 3;
                mclock.format_to376(rsp_apdu, &md.capture_time, CLOCK_MDhm), rsp_apdu += 4;    // 日时分秒
            }
            rsp->rsp_len = rsp_apdu - rsp->apdu;
            ret          = ACK_RIGHT;

            break;
        }
        default:
            break;
    }
    rsp->err = ret;
    logd("afn0C_get: fn: %02X, rsp_len: %d, ret: %d\r\n", req->fn, rsp->rsp_len, ret);
    return ret;
}

const gdw376_table_s afn0C_table = {
    .afn    = AFN_REQ_CLASS1_DATA,    ///< 功能码
    .reset  = NULL,                   ///< 复位函数
    .verify = NULL,                   ///< 验证函数
    .get    = afn0C_get,              ///< 获取函数
    .set    = NULL,                   ///< 设置函数
};