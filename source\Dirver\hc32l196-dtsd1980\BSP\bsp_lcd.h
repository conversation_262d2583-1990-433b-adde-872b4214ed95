/**
 ******************************************************************************
* @file    bsp_lcd.h
* <AUTHOR> @date    2024
* @brief   
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifndef __BSP_LCD_H
#define __BSP_LCD_H

/* Includes -----------------------------------------------------------------*/
#include "bsp_cfg.h"


/* 定义显示屏名称 */
#define LCD_MS                    0
#define LCD_PS                    1
#define LCD_P1S                   2
#define LCD_P2S                   3

/* 定义辅屏短码显示起始位置 */
#define LCD_PS_CODE_ADDR          0

/// @brief 图标类型定义，这里面可以增加液晶屏支持的图标类型，不用删除不支持的图标。
typedef enum
{
    TYPE_ICON_TIME,            // 时间指示符号 hh:mm:ss
    TYPE_ICON_DATE,            // 日期指示符号 YY.MM.DD
    TYPE_ICON_SIGN,            // 正负'-'符号

    TYPE_ICON_WH,              
    TYPE_ICON_KWH,             // 有功电能单位符号
    TYPE_ICON_MWH,             // 有功电能单位符号
    TYPE_ICON_VARH,
    TYPE_ICON_KVARH,           // 无功电能单位符号
    TYPE_ICON_MVARH,
    TYPE_ICON_VAH,
    TYPE_ICON_KVAH,            // 视在电能单位符号
    TYPE_ICON_MVAH,

    TYPE_ICON_W,               // 功率单位符号,W
    TYPE_ICON_KW,              // 有功功率单位符号,KW
    TYPE_ICON_MW,              // 有功功率单位符号,MW
    TYPE_ICON_VAR,
    TYPE_ICON_KVAR,            // 无功功率单位符号,KVAR
    TYPE_ICON_MVAR,
    TYPE_ICON_VA,
    TYPE_ICON_KVA,             // 视在功率单位符号,KVA
    TYPE_ICON_MVA,

    TYPE_ICON_V,               // 电压单位符号
    TYPE_ICON_V1,
    TYPE_ICON_V2,
    TYPE_ICON_V3,
    TYPE_ICON_KV,
    TYPE_ICON_KV1,
    TYPE_ICON_KV2,
    TYPE_ICON_KV3,
    TYPE_ICON_A,               // 电流单位符号
    TYPE_ICON_A1,
    TYPE_ICON_A2,
    TYPE_ICON_A3,
    TYPE_ICON_KA,
    TYPE_ICON_KA1,
    TYPE_ICON_KA2,
    TYPE_ICON_KA3,
    TYPE_ICON_Hz,              // 频率单位符号

    TYPE_ICON_PF,              // 功率因数单位符号
    TYPE_ICON_YUAN,            // 元单位符号
    TYPE_ICON_HOUR,            // 时间单位符号
    TYPE_ICON_PERCENT,         // 百分号符号,%
    TYPE_ICON_TEMP,            // 温度符号,℃
    TYPE_ICON_QIU_HE,          // 求和符合,Σ

    TYPE_ICON_P_START,         // 潜动符号
    TYPE_ICON_Q1,              // 功率象限I符号
    TYPE_ICON_Q2,              // 功率象限II符号
    TYPE_ICON_Q3,              // 功率象限III符号
    TYPE_ICON_Q4,              // 功率象限IV符号

    TYPE_ICON_ZU_HE,           // 组合
    TYPE_ICON_NEG,             // 反向
    TYPE_ICON_POS,             // 正向
    TYPE_ICON_REACT,           // 无功
    TYPE_ICON_ACT,             // 有功
    TYPE_ICON_TOTAL,           // 总
    TYPE_ICON_TARIFF,          // 费率

    TYPE_ICON_Q_I,             // 无功I
    TYPE_ICON_Q_II,            // 无功II
    TYPE_ICON_Q_III,           // 无功III
    TYPE_ICON_Q_IV,            // 无功IV
    
    TYPE_ICON_JIAN,            // 尖 符号
    TYPE_ICON_FENG,            // 峰 符号
    TYPE_ICON_PING,            // 平 符号
    TYPE_ICON_GU,              // 谷 符号

    TYPE_ICON_TF1,             // 费率'1'符号 (指示显示内容)
    TYPE_ICON_TF2,             // 费率'2'符号 (指示显示内容)
    TYPE_ICON_TF3,             // 费率'3'符号 (指示显示内容)
    TYPE_ICON_TF4,             // 费率'4'符号 (指示显示内容)
    TYPE_ICON_TF5,             // 费率'5'符号 (指示显示内容)
    TYPE_ICON_TF6,             // 费率'6'符号 (指示显示内容)
    TYPE_ICON_TF7,             // 费率'7'符号 (指示显示内容)
    TYPE_ICON_TF8,             // 费率'8'符号 (指示显示内容)
    TYPE_ICON_TF9,             // 费率'9'符号 (指示显示内容)
    
    TYPE_ICON_POWER,           // 功率
    TYPE_ICON_POWER_A,         // A相功率
    TYPE_ICON_POWER_B,         // B相功率
    TYPE_ICON_POWER_C,         // C相功率

    TYPE_ICON_VOLTAGE_A,       // A相电压
    TYPE_ICON_VOLTAGE_B,       // B相电压
    TYPE_ICON_VOLTAGE_C,       // C相电压

    TYPE_ICON_CURRENT_A,       // A相电流
    TYPE_ICON_CURRENT_B,       // B相电流
    TYPE_ICON_CURRENT_C,       // C相电流
    TYPE_ICON_CURRENT_N,       // N路电流

    TYPE_ICON_PF_A,            // A相功率因素
    TYPE_ICON_PF_B,            // B相功率因素
    TYPE_ICON_PF_C,            // C相功率因素

    TYPE_ICON_JIE_TI,          // 阶梯
    TYPE_ICON_ENERGY,          // 电量
    TYPE_ICON_REMAIN_kWh,      // 剩余电量
    TYPE_ICON_REMAIN_MONEY,    // 剩余电费
    TYPE_ICON_DEMAND,          // 需量
    TYPE_ICON_LING_XU,         // 零序
    TYPE_ICON_RI_LI,           // 日历
    TYPE_ICON_SHI_YA,          // 失压
    TYPE_ICON_SHI_LIU,         // 失流
    TYPE_ICON_SHI_JIAN,        // 时间
    TYPE_ICON_SHI_DUAN,        // 时段
    
    TYPE_ICON_MAX,             // Max
    TYPE_ICON_MIN,             // Min
    TYPE_ICON_AVG,             // Avg
    TYPE_ICON_MD,              // MD
    TYPE_ICON_THD,             // THD
    
    TYPE_ICON_AB_A1,           // ab a1指示符号
    TYPE_ICON_CA_A2,           // ca a2指示符号
    TYPE_ICON_AB_B1,           // ab b1指示符号
    TYPE_ICON_BC_B2,           // bc b2指示符号
    TYPE_ICON_BC_C1,           // ba c1指示符号
    TYPE_ICON_CA_C2,           // ca c2指示符号
    TYPE_ICON_N,               // n指示符号
    
    TYPE_ICON_Ua,              // A相电压指示符号
    TYPE_ICON_Ub,              // B相电压指示符号
    TYPE_ICON_Uc,              // C相电压指示符号

    TYPE_ICON_Ia,              // A相电流指示符号
    TYPE_ICON_Ib,              // B相电流指示符号
    TYPE_ICON_Ic,              // C相电流指示符号
       
    TYPE_ICON_Pa_s,            // A相功率反向指示符号
    TYPE_ICON_Pb_s,            // B相功率反向指示符号
    TYPE_ICON_Pc_s,            // C相功率反向指示符号

    TYPE_ICON_CUR_PRICE_SUITE, // 当前套电价 三角1
    TYPE_ICON_BAK_PRICE_SUITE, // 备用套电价 三角2

    TYPE_ICON_L_1,             // 当前运行第1阶梯电价
    TYPE_ICON_L_2,             // 当前运行第2阶梯电价
    TYPE_ICON_L_3,             // 当前运行第3阶梯电价
    TYPE_ICON_L_4,             // 当前运行第4阶梯电价
    TYPE_ICON_L_5,             // 当前运行第5阶梯电价
    TYPE_ICON_L_6,             // 当前运行第6阶梯电价
    TYPE_ICON_L_7,             // 当前运行第7阶梯电价
    TYPE_ICON_L_8,             // 当前运行第8阶梯电价
    TYPE_ICON_L_9,             // 当前运行第9阶梯电价

    TYPE_ICON_CUR_TARIFF,      // 当前套费率 圆形1
    TYPE_ICON_BAK_TARIFF,      // 备用套费率 圆形2  

    TYPE_ICON_CUR_TF_1,        // 当前费率状态1 (指示当前运行费率)
    TYPE_ICON_CUR_TF_2,        // 当前费率状态2 (指示当前运行费率)
    TYPE_ICON_CUR_TF_3,        // 当前费率状态3 (指示当前运行费率)
    TYPE_ICON_CUR_TF_4,        // 当前费率状态4 (指示当前运行费率)
    TYPE_ICON_CUR_TF_5,        // 当前费率状态5 (指示当前运行费率)
    TYPE_ICON_CUR_TF_6,        // 当前费率状态6 (指示当前运行费率)
    TYPE_ICON_CUR_TF_7,        // 当前费率状态7 (指示当前运行费率)
    TYPE_ICON_CUR_TF_8,        // 当前费率状态8 (指示当前运行费率)
    TYPE_ICON_CUR_TF_9,        // 当前费率状态9 (指示当前运行费率)
    TYPE_ICON_CUR_TF_10,       // 当前费率状态10(指示当前运行费率)
    TYPE_ICON_CUR_TF_11,       // 当前费率状态11(指示当前运行费率)
    TYPE_ICON_CUR_TF_12,       // 当前费率状态12(指示当前运行费率)
    TYPE_ICON_CUR_TF_13,       // 当前费率状态13(指示当前运行费率)
    TYPE_ICON_CUR_TF_14,       // 当前费率状态14(指示当前运行费率)
    TYPE_ICON_CUR_TF_15,       // 当前费率状态15(指示当前运行费率)
    TYPE_ICON_CUR_TF_16,       // 当前费率状态16(指示当前运行费率)
    TYPE_ICON_CUR_TF_17,       // 当前费率状态17(指示当前运行费率)
    TYPE_ICON_CUR_TF_18,       // 当前费率状态18(指示当前运行费率)
    TYPE_ICON_CUR_TF_19,       // 当前费率状态19(指示当前运行费率)

    TYPE_ICON_SIGNALTOWER,     // 信号塔
    TYPE_ICON_SIGNAL1,         // 信号强度1
    TYPE_ICON_SIGNAL2,         // 信号强度2
    TYPE_ICON_SIGNAL3,         // 信号强度3
    TYPE_ICON_SIGNAL4,         // 信号强度4   

    TYPE_ICON_COMM,            // 模块通信指示符号
    TYPE_ICON_IR_COMM,         // 红外模块通信指示符号
    TYPE_ICON_4851_COMM,       // 4851模块通信指示符号
    TYPE_ICON_4852_COMM,       // 4852模块通信指示符号
    TYPE_ICON_IR_VIRIFY,       // 红外认证有效
    TYPE_ICON_HANG_UP,         // 电表挂起
    TYPE_ICON_KEY,             // 测试密钥指示符号
    TYPE_ICON_ALARM,           // 告警指示符号

    TYPE_ICON_BAT1,            // 电池1
    TYPE_ICON_BAT2,            // 电池2
    
    TYPE_ICON_DO_U1,           // DO U1
    TYPE_ICON_DO_U2,
    TYPE_ICON_DO_U3,
    TYPE_ICON_DO_U4,
    TYPE_ICON_DO_U5,
    TYPE_ICON_DO_U6,
    TYPE_ICON_M_U1,            // M U1
    TYPE_ICON_M_U2,
    TYPE_ICON_M_U3,
    TYPE_ICON_M_U4,
    TYPE_ICON_M_U5,
    TYPE_ICON_M_U6,
    TYPE_ICON_DI_Y1,           // DI Y1
    TYPE_ICON_DI_Y2,
    TYPE_ICON_DI_Y3,
    TYPE_ICON_DI_Y4,
    TYPE_ICON_T_Y1,            // T Y1
    TYPE_ICON_T_Y2,
    TYPE_ICON_T_Y3,
    TYPE_ICON_T_Y4,

    TYPE_ICON_REV_SEQ,         // 逆序指示符号
    TYPE_ICON_SUCCESS,         // 成功符号
    TYPE_ICON_FAILURE,         // 失败符号
    TYPE_ICON_PURCHASE,        // 请购电
    TYPE_ICON_RLY_OFF,         // 继电器拉闸指示符号
    
    TYPE_ICON_NULL,
} ICON_TYPE_t;

typedef enum
{
    LCD_CLOSE = 0,      // 0-关闭
    LCD_PWRON_OPEN,     // 1-上电打开
    LCD_PWROFF_OPEN,    // 2-掉电打开
} LCD_CTRL_MODE_t;


struct lcd_s
{
    void (*ctrl)(LCD_CTRL_MODE_t on_off);
    void (*all_light)(char on_off);
    void (*icon_light)(ICON_TYPE_t icon, char on_off);
    void (*disp_char)(uint8_t screen, uint8_t pos, uint16_t ch);
    uint8_t (*disp_digit)(uint8_t screen, int32_t dat, uint8_t len, uint8_t dot, uint8_t lead);
    //显示bcd码
    void (*disp_bcd)(uint8_t screen, const uint8_t * bcd, uint8_t len);
    void (*disp_string)(uint8_t screen, uint8_t pos, const uint8_t * ch, uint8_t num);
    void (*disp_date)(uint8_t year, uint8_t month, uint8_t day);
    void (*disp_time)(uint8_t hour, uint8_t min, uint8_t sec);
    void (*refresh)(void);
};
extern const struct lcd_s lcd;


#endif
/* __LCD_SEG_H */



