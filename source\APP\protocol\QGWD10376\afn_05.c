/**
 ******************************************************************************
 * @file    afn_05.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 5 控制命令
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"
#include "timeapp.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t afn05_set(req_obj_s *req, rsp_obj_s *rsp)
{
    rsp_err_t ret = ACK_ERR;    // 默认返回错误
    switch(req->fn)
    {
        case 1:    // F1：遥控跳闸
        {
        }
        break;
        case 2:    // F2：允许合闸
        {
            ret = ACK_RIGHT;    // 分闸成功
        }
        break;
        case 31:    // F31: 对时命令
        {
            clock_s cal;
            if(req->apdu_len < 22) { break; }                              //
            mclock.unformat_frm376(req->req_apdu, &cal, CLOCK_YMDWhms);    // 将BCD格式的时钟转换为系统时钟

            cal.stus.value = 0;            // 设置时钟状态为有效
            mclock.sync_time_set(&cal);    // 设置系统时间
            ret = ACK_RIGHT;    // 分闸成功
            
            logd("AFN 05 Set Time Command: %02d-%02d-%02d %02d:%02d:%02d\r\n", cal.year, cal.month, cal.day, cal.hour, cal.minute, cal.second);
        }
    }
    logd("AFN 05 Set Control Command: fn=%d, ret=%X\r\n", req->fn, (uint8_t)ret);
    rsp->err = ret;    // 设置响应错误码
    return ret;        // 返回确认结果
}

const gdw376_table_s afn05_table = {
    .afn    = AFN_CTRL_CMD,    ///< 功能码
    .reset  = NULL,            ///< 复位函数
    .verify = NULL,            ///< 验证函数
    .get    = NULL,            ///< 获取函数
    .set    = afn05_set,       ///< 设置函数
};
