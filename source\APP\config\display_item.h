/*
 * @description:
 * @date: Do not edit
 * @lastAuthor:
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */
/**
 ******************************************************************************
 * @file    display_item.h
 * <AUTHOR> @date    2024
 * @brief   本文件配置电表默认显示项目
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef DISPLAY_ITEM_H
#define DISPLAY_ITEM_H
#include "typedef.h"
#include "DLT645_2007_id.h"

/// @brief 自动轮显默认显示项
const uint32_t disp_list1_id[] = {
    // C0_CMB_kWh(0,0),           /// 当前累计消耗组合有功电能总
    // C0_POS_kWh(0,0),           /// 当前累计消耗正向有功电能总
    // C0_NEG_kWh(0,0),           /// 当前累计消耗反向有功电能总
    C2_A_VOL,    /// A相电压
    C2_A_CUR,
    C4_DATE_WEEK,    /// 日期时间
    C4_TIME,         /// 时间
    C4_METER_NO,     /// 电表号
    C2_T_INS_P,      /// A相有功功率
    C2_T_PF,
};
const uint8_t disp_list1_id_num = eleof(disp_list1_id);

/// @brief 手动键显默认显示项
const uint32_t disp_list2_id[] = {
    C0_CMB_kWh(0, 0),    /// 当前累计消耗组合有功电能总
    C0_POS_kWh(0, 0),    /// 当前累计消耗正向有功电能总
    C0_NEG_kWh(0, 0),    /// 当前累计消耗反向有功电能总

    C2_A_VOL,    /// A相电压
#if defined(POLYPHASE_METER)
    C2_B_VOL,    /// B相电压
    C2_C_VOL,    /// C相电压
#endif
    C2_A_CUR,    /// A相   电流
#if defined(POLYPHASE_METER)
    C2_B_CUR,    /// B相   电流
    C2_C_CUR,    /// C相   电流
#endif
    C2_T_INS_P,    /// 总    有功功率
#if defined(POLYPHASE_METER)
    C2_A_INS_P,    /// A相   有功功率
    C2_B_INS_P,    /// B相   有功功率
    C2_C_INS_P,    /// C相   有功功率
#endif
    C2_T_INS_Q,    /// 总    无功功率
#if defined(POLYPHASE_METER)
    C2_A_INS_Q,    /// A相   无功功率
    C2_B_INS_Q,    /// B相   无功功率
    C2_C_INS_Q,    /// C相   无功功率
#endif
    C2_T_INS_S,    /// 总    视在功率
#if defined(POLYPHASE_METER)
    C2_A_INS_S,    /// A相   视在功率
    C2_B_INS_S,    /// B相   视在功率
    C2_C_INS_S,    /// C相   视在功率
#endif
    C2_T_PF,    /// 总    功率因素
#if defined(POLYPHASE_METER)
    C2_A_PF,    /// A相   功率因素
    C2_B_PF,    /// B相   功率因素
    C2_C_PF,    /// C相   功率因素
#endif
};
const uint8_t disp_list2_id_num = eleof(disp_list2_id);

/// @brief 掉电显示默认显示项
const uint32_t disp_list3_id[] = {
    C0_CMB_kWh(0, 0),    /// 当前累计消耗组合有功电能总
    C0_POS_kWh(0, 0),    /// 当前累计消耗正向有功电能总
    C0_NEG_kWh(0, 0),    /// 当前累计消耗反向有功电能总
};
const uint8_t disp_list3_id_num = eleof(disp_list3_id);

#endif /* DISPLAY_ITEM_H */
