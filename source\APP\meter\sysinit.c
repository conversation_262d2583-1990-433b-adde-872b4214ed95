/**
 ******************************************************************************
* @file    sysinit.c
* <AUTHOR> @date    2024
* @brief   系统初始化
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "app.h"
#include "sysinit.h"
#include "bsp.h"
#include "bsp_cfg.h"
#include "RelayApp.h"
#include "status.h"
#include "datastore.h"
#include "app_config.h"
#include "timeapp.h"
#include "local_port.h"
#include "tariff.h"
#if SW_PAYMENT_EN
#include "step_tariff.h"
#include "payment.h"
#endif
#include "dispApp.h"
#include "demand.h"
#include "power_event.h"
#include "billing.h"
#include "loadcurve.h"
#include "energy.h"
#include "api.h"
#include "event.h"
#include "module_para.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define RAM_INIT_FLAG   0xA55A5AA5

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static INIT_STATE sysinit_out_stus; ///本模块事件输出状态字
__no_init uint32_t  no_init_ram_flag; ///不受市电上下电（电池维持）影响的RAM检查标志。


/// @brief 冷启动RAM初始化（“.noinit”段的内存）。
/// @param
#if defined(__IAR_SYSTEMS_ICC__)
#pragma section = ".noinit"
static void RAM_init(void)
{
    char *   begin = __section_begin(".noinit");
    uint32_t size  = __section_size(".noinit");
    memset(begin, 0x00, size);
}
#else
static void RAM_init(void)
{
}
#endif

/// @brief 系统自检。用于实现底层驱动的检查。
/// @param
static void sysinit_self_checking(void)
{
    uint32_t addr;
    /* ==========================程序进入系统自检================================ */
    ///检查底层驱动
    if(bsp.state_query(STUS_BSP_WDG_RST))
    {
        sysinit_out_stus |= INIT_ABNORMAL_RST;
        mstatus.reg->self_chk.wdt_rest = true;
    }
    if(bsp.state_query(STUS_BSP_RTC_RST))
    {
        sysinit_out_stus |= INIT_RTC_LOSE;
        mstatus.reg->self_chk.rtc = true;
    }
    if(!bsp.state_query(STUS_BSP_RTC_LSE))
    {
        sysinit_out_stus |= INIT_LSE_ERROR;
        mstatus.reg->self_chk.rtc_osc_err = true;
    }
    if(bsp.state_query(STUS_BSP_PPM_ERR))
    {
        sysinit_out_stus |= INIT_PPM_ERROR;
        mstatus.reg->self_chk.ppm_err = true;
    }   
    if(bsp.state_query(STUS_BSP_POR_BOR))
    {
        /// 冷启动，强制RAM清理
        sysinit_out_stus |= INIT_POR_BOR;
        no_init_ram_flag = 0;     
        mstatus.reg->self_chk.por_bor = true;
    }


    ///检查存储
#if USE_EEPROM
    switch(nvm.check(EXTEE_BASE_ADDR))
    {
        case 0:
        sysinit_out_stus |= INIT_EE_ERROR;
        mstatus.reg->self_chk.eeprom = true;
        break;
        case 2: //新eeprom
        
        break;
    }
#endif

#if USE_DATAFLASH
    for(uint8_t i = 0; i < 3; i++)
    {
        if(nvm.check(EXTDF_BASE_ADDR) == 0)
        {
            sysinit_out_stus |= INIT_DF_ERROR;
            mstatus.reg->self_chk.falsh = true;
        }else break;
    }
#endif

    if(no_init_ram_flag != RAM_INIT_FLAG)
    {
        RAM_init();
        no_init_ram_flag = RAM_INIT_FLAG;
        //gMeterStatus.self_chk.ram_err = true; // RAM错误置位
    }
}

/// @brief 系统初始化模块状态查询
/// @param state
/// @return
static bool sysinit_state_query(INIT_STATE state)
{
    bool ret = FALSE;

    ret = boolof(sysinit_out_stus & state);
    // sysinit_out_stus &= ~state;
    return ret;
}

void sysinit_state_clr(void)
{
    sysinit_out_stus = 0;
}


/// @brief 重置系统
/// @return
static bool sysinit_reset(SysClearType_t type)
{
    SYS_RESET_TYPE tmp;

    switch(type)
    {
        case SYS_GROBAL_CLR:
        {
            if(!mstatus.reg->running.factory) return false;
            // 注意，系统清零不能清除电表序列号，等生产信息
            ///
            uint8_t  m_bc[BAR_CODE_LEN + 1];
            uint16_t len;
            len = api.product_info_get(PRODUCT_INFO(meter_bc), m_bc);
            nvm.clean(MCUEE);              
            nvm.clean(EXTEE);              
            nvm.clean(EXTDF);             
            api.product_info_set(PRODUCT_INFO(meter_bc), m_bc, len);
        }
        tmp = SYS_GLOBAL_RESET;
        sysinit_out_stus |= INIT_GLOBAL_CLEAR;
        break;

        case SYS_PARA_CLR:
        if(!mstatus.reg->running.factory) return false;
        tmp = SYS_PARA_RESET;
        sysinit_out_stus |= INIT_PARA_CLEAR;
        break;

        case SYS_DATA_CLR:
        if(!mstatus.reg->running.factory) return false;
        tmp = SYS_DATA_RESET;
        sysinit_out_stus |= INIT_DATA_CLEAR;
        break;

        case SYS_FD_STUS_CLR:
        return true;

        case SYS_CTRL_STUS_CLR:
        //  return control.his_ctrl_clr(TYPE_RLY_1); //清除历史状态
        return true;

        case SYS_EVT_CLR:
        event.group_clr(EVENT_TYPE_NUM);
        return true;

        case SYS_LC_CLR:
        return true;

        case SYS_BL_CLR:
        billing.reset(SYS_DATA_RESET);
        return true;

        case SYS_MD_CLR:
        demand.reset(SYS_DATA_RESET);
        sysinit_out_stus |= INIT_MD_CLEAR;
        return true;
        default: return false;
    }
	mclock.reset(tmp);
    // control.reset(tmp);
    display.reset(tmp);
    mstatus.reset(tmp);
    tariff.reset(tmp);
    billing.reset(tmp);
    demand.reset(tmp);
    energy.reset(tmp);
    event.reset(tmp);
    loadcurve.reset(tmp);
    power_event.reset(tmp);
#if HW_DCU_MODUL
    module_para.reset(tmp);
#endif
#if SW_PAYMENT_EN
    step_tariff.reset(tmp);
    pay.reset(tmp);
#endif
    local_port.reset(tmp);

    /// @brief 系统清0后，重新初始化电表
    if(type == SYS_GROBAL_CLR)
    {
        sysinit_self_checking();
        status_task.init();
        tariff.init();
    #if FB_PAYMENT_ENABLE
        step_tariff_task.init();
        payment_task.init();
    #endif
    #if USE_LCD
        display_task.init();
    #endif
    }

    return true;
}



/// @brief 声明初始化模块对象
const struct sysinit_s sysinit =
{
    .reset                  = sysinit_reset,
    .self_checking          = sysinit_self_checking,
    .state_query            = sysinit_state_query,
    .state_clr              = sysinit_state_clr,
};

