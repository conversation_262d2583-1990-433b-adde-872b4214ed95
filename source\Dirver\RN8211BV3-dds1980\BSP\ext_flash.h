/**
  ******************************************************************************
  * @file    flash.h
  * <AUTHOR> @date    2024
  * @brief   
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifndef __BSP_DATA_FLASH_H
#define __BSP_DATA_FLASH_H


/* Includes -----------------------------------------------------------------*/
#include "typedef.h"


/* Export macro -------------------------------------------------------------*/
/* 定义25系列Data Flash物理页大小 */
#define M25_PAGE_LEN            256ul     // Unit: 1byte
#define M25_PAGE_NUM            4096ul
#define M25_SECTOR_LEN          4096ul
#define M25_SECTOR_NUM          2048ul
#define M25_BLOCK_LEN           65536ul
#define M25_SizeGet(id)         (1ul << (uint8_t)(id & 0x1F))
#define M25_PEValid(id)         boolof(id & 0x8000)
#define M25_SECTOR_ADDR(x)      (x & ( ~((uint32_t)M25_SECTOR_LEN - 1)))
#define M25_BLOCK_ADDR(x)       (x & ( ~((uint32_t)M25_BLOCK_LEN - 1)))

#define TAG_DF_ROLL_ACCESS      0x20000000 // 采用滚动扇区方式访问dataflash的TAG定义

#define DATAFLASH_PAGE_LEN    M25_SECTOR_LEN
#define DATAFLASH_PAGE_NUM    M25_SECTOR_NUM
#define DATAFLASH_SIZE       (M25_SECTOR_LEN*M25_SECTOR_NUM)


/* Exported functions -------------------------------------------------------*/
/* 25系列DATAFLASH驱动 */
extern uint32_t bsp_ext_flash_check(void);
extern bool flash_sector_erase(uint32_t addr);
extern bool flash_chip_erase(void);
extern bool bsp_flash_read_bytes(uint32_t addr, void* dat, uint32_t length);
extern bool flash_write_bytes(uint16_t df_id, uint32_t addr, const void* dat, uint32_t num);
extern bool flash_program_bytes(uint16_t df_id, uint32_t addr, const void* dat, uint32_t num);

/// @brief DataFlash接口结构体
struct extflash_s
{
    uint32_t (*check)(void);
    bool (*read)(uint32_t addr, void* dat, uint32_t len);
    /// @brief 外部flash读数据
    bool (*write)(uint32_t addr, const void* dat, uint32_t len);
    /// @brief 外部flash写数据，用于boot区，应用层禁止调用
    bool (*program)(uint32_t addr, const void* dat, uint32_t len);
};
extern const struct extflash_s extflash;

#endif /* __BSP_DATA_FLASH_H */

