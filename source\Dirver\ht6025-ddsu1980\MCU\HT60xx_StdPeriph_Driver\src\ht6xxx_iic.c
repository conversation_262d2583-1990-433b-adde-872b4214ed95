/*
*********************************************************************************************************
*                                              HT6XXXX
*                                           Library Function
*
*                                   Copyright 2013, Hi-Trend Tech, Corp.
*                                        All Rights Reserved
*
*
* Project      : HT6xxx
* File         : ht6xxx_iic.c
* By           : Hitrendtech_SocTeam
* Version      : V1.0.0
* Description  :
*********************************************************************************************************
*/

#define  __HT6XXX_IIC_C

#include "ht6xxx_iic.h"

/*
*********************************************************************************************************
*                                           本地宏/结构体
*********************************************************************************************************
*/



/*
*********************************************************************************************************
*                                             本地变量
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                           本地函数申明
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                     INITIAL IIC MODULE
*
* 函数说明: 初始化IIC模块
*
* 入口参数: 无
*
* 返回参数: 无
*
* 特殊说明: 用户在配置IIC寄存器前应先使能IIC模块，具体参见HT_CMU_ClkCtrl0Config()函数
*********************************************************************************************************
*/
void HT_IIC_Init()
{
    /*  assert_param  */

}

