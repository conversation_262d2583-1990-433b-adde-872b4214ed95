/**
 ******************************************************************************
 * @file    des.c
 * <AUTHOR> @date    2025
 * @brief   DES加密和解密实现
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <string.h>

// DES的常量和置换表 (与之前相同，此处省略以保持代码简洁，实际使用时请包含完整代码)
// IP, FP, PC1, PC2, shift_bits, E_box, P_box, S_boxes...
// 为了完整性，将所有常量重新包含进来

// IP - 初始置换表
const int ip_table[64] = {58, 50, 42, 34, 26, 18, 10, 2, 60, 52, 44, 36, 28, 20, 12, 4, 62, 54, 46, 38, 30, 22, 14, 6, 64, 56, 48, 40, 32, 24, 16, 8,
                          57, 49, 41, 33, 25, 17, 9,  1, 59, 51, 43, 35, 27, 19, 11, 3, 61, 53, 45, 37, 29, 21, 13, 5, 63, 55, 47, 39, 31, 23, 15, 7};

// FP - 逆初始置换表
const int fp_table[64] = {40, 8, 48, 16, 56, 24, 64, 32, 39, 7, 47, 15, 55, 23, 63, 31, 38, 6, 46, 14, 54, 22, 62, 30, 37, 5, 45, 13, 53, 21, 61, 29,
                          36, 4, 44, 12, 52, 20, 60, 28, 35, 3, 43, 11, 51, 19, 59, 27, 34, 2, 42, 10, 50, 18, 58, 26, 33, 1, 41, 9,  49, 17, 57, 25};

// PC-1 - 密钥置换选择1
const int pc1_table[56] = {57, 49, 41, 33, 25, 17, 9,  1, 58, 50, 42, 34, 26, 18, 10, 2, 59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36,
                           63, 55, 47, 39, 31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37, 29, 21, 13, 5, 28, 20, 12, 4};

// PC-2 - 密钥置换选择2
const int pc2_table[48] = {14, 17, 11, 24, 1,  5,  3,  28, 15, 6,  21, 10, 23, 19, 12, 4,  26, 8,  16, 7,  27, 20, 13, 2,
                           41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32};

// 轮左移位数
const int shift_bits[16] = {1, 1, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 1};

// E-Box - 扩展置换表
const int e_table[48] = {32, 1,  2,  3,  4,  5,  4,  5,  6,  7,  8,  9,  8,  9,  10, 11, 12, 13, 12, 13, 14, 15, 16, 17,
                         16, 17, 18, 19, 20, 21, 20, 21, 22, 23, 24, 25, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 1};

// P-Box - P置换表
const int p_table[32] = {16, 7, 20, 21, 29, 12, 28, 17, 1, 15, 23, 26, 5, 18, 31, 10, 2, 8, 24, 14, 32, 27, 3, 9, 19, 13, 30, 6, 22, 11, 4, 25};

// S-Boxes
const int s_boxes[8][4][16] = {
    // S1
    {{14, 4, 13, 1, 2, 15, 11, 8, 3, 10, 6, 12, 5, 9, 0, 7},
     {0, 15, 7, 4, 14, 2, 13, 1, 10, 6, 12, 11, 9, 5, 3, 8},
     {4, 1, 14, 8, 13, 6, 2, 11, 15, 12, 9, 7, 3, 10, 5, 0},
     {15, 12, 8, 2, 4, 9, 1, 7, 5, 11, 3, 14, 10, 0, 6, 13}},
    // S2
    {{15, 1, 8, 14, 6, 11, 3, 4, 9, 7, 2, 13, 12, 0, 5, 10},
     {3, 13, 4, 7, 15, 2, 8, 14, 12, 0, 1, 10, 6, 9, 11, 5},
     {0, 14, 7, 11, 10, 4, 13, 1, 5, 8, 12, 6, 9, 3, 2, 15},
     {13, 8, 10, 1, 3, 15, 4, 2, 11, 6, 7, 12, 0, 5, 14, 9}},
    // S3
    {{10, 0, 9, 14, 6, 3, 15, 5, 1, 13, 12, 7, 11, 4, 2, 8},
     {13, 7, 0, 9, 3, 4, 6, 10, 2, 8, 5, 14, 12, 11, 15, 1},
     {13, 6, 4, 9, 8, 15, 3, 0, 11, 1, 2, 12, 5, 10, 14, 7},
     {1, 10, 13, 0, 6, 9, 8, 7, 4, 15, 14, 3, 11, 5, 2, 12}},
    // S4
    {{7, 13, 14, 3, 0, 6, 9, 10, 1, 2, 8, 5, 11, 12, 4, 15},
     {13, 8, 11, 5, 6, 15, 0, 3, 4, 7, 2, 12, 1, 10, 14, 9},
     {10, 6, 9, 0, 12, 11, 7, 13, 15, 1, 3, 14, 5, 2, 8, 4},
     {3, 15, 0, 6, 10, 1, 13, 8, 9, 4, 5, 11, 12, 7, 2, 14}},
    // S5
    {{2, 12, 4, 1, 7, 10, 11, 6, 8, 5, 3, 15, 13, 0, 14, 9},
     {14, 11, 2, 12, 4, 7, 13, 1, 5, 0, 15, 10, 3, 9, 8, 6},
     {4, 2, 1, 11, 10, 13, 7, 8, 15, 9, 12, 5, 6, 3, 0, 14},
     {11, 8, 12, 7, 1, 14, 2, 13, 6, 15, 0, 9, 10, 4, 5, 3}},
    // S6
    {{12, 1, 10, 15, 9, 2, 6, 8, 0, 13, 3, 4, 14, 7, 5, 11},
     {10, 15, 4, 2, 7, 12, 9, 5, 6, 1, 13, 14, 0, 11, 3, 8},
     {9, 14, 15, 5, 2, 8, 12, 3, 7, 0, 4, 10, 1, 13, 11, 6},
     {4, 3, 2, 12, 9, 5, 15, 10, 11, 14, 1, 7, 6, 0, 8, 13}},
    // S7
    {{4, 11, 2, 14, 15, 0, 8, 13, 3, 12, 9, 7, 5, 10, 6, 1},
     {13, 0, 11, 7, 4, 9, 1, 10, 14, 3, 5, 12, 2, 15, 8, 6},
     {1, 4, 11, 13, 12, 3, 7, 14, 10, 15, 6, 8, 0, 5, 9, 2},
     {6, 11, 13, 8, 1, 4, 10, 7, 9, 5, 0, 15, 14, 2, 3, 12}},
    // S8
    {{13, 2, 8, 4, 6, 15, 11, 1, 10, 9, 3, 14, 5, 0, 12, 7},
     {1, 15, 13, 8, 10, 3, 7, 4, 12, 5, 6, 11, 0, 14, 9, 2},
     {7, 11, 4, 1, 9, 12, 14, 2, 0, 6, 10, 13, 15, 3, 5, 8},
     {2, 1, 14, 7, 4, 10, 8, 13, 15, 12, 9, 0, 3, 5, 6, 11}}};

// 辅助函数：将一个64位的uint64_t转换成一个64位的比特数组
static void uint64_to_bits(uint64_t n, uint8_t *bits, int size)
{
    for(int i = 0; i < size; i++) { bits[i] = (n >> (size - 1 - i)) & 1; }
}

// 辅助函数：将一个比特数组转换回64位的uint64_t
static uint64_t bits_to_uint64(const uint8_t *bits, int size)
{
    uint64_t n = 0;
    for(int i = 0; i < size; i++) { n = (n << 1) | bits[i]; }
    return n;
}

// 将64位数据存储到字节数组中，按大端序存储
static void set_msbdata64(uint8_t *p, uint64_t dat)
{
    uint8_t *ptr = p + 7;    // 指向末尾
    for(uint8_t i = 0; i < 8; i++)
    {
        *ptr-- = (uint8_t)dat;
        dat >>= 8;
    }
}

// 从字节数组的前len个字节获取64位数据，len最大为8,不足部分补0
static uint64_t get_msbdata64(const uint8_t *p, uint8_t len)
{
    int8_t   i;
    uint64_t dat = 0;
    for(i = 0; i < 8; i++)
    {
        dat <<= 8;
        if(i < len) dat += p[i];
    }
    return dat;
}

// 进行置换
static void permute(const uint8_t *input, uint8_t *output, const int *table, int size)
{
    for(int i = 0; i < size; i++) { output[i] = input[table[i] - 1]; }
}

// 循环左移
static void left_shift(uint8_t *input, int size, int shifts)
{
    uint8_t temp[64];
    memcpy(temp, input, size);
    for(int i = 0; i < size; i++) { input[i] = temp[(i + shifts) % size]; }
}

// 生成16个子密钥
static void generate_subkeys(uint64_t key, uint8_t subkeys[16][48])
{
    uint8_t key_bits[64];
    uint8_t pc1_key[56];
    uint8_t c[28], d[28];

    uint64_to_bits(key, key_bits, 64);
    permute(key_bits, pc1_key, pc1_table, 56);

    memcpy(c, pc1_key, 28);
    memcpy(d, pc1_key + 28, 28);

    for(int i = 0; i < 16; i++)
    {
        left_shift(c, 28, shift_bits[i]);
        left_shift(d, 28, shift_bits[i]);

        uint8_t cd[56];
        memcpy(cd, c, 28);
        memcpy(cd + 28, d, 28);

        permute(cd, subkeys[i], pc2_table, 48);
    }
}

// DES的f函数（Feistel函数）
static uint64_t feistel_function(uint64_t R, const uint8_t *subkey)
{
    uint8_t r_bits[32];
    uint8_t e_bits[48];
    uint8_t s_out[32];
    uint8_t p_out[32];

    uint64_to_bits(R, r_bits, 32);
    permute(r_bits, e_bits, e_table, 48);

    // 异或
    for(int i = 0; i < 48; i++) { e_bits[i] ^= subkey[i]; }

    // S-Box替换
    for(int i = 0; i < 8; i++)
    {
        uint8_t row = (e_bits[i * 6] << 1) | e_bits[i * 6 + 5];
        uint8_t col = (e_bits[i * 6 + 1] << 3) | (e_bits[i * 6 + 2] << 2) | (e_bits[i * 6 + 3] << 1) | e_bits[i * 6 + 4];

        uint8_t s_val = s_boxes[i][row][col];

        // 将4位结果转换成比特
        s_out[i * 4 + 0] = (s_val >> 3) & 1;
        s_out[i * 4 + 1] = (s_val >> 2) & 1;
        s_out[i * 4 + 2] = (s_val >> 1) & 1;
        s_out[i * 4 + 3] = (s_val >> 0) & 1;
    }

    permute(s_out, p_out, p_table, 32);

    return bits_to_uint64(p_out, 32);
}

// 统一的DES加密/解密函数
// is_encrypt = 1 for encryption, 0 for decryption
// input:  待加密数据指针
// output: 密文输出
// key: 64位密钥
static uint64_t des_caculate(uint64_t data, uint64_t key, int is_encrypt)
{
    uint8_t data_bits[64];
    uint8_t ip_bits[64];
    uint8_t subkeys[16][48];

    generate_subkeys(key, subkeys);
    uint64_to_bits(data, data_bits, 64);

    // 初始置换
    permute(data_bits, ip_bits, ip_table, 64);

    uint64_t L = bits_to_uint64(ip_bits, 32);
    uint64_t R = bits_to_uint64(ip_bits + 32, 32);

    // 16轮Feistel网络
    for(int i = 0; i < 16; i++)
    {
        uint64_t new_L = R;
        // 根据模式选择子密钥顺序
        const uint8_t *current_subkey = is_encrypt ? subkeys[i] : subkeys[15 - i];
        uint64_t       new_R          = L ^ feistel_function(R, current_subkey);
        L                             = new_L;
        R                             = new_R;
    }

    // 交换左右两半
    uint64_t combined = (R << 32) | L;

    uint8_t combined_bits[64];
    uint64_to_bits(combined, combined_bits, 64);

    // 最终置换
    uint8_t final_bits[64];
    permute(combined_bits, final_bits, fp_table, 64);

    return bits_to_uint64(final_bits, 64);
}

// // 辅助函数：打印字节数组
// void print_bytes(const char *label, const uint8_t *data, size_t size)
// {
//     printf("%s: ", label);
//     for(size_t i = 0; i < size; i++) { printf("%02X", data[i]); }
//     printf("\n");
// }

/// @brief DES加密/解密处理函数
/// @param input  输入数据指针, 注意解密时候输入数据长度必须是8的倍数，加密时不是8的倍数时末尾自动补0
/// @param output 输出数据指针，注意确保有足够空间存储结果
/// @param len    输入数据长度
/// @param _key   密钥
/// @param is_encrypt  1-加密 0-解密
void des_process(const void *input, void *output, uint16_t len, uint8_t *_key, int is_encrypt)
{
    uint8_t *in   = (uint8_t *)input;
    uint8_t *out  = (uint8_t *)output;
    uint64_t data = 0;
    uint64_t key  = *((uint64_t *)_key);

    // if(len % 8 != 0) { return; }

    while(len)
    {
        uint8_t chunk_size = (len >= 8) ? 8 : len;

        len -= chunk_size;
        data = 0;
        data = get_msbdata64(in, chunk_size), in += 8;    // 确保数据是大端格式

        uint64_t result = des_caculate(data, key, is_encrypt);

        set_msbdata64(out, result), out += 8;    // 将结果存储到输出缓冲
    }
}

// 主函数示例
// int main()
// {
//     uint8_t  plaintext[] = {0x01, 0x04, 0x00, 0xEF, 0x01, 0x09, 0x21, 0x20, 0x32, 0x25, 0x10, 0x29, 0x09, 0x21, 0x20, 0x4E, 0x00, 0x00, 0x05, 0x00, 0x05, 0x00, 0xBE, 0xB4};    //
//     uint64_t key         = 0x829052EF8BE94987;
//     uint8_t  encrypted_data[512];
//     uint8_t  decrypted_data[512];
//     uint16_t len;

//     print_bytes("plaintext", plaintext, sizeof(plaintext));
//     printf("key: %llX\n", key);
//     // printf("len: %d\r\n", sizeof(plaintext));
//     // 加密
//     des_process(plaintext, encrypted_data, sizeof(plaintext), (uint8_t *)&key, 1);

//     len = (sizeof(plaintext) / 8 + (sizeof(plaintext) % 8 ? 1 : 0)) * 8;    // 确保长度是8的倍数
//     print_bytes("encrypted_data", encrypted_data, len);

//     // 解密

//     des_process(encrypted_data, decrypted_data, len, (uint8_t *)&key, 0);
//     print_bytes("decrypted_data", decrypted_data, len);

//     return 0;
// }
// end of file
