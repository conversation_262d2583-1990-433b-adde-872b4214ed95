
/**
 ******************************************************************************
* @file    led.c
* <AUTHOR> @date    2024
* @brief   led驱动.
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "led.h"
//#include "debug.h"

/* Private typedef -----------------------------------------------------------*/
typedef struct 
{
	uint16_t timer;
	uint16_t timeout;
    LedFreq_t freq;
    uint8_t type;
}LedCtrl_s;

/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static LedCtrl_s led_ctrl[LED_NUM];

///以下为led模块内部函数

/// @brief 关闭led
/// @param type led类型
static void led_off(uint8_t type)
{
    switch(type)
    {
        case RELAY_LED:
    #if USE_LED_RELAY
        LED_RELAY_OFF();
    #endif
        break;
        case BLACKIT_LED:
        LED_BACK_OFF();
        break;
    }        
}

/// @brief 打开led
/// @param type led类型
static void led_on(uint8_t type)
{
    // DBG_PRINTF(P_ANY, D, "\r\n>>>led_on... type: %d", type);
    switch(type)
    {
        case RELAY_LED:
    #if USE_LED_RELAY
        LED_RELAY_ON();
    #endif
        break;
        case BLACKIT_LED:
        // DBG_PRINTF(P_ANY, D, "\r\n>>>led_on...");
        LED_BACK_ON();
        break;
    }        
}

/// @brief 闪烁led
/// @param type led类型
static void led_flash(uint8_t type)
{
    switch(type)
    {
        case RELAY_LED:
        LED_RELAY_FLASH();
        break;
        case BLACKIT_LED:
        LED_BACK_FLASH();
        break;
    }        
}

/// @brief 处理led的定时器,放置到1ms中断中
/// @param None
/// @retval None
SYSTICKCALL(led_process)
{
    uint8_t i;
    for(i = 0; i < LED_NUM; i++)
    {
    	if(led_ctrl[i].timeout == 0) continue;
    	
    	if(led_ctrl[i].timer < led_ctrl[i].timeout)
    	{
    		led_ctrl[i].timer++;
    	}
    	else
    	{
    		led_ctrl[i].timer = 0;
    		led_flash(led_ctrl[i].type);
    	}
	}
}

///以下为led模块接口函数

/// @brief 控制led的打开、关闭、闪烁
/// @param type 灯的类型
/// @param freq 灯的闪烁频率
/// @retval None
void led_control(uint8_t type, LedFreq_t freq)
{
    if((type >> 4) >=  LED_NUM) return;
    
	LedCtrl_s *ptr = &led_ctrl[type >> 4];
	
	if(ptr->type == type && ptr->freq == freq) return;
	ptr->type = type;
	ptr->freq = freq;
	ptr->timer = 0;
	if(freq == FREQ_2HZ)        ptr->timeout = 250;
	else if(freq == FREQ_1HZ)   ptr->timeout = 500;
	else if(freq == FREQ_0_5HZ) ptr->timeout = 1000;
	else if(freq == FREQ_ON && type != BLACKIT_LED)    ptr->timeout = 5;  /// 100kHz方波控制常亮
	else                        ptr->timeout = 0;

	switch(freq)
	{
	    case FREQ_OFF:
	    led_off(type);
        break;
        case FREQ_0_5HZ:
        case FREQ_1HZ:
        case FREQ_2HZ:
        case FREQ_ON:
        led_on(type);
        break;
	}
}

/// @brief  初始化led模块
/// @param None
/// @retval None
void led_init(void)
{
    /// @attention 无论上下电，都需要初始化。关灯
    hal_gpio.ext_led_init(GPIO_OPEN);

	hal_timer.systick_insert(&led_process);
}


/// @brief 声明led子模块对象
const struct led_s led = 
{
    .init           = led_init,
    .ctrl           = led_control,
};

/// file end
