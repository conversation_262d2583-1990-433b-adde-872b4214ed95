1.目前检查计量校验和一秒一次，是否太频繁

2.以下三种情况下，重新开始一次校验和计算：系统复位、00H~6CH 某个寄存器发生写操作、EMUStatus

寄存器发生读操作。一次校验和计算需要 32 个 CPU 时钟。

3.ep_pulse_cnt，没有计量实际的脉冲

4.原有快速脉冲寄存器(0xC0~C8、0x100~~~108)、新增快速脉冲寄存器(0xE0~~FC)，手册0xE0对于这部分没说明

5,校表函数在645中的实现


/*

* @description: phy6252蓝牙soc
* @date: Do not edit
* @lastAuthor:
* @lastEditTime: Do not edit
* @filePath: Do not edit

 */

#ifdef__INTELLISENSE__

#include"bsp_cfg.h"

#include"hal_def.h"

#include"hal_uart.h"

#include"hal_gpio.h"

#endif

#include"bsp.h"

#include"api.h"

#include<string.h>    // For strcmp, strncpy

staticvoidble_send(uint8_t*msg, uint16_tlen);

staticboolble_send_over_query(void);

// ======================== 消息队列 ===========================

/**

* @brief 事件ID定义

 */

typedefenum

{

    EVENT_NONE,

    EVENT_BLE_RX_LINE,    // 蓝牙UART收到一行完整数据

} event_id_t;

/**

* @brief 事件结构体

 */

typedefstruct

{

    event_id_t id;

    char      *str_param;    // 所有事件都只传递字符串参数

} event_t;

#defineEVENT_QUEUE_SIZE16

staticevent_t          g_event_queue[EVENT_QUEUE_SIZE];

staticvolatileuint8_t g_event_queue_head =0;

staticvolatileuint8_t g_event_queue_tail =0;

staticvoidevent_queue_init(void)

{

    g_event_queue_head =0;

    g_event_queue_tail =0;

    memset(g_event_queue, 0, sizeof(g_event_queue));

}

staticboolevent_queue_put(event_tevent)

{

    uint8_t next_head = (g_event_queue_head +1) % EVENT_QUEUE_SIZE;

    if(next_head == g_event_queue_tail) returnfalse;    // 队列已满

    g_event_queue[g_event_queue_head] = event;

    g_event_queue_head                = next_head;

    returntrue;

}

staticevent_tevent_queue_get(void)

{

    event_t event = {.id = EVENT_NONE};

    if(g_event_queue_head != g_event_queue_tail)

    {

    event              =g_event_queue[g_event_queue_tail];

    g_event_queue_tail = (g_event_queue_tail +1) % EVENT_QUEUE_SIZE;

    }

    return event;

}

// ======================== 蓝牙模块 ===========================

/// @brief 串口状态

typedefenum

{

    COMM_IDLE     =0,

    COMM_WAIT_ACK =2,

} COMM_TYPE;

/// @brief 单任务状态

typedefenum

{

    RET_AT_CMD_NULL  =0,

    RET_AT_CMD_SUCC  =1,

    RET_AT_CMD_FAIL  =2,

    RET_AT_CMD_DOING =3,

} RET_AT_CMD_TYPE;

typedefenum

{

    PHY6252_CHECK_AT,           // 1. 刚上电，准备启动

    PHY6252_SET_ATE0,           // 2. 发送 "ATE0" 测试指令

    PHY6252_SET_NAME,           // 3. 发送 "AT+NAME=xxx" 设置名称

    PHY6252_SET_UUID,           // 5. 发送 "AT+ADDR=xxx" 设置地址

    PHY6252_SET_PSW,            //

    PHY6252_SET_ADV,            //

    PHY6252_SET_TRANSPARENT,    // 6. 发送 "AT+PASS=1" 设置透传模式

    PHY6252_TRANSPARENT,        // 7. 透传模式

    PHY6252_SOFT_RESET,         // 8. 发送 "AT+RESET" 软件复位

} ble_thread_s;

typedefstruct

{

    uint8_t*rx_buf;

    uint16_t rx_len;

    uint8_t*tx_buf;

    uint16_t tx_len;

    struct

    {

    COMM_TYPE    comm_type;

    ble_thread_s thread;

    uint8_t      retry_times;

    SwTimer_s    timer;

    } status;

} ble_params_t;

ble_params_t ble_params;

#defineBLE_TX_BUF_SIZE128

staticuint8_tble_tx_buf[BLE_TX_BUF_SIZE];

// ===================== 使用系统节拍定时器实现超时检测，移植需修改 ===========================

#defineBLE_TIMEOUT_SET(timeout) hal_timer.interval(&ble_params.status.timer, timeout)    // 设置超时时间

#defineBLE_TIMEOUT_CHECK() hal_timer.expired(&ble_params.status.timer)                   // 查询是否超时

#defineBLE_TIMEOUT_RESTART() hal_timer.restart(&ble_params.status.timer)                 // 重启计时器

/**

* @description: 判断AT命令是否发送成功
* @param {char} *cmd
* @param {char} *ack
* @param {uint32_t} timeout
* @param {uint8_t} maxtimes
* @return {*}

 */

__STATIC_INLINE RET_AT_CMD_TYPE check_at_cmd_ack(constchar*cmd, constchar*ack, uint32_ttimeout, uint8_tmaxtimes)

{

    if(ble_params.status.comm_type== COMM_IDLE)    // 发送AT命令

    {

    ble_params.status.comm_type= COMM_WAIT_ACK;

    BLE_TIMEOUT_SET(timeout);

    ble_params.tx_len=sprintf((char*)ble_params.tx_buf, "%s\r\n", cmd);

    ble_send(ble_params.tx_buf, ble_params.tx_len);

    }

    elseif(ble_params.status.comm_type== COMM_WAIT_ACK)

    {

    if(ble_params.rx_len>=strlen(ack) &&strstr((char*)ble_params.rx_buf, ack))

    {

    ble_params.status.comm_type= COMM_IDLE;

    return RET_AT_CMD_SUCC;

    }

    elseif(BLE_TIMEOUT_CHECK() !=0)

    {

    ble_params.status.retry_times++;

    if(ble_params.status.retry_times>= maxtimes)

    {

    ble_params.status.comm_type= COMM_IDLE;

    return RET_AT_CMD_FAIL;

    }

    else

    {

    BLE_TIMEOUT_RESTART();

    ble_params.status.comm_type= COMM_IDLE;

    return RET_AT_CMD_DOING;

    }

    }

    }

    return RET_AT_CMD_DOING;

}

__STATIC_INLINE RET_AT_CMD_TYPE ble_check_at(void*arg)

{

    (void)arg;

    returncheck_at_cmd_ack("AT\r\n", "OK", 1000, 3);

}

__STATIC_INLINE RET_AT_CMD_TYPE ble_set_ate0(void*arg)

{

    (void)arg;

    returncheck_at_cmd_ack("AT0\r\n", "OK", 1000, 3);

}

__STATIC_INLINE RET_AT_CMD_TYPE ble_set_name(void*arg)

{

    (void)arg;

    returncheck_at_cmd_ack("AT+NAME=DDS1980_T1\r\n", "OK", 1000, 3);

}

__STATIC_INLINE RET_AT_CMD_TYPE ble_soft_reset(void*arg)

{

    (void)arg;

    returncheck_at_cmd_ack("AT+SYSTEMRESET\r\n", "OK", 1000, 3);

}

__STATIC_INLINE RET_AT_CMD_TYPE ble_set_uuid(void*arg)

{

    (void)arg;

    uint8_taddr[6] = {0};

    api.meter_sn_get(addr);

    /// @brief 生成AT命令

    if(ble_params.status.comm_type== COMM_IDLE) sprintf((char*)ble_params.tx_buf, "AT+UUID=%02x%02x%02x%02x%02x%02x\r\n", addr[5], addr[4], addr[3], addr[2], addr[1], addr[0]);

    returncheck_at_cmd_ack((char*)ble_params.tx_buf, "OK", 1000, 3);

}

/// @brief  透传模式，检查数据格式

/// @paramarg

/// @return

__STATIC_INLINE RET_AT_CMD_TYPE ble_pass_transparent(void*arg)

{

    (void)arg;

    ble_params.status.comm_type= COMM_WAIT_ACK;

    return RET_AT_CMD_SUCC;

}

__STATIC_INLINE intat_thread_handle(RET_AT_CMD_TYPE ret, ble_thread_s succ, ble_thread_s err)

{

    if(ret == RET_AT_CMD_SUCC)

    ble_params.status.thread= succ;

    elseif(ret == RET_AT_CMD_FAIL)

    ble_params.status.thread= err;

    else {}

    return0;

}

__STATIC_INLINE boolble_phy6252_main_thread(void)

{

    switch(ble_params.status.thread)

    {

    case PHY6252_CHECK_AT:

    at_thread_handle(ble_check_at(NULL), PHY6252_SET_ATE0, PHY6252_SOFT_RESET);

    break;

    case PHY6252_SET_ATE0:

    at_thread_handle(ble_set_ate0(NULL), PHY6252_TRANSPARENT, PHY6252_SOFT_RESET);

    break;

    case PHY6252_SET_NAME:

    at_thread_handle(ble_set_name(NULL), PHY6252_SET_UUID, PHY6252_SOFT_RESET);

    break;

    case PHY6252_SET_UUID:

    at_thread_handle(ble_set_uuid(NULL), PHY6252_TRANSPARENT, PHY6252_SOFT_RESET);

    break;

    case PHY6252_TRANSPARENT:

    at_thread_handle(ble_pass_transparent(NULL), PHY6252_TRANSPARENT, PHY6252_SOFT_RESET);

    break;

    case PHY6252_SOFT_RESET:

    at_thread_handle(ble_soft_reset(NULL), PHY6252_CHECK_AT, PHY6252_SOFT_RESET);

    break;

    default:

    break;

    }

    returntrue;

}

/**

* @description: 接收函数，实时运行
* @return {*}

 */

staticuint16_tble_recv(void)

{

    ble_params.rx_len=hal_uart.recv(COM_BLE);

    ble_phy6252_main_thread();

    /// @brief 透传模式下，才返回数据长度

    return (ble_params.status.thread== PHY6252_TRANSPARENT) ?ble_params.rx_len:0;

}

/// @brief  蓝牙模块发送数据

staticvoidble_send(uint8_t*msg, uint16_tlen)

{

    hal_uart.send(COM_BLE, msg, len);

}

/// @brief  初始化蓝牙模块

staticvoidble_init(uint8_t*rx_buf, uint16_tbufsize)

{

    event_queue_init();

    hal_gpio.ext_ble(GPIO_OPEN);

    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_115200BPS, rx_buf, bufsize);

    memset(&ble_params, 0, sizeof(ble_params_t));

    ble_params.rx_buf= rx_buf;

    ble_params.tx_buf= ble_tx_buf;

}

/// @brief  蓝牙模块是否发送完成

staticboolble_send_over_query(void)

{

    returnhal_uart.send_over_query(COM_BLE);

}
