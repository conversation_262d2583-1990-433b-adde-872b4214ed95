# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf
*.pbi
*.dep
*.xcl


# List
# *.s
*.lst

# Linker output
*.ilk
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb
*.pbd
*.rsp
*.iar_deps
*.ninja_log
*.bin
*.sim
*.pbw
*.ninja
*.ninja_deps
*.Zip
*.map
*.ewt
*_build_cache
*.browse
*.out_nochecksum
*.gz
*DocPreview

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf
settings

# temp
*.tmp

# vscode
.vscode/
settings.json
iar-vsc.json
bookmarks.json
c_cpp_properties.json
todo.md
compile_commands.json