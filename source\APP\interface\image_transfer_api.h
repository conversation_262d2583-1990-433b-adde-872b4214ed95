/**
 ******************************************************************************
 * @file    image_transfer_api.h
 * <AUTHOR> @date    2024
 * @brief   处理升级相关的API接口
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#ifndef IMAGE_TRANSFER_API_H
#define IMAGE_TRANSFER_API_H

#include "typedef.h"
#include "boot_cfg.h"
#include "timeapp.h"
#include "ver.h"
#include "app.h"
#include "dcu.h"
#include "module_para.h"


#define IMAGE_TOTAL_SIZE FIRMWARE_MAX_SIZE    // 程序文件最大长度, 单位: B
#define IMAGE_BLOCK_MAX_SIZE 512              // 程序下载块最大长度, 必须是128的倍数
#define IMAGE_BLOCK_MAX_NUM (IMAGE_TOTAL_SIZE / 128)
#define HTTP_URL_LEN 128    // HTTP下载地址长度

typedef enum
{
    transfer_not_initiated = 0,    // 未启动程序传输状态
    transfer_initiated,            // 程序传输已初始化状态
    verification_initiated,        // 程序校验准备状态
    verification_successful,       // 程序校验成功状态
    verification_failed,           // 程序校验失败状态
    activation_initiated,          // 程序激活准备状态
    activation_successful,         // 程序激活成功状态
    activation_failed,             // 程序激活失败状态
    activation_temp_initiated,     // 程序激活临时状态
    http_download_initiated = 0xA5 // HTTP下载准备状态
} image_transfer_satus_t;

typedef struct http_url_struct
{
    uint8_t len;                         // HTTP下载地址长度，第一个字节为长度
    uint8_t url[MODULE_HTTP_URL_LEN];    // HTTP下载地址内容
} http_url_s;

typedef struct image_activate_info_struct
{
    uint32_t               image_size;                               // 程序大小
    uint32_t               crc32;                                    // 程序CRC32校验值
    http_url_s             http_url;                                 // HTTP下载URL
    uint8_t                http_dl_enable;                           // HTTP下载使能状态, 0xA5表示有更新文件需要下载，否则表示无更新文件
    image_transfer_satus_t transfer_status;                          // 程序传输状态
    uint8_t                block_status[IMAGE_BLOCK_MAX_NUM / 8];    // 程序块状态, 每个bit表示一个128字节的块是否已接收
    char                   app_last_version[VERSION_LEN_MAX];        // 预保存应用程序上一版本号
    uint8_t                enabled;                                  // 程序传输使能状态, 0表示禁止升级，1表示允许升级
    uint8_t                image_identifier[IDENTIFIER_MAX_LEN];     // 程序标识符, 第一个字节为标识符长度
    uint8_t                image_signature[SIGNATURE_MAX_LEN];       // 程序签
} image_info_s;

typedef uint16_t FMW_STATE;
#define APP_FMW_VERIFIED ((FMW_STATE)1 << 0)          // APP固件下载检验成功事件记录
#define APP_FMW_VERIFY_FAILURE ((FMW_STATE)1 << 1)    // APP固件校验失败事件记录
#define APP_FMW_UPGRADE ((FMW_STATE)1 << 2)           // APP固件升级成功事件记录

struct image_transfer_s
{
    uint16_t (*block_size_get)(void);
    uint16_t (*transferred_block_status_get)(uint8_t *block_status);
    uint32_t (*first_not_transferred_block_number)(void);
    uint32_t (*file_size_get)(void);
    bool (*transfer_enabled_get)(void);
    bool (*transfer_enabled_set)(uint8_t enabled);
    image_transfer_satus_t (*transfer_status_get)(void);
    void (*transfer_status_set)(image_transfer_satus_t t_status);
    void (*to_activate_info_get)(image_info_s *info);
    bool (*transfer_initiate)(uint32_t image_size, const uint8_t *indentifier);
    bool (*block_transfer)(uint32_t block_number, uint8_t *image, uint16_t len);
    bool (*verify)(void);
    bool (*activate)(void);

    void (*block_size_init)(uint16_t apdu_size, uint32_t block_size);
};
extern const struct image_transfer_s image;

struct fwm_upgrade_s
{
    void (*init)(void);
    void (*verify)(void);
    void (*activate_isp)(void);
    bool (*http_check)(void);
    bool (*http_init)(const void *buff, uint16_t json_len);
    bool (*http_url_get)(char *url, uint16_t max_len);
    bool (*http_transfer)(uint32_t block_number, uint8_t *image, uint16_t len);
    bool (*state_query)(FMW_STATE state);
    void (*state_clr)(void);
    uint32_t (*crc32_get)(void);
};
extern const struct fwm_upgrade_s fwm_upgrade;
extern const struct app_task_t    fwm_upgrade_task;

#endif /* IMAGE_TRANSFER_API_H */
