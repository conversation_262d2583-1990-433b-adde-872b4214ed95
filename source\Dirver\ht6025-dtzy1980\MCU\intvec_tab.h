/**
 ******************************************************************************
* @file    intvec_tab.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifndef __INTVEC_TAB_H__
#define __INTVEC_TAB_H__    

typedef enum 
{
	INT_PMU,
    INT_AES,
    INT_EXTI0,
    INT_EXTI1,
    INT_EXTI2,
    INT_EXTI3,
    INT_EXTI4,
    INT_EXTI5,
    INT_EXTI6,
    INT_UART0,
    INT_UART1,
    INT_UART2,
    INT_UART3,
    INT_UART4,
    INT_UART5,
    INT_TIMER_0,
    INT_TIMER_1,
    INT_TIMER_2,
    INT_TIMER_3,
    INT_TBS,
    INT_RTC,
    INT_I2C,
    INT_SPI0,
    INT_SPI1,
    INT_SelfTestFreq,
    INT_TIMER_4,
    INT_TIMER_5,
    INT_UART6,
    INT_EXTI7,
    INT_EXTI8,
    INT_EXTI9,
    INT_DMA,

    INT_NUM  ///< Total number of interrupt vectors
} INT_TYPE;


//
extern void int_vector_set(int irq, void vec(void));



#endif /* __INTVEC_TAB_H__ */

