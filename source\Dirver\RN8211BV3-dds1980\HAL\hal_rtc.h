/**
 ******************************************************************************
* @file    hal_rtc.h
* <AUTHOR> @date    2024
* @brief   MCU带有硬件RTC模块的驱动头文件.
*
******************************************************************************
*
* @note
* Copyright (C) 2024  JiShe Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifdef __cplusplus
extern "C" {
#endif

#ifndef __HAL_RTC_H
#define __HAL_RTC_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"


/* Exported types ------------------------------------------------------------*/
/** @brief  RTC年月日时分秒时间定义 */ 
struct rtc_t
{
    uint8_t YY;
    uint8_t MM;
    uint8_t DD;
    uint8_t hh;
    uint8_t mm;
    uint8_t ss;
    uint16_t ms;
};

/** @brief  RTC闹钟唤醒模式定义 */
typedef enum
{
    NONE_ALARM,
    SEC_ALARM,
    MIN_ALARM,
    HOUR_ALARM,
    DAY_ALARM,
} HAL_RTC_ALARM_MODE;

/** @brief  RTC初始化状态字定义 */
typedef union
{
    struct
    {
        /* rtc发生过复位初始化 */
        uint8_t rtc_init : 1;

        /* 0:rtc使用外部XOSC 32768Hz晶振
        1:rtc使用内部RC低速晶振
        2:rtc使用外部HOSC 高速晶振
        3:保留
        */
        uint8_t rtc_clksrc : 2;
        /* rtc秒中断生效过 */
        uint8_t rtc_second : 1;
        /* ppm未教准 */
        uint8_t rtc_ppm_err: 1;
    };
    uint8_t val;
} HAL_RTC_STUS;

/* Exported defines ----------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
struct hal_rtc_t
{
    /// @brief 当前秒
    const uint8_t* second;

    /// @brief RTC初始化
    HAL_RTC_STUS (*init)(void);

    /// @brief RTC初始化(低功耗电源)
    void (*init_nopower)(void);

    /// @brief RTC时钟获取
    bool (*time_get)(struct rtc_t* rtc);

    /// @brief RTC时钟设置
    bool (*time_set)(const struct rtc_t* rtc);

    /// @brief RTC秒中断设置
    bool (*irq_set)(HAL_RTC_ALARM_MODE mode, void func(void));

    /// @brief RTC秒中断查询
    bool (*irq_query)(void);

    /// @brief 写PPM校准值
    int16_t (*ppm_wr)(int16_t value);

    /// @brief 读取PPM基准值
    int16_t (*ppm_rd)(void);

    /// @brief 写校准寄存器，用于校准PPM，单位为0.1ppm，温度补偿
    void (*cali_reg_wr)(int16_t value);

    /// @brief 秒脉冲输出设置
    void (*sec_pulse_set)(uint8_t flag);
#if (EMU_TYPE==EMU_VIRTUAL)
    /// @brief 虚拟计量芯片电能积分回调函数
    void (*energy_integral_set)(void func(void));
#endif
};
extern const struct hal_rtc_t hal_rtc;

#endif /* __HAL_RTC_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

