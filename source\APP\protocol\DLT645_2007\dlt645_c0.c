/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c0.c
*    Describe:  DLT645-2007协议，00类数据部分     
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "energy.h"
#include "mic.h"
#include "billing.h"
#include "tariff.h"

extern uint8_t o07_id_energy_type_get(uint32_t id, uint8_t *ph);
/// @brief 读取数据处理
/// @param p_info 
/// @return 
uint16_t dlt_645_read_0(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;
    uint8_t index   = (uint8_t)(p_info->id);   // 0 当前，1-12 历史
    uint8_t rate    = (uint8_t)(p_info->id >> 8);
    uint8_t type;
    uint8_t ph;  // 分相
    uint8_t flg = 0;  /// 1-通讯获取，0-曲线捕获获取

    if(buff == NULL) { p_data = p_info->snd_dat; flg = 1;} //buff为空，则为通讯获取
#if SW_PAYMENT_EN 
    switch(p_info->id)
    {
        case C0_REMAIN_kWh:   

        case C0_OVERDRAFT_kWh:

        case C0_REMAIN_yuan: 

        case C0_OVERDRAFT_yuan:
            if(flg) memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
            ///
            
            return (flg ? 8 : 4); 
    }
#endif
    type = o07_id_energy_type_get(p_info->id, &ph);
    
    // 07协议只支持上12结算日的历史数据

    if((type == 0xFF)    || 
#if (!ENERGY_PHASE_ENABLE)
       (ph != T_PHASE)   || 
#endif
       (index  > BILLING_MONTH_LOG_NUM) || 
       ((rate > TARIFF_RATE_NUM || rate > tariff.day_tf_num_get()) && (rate != 0xFF)) || 
       ((ph != T_PHASE)  && (type == TYPE_ENERGY_ADD_ACT))|| 
       ((rate == 0xFF) && (index == 0xFF))) 
    { 
         if(flg){ *p_data = ERR_CODE_NO_DATA, p_info->err_f = TRUE; return 1; }
         else { memset(p_data, 0, 4); return 4; } //捕获没有的数据项返回0
    }

    if(flg) memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    if(rate ==0xFF)
    {
        for(uint8_t i = 0; i < TARIFF_RATE_NUM; i++)
        {
            ENERGY_DEF_FORMAT eng; 
            eng = energy.phs_cum_value_get(ph, (ENERGY_TYPE)type, i) / EN_SCALER_2;
            uint32_to_lsbbcd(p_data, (uint32_t)eng, 4), p_data += 4;
        }
    }
    else if(index == 0xFF)
    {
        // 当前
        ENERGY_DEF_FORMAT eng; 
        eng = energy.phs_cum_value_get(ph, (ENERGY_TYPE)type, rate) / EN_SCALER_2;
        uint32_to_lsbbcd(p_data, (uint32_t)eng, 4), p_data += 4;
        {
            BL_EN_TYPE_t en_type = billing.energy_type_get(type);
            for(uint8_t i = 1; i <= BILLING_MONTH_LOG_NUM; i++)  ///< 12个历史数据
            {
                if(en_type >= BL_EN_TYPE_NUM) 
                {
                    uint32_to_lsbbcd(p_data, 0, 4), p_data += 4;
                }
                else
                {
                    eng = billing.phs_cum_energy_get(bl_monthly, ph, i, en_type, rate);
                    uint32_to_lsbbcd(p_data, (uint32_t)eng, 4), p_data += 4;
                }
            }
        }
    }
    else if(index == 0)
    {
        // 当前
        ENERGY_DEF_FORMAT eng; 
        eng = energy.phs_cum_value_get(ph, (ENERGY_TYPE)type, rate) / EN_SCALER_2;
        uint32_to_lsbbcd(p_data, (uint32_t)eng, 4), p_data += 4;
    }
    else
    {
        // 上index结算日的历史数据
        ENERGY_DEF_FORMAT eng; 
        BL_EN_TYPE_t en_type = billing.energy_type_get(type);
        if(en_type >= BL_EN_TYPE_NUM) 
        {
            uint32_to_lsbbcd(p_data, 0, 4), p_data += 4;
        }
        else
        {
            eng = billing.phs_cum_energy_get(bl_monthly, ph, index, en_type, rate);
            uint32_to_lsbbcd(p_data, (uint32_t)eng, 4), p_data += 4;
        }
    }

    if(flg)
    {
        if((p_data - p_info->snd_dat) == 4) 
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
            return 1;
        } // 无数据
        return (uint16_t)(p_data - p_info->snd_dat);
    }
    return (uint16_t)(p_data - buff);
}

static class_unit_t energy_unit_get(uint32_t id)
{
    uint8_t ph;  // 分相
    uint8_t type;
    type = o07_id_energy_type_get(id, &ph);
    switch(type)
    {
        case TYPE_ENERGY_POS_ACT:    // 正向有功电能
        case TYPE_ENERGY_NEG_ACT:    // 反向有功电能
            return UNIT_kWh;
        case TYPE_ENERGY_Q1_REA:     // 一象限无功电能
        case TYPE_ENERGY_Q2_REA:     // 二象限无功电能
        case TYPE_ENERGY_Q3_REA:     // 三象限无功电能
        case TYPE_ENERGY_Q4_REA:     // 四象限无功电能
            return UNIT_kvarh;
    #if ENERGY_APP_ENABLE
        case TYPE_ENERGY_POS_APP:    // 正向视在电能
        case TYPE_ENERGY_NEG_APP:    // 反向视在电能
            return UNIT_kVAh;
    #endif
        case TYPE_ENERGY_POS_REA:    // 正向无功电能(Lag)
        case TYPE_ENERGY_NEG_REA:    // 反向无功电能(Lead)
            return UNIT_kvarh;
        case TYPE_ENERGY_ADD_ACT:    // 正向+反向有功组合电能
        case TYPE_ENERGY_SUB_ACT:    // 正向-反向有功组合电能
            return UNIT_kWh;
        case TYPE_ENERGY_ADD_REA:    // 正向+反向无功组合电能
            return UNIT_kvarh;
        case TYPE_ENERGY_ADD_APP:    // 正向+反向视在组合电能
            return UNIT_kVAh;
            break;
        default:
            return UNIT_UNDEF;
    }
}

/// @brief 读取数据格式
static data_format_s dlt_645_data0_format_get(uint32_t id)
{
    data_format_s format;
    format.len  = 4;
    format.type = DT_OCTET_STRING;  
    format.unit = energy_unit_get(id);
    return format;
}

/// @brief 用于曲线捕获获取数据
/// @param id 
/// @param p_data 
/// @return 
static uint16_t c0_data_get(uint32_t id, void *p_data)
{
    uint8_t *ptr = p_data;
    uint16_t len;
    DLT645_2007_MSG_S p_info;

    if(p_data == NULL) return 0;
    p_info.id = id;
    len = dlt_645_read_0(&p_info, ptr);
    if(len < 4) return 0;
    len -= 4;
    memcpy(ptr, ptr + 4, len); // 去掉ID
    return len;
}

const dlt645_data_s c0_data =
{
    .format = dlt_645_data0_format_get,
    .data   = c0_data_get,
};


/// end of file
