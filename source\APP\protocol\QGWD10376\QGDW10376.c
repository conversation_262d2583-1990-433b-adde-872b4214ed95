/**
 ******************************************************************************
 * @file    QGWD10376.c
 * <AUTHOR> @date    2025
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"
#include "utils.h"
#include "api.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

static uint8_t rsp_apdu_buff[DCU_DATA_BUF_SIZE];    ///< 应用层数据缓冲区

static req_obj_s      req_obj;
static rsp_obj_s      rsp_obj;
static last_request_s last_req;    ///< 最后一次请求的参数

extern const gdw376_table_s afn01_table;
extern const gdw376_table_s afn02_table;
extern const gdw376_table_s afn04_table;
extern const gdw376_table_s afn05_table;
extern const gdw376_table_s afn0A_table;
extern const gdw376_table_s afn0B_table;
extern const gdw376_table_s afn0C_table;
extern const gdw376_table_s afn0D_table;
extern const gdw376_table_s afn0E_table;
extern const gdw376_table_s afn10_table;

const gdw376_table_s *const gdw376_table[] = {
    &afn01_table,    ///< AFN 1
    &afn02_table,    ///< AFN 2
    &afn04_table,    ///< AFN 4
    &afn05_table,    ///< AFN 5
    &afn0A_table,    ///< AFN 0A
    &afn0B_table,    ///< AFN 0B
    &afn0C_table,    ///< AFN 0C
    &afn0D_table,    ///< AFN 0D
    &afn0E_table,    ///< AFN 0E
    &afn10_table     ///< AFN 16
};
const uint8_t gdw376_table_num = sizeof(gdw376_table) / sizeof(gdw376_table[0]);

/// @brief 376协议中断地址生成函数，由表号前4后5位组成，前4位BCD码+后4位十六进制
/// @param buff
static void gwd376_address_get(uint8_t *buff)
{
    uint8_t  addr[METER_SN_LEN] = {0};
    uint32_t addr2;

    api.meter_sn_get(addr);
    memcpy(buff, addr + 4, 2);                             // 将表号拷贝到缓冲区
    addr[2] &= 0x0F;                                       // 只保留低4位
    addr2 = lsbbcd_to_hex32((const uint8_t *)&addr, 3);    // 将BCD编码转换为十六进制
    memcpy(&buff[2], (uint8_t *)&addr2, 2);                // 将转换后的地址拷贝到缓冲区
}

/// @brief
/// @param buf
/// @return
static addr_type_t gwd376_address_check(const uint8_t *buf)
{
    uint8_t  tmp[METER_SN_LEN] = {0};
    uint16_t addr2;
    uint16_t addr1;
    uint16_t addr1_r;
    uint16_t addr2_r;
    uint8_t  addr3_r;

    memcpy(&addr1_r, buf, 2);        // 获取地址域A1
    memcpy(&addr2_r, buf + 2, 2);    // 获取地址域A2
    addr3_r = buf[4];                //

    if((addr2_r == 0xFFFF) && (addr3_r & 0x01)) return ADDR_BROADCAST;    // 广播地址
    if((addr2_r == 0x0000)) return ADDR_NO_MATCH;                         // 广播地址

    api.meter_sn_get(tmp);
    memcpy((uint8_t *)&addr1, tmp + 4, 2);                          // 将表号拷贝到缓冲区
    tmp[2] &= 0x0F;                                                 // 只保留低4位
    addr2 = (uint16_t)lsbbcd_to_hex32((const uint8_t *)&tmp, 3);    // 将BCD编码转换为十六进制

    return (((addr1 == addr1_r) && (addr2 == addr2_r)) ? ADDR_MATCH : ADDR_NO_MATCH);
}

static const gdw376_table_s *get_gdw376_table(apdu_afn_t afn)
{
    for(uint8_t i = 0; i < gdw376_table_num; i++)
    {
        if(gdw376_table[i] && (gdw376_table[i]->afn == afn)) { return gdw376_table[i]; }
    }
    return NULL;    // 未找到对应的功能码处理函数
}

/// @brief 创建376协议数据帧
/// @note 1,用户数据长度 L1由 D2～D15 组成，采用 BIN 编码，是控制域、地址域、链路用户数据（应用层）
/// @note   的字节总数，采用专用无线数传信道，长度 L1不应大于 255，采用网络传输，长度 L1不应大于 16383。
/// @note 2,FCB/ACD标志位。ACD 位用于上行响应报文中。ACD=1，表示终端有重要事件等待访问，
/// @note   则附加信息域中带有事件计数器 EC（EC 见本部分 5.3.4.6.3）；ACD=0，表示终端无事件数据等待访问0-表示此帧报文不是应答帧，1-表示此帧报文是应答帧
/// @note 3,PRM标志位。1-表示此帧报文来自启动站，0-表示此帧报文来自从动站
/// @note 4,DIR=0，表示此帧报文是由主站发出的下行报文；DIR=1，表示此帧报文是由终端发出的上行报文。
/// @param fun  数据链路层 功能码，包含在 prm1_fun_t prm0_fun_t
/// @param prm  PRM标志位 1 发起，0 响应
/// @param acd  ACD标志位 0-无事件数据等待访问，1-有事件数据等待访问
/// @param buf  数据帧缓冲区
/// @param apdu_len  数据域长度
static uint16_t gwd376_frame_creat(uint8_t fun, uint8_t prm, uint8_t acd, uint8_t msa, uint8_t *buf, uint8_t *apdu, uint16_t apdu_len)
{
    uint8_t *ptr = buf;
    uint16_t tlen;
    ctrl_s   c;

    if(apdu_len > DCU_DATA_BUF_SIZE) { return 0; }    // 检查数据长度是否超过缓冲区大小

    {
        // 前导码处理
#if HEAD_FE_NUM
        uint8_t head_fe_num = HEAD_FE_NUM;
        while(head_fe_num)    // 添加帧头前导码
        {
            *ptr++ = 0xFE;    // 帧头前导码
            head_fe_num--;
        }
#endif
    }

    /// 1,帧头
    ptr[HEAD] = 0x68;    // 帧头
    /// 2,帧长度
    tlen      = ((apdu_len + APDU - CTRL) << 2) | 0x0002;    // D0=0、D1=1：本协议使用
    ptr[LENL] = ptr[LENL2] = (uint8_t)tlen;                  // 帧长度低字节
    ptr[LENH] = ptr[LENH2] = (uint8_t)(tlen >> 8);           // 帧长度高字
    /// 3,帧头2
    ptr[HEAD2] = 0x68;    // 第二帧头
    /// 4,控制域C
    c.fun     = fun;               // 功能码
    c.fcv     = 0;                 // FCV标志位
    c.fcb_acd = acd ? 1 : 0;       // 事件标志位，0表示无事件数据等待访问
    c.prm     = prm;               // PRM
    c.dir     = 1;                 // DIR
    ptr[CTRL] = *(uint8_t *)&c;    // 控制域C
    /// 5,地址域A1
    gwd376_address_get(&ptr[ADR1L]);      // 地址域A1 A2
    if(prm == 1) { ptr[ADR3] = 0x00; }    // 地址域A3，启动站设置为0x00
    else { ptr[ADR3] = msa; }             // 地址域A3，暂时设置为0
    /// 6,应用层数据
    ptr += APDU;                                     // 移动指针到应用层数据位置
    memcpy(ptr, apdu, apdu_len), ptr += apdu_len;    // 拷贝应用层数据到帧中
    /// 7,校验和
    tlen   = apdu_len + APDU - CTRL;             // 校验和数据域，6=控制域1+地址域5
    *ptr++ = cal_checksum8(&buf[CTRL], tlen);    // 计算校验和
    /// 8,帧尾
    *ptr++ = 0x16;    // 帧尾

    return (uint16_t)(ptr - buf);    // 返回数据帧长度
}

/// @brief 根据pn, fn值生成信息点DA 以及信息类 DT
/// @param pn  信息点号 pn = 0-2040
/// @param fn  功能码 fn = 1-248
void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT)
{
    uint16_t adjust;

    if(pn == 0)
    {
        DADT[1] = 0x00;    // pn为0时，DA1, DA2都为0
        DADT[0] = 0x00;
    }
    else if(pn <= 2040)
    {
        adjust  = pn - 1;
        DADT[1] = (adjust / 8) + 1;       // DA2
        DADT[0] = (1 << (adjust % 8));    // DA1
    }
    if(fn == 0)
    {
        DADT[3] = 0;
        DADT[2] = 0;
    }
    else
    {
        adjust  = fn - 1;
        DADT[3] = (adjust / 8);           // DT2
        DADT[2] = (1 << (adjust % 8));    // DT1
    }
}

void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn)
{
    uint16_t adjust;

    if(DADT[0] == 0 || DADT[1] == 0)    // DA1, DA2都为0
    {
        if(DADT[0] == 0xff) { *pn = 0xFFFF; }    // pn为0xff表示全部测量点
        else { *pn = 0; }
    }
    else
    {
        adjust = (DADT[1] - 1) * 8 + ctz8(DADT[0]);    // 获取pn
        *pn    = adjust + 1;                           // pn = 1-2040
        if(*pn > 2040) { *pn = 2040; }                 // 确保pn不超过2040
    }

    if(DADT[2] == 0)    // DT1
    {
        *fn = 0;    // fn为0
    }
    else
    {
        adjust = (DADT[3] * 8) + ctz8(DADT[2]);    // 获取fn
        *fn    = adjust + 1;                       // fn = 1-248
        if(*fn > 248) { *fn = 248; }               // 确保fn不超过248
    }
}

/// @brief 设参
/// @param req
/// @param rsp
/// @return
static bool link_set(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t  *rsp_apdu = rsp->rsp_buf;
    rsp_err_t ret;

    if(req->apdu_len < 4) return 0;                                         // 数据长度不足
    DADT_to_fnpn(req->req_apdu, &req->pn, &req->fn), req->req_apdu += 4;    // 获取pn, fn
    req->apdu_len -= 4;                                                     // 减去DADT长度

    if(req->pn) { return false; }    // pn必须为0

    {
        const gdw376_table_s *parse = get_gdw376_table(req->afn);    // 获取对应的功能码处理函数
        if(parse == NULL || parse->set == NULL) { return false; }    // 如果没有找到对应的功能码处理函数，返回0  如果没有解析函数，返回0
        ret = parse->set(req, rsp);                                  // 调用解析函数进行处理
    }

    rsp->link_fun = RSP_FUN_ACK;    // 设置响应功能码为确认

    rsp->seq     = req->seq;    // 设置响应序号为
    rsp->seq.con = 0;           // 设置响应序号为不需要确认
    rsp->seq.tpv = 0;           // 设置响应序号为无时间戳

    *rsp_apdu++ = AFN_VERIFY;    // 确认/否认（AFN=00H）
    *rsp_apdu++ = rsp->seq.u8;
    {
        uint8_t fn;

        if(ret == ACK_RIGHT) { fn = 1; }    // 全部确认
        else { fn = 2; }                    // 全部否认
        fnpn_to_DADT(fn, 0, rsp_apdu), rsp_apdu += 4;
    }
    rsp->tlen = (uint16_t)(rsp_apdu - rsp->rsp_buf);    // 设置响应数据长度
    return true;                                        // 返回true表示解析成功
}

/// @brief 复位指令
/// @param req
/// @param rsp
/// @return
static bool link_reset(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t  *rsp_apdu = rsp->rsp_buf;
    rsp_err_t ret;

    if(req->apdu_len < 4) return 0;                                         // 数据长度不足
    DADT_to_fnpn(req->req_apdu, &req->pn, &req->fn), req->req_apdu += 4;    // 获取pn, fn
    req->apdu_len -= 4;                                                     // 减去DADT长度

    if(req->pn) { return false; }    // pn必须为0

    {
        const gdw376_table_s *parse = get_gdw376_table(req->afn);      // 获取对应的功能码处理函数
        if(parse == NULL || parse->reset == NULL) { return false; }    // 如果没有找到对应的功能码处理函数，返回0  如果没有解析函数，返回0
        ret = parse->reset(req, rsp);                                  // 调用解析函数进行处理
    }

    rsp->seq     = req->seq;    // 设置响应序号为
    rsp->seq.con = 0;           // 设置响应序号为不需要确认
    rsp->seq.tpv = 0;           // 设置响应序号为无时间戳

    *rsp_apdu++ = AFN_VERIFY;    // 确认/否认（AFN=00H）
    *rsp_apdu++ = rsp->seq.u8;
    {
        uint8_t fn;

        if(ret == ACK_RIGHT) { fn = 1; }    // 全部确认
        else { fn = 2; }                    // 全部否认
        fnpn_to_DADT(fn, 0, rsp_apdu), rsp_apdu += 4;
    }
    rsp->tlen = (uint16_t)(rsp_apdu - rsp->rsp_buf);    // 设置响应数据长度
    return true;                                        // 返回true表示解析成功
}

/// @brief 获取数据
static bool link_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *ptr = rsp->rsp_buf;

    rsp->seq     = req->seq;    // 设置响应序号为请求序号
    rsp->tlen    = 0;
    rsp->err     = ACK_ERR;    // 设置响应错误码为无错误
    rsp->seq.tpv = 0;          // 设置响应序号为无时间戳
    rsp->seq.con = 0;          // 设置响应序号为不需要确认

    // if(req->apdu_len < 4) { return false; }                                 // 数据长度不足
    // DADT_to_fnpn(req->req_apdu, &req->pn, &req->fn), req->req_apdu += 4;    // 获取pn, fn
    // req->apdu_len -= 4;

    *ptr++ = req->afn;
    *ptr++ = rsp->seq.u8;    // 设置响应序号
    // fnpn_to_DADT(req->fn, req->pn, ptr), ptr += 4;    // 设置响应的DADT
    {
        const gdw376_table_s *parse = get_gdw376_table(req->afn);    // 获取对应的功能码处理函数
        if(parse == NULL || parse->get == NULL) { return false; }    // 如果没有找到对应的功能码处理函数，返回0  如果没有解析函数，返回0
        do {
            if(req->apdu_len < 4) { break; }                                        // 数据长度不足
            DADT_to_fnpn(req->req_apdu, &req->pn, &req->fn), req->req_apdu += 4;    // 获取pn, fn
            req->apdu_len -= 4;
            fnpn_to_DADT(req->fn, req->pn, ptr), ptr += 4;    // 设置响应的DADT

            rsp->apdu = ptr;
            parse->get(req, rsp);                                     // 调用解析函数进行处理
            if(rsp->rsp_len)
            {
                ptr += rsp->rsp_len;                                      // 移动指针到下一个响应位置
            }
            else
            {
                ptr -= 4;    // 如果没有响应数据，指针后退4个字节, 去除DADT
            }
            if(ptr >= rsp->rsp_buf + DCU_DATA_BUF_SIZE) { break; }    // 检查是否超过缓冲区大小
        } while(req->apdu_len);
    }
    if(rsp->err == ACK_NO_RSP)
    {
        // 无需应答
        rsp->tlen = 0;
        return true;
    }
    rsp->err  = ACK_RIGHT;                         // 设置响应错误码为无错误
    rsp->tlen = (uint16_t)(ptr - rsp->rsp_buf);    // 设置响应数据长度
    if(rsp->tlen <= 2) 
    {
        *ptr++ = AFN_VERIFY;    // 确认/否认（AFN=00H）
        *ptr++ = rsp->seq.u8;
        fnpn_to_DADT(2, 0, ptr), ptr += 4;    // 全部否认
        rsp->tlen = (uint16_t)(ptr - rsp->rsp_buf);    // 设置响应数据长度
    }
    return true;    //
}

/// @brief 确认/否认
static uint16_t rsp_fun_ack_pro(req_obj_s *req, rsp_obj_s *rsp)
{
    apdu_afn_t afn;
    uint8_t   *ptr = req->req_apdu;
    if(req->pn != 0) return 0;    // pn必须为0

    req->verify = false;    // 设置为确认请求
    switch(req->fn)
    {
        case 1:    // F1 全部确认
        {
            last_request_s *last = &last_req;    // 获取最后一次请求的参数

            afn         = last->afn;
            req->son_pn = last->pn;    // 继承最后一次请求的pn
            req->son_fn = last->fn;    // 继承最后一次请求的fn
            req->verify = true;        // 设置为确认请求
        }
        break;
        case 2:    // F2 全部否认
        {
            last_request_s *last = &last_req;    // 获取最后一次请求的参数

            afn         = last->afn;
            req->son_pn = last->pn;    // 继承最后一次请求的pn
            req->son_fn = last->fn;    // 继承最后一次请求的fn
            req->verify = false;       // 设置为确认请求
        }
        break;
        case 3:    // F3 按数据单元标识确认和否认
        {
            if(req->apdu_len < 5) return false;                         // 数据长度不足
            afn = (apdu_afn_t)(*ptr), ptr++;                            // 要被确认的AFN
            DADT_to_fnpn(ptr, &req->son_pn, &req->son_fn), ptr += 4;    // 获取确认的pn, fn
            if(req->son_pn) return false;                               // 确认的pn必须为p0
            req->req_apdu = ptr;                                        // 设置请求的APDU数据
            req->apdu_len = req->apdu_len - 5;                          // 减去DADT长度
        }
        break;
        case 4:    // F4 硬件安全认证错误应答
        {
            return 0;
        }
        break;
        case 5:    // F5 终端心跳应答
        {
            if(req->ctrl.fun == REQ_FUN_LINK_TEST)
            {
                if(last_req.afn == AFN_LINK_TEST && last_req.fn == afn2_f3p0_heartbeat)    // 心跳
                {
                    afn         = AFN_LINK_TEST;
                    req->son_pn = 0;                      // 继承最后一次请求的pn
                    req->son_fn = afn2_f3p0_heartbeat;    // 继承最后一次请求的fn
                    req->verify = true;                   // 设置为确认请求
                }
            }
        }
        break;
        default:
            return 0;    // 未知的功能码
    }
    {
        const gdw376_table_s *parse = get_gdw376_table(afn);        // 获取对应的功能码处理函数
        if(parse == NULL || parse->verify == NULL) { return 0; }    // 如果没有找到对应的功能码处理函数，返回0
        rsp_err_t ret = parse->verify(req, rsp);                    // 调用解析函数进行处理
    }
    return 0;
}

/// @brief 作为启动站，组帧
static uint16_t linkup_encode(req_obj_s *req, rsp_obj_s *rsp)
{
    last_request_s *last = &last_req;    // 获取最后一次请求的参数
    uint8_t        *apdu;                // 使用全局应用层数据缓冲区
    uint8_t        *start;
    uint16_t        tlen;

    start = apdu = rsp->rsp_buf;

    // 功能码，seq 获取 1 + 1
    *apdu++ = req->afn;
    *apdu++ = rsp->seq.u8;
    fnpn_to_DADT((uint8_t)req->fn, 0, apdu), apdu += 4;    // DADT

    // 获取对应的功能码处理函数，并根据信息点信息类获取数据体
    const gdw376_table_s *parse = get_gdw376_table(req->afn);
    if(parse == NULL || parse->get == NULL) { return 0; }
    rsp->apdu = apdu;
    parse->get(req, rsp);

    // 计算数据长度
    tlen = apdu - start;    // 获取数据长度
    tlen += rsp->rsp_len;

    // 保存请求信息
    last->fn  = req->fn;
    last->pn  = 0;
    last->typ = (prm1_fun_t)req->ctrl.fun;
    last->afn = req->afn;

    return tlen;
}

/// @brief 链路测试报文获取
/// @param buf 数据缓冲区
/// @param typ 1表示登录，0表示心跳
/// @return 数据长度
static uint16_t gwd376_link_test_frame(uint8_t *buf, uint8_t typ)
{
    last_request_s *last = &last_req;    // 获取最后一次请求的参数
    uint16_t        tlen;
    rsp_obj_s       rsp;    // 响应对象
    req_obj_s       req;    // 请求对象

    memset(&rsp, 0, sizeof(rsp));     // 清空响应对象
    memset(&req, 0, sizeof(req));     // 清空请求对象
    rsp.rsp_buf = rsp_obj.rsp_buf;    // 设置响应缓冲区

    // 功能码，seq 获取 1 + 1
    req.afn = AFN_LINK_TEST;
    req.fn  = (typ == 1) ? afn2_f1p0_login : afn2_f3p0_heartbeat;    // 登录或心跳
    req.pn  = 0;

    rsp.seq.pseq_rseq = 0;    // PSEQ/RSEQ
    rsp.seq.con       = 1;
    rsp.seq.fin       = 1;
    rsp.seq.fir       = 1;
    rsp.seq.tpv       = 0;

    if((tlen = linkup_encode(&req, &rsp)) == 0) return 0;
    return gwd376_frame_creat(REQ_FUN_LINK_TEST, LINK_START_STATION, 0, 0, buf, rsp.rsp_buf, tlen);    // 缓存区可考虑换 p376_obj.com_buf
}

/// @brief 根据事件erc 获取 上报报文
/// @param erc  事件 erc

/// @param ack  是否需要主站应答
/// @param buf  报文缓存区
/// @return
static uint16_t event_report_create(erc_code_t erc, uint8_t fn, bool ack, uint8_t *buf)
{
    last_request_s *last = &last_req;    // 获取最后一次请求的参数
    uint16_t        tlen;
    rsp_obj_s       rsp;    // 响应对象
    req_obj_s       req;    // 请求对象

    memset(&rsp, 0, sizeof(rsp));     // 清空响应对象
    memset(&req, 0, sizeof(req));     // 清空请求对象
    rsp.rsp_buf = rsp_obj.rsp_buf;    // 设置响应缓冲区

    // 功能码，seq 获取 1 + 1
    req.afn  = AFN_REQ_CLASS3_DATA;
    req.fn   = fn;    // 事件上报固定方式 1
    req.pn   = 0;
    req.info = erc;

    rsp.seq.pseq_rseq = 0;    // PSEQ/RSEQ
    rsp.seq.con       = 1;
    rsp.seq.fin       = 1;
    rsp.seq.fir       = 1;
    rsp.seq.tpv       = 0;

    if((tlen = linkup_encode(&req, &rsp)) == 0) return 0;
    return gwd376_frame_creat(REQ_FUN_REQ_CLASS2_DATA, LINK_START_STATION, 1, 0, buf, rsp.rsp_buf, tlen);    // 缓存区可考虑换 p376_obj.com_buf
}

/// @brief  透明转发组帧，
/// @param buf 包含需要转发的内容, 发送buf
/// @param len
/// @return
static uint16_t transparent_forward_assemble(uint8_t *buf, uint8_t *apdu, uint16_t len, uint8_t com)
{
    uint8_t *ptr = buf + APDU;    // 指向APDU区域
    uint16_t tlen;

    if(!len)
    {
        *ptr++          = AFN_VERIFY;    // 设置AFN为透明转发
        req_obj.seq.con = 0;             // 设置SEQ为不需要确认
        req_obj.seq.tpv = 0;             // 设置SEQ为无时间戳

        *ptr++ = req_obj.seq.u8;    // 设置SEQ

        fnpn_to_DADT(2, 0, ptr), ptr += 4;    // 设置DADT F2 全部否认

        tlen = 6;    // 计算apdu总长度 AFN + SEQ + DADT 4字节

        return gwd376_frame_creat(RSP_FUN_ACK, LINK_SLAVE_STATION, 0, req_obj.msa, buf, buf + APDU, tlen);
    }
    else
    {
        *ptr++ = AFN_DATA_FORWARD;    // 设置AFN为透明转发

        req_obj.seq.con = 0;    // 设置SEQ为不需要确认
        req_obj.seq.tpv = 0;    // 设置SEQ为无时间戳

        *ptr++ = req_obj.seq.u8;    // 设置SEQ

        fnpn_to_DADT(req_obj.fn, req_obj.pn, ptr), ptr += 4;    // 设置DADT

        *ptr++ = com;                                 // 设置端口号
        memcpy(ptr, (uint8_t *)&len, 2), ptr += 2;    // 设置转发字节长度
        memcpy(ptr, apdu, len), ptr += len;           // 拷贝转发数据

        tlen = 9 + len;    // 计算apdu总长度 9 = AFN + SEQ + DADT 4字节 + COM 1字节 + LEN 2字节
        return gwd376_frame_creat(RSP_FUN_USER_DATA, LINK_SLAVE_STATION, 0, req_obj.msa, buf, buf + APDU, tlen);
    }
}

/// @brief 协议判断
/// @param buf
/// @param len
/// @return NULL表示验证失败，非NULL表示验证通过，返回帧起始地址
static uint8_t *gdw376_frame_verify(const uint8_t *buf, uint16_t len)
{
    uint8_t *ptr = (uint8_t *)buf;
    uint16_t tlen;

    while(len > 0 && *ptr == 0xFE)    // 跳过前导的0xFE
    {
        ptr++;
        len--;
    }
    if(len < 12) return NULL;    // 数据长度不足

    if(ptr[HEAD] != 0x68 || ptr[HEAD2] != 0x68) return NULL;               // 检查帧头
    if(ptr[LENL] != ptr[LENL2] || ptr[LENH] != ptr[LENH2]) return NULL;    // 检查帧长度

    tlen = (ptr[LENH] << 8) | ptr[LENL];          // 获取帧长度
    if((tlen & 0x0003) != 0x0002) return NULL;    // 检查帧长度是否符合要求 ,00 禁用， 01 130， 10 本协议， 11 保留
    tlen = (tlen >> 2) & 0x3FFF;                  // 获取实际数据长度
    if(tlen + 8 > len) return NULL;               // 检查数据长度是否超过实际长度, 8 = HEAD + LENL + LENH + LENL2 + LENH2 + HEAD2 + CS + END
    if(ptr[len - 1] != 0x16) return NULL;         // 检查帧尾

    if(cal_checksum8(&ptr[CTRL], len - 8) != ptr[len - 2]) return NULL;    // 校验和验证
    if(gwd376_address_check(&ptr[ADR1L]) == ADDR_NO_MATCH) return NULL;    // 地址验证

    return ptr;    // 验证通过
}

static bool gwd376_frame_pasre(const uint8_t *buf, uint16_t len, req_obj_s *obj)
{
    uint8_t *ptr = (uint8_t *)buf;
    uint16_t tlen;

    ptr = gdw376_frame_verify(buf, len);    // 验证数据帧
    if(ptr == NULL) return false;           // 验证失败，返回false

    tlen = (ptr[LENH] << 8) | ptr[LENL];    // 获取帧长度
    tlen = (tlen >> 2) & 0x3FFF;            // 获取实际数据长度

    obj->ctrl     = *(ctrl_s *)&ptr[CTRL];    // 控制域C
    obj->addr     = gwd376_address_check(&ptr[ADR1L]);
    obj->msa      = ptr[ADR3];                // 地址域A3
    obj->afn      = (apdu_afn_t)ptr[APDU];    // 功能码
    obj->seq      = *(apdu_seq_s *)&ptr[APDU + 1];
    obj->apdu_len = tlen - (APDU - CTRL) - 2;    // 应用层数据长度 8 = HEAD + LENL + LENH + LENL2 + LENH2 + HEAD2 + AFN + SEQ
    obj->req_apdu = ptr + APDU + 2;              // 应用层数据指针
    if(obj->ctrl.dir) return false;              // 如果DIR标志位为1，表示此帧报文是由终端发出的上行报文，返回false表示不处理

    return true;    // 返回true表示处理成功
}

/// @brief 数据帧错误
/// @param chn
/// @param ack
/// @param len
/// @return
uint16_t gwd_process(uint8_t chn, uint8_t *ack, uint16_t len)
{
    rsp_obj_s *rsp = &rsp_obj;    // 获取响应对象
    req_obj_s *req = &req_obj;    // 获取请求对象

    if(gwd376_frame_pasre((const uint8_t *)ack, len, req) == false) { return 0; }    // 如果解析失败，返回0

#if 1    // 目前主站只支持PRM标志位为1的情况，强制设置PRM标志位为1，待主站修改后删除此部分代码。
    if(req->afn == AFN_VERIFY && req->ctrl.fun == RSP_FUN_ACK)
    {
        req->ctrl.prm = 0;    // 设置PRM标志位为0，表示响应内容
    }
#endif

    if(req->ctrl.prm == 1)
    {
        // 如果是PRM标志位为1，表示表示主站请求
        rsp->rsp_len = 0;
        rsp->tlen    = 0;
        // if(req->ctrl.fun == REQ_FUN_REQ_CLASS1_DATA)
        if(req->afn == AFN_SET_PARAM || req->afn == AFN_RELAY_CMD || req->afn == AFN_AUTH_KEY || req->afn == AFN_CTRL_CMD)
        {
            // 主站请求1类数据，设置类
            if(link_set(req, rsp) == false) { return 0; }    // 解析失败
            rsp->link_fun = RSP_FUN_ACK;                     // 设置响应功能码为确认
            if(req->seq.con == 0) { return 0; }              // 如果不需要确认，直接返回0
        }
        // else if(req->ctrl.fun == REQ_FUN_REQ_CLASS2_DATA)
        else if(req->afn == AFN_REQ_TASK_DATA || req->afn == AFN_REQ_CLASS1_DATA || req->afn == AFN_REQ_CLASS2_DATA || req->afn == AFN_REQ_CLASS3_DATA ||
                req->afn == AFN_REQ_TERMINAL_CFG || req->afn == AFN_QUERY_PARAM || req->afn == AFN_DATA_FORWARD)
        {
            // 主站请求2类数据，获取数据
            if(link_get(req, rsp) == false) 
            { 
                return 0; 
            }    // 解析失败
            rsp->link_fun = RSP_FUN_USER_DATA;               // 设置响应功能码为用户数据
            // if(req->seq.con == 0) { return 0; }              // 如果不需要确认，直接返回0
        }
        // else if(req->ctrl.fun == REQ_FUN_LINK_TEST) { return 0; }    // 链路测试由中断发起，暂不接受主站测试指令
        // else if(req->ctrl.fun == REQ_FUN_RESET)
        else if(req->afn == AFN_RESET)
        {
            if(link_reset(req, rsp) == false) { return 0; }    // 解析失败
            rsp->link_fun = RSP_FUN_ACK;                       // 设置响应功能码为确认
            if(req->seq.con == 0) { return 0; }                // 如果不需要确认，直接返回0
        }
        else
        {
            return 0;    // 未知的功能码
        }

        if(rsp->tlen == 0) { return 0; }    // 如果响应长度为0，表示不需要应答
        return gwd376_frame_creat(rsp->link_fun, LINK_SLAVE_STATION, 0, req->msa, req->com_buf, rsp->rsp_buf, rsp->tlen);
    }
    else
    {
        // 主站响应内容，无需ACK
        if(req->apdu_len < 4) return 0;
        DADT_to_fnpn(req->req_apdu, &req->pn, &req->fn);
        req->apdu_len -= 4;    // 减去DADT长度
        req->req_apdu += 4;    // 移动指针到数据域

        if(req->pn > 2040) { return 0; }    // pn必须为0
        if(req->fn > 248) { return 0; }     // fn必须为1-248

        switch(req->ctrl.fun)
        {
            case RSP_FUN_ACK:                        //  确认
                return rsp_fun_ack_pro(req, rsp);    // 处理确认/否认
            case RSP_FUN_USER_DATA:                  // 用户数据
            case RSP_FUN_NO_DATA:                    // 无数据
                return 0;
            case RSP_FUN_LINK_STATUS:                // 响应帧 链路状态
                return rsp_fun_ack_pro(req, rsp);    // 处理确认/否认
        }
        return 0;
    }
}

void gdw376_init(uint8_t chn, uint8_t *buff)
{
    memset(&req_obj, 0, sizeof(req_obj_s));
    memset(&rsp_obj, 0, sizeof(rsp_obj_s));
    memset(rsp_apdu_buff, 0, sizeof(rsp_apdu_buff));

    rsp_obj.rsp_buf = rsp_apdu_buff;    // 设置响应应用层数据缓冲
    req_obj.com_buf = buff;             // 设置通信缓冲区
    req_obj.ack     = false;            // 默认不需要应答
}

uint16_t gdw376_get_lastgasp_frame(uint8_t *buf)
{
    event_report_create(erc_14_pwr_on_off, 1, false, buf);    // 获取最后气报文
    return (uint16_t)(0);                                     // 返回数据域长度
}

const struct gdw376_s gdw376 = {
    .init                = gdw376_init,
    .msg_process         = gwd_process,
    .get_linktest_frame  = gwd376_link_test_frame,
    .get_lastgasp_frame  = gdw376_get_lastgasp_frame,
    .transparent_forward = transparent_forward_assemble,
};
