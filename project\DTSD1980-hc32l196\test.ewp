<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>4</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ExePath</name>
                    <state>test\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>test\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>test\List</state>
                </option>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>test\BrowseInfo</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>A complete configuration of the C/C++14 runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>9.30.1.50052</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>9.40.1.63870</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Full.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>33</version>
                    <state>35</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>33</version>
                    <state>35</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>33</version>
                    <state>35</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>test\</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>33</version>
                    <state>35</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>HC32L196</state>
                    <state>DEBUG_PRINT</state>
                    <state>POLYPHASE_METER</state>
                    <state>METER_CURRENT=50600</state>
                    <state>HW_RELAY_INSIDE=0</state>
                    <state>HW_GPRS_4G_ENABLE=1</state>
                    <state>HW_BLE_ENABLE=0</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state>Pa082,Og014,pe177,Pa089</state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..</state>
                    <state>$PROJ_DIR$\..\..\source</state>
                    <state>$PROJ_DIR$\..\..\source\boot</state>
                    <state>$PROJ_DIR$\..\..\source\APP</state>
                    <state>$PROJ_DIR$\..\..\source\APP\config</state>
                    <state>$PROJ_DIR$\..\..\source\APP\datastore</state>
                    <state>$PROJ_DIR$\..\..\source\APP\interface</state>
                    <state>$PROJ_DIR$\..\..\source\APP\interface\BLE</state>
                    <state>$PROJ_DIR$\..\..\source\APP\interface\GPRS_4G</state>
                    <state>$PROJ_DIR$\..\..\source\APP\meter</state>
                    <state>$PROJ_DIR$\..\..\source\APP\protocol</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\CMSIS\Core\Include</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\rtt</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\Startup</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\common</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\inc</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\EWARM</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\EWARM\config\flashloader</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\EWARM\config</state>
                    <state>$PROJ_DIR$\..\..\source\toolkit</state>
                    <state>$PROJ_DIR$\..\..\source\toolkit\utils</state>
                    <state>$PROJ_DIR$\..\..\source\toolkit\pt-1.4</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\ext_lcd_driver</state>
                    <state>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\lcd_type</state>
                    <state>$PROJ_DIR$\..\..\source\APP\protocol\DLT645_2007</state>
                    <state>$PROJ_DIR$\..\..\source\APP\protocol\DLT698_2017</state>
                    <state>$PROJ_DIR$\..\..\source\APP\DCU\GPRS_4G</state>
                    <state>$PROJ_DIR$\..\..\source\APP\DCU</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>HT6x2x</state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>test.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>1</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>test.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\test.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state>lt009</state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0000</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x3FFFF</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>4</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>app_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>Driver</name>
        <group>
            <name>BSP</name>
            <group>
                <name>rtt</name>
                <excluded>
                    <configuration>Debug</configuration>
                </excluded>
                <file>
                    <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\rtt\SEGGER_RTT.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\rtt\SEGGER_RTT.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\rtt\SEGGER_RTT_Conf.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\rtt\SEGGER_RTT_printf.c</name>
                </file>
            </group>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\bsp_lcd.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\eeprom.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\BSP\ext_flash.c</name>
            </file>
        </group>
        <group>
            <name>HAL</name>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_adc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_adc.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_def.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_flash.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_flash.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_gpio.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_gpio.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_mcu.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_mcu.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_rtc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_rtc.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_spi.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_spi.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_spi_sw0.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_spi_sw1.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_timer.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_timer.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_uart.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\HAL\hal_uart.h</name>
            </file>
        </group>
        <group>
            <name>MCU</name>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\adt.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\aes.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\bgr.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\bt.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\dac.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\ddl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\dmac.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\flash.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\gpio.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\hc32l19x_adc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\hc32l19x_crc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\hc32l19x_debug.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\hc32l19x_lcd.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\i2c.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\lpm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\lptim.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\lpuart.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\lvd.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\opa.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\pca.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\pcnt.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\ram.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\reset.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\rtc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\spi.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\sysctrl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\timer3.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\trim.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\trng.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\uart.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\vc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\driver\src\wdt.c</name>
            </file>
        </group>
        <group>
            <name>Startup</name>
            <file>
                <name>$PROJ_DIR$\..\..\source\Dirver\hc32l196-dtsd1980\MCU\HC32L19x_DDL_Rev1.3.0\common\system_hc32l19x.c</name>
            </file>
        </group>
    </group>
    <group>
        <name>Tool</name>
        <file>
            <name>$PROJ_DIR$\..\..\source\toolkit\utils\bcd.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\source\toolkit\utils\crc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\source\toolkit\utils\crc16.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\source\toolkit\utils\crc32.c</name>
            <excluded>
                <configuration>Debug</configuration>
            </excluded>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\source\toolkit\utils\utils.c</name>
        </file>
    </group>
    <file>
        <name>$PROJ_DIR$\..\..\source\APP\debug.c</name>
    </file>
    <file>
        <name>$PROJ_DIR$\..\..\source\test\test.c</name>
    </file>
</project>
