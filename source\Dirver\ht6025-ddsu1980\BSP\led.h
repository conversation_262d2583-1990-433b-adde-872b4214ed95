/**
 ******************************************************************************
* @file    relay.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __LED_H
#define __LED_H

#include "..\bsp_cfg.h"

/* led控制频率类型定义 */
typedef enum 
{
	FREQ_OFF,
	FREQ_0_5HZ,
    FREQ_1HZ,        
	FREQ_2HZ,
	FREQ_ON,
}LedFreq_t;

typedef enum
{
#if USE_LED_RELAY
    LED_ALARM,
#endif
#if USE_LED_BACK
    LED_BLACKLIGHT,
#endif
    LED_NUM,
}LedType_t;

///@物理灯类型定义，高4位为物理灯类型定义，低4位预留
#define RELAY_LED       0x01        // 报警灯
#define BLACKIT_LED     0x11        // LCD背光灯

struct led_s
{ 
/**
  * @brief  Init and led.
  * @param  None
  * @retval None
  */
    void (*init)(void);

/**
  * @brief Control the LED.
  * @param type-灯的type, 
  *        freq-灯的闪烁频率
  * @retval None
  */
    void (*ctrl)(uint8_t type, LedFreq_t freq);
};
extern const struct led_s led;


#endif /* __LED_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

