/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      factory.c
 *    Describe:
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#include "typedef.h"
#include "factory.h"
#include "mic.h"

#include <string.h>

/// @brief 提取校表参数
/// @param mfc_data_std
/// @param buff
/// @return
static uint8_t dlt_645_factory_cali_para(mfc_cal_data_s *mfc_data_std, uint8_t *buff)
{
    uint8_t *p_data = buff;

    memcpy(&mfc_data_std->cmd, p_data, sizeof(mfc_cal_data_s));
    // // 指令
    // memcpy(&MfcData_Std->cmd, p_data, 1), p_data += 1;
    // // 脉冲常数倍数
    // memcpy(&MfcData_Std->const_mul, p_data, 1), p_data += 1;
    // // A,B,C相电压
    // memcpy(&MfcData_Std->CalData.Urms[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.Urms[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.Urms[2], p_data, 4), p_data += 4;
    // // A,B,C,N相电流
    // memcpy(&MfcData_Std->CalData.Irms[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.Irms[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.Irms[2], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.Irms[3], p_data, 4), p_data += 4;
    // // A,B,C相有功功率
    // memcpy(&MfcData_Std->CalData.power_p[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_p[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_p[2], p_data, 4), p_data += 4;
    // // A,B,C相无功功率
    // memcpy(&MfcData_Std->CalData.power_q[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_q[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_q[2], p_data, 4), p_data += 4;
    // // A,B,C相视在功率
    // memcpy(&MfcData_Std->CalData.power_s[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_s[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.power_s[2], p_data, 4), p_data += 4;
    // // A,B,C相计量误差
    // memcpy(&MfcData_Std->CalData.err[0], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.err[1], p_data, 4), p_data += 4;
    // memcpy(&MfcData_Std->CalData.err[2], p_data, 4), p_data += 4;

    return TRUE;
}

/// @brief 全部校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx1(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    CalibrateData_s Data_Samp;

    /// 三相电压、电流、功率校正
    for(uint8_t i; i < 3; i++)
    {
        if((Mfc_param >> i) & 0x01)
        {
            // 开始校表
            mic.cali(i, STEP_INIT, NULL, NULL);
            mic.cali(i, STEP_REFRESH, NULL, NULL);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 计量采样
            mic.cali(i, STEP_VOL, &MfcData_Std->CalData, &Data_Samp);       // 电压校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);
            mic.cali(i, STEP_CUR, &MfcData_Std->CalData, &Data_Samp);       // 电流校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);
            mic.cali(0, STEP_REFRESH, NULL, NULL);                          // 刷新
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 重新采样
            mic.cali(i, STEP_POWER, &MfcData_Std->CalData, &Data_Samp);     // 功率校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);      // 保存
            // 单点校正法 衍生方案一 等待1.2s
            hal_timer.msdly(1200);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 重新采样
            mic.cali(i, STEP_PHASE, &MfcData_Std->CalData, &Data_Samp);     // 校正相角
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);      // 保存
        }
    }
    mic.cali(0, STEP_REFRESH, NULL, NULL);
    return 0;
}

/// @brief 电压，电流校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx2(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    uint8_t ret = 0;
    CalibrateData_s Data_Samp;

    /// 三相电压、电流校正
    for(uint8_t i; i < 3; i++)
    {
        if((Mfc_param >> i) & 0x01)
        {
            // 开始校表
            mic.cali(i, STEP_INIT, NULL, NULL);
            mic.cali(i, STEP_REFRESH, NULL, NULL);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 计量采样
            mic.cali(i, STEP_VOL, &MfcData_Std->CalData, &Data_Samp);       // 电压校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);
            mic.cali(i, STEP_CUR, &MfcData_Std->CalData, &Data_Samp);       // 电流校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);
        }
    }
    mic.cali(0, STEP_REFRESH, NULL, NULL);
    return ret;
}

/// @brief 功率增益校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx3(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    uint8_t ret = 0;
    CalibrateData_s Data_Samp;

    /// 功率校正
    for(uint8_t i; i < 3; i++)
    {
        if((Mfc_param >> i) & 0x01)
        {
            // 开始校表
            mic.cali(i, STEP_INIT, NULL, NULL);
            mic.cali(i, STEP_REFRESH, NULL, NULL);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 计量采样
            mic.cali(i, STEP_POWER, &MfcData_Std->CalData, &Data_Samp);     // 功率校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);      // 保存
            // 相位校准
            mic.cali(i, STEP_REFRESH, NULL, NULL);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);    // 计量采样
            mic.cali(i, STEP_POWER, &MfcData_Std->CalData, &Data_Samp);     // 功率校准
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);      // 保存
        }
    }
    mic.cali(0, STEP_REFRESH, NULL, NULL);
    return ret;
}

/// @brief 相位校准1
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx4(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 相位校准2
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx5(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 相位校准3
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx6(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 功率偏移校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx7(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    uint8_t ret = 0;
    CalibrateData_s Data_Samp;

    /// 三相功率偏移校正
    for(uint8_t i; i < 3; i++)
    {
        if((Mfc_param >> i) & 0x01)
        {
            // 开始校表
            mic.cali(i, STEP_INIT, NULL, NULL);
            mic.cali(i, STEP_REFRESH, NULL, NULL);
            mic.cali(i, STEP_SAMPLE, &MfcData_Std->CalData, &Data_Samp);
            ret = mic.cali(i, STEP_POFST, &MfcData_Std->CalData, &Data_Samp);
            mic.cali(i, STEP_SAVE, &MfcData_Std->CalData, &Data_Samp);
        }
    }
    mic.cali(0, STEP_REFRESH, NULL, NULL);
    return ret;
}

/// @brief 电流偏移校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx8(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 小信号校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_Dx9(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 其它点校准
/// @param p_info
/// @param data
/// @return
static uint8_t dlt_645_factory_DxA(uint8_t Mfc_param, mfc_cal_data_s *MfcData_Std)
{
    return 0;
}

/// @brief 工厂模式校表命令处理
/// @param p_info
/// @param MfcData_Std
/// @return
static uint8_t dlt_645_factory_mfc_cali(DLT645_2007_MSG_S *p_info, mfc_cal_data_s *MfcData_Std)
{
    uint8_t ret = 0;
    uint8_t mfc_param = 0x07;

    /// 确认校准相
    switch(MfcData_Std->cmd & 0xF0)
    {
        case(MFCCMD_DO1 & 0xF0):
            mfc_param = 0x07;    // A、B、C三相
            break;
        case(MFCCMD_D11 & 0xF0):
            mfc_param = 0x01;    // A
            break;
        case(MFCCMD_D21 & 0xF0):
            mfc_param = 0x02;    // B
            break;
        case(MFCCMD_D31 & 0xF0):
            mfc_param = 0x04;    // C
            break;
        default:
            mfc_param = 0x07;    // 默认A、B、C三相
            break;
    }

    /// 确认校准参数
    switch(MfcData_Std->cmd & 0x0F)
    {
        case(MFCCMD_DO1 & 0x0F):
            ret =dlt_645_factory_Dx1(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO2 & 0x0F):
            ret =dlt_645_factory_Dx2(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO3 & 0x0F):
            ret =dlt_645_factory_Dx3(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO4 & 0x0F):
            ret =dlt_645_factory_Dx4(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO5 & 0x0F):
            ret =dlt_645_factory_Dx5(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO6 & 0x0F):
            ret =dlt_645_factory_Dx6(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO7 & 0x0F):
            ret =dlt_645_factory_Dx7(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO8 & 0x0F):
            ret =dlt_645_factory_Dx8(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DO9 & 0x0F):
            ret =dlt_645_factory_Dx9(mfc_param, MfcData_Std);
            break;
        case(MFCCMD_DOA & 0x0F):
            ret =dlt_645_factory_DxA(mfc_param, MfcData_Std);
            break;
        default:

            break;
    }

    return ret;
}

/// end of file
