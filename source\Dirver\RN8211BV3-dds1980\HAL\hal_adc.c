/**
 ******************************************************************************
* @file    hal_adc.c
* <AUTHOR> @version V1.0.0
* @date    2024
* @brief   本模块完成对MCU的AD采样驱动(单次单通道).
* @note    单次采样时间: 采样时间 + 转换时间;
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_adc.h"
#include "hal_mcu.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */
/**
 * @brief  多通道采样初始化
 * @param  None
 * @retval None
 */
void hal_adc_open(void)
{
    // SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralAdcBgr, TRUE);   // 使能ADC&BGR外设时钟

    // M0P_BGR->CR |= 0x1u;                               // 开启BGR
    // delay10us(2);

    // M0P_ADC->CR0 = 0x1u;                               // ADC 使能
    // delay10us(2);
    // M0P_ADC->CR0 |= (uint32_t)AdcMskClkDiv1       |    // 采样分频-1
    //                 (uint32_t)AdcMskRefVolSelAVDD |    // 参考电压选择-VCC
    //                 (uint32_t)AdcMskBufEnable     |    // OP BUF配置-关
    //                 (uint32_t)AdcMskSampCycle8Clk |    // 采样周期数-8
    //                 (uint32_t)AdcMskInRefDisable;      // 内部参考电压使能-关
    // M0P_ADC->CR1_f.MODE  = AdcSglMode;                 // 采样模式-扫描
    // M0P_ADC->CR1_f.ALIGN = AdcAlignRight;              // 转换结果对齐方式-右
}

/**
 * @brief  Init and Open the Adc(No Power), 单通道单次采样
 * @param  None
 * @retval None
 */
void hal_adc_open_nopower(void)
{
    // SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralAdcBgr, TRUE);   // 开启AdcBgr时钟
    // M0P_BGR->CR |= 0x1u;                                                              // 开启BGR
    // delay10us(2);
}

/**
 * @brief  Close the Adc
 * @param  None
 * @retval None
 */
void hal_adc_close(void)
{
    // SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralAdcBgr, FALSE);   // 关闭AdcBgr时钟
    // M0P_BGR->CR &= 0x2u;                                                               // 关闭BGR
}

/**
 * @brief  Close the Adc(No Power)
 * @param  None
 * @retval None
 */
void hal_adc_close_nopower(void)
{
    // SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralAdcBgr, FALSE);   // 关闭AdcBgr时钟
    // M0P_BGR->CR &= 0x2u;                                                               // 关闭BGR
}

/**
 * @brief  Start Adc Conversion
 * @param  chn-ADC channel
 * @retval None
 */
void hal_adc_start_conversion(HAL_ADC_CHN_TYPE chn)
{
    // M0P_ADC->CR0_f.SGLMUX = chn;    // 选择通道
    // M0P_ADC->ICR_f.SGLIC  = 0;      // 清除单次转换完成标志
    // M0P_ADC->SGLSTART     = 1u;     // 单次转换开始转换
}

/// @brief Get the ADC sample value
/// @param chn-ADC channel
/// @return
int32_t hal_adc_result_get(HAL_ADC_CHN_TYPE chn)
{
    // return M0P_ADC->RESULT;    // 获取采样值
   return 0;
}

/// @brief Get the current channel voltage value
/// @param chn-ADC channel
/// @return 电压值单位V
float hal_adc_voltage_get(HAL_ADC_CHN_TYPE chn)
{
    volatile float vol;
    vol = (float)hal_adc_result_get(chn);
    vol = vol * 0.0258 + 4.7559;
    return (vol / 1000);
}

/// @brief Get the current temperature value
/// @param
/// @return
float hal_adc_temperature_get(void)
{
    float temp;
    temp = 12.9852 - (hal_adc_result_get(HAL_ADC_TEMP)) * 0.002828;
    return temp;
}

/// @brief 声明hal_adc子模块对象
const struct hal_adc_t hal_adc = {
    .open          = hal_adc_open,
    .open_nopower  = hal_adc_open_nopower,
    .close         = hal_adc_close,
    .close_nopower = hal_adc_close_nopower,
    .start         = hal_adc_start_conversion,
    .result        = hal_adc_result_get,
    .voltage       = hal_adc_voltage_get,
    .temperature   = hal_adc_temperature_get,
};

/** @} */
