/**
  ******************************************************************************
  * @file    hal_spi.c
  * <AUTHOR> @date    2024
  * @brief   本模块完成MASTER SPI总线的硬件驱动(MCU 硬件SPI).
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  * ht6025 SPI 配置顺序：
  * 1、 将 spi 相关引脚配置为 gpio 功能；
  * 2、 初始化 spi 模块配置，比如模式，波特率等，推荐最后打开 SPI_EN；
  * 3、 将 spi 相关引脚配置为 spi 复用功能 ；
  * 4、 多从机应用中若要修改 spi 模块的配置，比如模式，波特率等，先关闭 SPI_EN,然后修改 spi 模块配
       置后再打开 SPI_EN.
  ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_spi.h"
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define SPI_HW0_CLOSE 0
#define HW_SPI0 SPI_HW0_CLOSE
#define SPI0_CLK SPI_HW0_CLOSE
#define SPI0_RST SPI_HW0_CLOSE

#define HW_SPI1 SPI_HW0_CLOSE
#define SPI1_CLK SPI_HW0_CLOSE
#define SPI1_RST SPI_HW0_CLOSE

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/// @brief 读一个字符
__INLINE uint8_t mcu_spi_char_read(void)
{
    uint8_t  ch = 0;
    uint16_t time_out;


    return ch;
}

/// @brief 写一个字符到发送FIFO
/// @param ch
/// @return
__INLINE void mcu_spi_char_write(uint8_t ch)
{

}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  SPI0总线向从设备发送一个字符并收回一个字符.
 * @param  [in]  ch-发送字符
 * @retval 回收字符
 */
char hal_spi0_trans(uint8_t ch, uint8_t mod)
{
    uint16_t time_out = 10000;


    return ch;
}

/** @brief Open SPI0总线
 * @param  [in]  kbps-总线速率值, 单位3000 KBPS
 * @retval None
 */
void hal_spi0_open(uint16_t kbps)
{

}

/* @brief Close SPI0总线 */
void hal_spi0_close(void)
{

}

void hal_spi0_deviceon(void)
{

}

void hal_spi0_deviceoff(void)
{

}

/**
 * @brief  SPI1总线向从设备发送一个字符并收回一个字符.
 * @param  [in]  ch-发送字符
 * @retval 回收字符
 */
char hal_spi1_trans(uint8_t ch, uint8_t mod)
{
    uint16_t time_out = 10000;

    
    return ch;
}

/** @brief Open SPI1总线
 * @param  [in]  kbps-总线速率值, 单位3000 KBPS
 * @retval None
 */
void hal_spi1_open(uint16_t kbps)
{

}

/* @brief Close SPI1总线 */
void hal_spi1_close(void)
{

}

void hal_spi1_deviceon(void)
{

}

void hal_spi1_deviceoff(void)
{

}

/** @} */
/** @} */
/** @} */
