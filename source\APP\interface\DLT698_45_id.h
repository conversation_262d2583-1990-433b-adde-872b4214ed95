/**
 ******************************************************************************
* @file    id_table.h
* <AUTHOR> @date    2024
* @brief   698 OAD table header file. 根据DLT 698.45标准定义的OAD表头文件。
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __OAD_TABLE_H__
#define __OAD_TABLE_H__

/// @ ID 解析：0xcc, 0xaaaannmm
/// cc为698定义的类，aaaannmm没有特殊说明的情况下，表示为698 OAD
/// aaaa 为对象标识OI，比如合相组合有功 0000 
/// nn   为属性(get_request-5)
/// mm   为索引号（0

// OAD∷=SEQUENCE
// {
//  对象标识 OI，
//  属性标识及其特征 unsigned，
//  属性内元素索引 unsigned（1…255）
// }
// 对象标识——见 8.3 。
// 属性标识及其特征，其中：
// 1) bit0…bit4 编码表示对象属性编号，取值 0…31，其中 0 表
// 示整个对象属性，即对象的所有属性，见 8.2.1
// 2) bit5…bit7 编码表示属性特征，属性特征是对象同一个属性
// 在不同快照环境下取值模式，取值 0…7，特征含义在具体类
// 属性中描述。
// 属性内元素索引——00H 表示整个属性全部内容。如果属性是结构
// 或数组，01H 指向对象属性的第一个元素；如果属性是记录型的存
// 储区，非 0 值 n 表示最近第 n 次的记录。


#define ID_INDEX(n)                    ((uint32_t)n) 

/// @ 类1 class id = 1  电能量类,本接口类定义了电能量数据信息
/// 对于类1，索引号为0，表示总，>1 表示费率，比如费率1表示费率1，费率2表示费率2，以此类推。
/// 属性2，2位小数电能量值，单位为kWh，精度0.01kWh
#define C1_OAD_COM_ACT(n)                           {0x01, (0x00000200 | ID_INDEX(n))}          // 组合有功电能 电能量∷=double-long；单位：kWh，换算：-2
#define C1_OAD_POS_ACT(n)                           {0x01, (0x00100200 | ID_INDEX(n))}          // 正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_A_POS_ACT(n)                         {0x01, (0x00110200 | ID_INDEX(n))}          // A 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_B_POS_ACT(n)                         {0x01, (0x00120200 | ID_INDEX(n))}          // B 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_C_POS_ACT(n)                         {0x01, (0x00130200 | ID_INDEX(n))}          // C 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_NEG_ACT(n)                           {0x01, (0x00200200 | ID_INDEX(n))}          // 反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_A_NEG_ACT(n)                         {0x01, (0x00210200 | ID_INDEX(n))}          // A 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_B_NEG_ACT(n)                         {0x01, (0x00220200 | ID_INDEX(n))}          // B 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_C_NEG_ACT(n)                         {0x01, (0x00230200 | ID_INDEX(n))}          // C 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_COM1_REA(n)                          {0x01, (0x00300200 | ID_INDEX(n))}          // 组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_A_COM1_REA(n)                        {0x01, (0x00310200 | ID_INDEX(n))}          // A 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_B_COM1_REA(n)                        {0x01, (0x00320200 | ID_INDEX(n))}          // B 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_C_COM1_REA(n)                        {0x01, (0x00330200 | ID_INDEX(n))}          // C 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_COM2_REA(n)                          {0x01, (0x00400200 | ID_INDEX(n))}          // 组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_A_COM2_REA(n)                        {0x01, (0x00410200 | ID_INDEX(n))}          // A 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_B_COM2_REA(n)                        {0x01, (0x00420200 | ID_INDEX(n))}          // B 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_C_COM2_REA(n)                        {0x01, (0x00430200 | ID_INDEX(n))}          // C 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-2
#define C1_OAD_Q1_REA(n)                            {0x01, (0x00500200 | ID_INDEX(n))}          // 第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_A_Q1_REA(n)                          {0x01, (0x00510200 | ID_INDEX(n))}          // A 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_B_Q1_REA(n)                          {0x01, (0x00520200 | ID_INDEX(n))}          // B 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_C_Q1_REA(n)                          {0x01, (0x00530200 | ID_INDEX(n))}          // C 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_Q2_REA(n)                            {0x01, (0x00600200 | ID_INDEX(n))}          // 第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_A_Q2_REA(n)                          {0x01, (0x00610200 | ID_INDEX(n))}          // A 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_B_Q2_REA(n)                          {0x01, (0x00620200 | ID_INDEX(n))}          // B 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_C_Q2_REA(n)                          {0x01, (0x00630200 | ID_INDEX(n))}          // C 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_Q3_REA(n)                            {0x01, (0x00700200 | ID_INDEX(n))}          // 第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_A_Q3_REA(n)                          {0x01, (0x00710200 | ID_INDEX(n))}          // A 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_B_Q3_REA(n)                          {0x01, (0x00720200 | ID_INDEX(n))}          // B 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_C_Q3_REA(n)                          {0x01, (0x00730200 | ID_INDEX(n))}          // C 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_Q4_REA(n)                            {0x01, (0x00800200 | ID_INDEX(n))}          // 第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_A_Q4_REA(n)                          {0x01, (0x00810200 | ID_INDEX(n))}          // A 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_B_Q4_REA(n)                          {0x01, (0x00820200 | ID_INDEX(n))}          // B 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_C_Q4_REA(n)                          {0x01, (0x00830200 | ID_INDEX(n))}          // C 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-2
#define C1_OAD_POS_APP(n)                           {0x01, (0x00900200 | ID_INDEX(n))}          // 正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_A_POS_APP(n)                         {0x01, (0x00910200 | ID_INDEX(n))}          // A 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_B_POS_APP(n)                         {0x01, (0x00920200 | ID_INDEX(n))}          // B 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_C_NEG_APP(n)                         {0x01, (0x00930200 | ID_INDEX(n))}          // C 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_A_NEG_APP(n)                         {0x01, (0x00A10200 | ID_INDEX(n))}          // A 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_B_NEG_APP(n)                         {0x01, (0x00A20200 | ID_INDEX(n))}          // B 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_C_NEG_APP(n)                         {0x01, (0x00A30200 | ID_INDEX(n))}          // C 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-2
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01100200 | ID_INDEX(n))}          // 正向有功基波总电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01110200 | ID_INDEX(n))}          // A 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01120200 | ID_INDEX(n))}          // B 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01130200 | ID_INDEX(n))}          // C 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01200200 | ID_INDEX(n))}          // 反向有功基波总电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01210200 | ID_INDEX(n))}          // A 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01220200 | ID_INDEX(n))}          // B 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01230200 | ID_INDEX(n))}          // C 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-2
/// 属性4，扩展精度电能，4位小数，单位为kWh，精度0.0001kWh
#define C1_OAD_COM_ACT(n)                           {0x01, (0x00000400 | ID_INDEX(n))}          // 组合有功电能 电能量∷=double-long；单位：kWh，换算：-4
#define C1_OAD_POS_ACT(n)                           {0x01, (0x00100400 | ID_INDEX(n))}          // 正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_A_POS_ACT(n)                         {0x01, (0x00110400 | ID_INDEX(n))}          // A 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_B_POS_ACT(n)                         {0x01, (0x00120400 | ID_INDEX(n))}          // B 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_C_POS_ACT(n)                         {0x01, (0x00130400 | ID_INDEX(n))}          // C 相正向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_NEG_ACT(n)                           {0x01, (0x00200400 | ID_INDEX(n))}          // 反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_A_NEG_ACT(n)                         {0x01, (0x00210400 | ID_INDEX(n))}          // A 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_B_NEG_ACT(n)                         {0x01, (0x00220400 | ID_INDEX(n))}          // B 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_C_NEG_ACT(n)                         {0x01, (0x00230400 | ID_INDEX(n))}          // C 相反向有功电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_COM1_REA(n)                          {0x01, (0x00300400 | ID_INDEX(n))}          // 组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_A_COM1_REA(n)                        {0x01, (0x00310400 | ID_INDEX(n))}          // A 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_B_COM1_REA(n)                        {0x01, (0x00320400 | ID_INDEX(n))}          // B 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_C_COM1_REA(n)                        {0x01, (0x00330400 | ID_INDEX(n))}          // C 相组合无功 1 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_COM2_REA(n)                          {0x01, (0x00400400 | ID_INDEX(n))}          // 组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_A_COM2_REA(n)                        {0x01, (0x00410400 | ID_INDEX(n))}          // A 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_B_COM2_REA(n)                        {0x01, (0x00420400 | ID_INDEX(n))}          // B 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_C_COM2_REA(n)                        {0x01, (0x00430400 | ID_INDEX(n))}          // C 相组合无功 2 电能 电能量∷=double-long；单位：kvarh，换算：-4
#define C1_OAD_Q1_REA(n)                            {0x01, (0x00500400 | ID_INDEX(n))}          // 第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_A_Q1_REA(n)                          {0x01, (0x00510400 | ID_INDEX(n))}          // A 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_B_Q1_REA(n)                          {0x01, (0x00520400 | ID_INDEX(n))}          // B 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_C_Q1_REA(n)                          {0x01, (0x00530400 | ID_INDEX(n))}          // C 相第一象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_Q2_REA(n)                            {0x01, (0x00600400 | ID_INDEX(n))}          // 第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_A_Q2_REA(n)                          {0x01, (0x00610400 | ID_INDEX(n))}          // A 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_B_Q2_REA(n)                          {0x01, (0x00620400 | ID_INDEX(n))}          // B 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_C_Q2_REA(n)                          {0x01, (0x00630400 | ID_INDEX(n))}          // C 相第二象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_Q3_REA(n)                            {0x01, (0x00700400 | ID_INDEX(n))}          // 第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_A_Q3_REA(n)                          {0x01, (0x00710400 | ID_INDEX(n))}          // A 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_B_Q3_REA(n)                          {0x01, (0x00720400 | ID_INDEX(n))}          // B 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_C_Q3_REA(n)                          {0x01, (0x00730400 | ID_INDEX(n))}          // C 相第三象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_Q4_REA(n)                            {0x01, (0x00800400 | ID_INDEX(n))}          // 第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_A_Q4_REA(n)                          {0x01, (0x00810400 | ID_INDEX(n))}          // A 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_B_Q4_REA(n)                          {0x01, (0x00820400 | ID_INDEX(n))}          // B 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_C_Q4_REA(n)                          {0x01, (0x00830400 | ID_INDEX(n))}          // C 相第四象限无功电能 电能量∷=double-long-unsigned；单位：kvarh，换算：-4
#define C1_OAD_POS_APP(n)                           {0x01, (0x00900400 | ID_INDEX(n))}          // 正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_A_POS_APP(n)                         {0x01, (0x00910400 | ID_INDEX(n))}          // A 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_B_POS_APP(n)                         {0x01, (0x00920400 | ID_INDEX(n))}          // B 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_C_NEG_APP(n)                         {0x01, (0x00930400 | ID_INDEX(n))}          // C 相正向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_A_NEG_APP(n)                         {0x01, (0x00A10400 | ID_INDEX(n))}          // A 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_B_NEG_APP(n)                         {0x01, (0x00A20400 | ID_INDEX(n))}          // B 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_C_NEG_APP(n)                         {0x01, (0x00A30400 | ID_INDEX(n))}          // C 相反向视在电能 电能量∷=double-long-unsigned；单位：kVAh，换算：-4
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01100400 | ID_INDEX(n))}          // 正向有功基波总电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01110400 | ID_INDEX(n))}          // A 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01120400 | ID_INDEX(n))}          // B 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_POS_FUND_ACT(n)                      {0x01, (0x01130400 | ID_INDEX(n))}          // C 相正向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01200400 | ID_INDEX(n))}          // 反向有功基波总电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01210400 | ID_INDEX(n))}          // A 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01220400 | ID_INDEX(n))}          // B 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4
#define C1_OAD_NEG_FUND_ACT(n)                      {0x01, (0x01230400 | ID_INDEX(n))}          // C 相反向有功基波电能 电能量∷=double-long-unsigned；单位：kWh，换算：-4


/// @ 类2 class id = 2  最大需量类,本接口类定义了最大需量数据信息
///  类后面非698 OAD！！！！！！！！！！！！！！！！ 
/// 类2 ID ccnnnmmt  nn 代表类，nnn代表需量类型 mm 代表上MM月，0为当前周期。 t 代表费率 0为总，1为费率1，2为费率2，3为费率3，以此类推。
#define MON_TARIFF(m,n) ((uint32_t)(m << 4) | (uint32_t)(n & 0x0F))   // 月度费率 0x82000000 + (m<<16) + (n<<8)
#define C2_MMD_POS_KW(m,n)                          {0x02, (0x10100000 | MON_TARIFF(m,n))}      // 正向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_A_POS_KW(m,n)                        {0x02, (0x10110000 | MON_TARIFF(m,n))}      // A 相正向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_B_POS_KW(m,n)                        {0x02, (0x10120000 | MON_TARIFF(m,n))}      // B 相正向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_C_POS_KW(m,n)                        {0x02, (0x10130000 | MON_TARIFF(m,n))}      // C 相正向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_NEG_KW(m,n)                          {0x02, (0x10200000 | MON_TARIFF(m,n))}      // 反向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_A_NEG_KW(m,n)                        {0x02, (0x10210000 | MON_TARIFF(m,n))}      // A 相反向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_B_NEG_KW(m,n)                        {0x02, (0x10220000 | MON_TARIFF(m,n))}      // B 相反向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_C_NEG_KW(m,n)                        {0x02, (0x10230000 | MON_TARIFF(m,n))}      // C 相反向有功最大需量 最大需量值∷=double-long-unsigned 单位：kW，换算：-4
#define C2_MMD_CMB1_KVAR(m,n)                       {0x02, (0x10300000 | MON_TARIFF(m,n))}      // 组合无功 1 最大需量 最大需量值∷=double-long           单位：kvar，换算：-4
#define C2_MMD_A_CMB1_KVAR(m,n)                     {0x02, (0x10310000 | MON_TARIFF(m,n))}      // A 相组合无功 1 最大需量 最大需量值∷=double-long       单位：kvar，换算：-4
#define C2_MMD_B_CMB1_KVAR(m,n)                     {0x02, (0x10320000 | MON_TARIFF(m,n))}      // B 相组合无功 1 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_C_CMB1_KVAR(m,n)                     {0x02, (0x10330000 | MON_TARIFF(m,n))}      // C 相组合无功 1 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_CMB2_KVAR(m,n)                       {0x02, (0x10400000 | MON_TARIFF(m,n))}      // 组合无功 2 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_A_CMB2_KVAR(m,n)                     {0x02, (0x10410000 | MON_TARIFF(m,n))}      // A 相组合无功 2 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_B_CMB2_KVAR(m,n)                     {0x02, (0x10420000 | MON_TARIFF(m,n))}      // B 相组合无功 2 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_C_CMB2_KVAR(m,n)                     {0x02, (0x10430000 | MON_TARIFF(m,n))}      // C 相组合无功 2 最大需量 最大需量值∷=double-long 单位：kvar，换算：-4
#define C2_MMD_Q1_KVAR(m,n)                         {0x02, (0x10500000 | MON_TARIFF(m,n))}      // 第一象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_A_Q1_KVAR(m,n)                       {0x02, (0x10510000 | MON_TARIFF(m,n))}      // A 相第一象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_B_Q1_KVAR(m,n)                       {0x02, (0x10520000 | MON_TARIFF(m,n))}      // B 相第一象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_C_Q1_KVAR(m,n)                       {0x02, (0x10530000 | MON_TARIFF(m,n))}      // C 相第一象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_Q2_KVAR(m,n)                         {0x02, (0x10600000 | MON_TARIFF(m,n))}      // 第二象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_A_Q2_KVAR(m,n)                       {0x02, (0x10610000 | MON_TARIFF(m,n))}      // A 相第二象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_B_Q2_KVAR(m,n)                       {0x02, (0x10620000 | MON_TARIFF(m,n))}      // B 相第二象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_C_Q2_KVAR(m,n)                       {0x02, (0x10630000 | MON_TARIFF(m,n))}      // C 相第二象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_Q3_KVAR(m,n)                         {0x02, (0x10700000 | MON_TARIFF(m,n))}      // 第三象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_A_Q3_KVAR(m,n)                       {0x02, (0x10710000 | MON_TARIFF(m,n))}      // A 相第三象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_B_Q3_KVAR(m,n)                       {0x02, (0x10720000 | MON_TARIFF(m,n))}      // B 相第三象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_C_Q3_KVAR(m,n)                       {0x02, (0x10730000 | MON_TARIFF(m,n))}      // C 相第三象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_Q4_KVAR(m,n)                         {0x02, (0x10800000 | MON_TARIFF(m,n))}      // 第四象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_A_Q4_KVAR(m,n)                       {0x02, (0x10810000 | MON_TARIFF(m,n))}      // A 相第四象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_B_Q4_KVAR(m,n)                       {0x02, (0x10820000 | MON_TARIFF(m,n))}      // B 相第四象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_C_Q4_KVAR(m,n)                       {0x02, (0x10830000 | MON_TARIFF(m,n))}      // C 相第四象限最大需量 最大需量值∷=double-long-unsigned 单位：kvar，换算：-4
#define C2_MMD_POS_KVA(m,n)                         {0x02, (0x10900000 | MON_TARIFF(m,n))}      // 正向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_A_POS_KVA(m,n)                       {0x02, (0x10910000 | MON_TARIFF(m,n))}      // A 相正向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_B_POS_KVA(m,n)                       {0x02, (0x10920000 | MON_TARIFF(m,n))}      // B 相正向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_C_POS_KVA(m,n)                       {0x02, (0x10930000 | MON_TARIFF(m,n))}      // C 相正向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_NEG_KVA(m,n)                         {0x02, (0x10A00000 | MON_TARIFF(m,n))}      // 反向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_A_NEG_KVA(m,n)                       {0x02, (0x10A10000 | MON_TARIFF(m,n))}      // A 相反向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_B_NEG_KVA(m,n)                       {0x02, (0x10A20000 | MON_TARIFF(m,n))}      // B 相反向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4
#define C2_MMD_C_NEG_KVA(m,n)                       {0x02, (0x10A30000 | MON_TARIFF(m,n))}      // C 相反向视在最大需量 最大需量值∷=double-long-unsigned 单位：kVA，换算：-4


/// @ 类3 class id = 3  分相变量类,本接口类定义了电压、电流、相角等分相变量数据信息\
/// 类3 n=0 所有相电压(单相给A) 123=ABC 数值数组按 A 相、B 相、C 相顺序排列，单相时，数值数组仅包含一个元素
#define C3_INS_VOL(n)                               {0x03, (0x20000200 | ID_INDEX(n))}          // 数据类型：long-unsigned，单位：V，换算：-1
#define C3_INS_CUR(n)                               {0x03, (0x20010200 | ID_INDEX(n))}          // 电流 数据类型：double-long，单位：A 换算：-3 索引 4 零线电流∷=double-long， 单位：A 换算：-3
#define C3_VV_ANGLE(n)                              {0x03, (0x20020200 | ID_INDEX(n))}          // 电压相角 数据类型：long-unsigned，单位：度，换算：-1
#define C3_VI_ANGLE(n)                              {0x03, (0x20030200 | ID_INDEX(n))}          // 电压电流相角 数据类型：long-unsigned，单位：度，换算：-1
#define C3_V_THD(n)                                 {0x03, (0x200B0200 | ID_INDEX(n))}          // 电压波形失真度 数据类型：long，单位：%，换算：-2
#define C3_I_THD(n)                                 {0x03, (0x200C0200 | ID_INDEX(n))}          // 电流波形失真度 数据类型：long，单位：%，换算：-2


/// @ 类4 class id = 4  功率类,本接口类定义了功率、功率因数等数据信息，
// 数值数组按总、A 相、B 相、C 相顺序排列，单相时，数值数组包含两个元素，为总及单相数值。
#define C4_INS_kW(n)                                {0x04, (0x20040200 | ID_INDEX(n))}          // 有功功率 数据类型：double-long，单位：W，换算：-1
#define C4_INS_kvar(n)                              {0x04, (0x20050200 | ID_INDEX(n))}          // 无功功率 数据类型：double-long，单位：var，换算：-1
#define C4_INS_kVA(n)                               {0x04, (0x20060200 | ID_INDEX(n))}          // 视在功率 数据类型：double-long，单位：VA，换算：-1
#define C4_AVR_kW(n)                                {0x04, (0x20070200 | ID_INDEX(n))}          // 一分钟平均有功功率 数据类型：double-long，单位：W，换算：-1
#define C4_AVR_kvar(n)                              {0x04, (0x20080200 | ID_INDEX(n))}          // 一分钟平均无功功率 数据类型：double-long，单位：var，换算：-1
#define C4_AVR_kVA(n)                               {0x04, (0x20090200 | ID_INDEX(n))}          // 一分钟平均视在功率 数据类型：double-long，单位：VA，换算：-1
#define C4_INS_PF(n)                                {0x04, (0x200A0200 | ID_INDEX(n))}          // 功率因数 数据类型：long，单位：无，换算：-3


/// @ 类5 class id = 5  谐波变量类,本接口类定义了谐波变量数据信息，
/// 类5 n =0 总，1-n 代表2-n+1次谐波
#define C5_V_A_HR(n)                                {0x05, (0x200D0200 | ID_INDEX(n))}          // A相电压谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_V_B_HR(n)                                {0x05, (0x200D0300 | ID_INDEX(n))}          // B相电压谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_V_C_HR(n)                                {0x05, (0x200D0400 | ID_INDEX(n))}          // C相电压谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_V_MAX_RD                                 {0x05, 0x200D0500}                          // 最高谐波次数 unsigned
#define C5_I_A_HR(n)                                {0x05, (0x200E0200 | ID_INDEX(n))}          // A相电流谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_I_B_HR(n)                                {0x05, (0x200E0300 | ID_INDEX(n))}          // B相电流谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_I_C_HR(n)                                {0x05, (0x200E0400 | ID_INDEX(n))}          // C相电流谐波含有率（总及 2…n 次） 数据类型：long，单位：%，换算：-2
#define C5_I_MAX_RD                                 {0x05, 0x200E0500}                          // 最高谐波次数 unsigned


/// @ 类6 class id = 6  数据变量类,本接口类定义了过程值或与过程值单元相关的状态值数据信息
///类6只有一个元素
#define C6_FREQUENCY                                {0x06, (0x200F0200)}                          // 电网频率 数据类型：long-unsigned，单位：Hz，换算：-2
#define C6_TEMPARETURE                              {0x06, (0x20100200)}                          // 表内温度 数据类型：long，单位：℃，换算：-1
#define C6_INBAT_VOL                                {0x06, (0x20110200)}                          // 时钟电池电压 数据类型：long-unsigned，单位：V，换算：-2
#define C6_EXBAT_VOL                                {0x06, (0x20120200)}                          // 停电抄表电池电压 数据类型：long-unsigned，单位：V，换算：-2
#define C6_INBAT_RUN_TIME                           {0x06, (0x20130200)}                          // 时钟电池工作时间 数据类型：double-long-unsigned，单位：分钟，无换算
#define C6_METER_RUN_STUS                           {0x06, (0x20140200)}                          // 电能表运行状态字 数据类型：array bit-string，无单位，无换算，包括电
#define C6_PUSH_STUS                                {0x06, (0x20150200)}                          // 电能表跟随上报状态字 数据类型：bit-string(SIZE(32))，无单位，无换算，见
#define C6_CUR_MD_KW                                {0x06, (0x20170200)}                          // 当前有功需量 数据类型：double-long，单位：kW，换算：-4
#define C6_CUR_MD_KVAR                              {0x06, (0x20180200)}                          // 当前无功需量 数据类型：double-long，单位：kvar，换算：-4
#define C6_CUR_MD_KVA                               {0x06, (0x20190200)}                          // 当前视在需量 数据类型：double-long，单位：kVA，换算：-4
#define C6_CUR_PRICE                                {0x06, (0x201A0200)}                          // 当前电价 数据类型：double-long-unsigned单位：元/kWh，换算：-4
#define C6_CUR_TARIFF_PRICE                         {0x06, (0x201B0200)}                          // 当前费率电价 数据类型：double-long-unsigned单位：元/kWh，换算：-4
#define C6_CUR_STEP_PRICE                           {0x06, (0x201C0200)}                          // 当前阶梯电价 数据类型：double-long-unsigned 单位：元/kWh，换算：-4
#define C6_VOL_UNB_PER                              {0x06, (0x20260200)}                          // 电压不平衡率 数据类型：long-unsigned，单位：%，换算：-2
#define C6_CUR_UBN_PER                              {0x06, (0x20270200)}                          // 电流不平衡率 数据类型：long-unsigned，单位：%，换算：-2
#define C6_LOAD_UBN_PER                             {0x06, (0x20280200)}                          // 负载率 数据类型：long-unsigned，单位：%，换算：-2
#define C6_AH_VALUE                                 {0x06, (0x20290200)}                          // 安时值 属性 2 安时数值∷=array 相安时值
#define C6_CUR_OVERDRAFT                            {0x06, (0x202D0200)}                          // （当前）透支金额 数据类型：double-long-unsigned，单位：元，换算：-2
#define C6_CUM_PERCHASE                             {0x06, (0x202E0200)}                          // 累计购电金额 数据类型：double-long-unsigned，单位：元，换算：-2
#define C6_MON_USE_KWH                              {0x06, (0x20310200)}                          // 月度用电量 属性 2 用电量∷=double-long-unsigned，
#define C6_CTRL_ACTION_STUS                         {0x06, (0x20400200)}                          // 控制命令执行状态字 数据类型：bit-string(SIZE(16))，无单位，无换算
#define C6_CTRL_ERR_STUS                            {0x06, (0x20410200)}                          // 控制命令错误状态字 数据类型：bit-string(SIZE(16))，无单位，无换算

/// @ 类7 class id = 7  事件对象类,本接口类定义了配置、存储事件记录数据信息，


/// @ 类8 class id = 8  参数变量类,本接口类定义了参数信息
/// @ 类8 元素个数不统一，特定
#define C8_EVT_START_TIME                           {0x08, 0x201E0200}                            // 事件发生时间 数据类型：date_time_s
#define C8_EVT_END_TIME                             {0x08, 0x20200200}                            // 事件结束时间 数据类型：date_time_s
#define C8_FRZ_TIME                                 {0x08, 0x20210200}                            // 数据冻结时间 数据类型：date_time_s
#define C8_EVT_NO                                   {0x08, 0x20220200}                            // 事件记录序号 数据类型：double-long-unsigned
#define C8_FRZ_NO                                   {0x08, 0x20230200}                            // 冻结记录序号 数据类型：double-long-unsigned
#define C8_EVT_TYPE                                 {0x08, 0x20240200}                            // 事件发生源 具体对象定义。
#define C8_EVT_CUR_VALUE                            {0x08, 0x20250200}                            // 事件当前值 structure{事件发生次数 double-long-unsigned，事件累计时间 double-long-unsigned（单位：秒，无换算）}
#define C8_EVT_NUM                                  {0x08, 0x20250201}                            // 事件发生次数 double-long-unsigned
#define C8_EVT_CUM_TIME                             {0x08, 0x20250202}                            // 事件累计时间 double-long-unsigned（单位：秒，无换算）
#define C8_OBJ_SERVER_ADDR                          {0x08, 0x202A0200}                            // 目标服务器地址 属性 2∷=TSA
#define C8_CUR_PURSE_FILE                           {0x08, 0x202C0200}                            // （当前）钱包文件 数值∷=structure 剩余金额 double-long-unsigned（单位：元，换算：-2）， 购电次数 double-long-unsigned
#define C8_CUR_PURSE_AMOUNT                         {0x08, 0x202C0201}                            // 剩余金额 double-long-unsigned（单位：元，换算：-2）
#define C8_CUR_PURSE_COUNT                          {0x08, 0x202C0202}                            // 购电次数 double-long-unsigned


/// @ 类9 class id = 9  冻结数据类,本接口类定义了配置、存储冻结数据及相关信息
/// @ 类10 class id = 10  采集类,本接口类定义了配置和存储与采集相关的参数、数据和记录
/// @ 类11 class id = 11  集合类,本接口类定义了一种通用的资料信息集合
/// @ 类12 class id = 12  脉冲计量类,本接口类定义了脉冲计量相关配置和数据信息
/// @ 类13 class id = 13  控制类,本接口类定义了用电负荷控制的功能，
/// @ 类14 class id = 14  区间统计类,本接口类定义了统计越限相关的信息
/// @ 类15 class id = 15  累加平均类,本接口类定义了对相同属性的数值进行累加、平均的运算功能，
/// @ 类16 class id = 16  极值统计类,本接口类定义了生成最大值、最小值及发生时间的功能
/// @ 类17 class id = 17  显示类,本接口类定义了与显示相关的信息
/// @ 类18 class id = 18  文件传输类,本接口类定义了服务器实现上传和下载文件的功能，
/// @ 类19 class id = 19  设备管理类,本接口类定义了设备管理相关信息
/// @ 类20 class id = 20  应用连接类,本接口类定义了应用连接相关信息
/// @ 类21 class id = 21  ESAM 接口类,本接口类定义了ESAM相关接口，
/// @ 类22 class id = 22  输入输出设备类,本接口类定义了输入输出设备相关信息
/// @ 类23 class id = 23  总加组类,本接口类定义了总加组相关信息
/// @ 类24 class id = 24  分项事件对象类,本接口类定义了配置、存储分项事件数据信息
/// @ 类25 class id = 25  无线公网/专网通信接口类,本接口类定义了无线公网/专网通信接口
/// @ 类26 class id = 26  以太网通信接口类,本接口类定义了以太网通信接口
 





3000 24 电能表失压事件 属性 5（配置参数）∷=structure
{
电压触发上限 long-unsigned（单位：V，换算：-1），
 电压恢复下限 long-unsigned（单位：V，换算：-1），
 电流触发下限 double-long（单位：A，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
属性 13（失压统计）∷=structure
{
事件发生总次数 double-long-unsigned，
事件总累计时间 double-long-unsigned（单位：秒，无换算），
最近一次失压发生时间 date_time_s，
最近一次失压结束时间 date_time_s
}
事件发生总次数：A、B、C 相失压累计次数之和。
事件总累计时间：A、B、C 相失压累计时间之和。
3001 24 电能表欠压事件 属性 5（配置参数）∷=structure
{
 电压触发上限 long-unsigned（单位：V，换算：-1），
Q/GDW 11778—2017
124
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
3001 24 电能表欠压事件 判定延时时间 unsigned（单位：s，换算：0）
}
3002 24 电能表过压事件 属性 5（配置参数）∷=structure
{
 电压触发下限 long-unsigned（单位：V，换算：-1），
 判定延时时间 unsigned（单位：s，换算：0）
}
3003 24 电能表断相事件 属性 5（配置参数）∷=structure
{
 电压触发上限 long-unsigned（单位：V，换算：-1），
 电流触发上限 double-long（单位：A，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
3004 24 电能表失流事件 属性 5（配置参数）∷=structure
{
 电压触发下限 long-unsigned（单位：V，换算：-1），
 电流触发上限 double-long（单位：A，换算：-4），
 电流触发下限 double-long（单位：A，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
3005 24 电能表过流事件 属性 5（配置参数）∷=structure
{
 电流触发限值 double-long（单位：A，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
3006 24 电能表断流事件 属性 5（配置参数）∷=structure
{
 电压触发下限 long-unsigned（单位：V，换算：-1），
 电流触发上限 double-long（单位：A，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
3007 24 电能表功率反向事件 属性 5（配置参数）∷=structure
{
 有功功率触发限值 double-long（单位：W，换算：-1），
 判定延时时间 unsigned（单位：s，换算：0）
}
3008 24 电能表过载事件 属性 5（配置参数）∷=structure
{
 有功功率触发限值 double-long（单位：W，换算：-1），
Q/GDW 11778—2017
125
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
3008 24 电能表过载事件 判定延时时间 unsigned（单位：s，换算：0）
}
3009 7 电能表正向有功需量超限
事件
属性 2（事件记录表）∷=array 电能表需量超限事件单元
属性 6（配置参数）∷=structure
{
 触发限值 double-long-unsigned（单位：kW，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
300A 7 电能表反向有功需量超限
事件
属性 2（事件记录表）∷=array 电能表需量超限事件单元
属性 6（配置参数）∷=structure
{
 触发限值 double-long-unsigned（单位：kW，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
300B 24 电能表无功需量超限事件 属性 5（配置参数）∷=structure
{
 触发限值 double-long-unsigned（单位：kvar，换算：-4），
 判定延时时间 unsigned（单位：s，换算：0）
}
属性 6（事件记录表 1）∷=array 电能表需量超限事件单元
属性 7（事件记录表 2）∷=array 电能表需量超限事件单元
属性 8（事件记录表 3）∷=array 电能表需量超限事件单元
属性 9（事件记录表 4）∷=array 电能表需量超限事件单元
300C 7 电能表功率因数超下限事
件
属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 下限阀值 long（单位：%，换算：-1），
 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
300D 7 电能表全失压事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
300E 7 电能表辅助电源掉电事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
Q/GDW 11778—2017
126
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
300E 7 电能表辅助电源掉电事件 判定延时 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
300F 7 电能表电压逆相序事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 判定延时 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
3010 7 电能表电流逆相序事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 判定延时 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
3011 7 电能表掉电事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 判定延时 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
3012 7 电能表编程事件 属性 2（事件记录表）∷=array 编程记录事件单元
属性 6（配置参数）∷=structure
{
}
3013 7 电能表清零事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3014 7 电能表需量清零事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3015 7 电能表事件清零事件 属性 2（事件记录表）∷=array 事件清零事件记录单元
属性 6（配置参数）∷=structure
{
}
Q/GDW 11778—2017
127
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
3015 7 电能表事件清零事件 事件发生源∷=NULL
3016 7 电能表校时事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3017 7 电能表时段表编程事件 属性 2（事件记录表）∷=array 电能表时段表编程事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3018 7 电能表时区表编程事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3019 7 电能表周休日编程事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
301A 7 电能表结算日编程事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
301B 7 电能表开盖事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
301C 7 电能表开端钮盒事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
301D 7 电能表电压不平衡事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 限值 long（单位：%，换算：-2），
Q/GDW 11778—2017
128
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
301D 7 电能表电压不平衡事件 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
301E 7 电能表电流不平衡事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
 限值 long（单位：%，换算：-2），
 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
301F 7 电能表跳闸事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3020 7 电能表合闸事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3021 7 电能表节假日编程事件 属性 2（事件记录表）∷=array 电能表节假日编程事件单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3022 7 电能表有功组合方式编程
事件
属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3023 7 电能表无功组合方式编程
事件
属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=enum
{
无功组合方式 1 特征字（0），
无功组合方式 2 特征字（1）
}
Q/GDW 11778—2017
129
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
3024 7 电能表费率参数表编程事
件
属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3025 7 电能表阶梯表编程事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3026 7 电能表密钥更新事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3027 7 电能表异常插卡事件 属性 2（事件记录表）∷=array 电能表异常插卡记录单元
属性 6（配置参数）∷=structure
{
}
属性 11（非法插卡总次数）∷=double-long-unsigned
事件发生源∷=NULL
3028 7 电能表购电记录 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3029 7 电能表退费记录 属性 2（事件记录表）∷=array 电能表退费记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
302A 7 电能表恒定磁场干扰事件 属性 2（事件记录表）∷=array 标准事件记录单元
事件发生源∷=NULL
302B 7 电能表负荷开关误动作事
件
属性 2（事件记录表）∷=array 标准事件记录单元
事件发生源∷=NULL
302C 7 电能表电源异常事件 属性 2（事件记录表）∷=array 标准事件记录单元
事件发生源∷=NULL
302D 7 电能表电流严重不平衡事
件
属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
Q/GDW 11778—2017
130
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
302D 7 电能表电流严重不平衡事
件
{
 限值 long（单位：%，换算：-2）
 判定延时时间 unsigned（单位：s，换算：0）
}
事件发生源∷=NULL
302E 7 电能表时钟故障事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
302F 7 电能表计量芯片故障事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3030 7 通信模块变更事件 属性 2（事件记录表）∷=array 通信模块变更事件单元
属性 6（配置参数）∷=structure 
{
 判定延时 unsigned（单位：s，换算：0）
}
事件发生源∷=OAD
事件发生源为通信模块 OAD。
3100 7 终端初始化事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3101 7 终端版本变更事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3104 7 终端状态量变位事件 属性 2（事件记录表）∷=array 标准事件记录单元
属性 6（配置参数）∷=structure
{
}
事件发生源∷=NULL
3105 7 电能表时钟超差事件 属性 2（事件记录表）∷=array 电能表时钟超差记录单元
属性 6（配置参数）∷=structure
Q/GDW 11778—2017
131
表 A.5 （续）
OI IC 对象名称 实例的对象属性及方法定义
3105 7 电能表时钟超差事件 {
异常判别阈值 long-unsigned（单位：秒），
关联采集任务号 unsigned
}
事件发生源∷=TSA
采集任务中需要配置相关 OAD 的采集任务。
3106 7 终端停/上电事件 属性 2（事件记录表）∷=array 停/上电事件记录单元
属性 6（配置参数）∷=structure
{
停电数据采集配置参数 structure
{
采集标志 bit-string(SIZE(8))，
停电事件抄读时间间隔（小时） unsigned ，
停电事件抄读时间限值（分钟） unsigned，
需要读取停电事件的电能表 array TSA
}，
停电事件甄别限值参数 structure
{
停电时间最小有效间隔（分钟） long-unsigned，
停电时间最大有效间隔（分钟） long-unsigned，
停电事件起止时间偏差限值（分钟） long-unsigned，
停电事件时间区段偏差限值（分钟） long-unsigned，
停电发生电压限值 long-unsigned（单位：V，换算：-1），
停电恢复电压限值 long-unsigned（单位：V，换算：-1）
}
}
采集标志：
bit0：置“1”有效，置“0”无效；
bit1：置“1”随机选择电能表，置“0”只采集设置的电能表。
事件发生源∷=NULL






#endif /* __OAD_TABLE_H__ */
