/**
 ******************************************************************************
 * @file    fft_calculate.c
 * <AUTHOR> @version V1.0.0
 * @date    2025
 * @brief   本文件主要包含对FFT算法相关接口的调用。
 ******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include <math.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "bsp_cfg.h"
#include "debug.h"
#include "mic.h" 

typedef struct
{
    float real;    // real data
    float imag;    // zero
} compx;

#define sample_num 128    // 采样点数/1个周波
/************ const data of sin/cos ************/
const float cosf_tab[256] = {
    0.9999,  0.9996,  0.9987,  0.9972,  0.9951,  0.9924,  0.9891,  0.9852,  0.9807,  0.9757,  0.9700,  0.9637,  0.9569,  0.9495,  0.9415,  0.9329,  0.9238,  0.9142,  0.9039,
    0.8932,  0.8819,  0.8700,  0.8577,  0.8448,  0.8314,  0.8175,  0.8032,  0.7883,  0.7730,  0.7572,  0.7409,  0.7242,  0.7071,  0.6895,  0.6715,  0.6531,  0.6343,  0.6152,
    0.5956,  0.5758,  0.5555,  0.5349,  0.5141,  0.4928,  0.4713,  0.4496,  0.4275,  0.4052,  0.3826,  0.3598,  0.3368,  0.3136,  0.2902,  0.2667,  0.2429,  0.2191,  0.1950,
    0.1709,  0.1467,  0.1224,  0.0980,  0.0735,  0.0490,  0.0245,  0.0,     -0.0245, -0.0490, -0.0735, -0.0980, -0.1224, -0.1467, -0.1709, -0.1950, -0.2190, -0.2429, -0.2667,
    -0.2902, -0.3136, -0.3368, -0.3598, -0.3826, -0.4052, -0.4275, -0.4496, -0.4713, -0.4928, -0.5141, -0.5349, -0.5555, -0.5758, -0.5956, -0.6152, -0.6343, -0.6531, -0.6715,
    -0.6895, -0.7071, -0.7242, -0.7409, -0.7572, -0.7730, -0.7883, -0.8032, -0.8175, -0.8314, -0.8448, -0.8577, -0.8700, -0.8819, -0.8932, -0.9039, -0.9142, -0.9238, -0.9329,
    -0.9415, -0.9495, -0.9569, -0.9637, -0.9700, -0.9757, -0.9807, -0.9852, -0.9891, -0.9924, -0.9951, -0.9972, -0.9987, -0.9996, -0.9999, -0.9996, -0.9987, -0.9972, -0.9951,
    -0.9924, -0.9891, -0.9852, -0.9807, -0.9757, -0.9700, -0.9637, -0.9569, -0.9495, -0.9415, -0.9329, -0.9238, -0.9142, -0.9039, -0.8932, -0.8819, -0.8700, -0.8577, -0.8448,
    -0.8314, -0.8175, -0.8032, -0.7883, -0.7730, -0.7572, -0.7409, -0.7242, -0.7071, -0.6895, -0.6715, -0.6531, -0.6343, -0.6152, -0.5957, -0.5758, -0.5555, -0.5350, -0.5141,
    -0.4929, -0.4713, -0.4496, -0.4275, -0.4052, -0.3826, -0.3598, -0.3368, -0.3136, -0.2902, -0.2667, -0.2429, -0.2191, -0.1950, -0.1709, -0.1467, -0.1224, -0.0980, -0.0735,
    -0.0490, -0.0245, -0.0,    0.0245,  0.0490,  0.0735,  0.0980,  0.1224,  0.1467,  0.1709,  0.1950,  0.2190,  0.2429,  0.2667,  0.2902,  0.3136,  0.3368,  0.3598,  0.3826,
    0.4052,  0.4275,  0.4496,  0.4713,  0.4928,  0.5140,  0.5349,  0.5555,  0.5758,  0.5956,  0.6152,  0.6343,  0.6531,  0.6715,  0.6895,  0.7071,  0.7242,  0.7409,  0.7572,
    0.7730,  0.7883,  0.8032,  0.8175,  0.8314,  0.8448,  0.8577,  0.8700,  0.8819,  0.8932,  0.9039,  0.9142,  0.9238,  0.9329,  0.9415,  0.9495,  0.9569,  0.9637,  0.9700,
    0.9757,  0.9807,  0.9852,  0.9891,  0.9924,  0.9951,  0.9972,  0.9987,  0.9996};

const float sinf_tab[256] = {
    0,       0.0245,  0.0490,  0.0735,  0.0980,  0.1224,  0.1467,  0.1709,  0.1950,  0.2191,  0.2429,  0.2667,  0.2902,  0.3136,  0.3368,  0.3598,  0.3826,  0.4052,  0.4275,
    0.4496,  0.4713,  0.4928,  0.5141,  0.5349,  0.5555,  0.5758,  0.5956,  0.6152,  0.6343,  0.6531,  0.6715,  0.6895,  0.7071,  0.7242,  0.7409,  0.7572,  0.7730,  0.7883,
    0.8032,  0.8175,  0.8314,  0.8448,  0.8577,  0.8700,  0.8819,  0.8932,  0.9039,  0.9142,  0.9238,  0.9329,  0.9415,  0.9495,  0.9569,  0.9637,  0.9700,  0.9757,  0.9807,
    0.9852,  0.9891,  0.9924,  0.9951,  0.9972,  0.9987,  0.9996,  0.9999,  0.9996,  0.9987,  0.9972,  0.9951,  0.9924,  0.9891,  0.9852,  0.9807,  0.9757,  0.9700,  0.9637,
    0.9569,  0.9495,  0.9415,  0.9329,  0.9238,  0.9142,  0.9039,  0.8932,  0.8819,  0.8700,  0.8577,  0.8448,  0.8314,  0.8175,  0.8032,  0.7883,  0.7730,  0.7572,  0.7409,
    0.7242,  0.7071,  0.6895,  0.6715,  0.6531,  0.6343,  0.6152,  0.5957,  0.5758,  0.5555,  0.5349,  0.5141,  0.4929,  0.4713,  0.4496,  0.4275,  0.4052,  0.3826,  0.3598,
    0.3368,  0.3136,  0.2902,  0.2667,  0.2429,  0.2191,  0.1950,  0.1709,  0.1467,  0.1224,  0.0980,  0.0735,  0.0490,  0.0245,  0.0,     -0.0245, -0.0490, -0.0735, -0.0980,
    -0.1224, -0.1467, -0.1709, -0.1950, -0.2190, -0.2429, -0.2667, -0.2902, -0.3136, -0.3368, -0.3598, -0.3826, -0.4052, -0.4275, -0.4496, -0.4713, -0.4928, -0.5141, -0.5349,
    -0.5555, -0.5758, -0.5956, -0.6152, -0.6343, -0.6531, -0.6715, -0.6895, -0.7071, -0.7242, -0.7409, -0.7572, -0.7730, -0.7883, -0.8032, -0.8175, -0.8314, -0.8448, -0.8577,
    -0.8700, -0.8819, -0.8932, -0.9039, -0.9142, -0.9238, -0.9329, -0.9415, -0.9495, -0.9569, -0.9637, -0.9700, -0.9757, -0.9807, -0.9852, -0.9891, -0.9924, -0.9951, -0.9972,
    -0.9987, -0.9996, -0.9999, -0.9996, -0.9987, -0.9972, -0.9951, -0.9924, -0.9891, -0.9852, -0.9807, -0.9757, -0.9700, -0.9637, -0.9569, -0.9495, -0.9415, -0.9329, -0.9238,
    -0.9142, -0.9039, -0.8932, -0.8819, -0.8700, -0.8577, -0.8448, -0.8314, -0.8175, -0.8032, -0.7883, -0.7730, -0.7572, -0.7409, -0.7242, -0.7071, -0.6895, -0.6715, -0.6531,
    -0.6343, -0.6152, -0.5957, -0.5758, -0.5555, -0.5350, -0.5141, -0.4929, -0.4714, -0.4496, -0.4275, -0.4052, -0.3826, -0.3598, -0.3368, -0.3136, -0.2902, -0.2667, -0.2429,
    -0.2191, -0.1950, -0.1709, -0.1467, -0.1224, -0.0980, -0.0735, -0.0490, -0.0245};

const float pi = 3.1415926;

const float FftCoefficient[42] = {    ////1~41 harmonic coefficient
    1.000296421124878, 1.001210044356499, 1.002742895402842, 1.004898174711047, 1.007680390476693, 1.011095376335698, 1.015150314324972, 1.019853763253874, 1.025215692662640,
    1.031247522579792, 1.037962169328204, 1.045374097669509, 1.053499379619040, 1.062355760309259, 1.071962731328623, 1.082341612016155, 1.093515639249554, 1.105510066327643,
    1.118352271616542, 1.132071877704358, 1.146700881891770, 1.162273798937021, 1.178827817074347, 1.196402968435944, 1.215042315130720, 1.234792152369594, 1.255702230178997,
    1.277825995412994, 1.301220855962973, 1.325948469273839, 1.352075057510752, 1.379671751983324, 1.408814969728950, 1.439586825487704, 1.472075582672816, 1.506376147359001,
    1.542590609781942, 1.580828838373713, 1.621209131959367, 1.663858936418868, 1.708915632887857, 1.756527405443151};

/***************** declaretion ********************/
void  FFT(compx *xin, short N);
short max_find(short *x, unsigned char num);

/************ MAIN begin *****************/
compx EE(compx b1, compx b2)
{
    compx b3;
    b3.real = b1.real * b2.real - b1.imag * b2.imag;
    b3.imag = b1.real * b2.imag + b1.imag * b2.real;
    return (b3);
}

void harmonic_wave_fft_calculate(signed short *actual_dat, float *fftresult)
{
    compx s[sample_num] = {{0, 0}};    // length is sample_num
    unsigned char i;
    signed short  imax;
    float         fft_result_tmp;

    imax = max_find(actual_dat, sample_num);

    for(i = 0; i < sample_num; i++)
    {
        s[i].real = actual_dat[i];
        s[i].real = s[i].real / (imax);    // 减小幅值范围
        //	s[i].real=s[i].real/32768;
        s[i].imag = 0;
    }

    FFT(s, sample_num);

    for(i = 0; i < (sample_num >> 1); i++)    // fft_result mirror, so only need half
    {
        fft_result_tmp = sqrt(s[i].real * s[i].real + s[i].imag * s[i].imag);
        fftresult[i]  = fft_result_tmp * 2 / sample_num;    // 减小幅值范围
    }
}

/**  FFT routine */
void FFT(compx *xin, short N)
{
    unsigned char  f, j, i, k, m, L;
    unsigned short le, B, ip;
    compx          w, t;

    f = N;
    for(m = 1; (f = f / 2) != 1; m++);

    j = N / 2;
    for(i = 1; i <= (N - 2); i++)    // data saved at 0 need no address reverse . reverse to N/2 is enough??
    {
        if(i < j)
        {
            t      = xin[j];
            xin[j] = xin[i];
            xin[i] = t;
        }
        k = N / 2;
        while(j >= k)
        {
            j = j - k;
            k = k / 2;
        }
        j = j + k;
    }

    for(L = 1; L <= m; L++)    // L scope 1--m
    {
        le = (unsigned short)pow(2, L);
        B  = le / 2;

        for(j = 0; j <= B - 1; j++)
        {
            w.real = cosf_tab[(256 * j) >> L];
            //	w.real = cos(pi/B);
            w.imag = -sinf_tab[(256 * j) >> L];

            for(i = j; i <= N - 1; i = i + le)
            {
                ip = i + B;
                t  = EE(xin[ip], w);

                xin[ip].real = xin[i].real - t.real;
                xin[ip].imag = xin[i].imag - t.imag;
                xin[i].real  = xin[i].real + t.real;
                xin[i].imag  = xin[i].imag + t.imag;
            }
        }
    } /***** FFT done****/    // 到这里取s，即a+bi
    // WDTE=0xac;
}
/************ min & max *********************/
short max_find(short *x, unsigned char num)    // found the max data
{
    short iax, maxret, tmp;

    tmp = abs(x[0]);
    for(iax = 0; iax < num; iax++)
    {
        if((x[iax] < 0) && ((tmp + x[iax]) < 0))
        {
            maxret = -x[iax];
            tmp    = maxret;
        }
        if((x[iax] > 0) && ((tmp - x[iax]) < 0))
        {
            maxret = x[iax];
            tmp    = maxret;
        }
    }
    return (maxret);
}

/**********************************************************************************/
/*task of ht7136 harmonic wave calculate                                                         */
/**********************************************************************************/

bool ht7136_harmonic(unsigned short *sample_buffer, float *harmonic_per, uint8_t chn)
{
    signed short  actual_data[sample_num];               // one channel 128 points data for calculate FFT
    float         fft_result[HARMONIC_WAVE] = {0};       // 
    unsigned char j, i;
    signed long   DataAverage;
    float         FundamentalWave;
    unsigned long Percent_Data;

    DataAverage = 0;
    for(i = 0; i < sample_num; i++)
    {
        actual_data[i] = (signed short)(sample_buffer[7 * i + chn] & 0xffff);    // 2 bytes of the lower are effective
        DataAverage += actual_data[i];                                           // printf("actual_data[%d]: %d\n", i, actual_data[i]);
    }
    DataAverage = DataAverage / sample_num;

    for(i = 0; i < sample_num; i++)
    {
        actual_data[i] = actual_data[i] - (signed short)DataAverage;    // wipe off the direct current
    }
    harmonic_wave_fft_calculate(actual_data, fft_result);    // do FFT

    for(j = 0; j < HARMONIC_WAVE; j++)
    {
        fft_result[j + 1] = FftCoefficient[j] * fft_result[j + 1];    // multiply FFT results by FftCoefficient
    }

    FundamentalWave = ((fft_result[1]) * 100);
    if(equ(FundamentalWave,0)) { return false; }    // if fundamental wave is very small,ignore calculating the harmonics,evaluate them 0

    for(j = 0; j < (HARMONIC_WAVE + 1); j++)
    {
        if(j)
        {
            harmonic_per[j - 1] = ((fft_result[j]) * 100) / FundamentalWave;    // caculate the harmonic waves except 0 times
            if(harmonic_per[j - 1] < 0.005) {harmonic_per[j - 1] = 0;}
        }
    }
    return true;
}

float ht7136_thd(float *harmonic_per, int harmonic_num)
{
    float sum_squares = 0.0f;
    float thd_per;
    int   i;
    // 计算谐波幅值平方和 二次谐波开始
    for(i = 1; i < harmonic_num; i++) { sum_squares += (harmonic_per[i]) * (harmonic_per[i]); }

    // 计算THD 幅值平方和/一次幅值 开方
    thd_per = sqrtf(sum_squares / harmonic_per[0]) * 1.0;
    if(thd_per < 0.005) {thd_per = 0;}

    return thd_per;
}
