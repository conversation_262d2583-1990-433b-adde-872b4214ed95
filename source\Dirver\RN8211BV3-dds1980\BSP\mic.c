/*
 * @Author: lijing <EMAIL>
 * @Date: 2025-08-07 11:53:30
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-08-29 11:42:13
 * @FilePath: \smart-meter\source\Dirver\RN8211BV3-dds1980\BSP\mic.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

/**
 ******************************************************************************
 * @file    mic.c
 * <AUTHOR> @date    2024
 * @brief   计量芯片驱动
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "bsp_cfg.h"

#if EMU_TYPE == EMU_HT7136
#include "emu_ht7136.c"
#elif EMU_TYPE == EMU_VIRTUAL
#include "emu_virtual.inc"
#elif EMU_TYPE == RN8211_B
#include "RN8211_inside.inc"
#else
#error "计量芯片类型不支持!"
#endif