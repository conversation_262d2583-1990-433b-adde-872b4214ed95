/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__     = 0x00000000;
define symbol __Reset_Handler_text_start__ = 0x000000C0;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = 0x00000000;
define symbol __ICFEDIT_region_ROM_end__   = 0x0003FFFF;  
define symbol __ICFEDIT_region_RAM_start__ = 0x20000000;
define symbol __ICFEDIT_region_RAM_end__   = 0x20007FFF;
/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 8k;
define symbol __ICFEDIT_size_heap__   = 0;
/**** End of ICF editor section. ###ICF###*/

/*** 以上不可手动修改，下面自定义可以更改 ****/
define exported symbol __ICFEDIT_BOOT_start__    = __ICFEDIT_region_ROM_start__;
define exported symbol __ICFEDIT_BOOT_api__      = __ICFEDIT_BOOT_start__ + 0x100;
define exported symbol __ICFEDIT_BOOT_size__     = 16K;

define exported symbol __ICFEDIT_APP_start__     = __ICFEDIT_BOOT_start__ + __ICFEDIT_BOOT_size__;
define exported symbol __ICFEDIT_APP_api__       = __ICFEDIT_APP_start__ + 0x100;
define exported symbol __ICFEDIT_APP_size__      = 220K;

define exported symbol __ICFEDIT_CRC_start__    = __ICFEDIT_APP_start__ + __ICFEDIT_APP_size__;
define exported symbol __ICFEDIT_CRC_size__     = 1K;

define exported symbol __ICFEDIT_DATA_start__    = __ICFEDIT_CRC_start__ + __ICFEDIT_CRC_size__;
define exported symbol __ICFEDIT_DATA_size__     = 16K;

/*app 向量表地址定义*/
define exported symbol __ICFEDIT_app_intvec_start__       = 0x4400;

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_BOOT_start__ to __ICFEDIT_BOOT_start__ + __ICFEDIT_BOOT_size__ - 1];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_start__ + 0x1000];

define region STACK_region = mem:[from __ICFEDIT_region_RAM_end__ + 1 - __ICFEDIT_size_cstack__  to __ICFEDIT_region_RAM_end__];

export symbol __ICFEDIT_region_RAM_start__;
export symbol __ICFEDIT_region_RAM_end__;

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

initialize by copy { readwrite };
do not initialize  { section .noinit };
place at address mem:__ICFEDIT_intvec_start__       { readonly section .intvec };
place at address mem:__Reset_Handler_text_start__   { readonly section .Reset_Handler_text };

place at address mem:__ICFEDIT_BOOT_api__    { readonly section .boot_api };


place in ROM_region                          { readonly };
place in RAM_region                          { readwrite, block HEAP };
place in STACK_region                        { block CSTACK};

