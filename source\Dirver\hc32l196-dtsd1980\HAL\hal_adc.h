/**
  ******************************************************************************
  * @file    hal_adc.h
  * <AUTHOR> @date    2024
  * @brief   
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifdef __cplusplus
extern "C"
{
#endif

#ifndef __HAL_ADC_H
#define __HAL_ADC_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"

    /* Exported types ------------------------------------------------------------*/

    /*< Defined the HAL_ADC_CHN_TYPE as enum*/
    typedef enum
    {
        HAL_ADC_CHN0           = AdcExInputCH0,       // 外部输入vin1
        HAL_ADC_CHN1           = AdcExInputCH1,       // 外部输入vin2
        HAL_ADC_CHN2           = AdcExInputCH2,       // 外部输入vin3
        HAL_ADC_CHN3           = AdcExInputCH3,       // 外部输入vin4
        HAL_ADC_CHN4           = AdcExInputCH4,       // 外部输入vin5
        HAL_ADC_CHN5           = AdcExInputCH5,       // 外部输入vin6
        HAL_ADC_CHN6           = AdcExInputCH6,       // 外部输入vin7
        HAL_ADC_CHN7           = AdcExInputCH7,       // 外部输入vin8
        HAL_ADC_CHN8           = AdcExInputCH8,       // 外部输入vin9
        HAL_ADC_CHN9           = AdcExInputCH9,       // 外部输入vin10
        HAL_ADC_CHN10          = AdcExInputCH10,      // 外部输入vin11
        HAL_ADC_CHN11          = AdcExInputCH11,      // 外部输入vin12
        HAL_ADC_CHN12          = AdcExInputCH12,      // 外部输入vin13
        HAL_ADC_CHN13          = AdcExInputCH13,      // 外部输入vin14
        HAL_ADC_CHN14          = AdcExInputCH14,      // 外部输入vin15
        HAL_ADC_CHN15          = AdcExInputCH15,      // 外部输入vin16
        HAL_ADC_CHN16          = AdcExInputCH16,      // 外部输入vin17
        HAL_ADC_CHN17          = AdcExInputCH17,      // 外部输入vin18
        HAL_ADC_CHN18          = AdcExInputCH18,      // 外部输入vin19
        HAL_ADC_CHN19          = AdcExInputCH19,      // 外部输入vin20
        HAL_ADC_CHN20          = AdcExInputCH20,      // 外部输入vin21
        HAL_ADC_CHN21          = AdcExInputCH21,      // 外部输入vin22
        HAL_ADC_CHN22          = AdcExInputCH22,      // 外部输入vin23
        HAL_ADC_CHN23          = AdcExInputCH23,      // 外部输入vin24
        HAL_ADC_CHN24          = AdcExInputCH24,      // 外部输入vin25
        HAL_ADC_CHN25          = AdcExInputCH25,      // 外部输入vin26
        HAL_ADC_DAC_INPUT      = AdcDacInput,         // /*!<使用DAC输出(必须使用输入增益)*/
        HAL_ADC_VCC_DIV3_INPUT = AdcAVccdiv3Input,    //
        HAL_ADC_TEMP           = AdcAiTsInput,    // TemperatureSensor /*!<使用内置温度传感器BGR_TS(必须使用输入增益)*/
        // HAL_ADC_REF1_2         = AdcVref1_2Input, /*!<使用内部基准1.2V(必须使用输入增益)*/
        HAL_ADC_CHN_NUM,
    } HAL_ADC_CHN_TYPE;

    /* Exported defines ----------------------------------------------------------*/
    /* Exported macro ------------------------------------------------------------*/
    /* Exported functions --------------------------------------------------------*/
    struct hal_adc_t
    {
        /**
         * @brief  Init and Open the Adc, 单通道单次采样
         * @param  None
         * @retval None
         */
        void (*open)(void);

        /**
         * @brief  Init and Open the Adc(No Power), 单通道单次采样
         * @param  None
         * @retval None
         */
        void (*open_nopower)(void);

        /**
         * @brief  Close the Adc
         * @param  None
         * @retval None
         */
        void (*close)(void);

        /**
         * @brief  Close the Adc(No Power)
         * @param  None
         * @retval None
         */
        void (*close_nopower)(void);

        /**
         * @brief  Start a single ADC sample
         * @param  [in ] chn-ADC channel
         * @retval None
         */
        void (*start)(HAL_ADC_CHN_TYPE chn);

        /**
         * @brief  Get the ADC sample value
         * @param  None
         * @retval Return ADC sample value
         */
        int32_t (*result)(HAL_ADC_CHN_TYPE chn);

        /**
         * @brief  Get the ADC sample voltage
         * @param  None
         * @retval Return ADC sample voltage
         */
        float (*voltage)(HAL_ADC_CHN_TYPE chn);

        /**
         * @brief  Get the current temperature value
         * @param  None
         * @retval
         */
        float (*temperature)(void);
    };
    extern const struct hal_adc_t hal_adc;

#endif /* __HAL_ADC_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif
