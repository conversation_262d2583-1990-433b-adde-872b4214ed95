#ifndef __RELAY_APP_H
#define __RELAY_APP_H

#include "typedef.h"
#include "relay.h"
#include "status.h"
#include "timeapp.h"

/* 继电器控制输出状态位定义 */
typedef uint16_t TYPE_STUS_CONTROL;
#define STUS_DISCONNECTED(x)        (x + ((TYPE_STUS_CONTROL)1 << 2))     // 拉闸
#define STUS_CONNECTED(x)           (x + ((TYPE_STUS_CONTROL)1 << 3))     // 合闸
#define STUS_READY_CONNECT(x)       (x + ((TYPE_STUS_CONTROL)1 << 4))     // 预合闸
#define STUS_REMOTE_DISCONNECTED(x) (x + ((TYPE_STUS_CONTROL)1 << 5))     // 远程拉闸
#define STUS_REMOTE_CONNECTED(x)    (x + ((TYPE_STUS_CONTROL)1 << 6))     // 远程合闸
#define STUS_LOCAL_DISCONNECTED(x)  (x + ((TYPE_STUS_CONTROL)1 << 7))     // 本地拉闸
#define STUS_LOCAL_CONNECTED(x)     (x + ((TYPE_STUS_CONTROL)1 << 8))     // 本地合闸
#define STUS_MANU_DISCONNECTED(x)   (x + ((TYPE_STUS_CONTROL)1 << 9))     // 手动拉闸
#define STUS_MANU_CONNECTED(x)      (x + ((TYPE_STUS_CONTROL)1 << 10))    // 手动合闸
#define STUS_RLY_ERR(x)             (x + ((TYPE_STUS_CONTROL)1 << 15))    // 继电器故障

/* 继电器物理状态定义 */
typedef bool RLY_OUT_STATE;
#define RLY_CONNECTED       true
#define RLY_DISCONNECTED    false


/* 继电器逻辑控制状态定义 */
typedef enum
{
    DISCONNECTED = 0,
    CONNECTED,
    READY_FOR_RECONNECTION
}ctrl_state_s;

/// @brief 远程控制类枚举
typedef enum
{
    TYPE_REMOTE_OFF       = 0x1A, // 拉闸
    TYPE_REMOTE_READY_ON  = 0x1B, // 合闸允许
    TYPE_REMOTE_ON        = 0x1C, // 合闸
    TYPE_REMOTE_ON_DELAY  = 0x1D, // 延时合闸
    TYPE_REMOTE_READY_DELAY = 0x1E, // 延时预合闸
    TYPE_REMOTE_ALARM_ON  = 0x2A, // 报警
    TYPE_REMOTE_ALARM_OFF = 0x2B, // 报警解除
    TYPE_REMOTE_KEEP_ON   = 0x3A, // 保电
    TYPE_REMOTE_KEEP_DIS  = 0x3B, // 保电解除
} remote_ctrl_s;

/// @brief 手动控制类枚举
typedef enum
{
    TYPE_MANUAL_OFF = 0,
    TYPE_MANUAL_ON,
}manual_ctrl_s;

/// @brief 本地控制类枚举
typedef enum
{
    TYPE_LOCAL_OFF = 0, // 本地断闸
    TYPE_LOCAL_ON,      // 本地合闸
}local_ctrl_s;

/// @brief 其它控制状态
typedef union 
{
    struct 
    {
        uint32_t remote       :1;  // 远程控制1-拉闸，0-合闸
        uint32_t remote_rdy   :1;  // 远程控制预合闸
        uint32_t manual       :1;  // 手动控制拉闸
    };
    uint32_t lword;
}ext_ctrl_stus_s;

typedef struct
{
    control_status_s evt_off;    // 本地事件触发继电器拉闸状态字
    ext_ctrl_stus_s  ext_off;    // 远程控制或者手动控制继电器拉闸状态字
} rly_off_reason_s, rly_ctrl_stus_s;


/// @brief 控制模块参数
typedef struct
{
    uint16_t crc;
    uint16_t chk;  

    uint16_t over_power_cnt_thd;     // 超功率拉闸自动合闸次数限制，超过次数后不再自动合闸，第二天恢复正常
    uint16_t malignant_load_cnt_thd; // 恶性负载自动合闸次数限制，超过次数后不再自动合闸，第二天恢复正常
    
    control_status_s current_filter; // 继电器受控的状态filter，当前事件（事件发生拉闸，事件结束合闸）
    control_status_s history_filter; // 继电器受控的状态filter，历史事件（只要事件发生过，就一直有效，直到事件解除并且管理员将历史事件清除成功。）

}ctrl_para_s;

/// @brief 控制模块数据
typedef struct
{
    uint16_t crc;
    uint16_t chk; 

    uint32_t         rly_off_cnt;         // 继电器拉闸次数
    uint32_t         ctrl_delay;          // 指令延时开延时，单位：秒
    rly_off_reason_s reason;              // 记录拉闸时刻的继电器受控拉闸原因
    control_status_s history_evt_stus;    // 控制继电器的历史状态
    ext_ctrl_stus_s  lst_ext_off;         // 上一次远程控制或者手动控制继电器拉闸状态字
    clock_s          rly_off_stamp;       // 上一次拉闸时间
    clock_s          keep_mode_end_time;  // 继电器保电模式结束时间
    ctrl_state_s     cur_state;           // 当前继电器控制逻辑状态
    ctrl_state_s     last_state;          // 上一次继电器控制逻辑状态
    remote_ctrl_s    delay_ctrl_type;     // 延时控制类型
    bool             keep_mode;           // 继电器保电模式，0：不保电，1：保电
    bool             relay_err;           // 继电器1故障
}ctrl_data_s;

struct control_t
{
    void (*reset)(uint8_t type);
    bool (*state_query)(TYPE_STUS_CONTROL state);
    void (*state_clr)(void);

    const ctrl_para_s* (*para_get)(RLY_TYPE_t type);
    bool (*para_set)(RLY_TYPE_t type, uint16_t ofst, const void* val);
    RLY_OUT_STATE (*out_state_get)(RLY_TYPE_t type);
    ctrl_state_s  (*ctrl_state_get)(uint8_t mode, RLY_TYPE_t type);
    ctrl_data_s* (*data_get)(RLY_TYPE_t type);
    uint32_t (*opt_code_get)(void);
    rly_off_reason_s (*disconnect_reason_get)(RLY_TYPE_t type, uint8_t state);
    void (*load_ctrl_clr)(RLY_TYPE_t type);
    bool (*his_ctrl_clr)(RLY_TYPE_t type);
    bool (*remote)(remote_ctrl_s type, RLY_TYPE_t type_rly, const uint8_t *dat);
    bool (*manual)(manual_ctrl_s type, RLY_TYPE_t rly_type);
    bool (*local)(local_ctrl_s   type, RLY_TYPE_t type_rly);
    bool (*test)(uint16_t on_period, uint16_t off_period, uint16_t cycle, RLY_TYPE_t type);

};
extern const struct control_t  control;
#endif

///
