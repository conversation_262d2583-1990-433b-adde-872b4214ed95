/**
 * @file RN831x_RN861x_MCU_V3_lib_api.h
 * @brief MCU V3 lib api header
 *
 * @copyright Copyright (c) 2024
 *
 * Copyright (C) , Renergy Technology Inc. All rights reserved.
 ******************************************************************************/
#ifndef RN831x_RN861x_MCU_V3_LIB_API_H
#define RN831x_RN861x_MCU_V3_LIB_API_H

// #include "RN831x_RN861x_MCU_V3_lib_def.h"

typedef struct RN_LIB_T_ {
    unsigned int            (*LL_SYSCLK_GetLibVersion)(void);
    unsigned int            (*LL_SYSCLK_GetCodeVersion)(void);
    eSysclkRet_TypeDef      (*LL_SYSCLK_SysModeChg)(eSysclkMode_TypeDef mode, eSysclkDiv_TypeDef cdiv);
    void                    (*LL_SYSCLK_FastSysModeChg)(eSysclkMode_TypeDef mode);
    void                    (*LL_SYSCLK_NvmActive)(void);
    void                    (*LL_SYSCLK_NvmSleep)(void);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_Dota0CfgSet)(unsigned short dota0);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_AlphalCfgSet)(unsigned short alphal);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_AlphahCfgSet)(unsigned short alphah);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_Xt0CfgSet)(unsigned short xt0);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_Remap4Sel)(unsigned char sel);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_Dota0CfgGet)(unsigned short *dota0);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_AlphalCfgGet)(unsigned short *alphal);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_AlphahCfgGet)(unsigned short *alphah);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_Xt0CfgGet)(unsigned short *xt0);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_RemapGet)(unsigned short *remap);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_SysConfCheck)(void);
    eSysoptionRet_TypeDef   (*LL_SYSOPTION_FlashVoltageTrim)(void);
    eMcuVer_TypeDef         (*LL_SYSOPTION_GetMcuVersion)(void);
    unsigned short          (*LL_RTC_ReadDota)(void);
    eRtcRet_TypeDef         (*LL_RTC_WriteDota)(unsigned short dota);
    eFlashRet_TypeDef       (*LL_FLASH_PageErase)(unsigned int pg);
    eFlashRet_TypeDef       (*LL_FLASH_SectorErase)(unsigned int sec);
    eFlashRet_TypeDef       (*LL_FLASH_Program)(unsigned int dst_addr, unsigned int src_addr, unsigned int len);
} rn_lib_t;

#endif
/* r1524 */
