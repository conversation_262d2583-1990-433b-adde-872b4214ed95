/**
 * @file RN821x_RN721x_SOC_lib_def.h
 * @brief SOC V2&V3 lib define header
 *
 * @copyright Copyright (c) 2024
 *
 * Copyright (C) , Renergy Technology Inc. All rights reserved.
 ******************************************************************************/
#ifndef RN821x_RN721x_SOC_LIB_DEF_H
#define RN821x_RN721x_SOC_LIB_DEF_H

typedef enum
{
    EEPROM_PASS = 0,
    EEPROM_COUNT_ERROR = 6,
    EEPROM_INVALID_PAGE = 8,
    EEPROM_SRC_ADDR_NOT_ALIGN = 10,
    EEPROM_SRC_ADDR_NOT_MAPPED = 11,
    EEPROM_DST_ADDR_NOT_ALIGN = 12,
    EEPROM_DST_ADDR_NOT_MAPPED = 13,
    <PERSON>EPROM_VOL_INVALID = 20,
    EEPROM_CLK_ERROR = 21
} eEepromRet_TypeDef;

typedef enum
{
    FLASH_PASS = 0,
    FLASH_COUNT_ERROR = 6,
    FLASH_INVALID_PAGE = 8,
    FLASH_SRC_ADDR_NOT_ALIGN = 10,
    FLASH_SRC_ADDR_NOT_MAPPED = 11,
    FLASH_DST_ADDR_NOT_ALIGN = 12,
    FLASH_DST_ADDR_NOT_MAPPED = 13,
    FLASH_VOL_INVALID = 20,
    FLASH_CLK_ERROR = 21
} eFlashRet_TypeDef;

typedef enum
{
    RTC_PASS = 0,
    RTC_FAIL = 1,
} eRtcRet_TypeDef;

typedef enum
{
    SYSCLK_PASS = 0,
    SYSCLK_FAIL = 1
} eSysclkRet_TypeDef;

typedef enum
{
    Clock_Losc = 0x00,
    Clock_RC_1M8 = 0x01,
    Clock_PLL_7M4 = 0x02,
    Clock_Hosc = 0x03,
    Clock_PLL_14M7 = 0x05,
    Clock_RC_29M5 = 0x06,
    Clock_PLL_29M5 = 0x07,
    Clock_RCL_32K = 0x08,
    Clock_PLL_58M9_DIV2 = 0x09,
    Clock_CHG_MAX = 0x0a,
} eSysclkMode_TypeDef;

typedef enum
{
    Clock_Div_1 = 0x00U,
    Clock_Div_2 = 0x01U,
    Clock_Div_4 = 0x02U,
    Clock_Div_8 = 0x03U,
    Clock_Div_MAX = 0x4U
} eSysclkDiv_TypeDef;

typedef enum
{
    SYSOPTION_PASS = 0,
    SYSOPTION_FAIL = 1
} eSysoptionRet_TypeDef;

typedef enum {
    MCU_VER_V21 = 0x21,
    MCU_VER_V22 = 0x22,
    MCU_VER_V3  = 0x30,
    MCU_VER_INVALID = 0xff
} eMcuVer_TypeDef;

#endif
/* r2356 */
