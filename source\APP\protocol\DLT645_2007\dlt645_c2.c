/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      dlt645_c2.c
 *    Describe:  DLT645-2007协议，02类数据部分
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#include "mic.h"
#include "status.h"
#include "DLT645_2007_id.h"

/// @brief 读取数据处理
/// @param p_info
/// @return
static uint16_t dlt_645_read_2(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t         *p_data = buff;
    volatile uint8_t ph     = true;    // 分相标志位
    uint8_t          harmonic_typ, harmonic_idx;

    if(buff == NULL)
    {
        p_data = p_info->snd_dat;
        memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    }
    switch(p_info->id)
    {
        case C2_M_VOL:    // @0x0201FF00  /// 所有相电压
            ph = false;
        case C2_A_VOL:    // @0x02010100  /// A相电压
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vrms[0] * 10), 2), p_data += 2;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_VOL:    // @0x02010200  /// B相电压
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vrms[1] * 10), 2), p_data += 2;
            if(ph == true) break;
        case C2_C_VOL:    // @0x02010300  /// C相电压
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vrms[2] * 10), 2), p_data += 2;
#endif
            break;

        case C2_M_CUR:    // @0x0202FF00  /// 所有相电流
            ph = false;
        case C2_A_CUR:    // @0x02020100  /// A相   电流
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->irms[0] * 1000), 3), p_data += 3;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_CUR:    // @0x02020200  /// B相   电流
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->irms[1] * 1000), 3), p_data += 3;
            if(ph == true) break;
        case C2_C_CUR:    // @0x02020300  /// C相   电流
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->irms[2] * 1000), 3), p_data += 3;
#endif
            break;

        case C2_M_INS_P:    // @0x0203FF00  /// 数据块有功功率
            ph = false;
        case C2_T_INS_P:    // @0x02030000  /// 总    有功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_p[0] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_A_INS_P:    // @0x02030100  /// A相   有功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_p[1] * 10000), 3), p_data += 3;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_INS_P:    // @0x02030200  /// B相   有功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_p[2] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_C_INS_P:    // @0x02030300  /// C相   有功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_p[3] * 10000), 3), p_data += 3;
#endif
            break;

        case C2_M_INS_Q:    // @0x0204FF00  /// 数据块无功功率
            ph = false;
        case C2_T_INS_Q:    // @0x02040000  /// 总    无功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_q[0] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_A_INS_Q:    // @0x02040100  /// A相   无功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_q[1] * 10000), 3), p_data += 3;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_INS_Q:    // @0x02040200  /// B相   无功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_q[2] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_C_INS_Q:    // @0x02040300  /// C相   无功功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_q[3] * 10000), 3), p_data += 3;
#endif
            break;

        case C2_M_INS_S:    // @0x0205FF00  /// 数据块视在功率
            ph = false;
        case C2_T_INS_S:    // @0x02050000  /// 总    视在功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_s[0] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_A_INS_S:    // @0x02050100  /// A相   视在功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_s[1] * 10000), 3), p_data += 3;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_INS_S:    // @0x02050200  /// B相   视在功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_s[2] * 10000), 3), p_data += 3;
            if(ph == true) break;
        case C2_C_INS_S:    // @0x02050300  /// C相   视在功率
            int32_to_lsbbcd(p_data, (int32_t)(mic.ins->pwr_s[3] * 10000), 3), p_data += 3;
#endif
            break;

        case C2_M_PF:    // @0x0206FF00  /// 数据块功率因素
            ph = false;
        case C2_T_PF:    // @0x02060000  /// 总    功率因素
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->pf[0] * 1000), 2), p_data += 2;
            if(ph == true) break;
        case C2_A_PF:    // @0x02060100  /// A相   功率因素
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->pf[1] * 1000), 2), p_data += 2;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_PF:    // @0x02060200  /// B相   功率因素
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->pf[2] * 1000), 2), p_data += 2;
            if(ph == true) break;
        case C2_C_PF:    // @0x02060300  /// C相   功率因素
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->pf[3] * 1000), 2), p_data += 2;
#endif
            break;

        case C2_M_ANGEL:    // @0x0207FF00  /// 数据块相角
            ph = false;
        case C2_A_ANGEL:    // @0x02070100  /// A相   相角
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vi_angle[0] * 10), 2), p_data += 2;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_ANGEL:    // @0x02070200  /// B相   相角
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vi_angle[1] * 10), 2), p_data += 2;
            if(ph == true) break;
        case C2_C_ANGEL:    // @0x02070300  /// C相   相角
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->vi_angle[2] * 10), 2), p_data += 2;
#endif
            break;
#if HARMONIC_WAVE
        case C2_M_VOL_THD:    // @0x0208FF00  /// 数据块电压波形失真度
            ph = false;
        case C2_A_VOL_THD:    // @0x02080100  /// A相   电压波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->v_thd[0] * 10000), 2), p_data += 2;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_VOL_THD:    // @0x02080200  /// B相   电压波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->v_thd[1] * 10000), 2), p_data += 2;
            if(ph == true) break;
        case C2_C_VOL_THD:    // @0x02080300  /// C相   电压波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->v_thd[2] * 10000), 2), p_data += 2;
#endif
            break;

        case C2_M_CUR_THD:    // @0x0209FF00  /// 数据块电流波形失真度
            ph = false;
        case C2_A_CUR_THD:    // @0x02090100  /// A相   电流波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->i_thd[0] * 10000), 2), p_data += 2;
#ifdef POLYPHASE_METER
            if(ph == true) break;
        case C2_B_CUR_THD:    // @0x02090200  /// B相   电流波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->i_thd[1] * 10000), 2), p_data += 2;
            if(ph == true) break;
        case C2_C_CUR_THD:    // @0x02090300  /// C相   电流波形失真度
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->i_thd[2] * 10000), 2), p_data += 2;
#endif
            break;
#endif
        case C2_N_CUR:    // @0x02800001  /// 零线电流
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->n_irms * 1000), 3), p_data += 3;
            break;

        case C2_FREQUENCY:    // @0x02800002  /// 电网频率
            uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->freq * 100), 2), p_data += 2;
            break;
        case C2_AVR_POWER_1M:    // @0x02800003  /// 一分钟有功平均功率

            break;
        case C2_CUR_DM_kW:    // @0x02800004  /// 当前有功需量
            break;
        case C2_CUR_DM_kva:    // @0x02800005  /// 当前无功需量
            break;
        case C2_CUR_DM_kV:    // @0x02800006  /// 当前视在需量
            break;
        case C2_TEMPARETURE:    // @0x02800007  /// 温度
            int32_to_lsbbcd(p_data, (int32_t)mstatus.temperature_get(), 2), p_data += 2;
            break;
        case C2_INTBAT_VOL:    // @0x02800008  /// 内部时钟电池电压
            uint32_to_lsbbcd(p_data, (uint32_t)mstatus.battery_voltage_get(1) / 10, 2), p_data += 2;
            break;
        case C2_EXTBAT_VOL:    // @0x02800009  /// 外部抄表电池电压
            uint32_to_lsbbcd(p_data, (uint32_t)mstatus.battery_voltage_get(0) / 10, 2), p_data += 2;
            break;
        case C2_INTBAT_RUN_TIME:    // @0x0280000A  /// 内部电池工作时间

            break;
        case C2_CUR_STEP_PRICE:    // @0x0280000B  /// 当前阶梯电价

            break;
        default:
#if HARMONIC_WAVE
            if((ITEM(p_info->id) == ITEM(C2_A_VOL_HARMONIC(1))) || (ITEM(p_info->id) == ITEM(C2_B_VOL_HARMONIC(1))) || (ITEM(p_info->id) == ITEM(C2_C_VOL_HARMONIC(1))))
            {
                // 三相电压 1-21次谐波
                harmonic_typ = ((p_info->id & 0x0000FF00) >> 8) - 1;
                harmonic_idx = (p_info->id & 0x000000FF) - 1;
                if((harmonic_idx + 1) == 0xFF)
                {
                    // 电压谐波数据块
                    for(uint8_t i = 0; i < 21; i++) { uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->v_harmonic_per[harmonic_typ][i] * 10000), 2), p_data += 2; }
                }
                else { uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->v_harmonic_per[harmonic_typ][harmonic_idx] * 10000), 2), p_data += 2; }
            }
            else if((ITEM(p_info->id) == ITEM(C2_A_CUR_HARMONIC(1))) || (ITEM(p_info->id) == ITEM(C2_B_CUR_HARMONIC(1))) || (ITEM(p_info->id) == ITEM(C2_C_CUR_HARMONIC(1))))
            {
                // 三相电流 1-21次谐波
                harmonic_typ = ((p_info->id & 0x0000FF00) >> 8) - 1;
                harmonic_idx = (p_info->id & 0x000000FF) - 1;
                if((harmonic_idx + 1) == 0xFF)
                {
                    // 电流谐波数据块
                    for(uint8_t i = 0; i < 21; i++) { uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->i_harmonic_per[harmonic_typ][i] * 10000), 2), p_data += 2; }
                }
                else { uint32_to_lsbbcd(p_data, (uint32_t)(mic.ins->i_harmonic_per[harmonic_typ][harmonic_idx] * 10000), 2), p_data += 2; }
            }
#endif
            break;
    }
    if(buff == NULL)
    {
        if((p_data - p_info->snd_dat) == 4)
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE;
            return 1;
        }    // 无数据
    }
    else
    {
        if(p_data == buff) return 0;
    }
    return (uint16_t)(p_data - p_info->snd_dat);
}

/// @brief 读取数据格式
static data_format_s dlt_645_data2_format_get(uint32_t id)
{
    data_format_s format;
    format.len  = 4;
    format.type = DT_OCTET_STRING;
    format.unit = energy_unit_get(id);
    return format;
}

/// @brief 用于曲线捕获获取数据
/// @param id
/// @param p_data
/// @return
static uint16_t c2_data_get(uint32_t id, void *p_data)
{
    uint8_t          *ptr = (uint8_t *)p_data;
    uint16_t          len;
    DLT645_2007_MSG_S p_info;

    if(ptr == NULL) return 0;
    p_info.id = id;
    len       = dlt_645_read_2(&p_info, ptr);
    if(len < 4) return 0;
    len -= 4;
    memcpy(ptr, ptr + 4, len);    // 去掉ID
    return len;
}

const dlt645_data_s c2_data = {
    .format = dlt_645_data0_format_get,
    .data   = c0_data_get,
};

/// end of file
