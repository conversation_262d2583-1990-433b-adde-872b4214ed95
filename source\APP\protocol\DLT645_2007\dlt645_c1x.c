/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:  dlt645_c1x.c
*    Describe:  DLT645-2007协议，07协议扩展协议，事件记录部分    
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "mic.h"
#include "status.h"
#include "DLT645_2007_id.h"
#include "profile_capture_obj.h"
#include "profile.h"
#include "..\..\config\app_config.h"

#define EVT_TYPE(i)   ((uint8_t)(i >> 24))

/// @brief  读取数据处理
/// @param  p_info 
/// @return 
static uint16_t dlt_645_read_1x(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data      = buff;
    // evt_type_t evt_type  = (evt_type_t)(p_info->id >> 24);  //事件类型
    uint8_t ph   = (uint8_t)(p_info->id >> 16);  //相
    uint8_t dtyp = (uint8_t)(p_info->id >> 8);   //数据项
    uint8_t point= (uint8_t)(p_info->id);        //次数

    if(buff == NULL) //通讯获取数据
    {
        p_data = p_info->snd_dat;
        memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    }

    if(dtyp == 0 || (p_info->id == C10_LAST_LOS_VOL_TMR_S) || (p_info->id == C10_LAST_LOS_VOL_TMR_E)) //次数累计时间
    {
        uint32_t temp32;
        switch(p_info->id )
        {
        #if EVENT_LOSS_VOL_EN
            case C10_LOS_VOL_CNT       :      /// 失压总次数
            case C10_LOS_VOL_A_CNT     :      /// A相失压总次数
            case C10_LOS_VOL_B_CNT     :      /// B相失压总次数
            case C10_LOS_VOL_C_CNT     :      /// C相失压总次数
                temp32 = power_event.pwrdn_data_get()->vol_loss_cnt[ph]; 
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C10_LOS_VOL_TMR       :      /// 失压总累计时间
            case C10_LOS_VOL_A_TMR     :      /// A相失压总累计时间
            case C10_LOS_VOL_B_TMR     :      /// B相失压总累计时间
            case C10_LOS_VOL_C_TMR     :      /// C相失压总累计时间
                temp32 = power_event.pwrdn_data_get()->vol_loss_time_cnt[ph] / 60; //单位：分钟
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C10_LAST_LOS_VOL_TMR_S:      /// 最近 1 次失压发生时刻
                mclock.gseconds_to645(p_data, power_event.pwrdn_data_get()->vol_loss_time_s, CLOCK_YMDhms), p_data += 6;
                break;
            case C10_LAST_LOS_VOL_TMR_E:      /// 最近 1 次失压结束时刻
                mclock.gseconds_to645(p_data, power_event.pwrdn_data_get()->vol_loss_time_e, CLOCK_YMDhms), p_data += 6;
                break;
        #endif    
        #if EVENT_LOW_VOL_EN
            case C11_LOW_VOL_A_CNT  :         /// A相欠压总次数
            case C11_LOW_VOL_B_CNT  :         /// B相欠压总次数
            case C11_LOW_VOL_C_CNT  :         /// C相欠压总次数
                temp32 = power_event.pwrdn_data_get()->vol_low_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C11_LOW_VOL_A_TMR  :         /// A相欠压总累计时间
            case C11_LOW_VOL_B_TMR  :         /// B相欠压总累计时间
            case C11_LOW_VOL_C_TMR  :         /// C相欠压总累计时间
                temp32 = power_event.pwrdn_data_get()->vol_low_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif    
        #if EVENT_OVR_VOL_EN
            case C12_OVR_VOL_A_CNT  :         /// A相过压总次数
            case C12_OVR_VOL_B_CNT  :         /// B相过压总次数
            case C12_OVR_VOL_C_CNT  :         /// C相过压总次数
                temp32 = power_event.pwrdn_data_get()->vol_ovr_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C12_OVR_VOL_A_TMR  :         /// A相过压总累计时间
            case C12_OVR_VOL_B_TMR  :         /// B相过压总累计时间
            case C12_OVR_VOL_C_TMR  :         /// C相过压总累计时间
                temp32 = power_event.pwrdn_data_get()->vol_ovr_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_MISS_VOL_EN
            case C13_MIS_VOL_A_CNT  :         /// A相断相总次数
            case C13_MIS_VOL_B_CNT  :         /// B相断相总次数
            case C13_MIS_VOL_C_CNT  :         /// C相断相总次数
                temp32 = power_event.pwrdn_data_get()->ph_miss_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C13_MIS_VOL_A_TMR  :         /// A相断相总累计时间
            case C13_MIS_VOL_B_TMR  :         /// B相断相总累计时间
            case C13_MIS_VOL_C_TMR  :         /// C相断相总累计时间
                temp32 = power_event.pwrdn_data_get()->ph_miss_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_V_REV_SQR_EN
            case C14_VOL_REV_SEQ_CNT:         /// 电压逆相序总次数
                temp32 = power_event.pwrdn_data_get()->vol_rev_sqr_cnt;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C14_VOL_REV_SEQ_TMR:         /// 电压逆相序总累计时间
                temp32 = power_event.pwrdn_data_get()->vol_rev_sqr_time_cnt / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_I_REV_SQR_EN
            case C15_CUR_REV_SEQ_CNT:         /// 电流逆相序总次数
                temp32 = power_event.pwrdn_data_get()->i_rev_sqr_cnt;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C15_CUR_REV_SEQ_TMR:         /// 电流逆相序总累计时间
                temp32 = power_event.pwrdn_data_get()->i_rev_sqr_time_cnt / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_V_UNB_EN  
            case C16_VOL_UNB_CNT    :         /// 电压不平衡总次数
                temp32 = power_event.pwrdn_data_get()->vol_unb_cnt;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C16_VOL_UNB_TMR    :         /// 电压不平衡总累计时间
                temp32 = power_event.pwrdn_data_get()->vol_unb_time_cnt / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_I_UNB_EN 
            case C17_CUR_UNB_CNT    :         /// 电流不平衡总次数
                temp32 = power_event.pwrdn_data_get()->i_unb_cnt;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C17_CUR_UNB_TMR    :         /// 电流不平衡总累计时间
                temp32 = power_event.pwrdn_data_get()->i_unb_time_cnt / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_LOS_CUR_EN   
            case C18_LOS_CUR_A_CNT  :         /// A相失流总次数
            case C18_LOS_CUR_B_CNT  :         /// B相失流总次数
            case C18_LOS_CUR_C_CNT  :         /// C相失流总次数
                temp32 = power_event.pwrdn_data_get()->i_loss_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C18_LOS_CUR_A_TMR  :         /// A相失流总累计时间
            case C18_LOS_CUR_B_TMR  :         /// B相失流总累计时间
            case C18_LOS_CUR_C_TMR  :         /// C相失流总累计时间
                temp32 = power_event.pwrdn_data_get()->i_loss_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_OVR_CUR_EN  
            case C19_OVR_CUR_A_CNT  :         /// A相过流总次数
            case C19_OVR_CUR_B_CNT  :         /// B相过流总次数
            case C19_OVR_CUR_C_CNT  :         /// C相过流总次数
                temp32 = power_event.pwrdn_data_get()->i_ovr_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
            case C19_OVR_CUR_A_TMR  :         /// A相过流总累计时间
            case C19_OVR_CUR_B_TMR  :         /// B相过流总累计时间
            case C19_OVR_CUR_C_TMR  :         /// C相过流总累计时间
                temp32 = power_event.pwrdn_data_get()->i_ovr_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        #if EVENT_MISS_CUR_EN  
            case C1A_MIS_VOL_A_CNT  :         /// A相断流总次数
            case C1A_MIS_VOL_B_CNT  :         /// B相断流总次数
            case C1A_MIS_VOL_C_CNT  :         /// C相断流总次数
                temp32 = power_event.pwrdn_data_get()->i_miss_cnt[ph];
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;

            case C1A_MIS_VOL_A_TMR  :         /// A相断流总累计时间
            case C1A_MIS_VOL_B_TMR  :         /// B相断流总累计时间
            case C1A_MIS_VOL_C_TMR  :         /// C相断流总累计时间
                temp32 = power_event.pwrdn_data_get()->i_miss_time_cnt[ph] / 60;
                uint32_to_lsbbcd(p_data, temp32, 3), p_data += 3;
                break;
        #endif
        }
    }
    else
    {
        evt_type_t rcd_typ = EVENT_TYPE_NUM;
        
        switch(EVT_TYPE(p_info->id))
        {
        #if EVENT_LOSS_VOL_EN 
            case 0x10: // 失压
                rcd_typ = (evt_type_t)(EVENT_TYPE_LOSS_VOL_A + ph -1);
                break;
        #endif
        #if EVENT_LOW_VOL_EN
            case 0x11: // 欠压
                rcd_typ = (evt_type_t)(EVENT_TYPE_LOW_VOL_A + ph -1);
                break;
        #endif
        #if EVENT_OVR_VOL_EN 
            case 0x12: // 过压
                rcd_typ = (evt_type_t)(EVENT_TYPE_OVR_VOL_A + ph -1);
                break;
        #endif
        #if EVENT_MISS_VOL_EN
            case 0x13: // 断相
                rcd_typ = (evt_type_t)(EVENT_TYPE_MISS_VOL_A + ph -1);
                break;
        #endif
        #if EVENT_V_REV_SQR_EN
            case 0x14: // 电压逆序  
                rcd_typ = EVENT_TYPE_V_REV_SQR;
                break;
        #endif
        #if EVENT_I_REV_SQR_EN
            case 0x15: // 电流逆序
                rcd_typ = EVENT_TYPE_I_REV_SQR;
                break;
        #endif
        #if EVENT_V_UNB_EN
            case 0x16: // 电压不平衡
                rcd_typ = EVENT_TYPE_V_UNB;
                break;
        #endif
        #if EVENT_I_UNB_EN
            case 0x17: // 电流不平衡
                rcd_typ = EVENT_TYPE_I_UNB;
                break;
        #endif
        #if EVENT_LOS_CUR_EN
            case 0x18: // 失流
                rcd_typ = (evt_type_t)(EVENT_TYPE_LOS_CUR_A + ph -1);
                break;
        #endif
        #if EVENT_OVR_CUR_EN
            case 0x19: // 过流
                rcd_typ = (evt_type_t)(EVENT_TYPE_OVR_CUR_A + ph -1);
                break;
        #endif
        #if EVENT_MISS_CUR_EN
            case 0x1A: // 断流
                rcd_typ = (evt_type_t)(EVENT_TYPE_MISS_CUR_A + ph -1);
                break;
        #endif
            default: break;
        }
        if(rcd_typ < EVENT_TYPE_NUM)
        {
            p_data += profile.evt_get(p_data, point, rcd_typ, dtyp);
        }
    }
    if(buff == NULL)
    {
        if((p_data - p_info->snd_dat) == 4) 
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
            return 1;
        } // 无数据
        return (uint16_t)(p_data - p_info->snd_dat);
    }
    return (uint16_t)(p_data - buff);
}


/// end of file
