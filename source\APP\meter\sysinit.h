/**
 ******************************************************************************
* @file    sysinit.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __SYSINIT_H
#define __SYSINIT_H

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"

/* Exported types ------------------------------------------------------------*/
/* API清零方式 */
typedef enum
{
    SYS_CLR_NULL = 0,         // 无效清零
    SYS_GROBAL_CLR,           // 整个电表数据复位
    SYS_PARA_CLR,             // 电表参数清零
    SYS_DATA_CLR,             // 电表运行数据清零
    SYS_FD_STUS_CLR,          // 清窃电
    SYS_EVT_CLR,              // 清事件
    SYS_LC_CLR,               // 清负荷曲线(包括日结算曲线)
    SYS_BL_CLR,               // 清月结曲线
    SYS_CTRL_STUS_CLR,        // 清继电器控制状态
    SYS_MD_CLR,               // 清需量
    SYS_CLR_NUM,              // 清零类型数
} SysClearType_t;

/* Exported defines ----------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
typedef uint16_t INIT_STATE;
#define INIT_EE_ERROR            ((INIT_STATE)1 << 1 ) // EEPROM错误
#define INIT_DF_ERROR            ((INIT_STATE)1 << 2 ) // 外部DATAFLASH错误
#define INIT_NVM_DATA_LOSS       ((INIT_STATE)1 << 3 ) // NVM数据丢失
#define INIT_PPM_ERROR           ((INIT_STATE)1 << 4 ) // 外部主时钟源有问题
#define INIT_LSE_ERROR           ((INIT_STATE)1 << 5 ) // RTC晶振源(32768)失效
#define INIT_RTC_LOSE            ((INIT_STATE)1 << 6 ) // 硬件RTC丢失(时钟电源不足)
#define INIT_ABNORMAL_RST        ((INIT_STATE)1 << 7 ) // 异常复位
#define INIT_GLOBAL_CLEAR        ((INIT_STATE)1 << 8)  // 全局清0-恢复出厂状态
#define INIT_DATA_CLEAR          ((INIT_STATE)1 << 9)  // 数据清0
#define INIT_PARA_CLEAR          ((INIT_STATE)1 << 10) // 参数恢复默认值
#define INIT_POR_BOR             ((INIT_STATE)1 << 11) // POR/BOR复位,冷启动
#define INIT_MD_CLEAR            ((INIT_STATE)1 << 12) // 需量清0

/* Exported variables --------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
struct sysinit_s
{
    bool (*reset)(SysClearType_t type);
    void (*self_checking)(void);
    bool (*state_query)(INIT_STATE state);
    void (*state_clr)(void);
};
extern const struct sysinit_s sysinit;

#endif /* __SYSINIT_H */

