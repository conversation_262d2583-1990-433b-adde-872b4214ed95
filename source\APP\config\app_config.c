/**
 ******************************************************************************
 * @file    app_config.c
 * <AUTHOR> @date    2024
 * @brief   电表默认参数配置
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "app_config.h"
#include "status.h"
#include "timeapp.h"
#include "bsp_cfg.h"
#include "local_port.h"
#include "tariff.h"
#if SW_PAYMENT_EN
#include "step_tariff.h"
#include "payment.h"
#endif
#include "dispApp.h"
#include "demand.h"
#include "power_event.h"
#include "mic.h"
#include "billing.h"
#include "loadcurve.h"
#include "energy.h"
#include "api.h"
#include "RelayApp.h"

/// @brief led，按键，蜂鸣器控制掩码默认值，0：关闭，1：开启
const StatusPara_s status_default_para = {
    .led_ctrl_filter =
        {
            .top_cov_opn = 0,
            .rly_ready   = 1,    /// 预合闸
        },
    .beep_ctrl_filter =
        {
            .over_load = 0,
        },
};

const energy_para_s energy_default_para = {
    // bit3-反向有功 (0 不减，1 减)
    // bit2-反向有功 (0 不加，1 加)
    // bit1-正向有功 (0 不减，1 减)
    // bit0-正向有功 (0 不加，1 加)
    .comb_kWh_code = 0x05,    // 默认正+反

    // bit7-IV  象限 (0 不减，1 减)
    // bit6-IV  象限 (0 不加，1 加)1
    // bit5-III 象限 (0 不减，1 减)
    // bit4-III 象限 (0 不加，1 加)1
    // bit3-II  象限 (0 不减，1 减)
    // bit2-II  象限 (0 不加，1 加)1
    // bit1-I   象限 (0 不减，1 减)
    // bit0-I   象限 (0 不加，1 加)1
    .comb1_kvarh_code = 0x41,    // 默认 I + IV
    .comb2_kvarh_code = 0x14,    // 默认 II + III
};
/// @brief 时钟默认参数配置
const ClockPara_s clock_default_para = {
    .rtc_ppm         = 0,                                // RTC 误差 ppm
    .bc_limit_max    = BROADCAST_CLOCK_ADJ_LIMIT_MAX,    // 接受广播校时的最大偏差，超过不接受校时，并置时钟错误（一天最多一次事件）
    .bc_limit_min    = BROADCAST_CLOCK_ADJ_LIMIT_MIN,    // 接受广播校时的最小偏差，小于不接受校时
    .shift_limit_max = 0,                                // 接受校时的最大偏差，超过不接受校时, 0：不限制
    .shift_limit_min = 0,                                // 接受校时的最小偏差，小于不接受校时, 0：不限制
    .bc_limit_num    = BROADCAST_CLOCK_ADJ_NUM_LIMIT,    // 每天接受广播校时的次数，超过不接受校时
    .shift_limit_num = 0,                                // 每天接受校时的次数，超过不接受校时 0：不限制
};

/// @brief 通讯口默认参数配置
/// 通道顺序与comm_phy.h中定义的通道顺序一致, 不要乱序
const LocalPortPara_s local_port_default_para[] = {
#ifdef COM_IR
    /// 红外通道
    {
        .inactivity_timeout = 180,    // 空闲超时时间,单位秒
        .hangup_cnt_thd     = 200,
        .auth_timeout       = 5,    // 校验超时时间,单位分钟
        .baud_rate          = 2,    // 波特率,1200
        .check_bits         = 0,    // 校验位,8E1
        .modbus_addr        = 0,    // modbus地址,0 不启用
        .modbus_delay       = 0,    // modbus发送延时,0
    },
#endif
#ifdef COM_BLE
    /// 蓝牙通道
    {
        .inactivity_timeout = 180,    // 空闲超时时间,单位秒
        .hangup_cnt_thd     = 200,
        .auth_timeout       = 5,    // 校验超时时间,单位分钟
        .baud_rate          = 9,    // 波特率,115200
        .check_bits         = 0,    // 校验位,8E1
        .modbus_addr        = 0,    // modbus地址,0 不启用
        .modbus_delay       = 0,    // modbus发送延时,0
    },
#endif
#ifdef COM_MODULE
    /// 模块通道
    {
        .inactivity_timeout = 180,    // 空闲超时时间,单位秒
        .hangup_cnt_thd     = 200,
        .auth_timeout       = 5,    // 校验超时时间,单位分钟
        .baud_rate          = 5,    // 波特率,9600
        .check_bits         = 0,    // 校验位,8E1
        .modbus_addr        = 0,    // modbus地址,0 不启用
        .modbus_delay       = 0,    // modbus发送延时,0
    },
#endif
#ifdef COM_RS4851
    /// 485 1通道
    {
        .inactivity_timeout = 180,    // 空闲超时时间,单位秒
        .hangup_cnt_thd     = 200,
        .auth_timeout       = 5,    // 校验超时时间,单位分钟
        .baud_rate          = 5,    // 波特率,9600
        .check_bits         = 0,    // 校验位,8E1
        .modbus_addr        = 1,    // modbus地址,1
        .modbus_delay       = 0,    // modbus发送延时,0
    },
#endif
#ifdef COM_RS4852
    /// 485 2通道
    {
        .inactivity_timeout = 180,    // 空闲超时时间,单位秒
        .hangup_cnt_thd     = 200,
        .auth_timeout       = 5,    // 校验超时时间,单位分钟
        .baud_rate          = 5,    // 波特率,9600
        .check_bits         = 0,    // 校验位,8E1
        .modbus_addr        = 0,    // modbus地址,0 不启用
        .modbus_delay       = 0,    // modbus发送延时,0
    },
#endif
};

/// @brief 费率模块默认参数配置
const tariff_data_s tariff_default_data = {
    .cur_zone   = 1,
    .cur_day_id = 1,
    .cur_rate   = 1,
    .is_weekend = 0,
    .is_holiday = 0,
};

/// 默认费率参数
const tariff_para_s tariff_default_para = { // +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        .calendar_para = //
            {
                .zonel_activate_time = {.cale = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, .stus.value = 0x00, .u32datetime = 0xFFFFFFFF},    /// 备用套时区切换时间
                .dayl_activate_time  = {.cale = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, .stus.value = 0x00, .u32datetime = 0xFFFFFFFF},    /// 备用套日表切换时间
            },

        .zone_list =
            {
                .zone_num = 1,    /// 时区个数，默认0个有效时区，最大可配置TARIFF_ZONE_NUM 14个。
                .zone =
                    {
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                        {.date = {1, 1}, .day_id = 1},    /// 时区号，起始月日，日表序号
                    },
            },

        .day_list =
            {
                .day_num = 1,    /// 日表个数，默认1个有效日表，最大可配置TARIFF_SCH_TAB_NUM 8个。
                .tf_num  = 8,    /// 日表有效费率数，默认1个有效费率，最大可配置TARIFF_RATE_NUM 12个。
                .day =
                    {
                        {
                            .day_id       = 1,     /// 日表序号
                            .schedule_num = 10,    /// 有效的费率数段数，默认4个有效时段(尖峰平谷)，最大TARIFF_PERIOD_NUM 14个。
                            .action =
                                {
                                    {.start_time = {0, 0}, .script_selector = 4},     /// 时分，费率号1
                                    {.start_time = {7, 0}, .script_selector = 3},     /// 时分，费率号1
                                    {.start_time = {8, 0}, .script_selector = 2},     /// 时分，费率号1
                                    {.start_time = {11, 0}, .script_selector = 3},    /// 时分，费率号1
                                    {.start_time = {15, 0}, .script_selector = 2},    /// 时分，费率号1
                                    {.start_time = {19, 0}, .script_selector = 1},    /// 时分，费率号1
                                    {.start_time = {22, 0}, .script_selector = 3},    /// 时分，费率号1
                                    {.start_time = {23, 0}, .script_selector = 4},    /// 时分，费率号1
                                    {.start_time = {23, 0}, .script_selector = 4},    /// 时分，费率号1
                                    {.start_time = {23, 0}, .script_selector = 4},    /// 时分，费率号1
                                },
                        },
                        // {
                        //     .day_id = 2,        /// 日表序号
                        //     .schedule_num = 4,  /// 有效的日时段数,最大TARIFF_PERIOD_NUM 14个。
                        //     .action =
                        //     {
                        //         { .start_time = {0,  0}, .script_selector = 1 },  /// 时分，费率号1
                        //         { .start_time = {6,  0}, .script_selector = 2 },  /// 时分，费率号2
                        //         { .start_time = {12, 0}, .script_selector = 3 },  /// 时分，费率号3
                        //         { .start_time = {18, 0}, .script_selector = 4 },  /// 时分，费率号4
                        //     },
                        // },
                    },
            },

        .week_list = {{.sun = 0, .mon = 1, .tue = 1, .wed = 1, .thu = 1, .fri = 1, .sat = 0}, .day_id = 0xFF},    /// 周休日特征字，启用日表序号

        .special_day_list =
            {
                .entry_num = 0,    /// 特殊日表个数，默认0个有效特殊日，最大可配置TARIFF_HOLIDAY_NUM 50个。
                .entry =
                    {
                        /// 特殊日编号,特殊日开始日期,日表序号
                        {.date = {24, 1, 1}, .day_id = 1},
                        {.date = {24, 2, 2}, .day_id = 1},
                    },
            }    //
};

#if SW_PAYMENT_EN
/// 阶梯费率模块默认参数配置
const step_tariff_para_s step_tariff_default_para = {
    .active_tf =
        {
            .tariff_num = 1,      // 最大设置 TARIFF_RATE_NUM 12个
            .price      = {0},    /// 价格，单位0.0001元
        },
    .passive_tf =
        {
            .tariff_num = 1,
            .price      = {0, 0, 0, 0, 0, 0, 0, 0},    /// 价格，单位0.0001元
        },
    .active_step =
        {
            .step_num = 1,    // 最大 STEP_TARIFF_NUM 7个
            .value    = {99999999, 99999999, 99999999, 99999999, 99999999, 99999999, 99999999},
            .price    = {10000, 10000, 10000, 10000, 10000, 10000, 10000},    /// 价格，单位0.0001元
            .bill_time =
                {
                    {0x99, 0x99, 0x99},    /// 最大四个结算日执行年阶梯有效
                    {0x99, 0x99, 0x99},
                    {0x99, 0x99, 0x99},
                    {0x99, 0x99, 0x99},
                },
        },
    .passive_step =
        {
            .step_num = 1,    // 最大 STEP_TARIFF_NUM 7个
            .value    = {99999999, 99999999, 99999999, 99999999, 99999999, 99999999, 99999999},
            .price    = {10000, 10000, 10000, 10000, 10000, 10000, 10000},    /// 价格，单位0.01元
            .bill_time =
                {
                    {0x99, 0x99, 0x99},    /// 最大四个结算日执行年阶梯有效
                    {0x99, 0x99, 0x99},
                    {0x99, 0x99, 0x99},
                    {0x99, 0x99, 0x99},
                },
        },
    .switch_time_tf   = {.cale = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, .stus.value = 0x00, .u32datetime = 0xFFFFFFFF},    /// 备用套费率切换时间
    .switch_time_step = {.cale = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, .stus.value = 0x00, .u32datetime = 0xFFFFFFFF},    /// 备用套阶梯切换时间
};

const pay_para_s pay_default_para = {
    .warning_threshold1   = {.remain = 0, .data = DEF_LOW_CRDT_LVL1},
    .warning_threshold2   = {.remain = 0, .data = DEF_LOW_CRDT_LVL2},
    .preset_credit_amount = {.remain = 0, .data = 0},
    .connect_threshold    = {.remain = 0, .data = 100},
    .max_credit           = 99999999,      // 最大囤积门限 0.01
    .max_vend             = 0x7FFFFFFF,    // 单次最大购电门限,默认不做限制 0.01
};

#endif

/// @brief billing 结算模块默认参数配置
const billing_para_s bl_default_para = {
    .month_billing_time =
        {
            {01, 00},    /// 默认一个结算日 每月1号0时0分执行结算
            {99, 99},    /// 结算月份，年月
            {99, 99},    /// 结算月份，年月
        },
    .daily_frozen_time = {0x00, 0x00},    /// 冻结时间，时分,默认0时0分
};

#if (LC1_ENABLE || LC2_ENABLE)
const lc_para_s lc_default_para = {
    .period =
        {
            LC1_PERIOD,
#if LC2_ENABLE
            LC2_PERIOD,
#endif
        },
    .start_time =
        {
            .year       = 99,  // 通配
            .month      = 1,//
            .day        = 1,//
            .hour       = 0,//
            .minute     = 0,//
            .second     = 0,//
            .stus.value = 0,//
        },    // 没有要求时，默认值定在程序发布时间后
    .mode = {.ins = 1, .pwr = 1, .pf = 1, .eng = 1, .quadrant = 0, .md = 0},
};
#endif
/// @brief display 显示模块默认参数+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
const disp_para_s disp_default_para = {
    .req_filter =
        {
            .open_cov = 1,    // 开盖
            .p_over   = 1,    // 超功率拉闸
        },
    .time =
        {
            .lcd_pow_off_time = 0,                       // 液晶掉电显示时间
            .auto_Period      = 3,                       // 自动轮显周期
            .mode_outtime     = 30,                      // 模式切换时间
            .alarm_time       = 3,                       // 报警时间
            .backlight_time   = 60,                      // 背光时间
            .pwdn_auto_Period = 5,                       // 掉电轮显周期-每一屏显示时间
            .pwon_all_disp    = DISP_PWRON_DISP_TIME,    // 开机全屏显示时间，默认5秒，5-30秒可设置
        },
    .spe =
        {
            .spe_format =
                {
                    .energy_lz  = 0,    // 电能值显示前导零 1 显示 0 不显示
                    .power_lz   = 0,    // 功率值显示前导零 1 显示 0 不显示
                    .current_lz = 0,    // 电流值显示前导零 1 显示 0 不显示
                    .voltage_lz = 0,    // 电压值显示前导零 1 显示 0 不显示

                    .energy_decimal  = 2,    // 电能显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
                    .power_decimal   = 4,    // 功率，需量显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
                    .current_decimal = 2,    // 电流显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
                    .voltage_decimal = 2,    // 电压显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
                    .power_dimen     = 1,    // 功率，需量显示单位(0 W,1 KW, 2 MW)
                    .energy_dimen    = 1,    // 电能显示单位(0 WH, 1 KWH, 2 MWH)
                    .current_dimen   = 0,    // 电流显示单位(0 A,1 KA)
                    .voltage_dimen   = 0,    // 电压显示单位(0 V, 1 KV)
                    .primary_side    = 1,    // 电压,电流，功率，电能 显示(0 二次侧，1 一次侧)
                },
        },
};

const demand_para_s demand_default_para = {
    .period      = DEMAND_DEF_PERIOD,
    .slip_period = DEMAND_DEF_SLIP_PERIOD,
};

/// @brief power event 电源事件模块默认参数+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
const pe_para_s pe_default_para = {
    .crc = 0,
/// 判断条件
#if EVENT_LOSS_VOL_EN
    .vol_loss_thd_v_h = (uint16_t)(0.78 * INPUT_VOLTAGE * 10),       // 0.1V 失压事件电压触发上限
    .vol_loss_thd_v_l = (uint16_t)(0.85 * INPUT_VOLTAGE * 10),       // 失压事件电压恢复下限
    .vol_loss_thd_i   = (uint32_t)(0.005 * BASE_CURRENT * 10000),    // 0.0001A 失压事件电流触发下限
#endif

#if EVENT_LOW_VOL_EN
    .vol_low_thd_v = (uint16_t)(0.78 * INPUT_VOLTAGE * 10),    // 欠压事件电压触发上限
#endif

#if EVENT_OVR_VOL_EN
    .vol_ovr_thd_v = (uint16_t)(1.2 * INPUT_VOLTAGE * 10),    // 过压事件电压触发上限
#endif

#if EVENT_MISS_VOL_EN
    .vol_miss_thd_v = MIN_WORK_VOLTAGE,                            // 断相事件电压触发上限
    .vol_miss_thd_i = (uint32_t)(0.005 * BASE_CURRENT * 10000),    // 断相事件电流触发上限
#endif
#if EVENT_ALL_LOSS_VOL_EN
    .vol_all_mis_thd_v = MIN_WORK_VOLTAGE,                           // 全失压事件电压触发
    .vol_all_mis_thd_i = (uint32_t)(0.05 * BASE_CURRENT * 10000),    // 全失压事件电流触发
#endif
#if EVENT_V_UNB_EN
    .vol_unb_thd = 3000,    // 30% 电压不平衡率限值
#endif

#if EVENT_I_UNB_EN
    .i_unb_thd = 3000,    // 电流不平衡率限值
#endif

#if EVENT_LOS_CUR_EN
    .i_loss_thd_v   = (uint16_t)(0.7 * INPUT_VOLTAGE * 10),        // 失流事件电压触发上限
    .i_loss_thd_i_h = (uint32_t)(0.05 * BASE_CURRENT * 10000),     // 失流事件电流触发上限
    .i_loss_thd_i_l = (uint32_t)(0.005 * BASE_CURRENT * 10000),    // 失流事件电流触发下限
#endif

#if EVENT_OVR_CUR_EN
    .i_ovr_thd_i = (uint32_t)(1.2 * MAXIMUM_CURRENT * 10),    // 0.1A过流事件电流触发上限
#endif

#if EVENT_MISS_CUR_EN
    .i_miss_thd_v = MIN_WORK_VOLTAGE,                            // 断流事件电压触发下限
    .i_miss_thd_i = (uint32_t)(0.005 * BASE_CURRENT * 10000),    // 断流事件电流触发上限
#endif

#if EVENT_REV_EN
    .p_rev_thd_p = (uint32_t)(0.005 * INPUT_VOLTAGE * BASE_CURRENT * 10),    // 0.1W 潮流反向功率触发下限
#endif

#if EVENT_OVR_LOAD_EN
    .p_ovr_thd_p = (uint32_t)(1.2 * INPUT_VOLTAGE * MAXIMUM_CURRENT * 10),    // 过载功率触发下限
#endif

#if EVENT_LOW_PF_EN
    .pf_low_thd_pf = 300,    // 0.001总功率因素低触发下限
#endif

// 判定延时时间
#if EVENT_LOSS_VOL_EN
    .vol_loss_thd_time = 5,    // 失压事件判定延时时间
#endif
#if EVENT_LOW_VOL_EN
    .vol_low_thd_time = 5,    // 失压事件判定延时时间
#endif
#if EVENT_OVR_VOL_EN
    .vol_ovr_thd_time = 5,    // 过压事件判定延时时间
#endif
#if EVENT_MISS_VOL_EN
    .vol_miss_thd_time = 5,    // 断相事件判定延时时间
#endif
#if EVENT_ALL_LOSS_VOL_EN
    .vol_all_mis_thd_time = 5,    // 全失压事件判定延时时间
#endif
#if EVENT_V_UNB_EN
    .vol_unb_thd_time = 5,    // 电压不平衡判定延时时间
#endif
#if EVENT_I_UNB_EN
    .i_unb_thd_time = 5,    // 电流不平衡判定延时时间
#endif
#if EVENT_LOS_CUR_EN
    .i_loss_thd_time = 5,    // 失流事件判定延时时间
#endif
#if EVENT_OVR_CUR_EN
    .i_ovr_thd_time = 5,    // 过流事件判定延时时间
#endif
#if EVENT_MISS_CUR_EN
    .i_miss_thd_time = 5,    // 断流事件判定延时时间
#endif
#if EVENT_REV_EN
    .p_rev_thd_time = 5,    // 潮流反向功率判定延时时间
#endif
#if EVENT_OVR_LOAD_EN
    .p_ovr_thd_time = 5,    // 过载功率判定延时时间
#endif
#if EVENT_LOW_PF_EN
    .pf_low_thd_time = 5,    // 总功率因素低判定延时时间
#endif
};

/// @brief control 控制模块默认参数+++++++++++++++++++++++++++++++++++++++++++++++
const ctrl_para_s control_default_para = {
    .over_power_cnt_thd     = 3,
    .malignant_load_cnt_thd = 3,

    .current_filter =
        {
#if SW_PAYMENT_EN
            .debt = 1,    // 0-不允许欠费拉闸，1-允许欠费拉闸
#else
            .debt = 0,
#endif
            .over_load      = 0,    // 0-不允许过载拉闸，1-允许过载拉闸
            .malignant_load = 0,    // 0-不允许恶性负载拉闸，1-允许恶性负载拉闸
        },

    .history_filter =
        {
            .top_cov_opn = 0,
            .bot_cov_opn = 0,
        },
};

const private_info_s private_info_default_para = {.password = {
                                                      {0x00, 0x00, 0x00},    // 密码0
                                                      {0x00, 0x00, 0x00},    // 密码1
                                                      {0x00, 0x00, 0x00},    // 密码2
                                                      {0x00, 0x00, 0x00},    // 密码3
                                                      {0x00, 0x00, 0x00},    // 密码4
                                                      {0x00, 0x00, 0x00},    // 密码5
                                                      {0x00, 0x00, 0x00},    // 密码6
                                                      {0x00, 0x00, 0x00},    // 密码7
                                                      {0x00, 0x00, 0x00},    // 密码8
                                                      {0x00, 0x00, 0x00},    // 密码9
                                                  }};

/// end of file app_config.c
