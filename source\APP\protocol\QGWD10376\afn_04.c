/**
 ******************************************************************************
 * @file    afn_04.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 4 参数设置
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"
#include "module_para.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t afn04_set(req_obj_s *req, rsp_obj_s *rsp)
{
    rsp_err_t ret = ACK_ERR;    // 默认返回错误
    switch(req->fn)
    {
        case 3:    // F3：主站IP地址和端口
        {
            uint8_t tmp[MODULE_IP_LEN + 1];
            uint8_t ip[4];
            uint8_t len;
            if(req->apdu_len < 28) { return ACK_ERR; }    // 数据长度不足 4 + 2 + 4 + 2 + 16
            req->apdu_len -= 28;                          // 减去IP地址和端口长度

            // TCP主站地址和端口 4 + 2
            memcpy(ip, req->req_apdu, 4), req->req_apdu += 4;
            len = sprintf((char *)tmp, "%hhu.%hhu.%hhu.%hhu", ip[0], ip[1], ip[2], ip[3]);
            module_para.para_set(tmp, len, MODULE_TCP_IP);
            module_para.para_set(req->req_apdu, 2, MODULE_TCP_PORT), req->req_apdu += 2;

            // TCP备份地址和端口 4 + 2
            memcpy(ip, req->req_apdu, 4), req->req_apdu += 4;
            len = sprintf((char *)tmp, "%hhu.%hhu.%hhu.%hhu", ip[0], ip[1], ip[2], ip[3]);
            module_para.para_set(tmp, len, MODULE_TCP_IP_BAK);
            module_para.para_set(req->req_apdu, 2, MODULE_TCP_BAK_PORT), req->req_apdu += 2;

            // APN 16 字节
            module_para.para_set(req->req_apdu, 16, MODULE_APN), req->req_apdu += 16;
        }
        case 10:    // F10：终端电能表/交流采样装置配置参数
        {
            // uint16_t tnum, num, point; //配置数量， 装置序号
            // memcpy(&num, req->req_apdu, 2), req->req_apdu += 2, req->apdu_len -= 2;
            // num = 1; // 电表固定一个点
            // memcpy(&tnum, req->req_apdu, 2), req->req_apdu += 2, req->apdu_len -= 2;
            // tnum = 1;
            // memcpy(&point, req->req_apdu, 2), req->req_apdu += 2, req->apdu_len -= 2;
            // point = 1;
            // 电表端，忽略配置数量2，装置序号2，测量点2，通讯速录1，协议类型1, 通讯密码6
            if(req->apdu_len < 45) { ret = ACK_ERR; }    // 数据长度不足
            req->req_apdu += 14, req->apdu_len -= 14;
            // 设置费率
            req->req_apdu += 1, req->apdu_len -= 1;

            // 电能整数，小数
            req->req_apdu += 1, req->apdu_len -= 1;

            // 采集器地址
            req->req_apdu += 6, req->apdu_len -= 6;

            // 用户大类号及用户小类号
            req->req_apdu += 1, req->apdu_len -= 1;

            // PW
            req->req_apdu += 16, req->apdu_len -= 15;
        }
        break;
    }
    ret = ACK_RIGHT;    // 设置成功
    logd("AFN 4 Set Terminal Address: type=%d, ret=%X", req->fn, (uint8_t)ret);
    rsp->err = ACK_RIGHT;    // 默认返回正确
    return ret;
}

const gdw376_table_s afn04_table = {
    .afn    = AFN_SET_PARAM,    ///< 功能码
    .reset  = NULL,             ///< 复位函数
    .verify = NULL,             ///< 验证函数
    .get    = NULL,             ///< 获取函数
    .set    = afn04_set,        ///< 设置函数
};
