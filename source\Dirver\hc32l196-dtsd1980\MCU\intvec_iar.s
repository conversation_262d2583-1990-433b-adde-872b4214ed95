;
; Cortex-M0/M3/M4 BOOT中断向量跳转至APP的中断向量表
;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

        SECTION .text:CODE(2)
        THUMB

        IMPORT __ICFEDIT_app_intvec_start__    ;应用分区中断向量地址

;        PUBLIC HardFault_Handler
;HardFault_Handler:
;        LDR R0, =__ICFEDIT_app_intvec_start__
;        LDR R0, [R0, #0x0C]
;        BX R0
        
        PUBLIC SVC_Handler    ;FreeRTOS need this interrupt handler function
SVC_Handler:
        LDR R0, =__ICFEDIT_app_intvec_start__
        LDR R0, [R0, #0x2C]
        BX R0
        
        PUBLIC PendSV_Handler
PendSV_Handler:
        LDR R0, =__ICFEDIT_app_intvec_start__
        LDR R0, [R0, #0x38]
        BX R0

        PUBLIC SysTick_Handler
SysTick_Handler:
        LDR R0, =__ICFEDIT_app_intvec_start__
        LDR R0, [R0, #0x3C]
        BX  R0


        END
