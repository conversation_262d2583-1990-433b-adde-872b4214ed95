/**
 ******************************************************************************
 * @file    afn_01.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 1  复位
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t afn1_reset(req_obj_s *req, rsp_obj_s *rsp)
{
    rsp_err_t ret = ACK_ERR;    // 默认返回错误

    if(req->apdu_len < 16) { return false; }    // 数据长度不足
    switch(req->fn)
    {
        case 1:    // F1 硬件初始化
        {
            // 硬件初始化
            ret = ACK_RIGHT;    //
        }
        break;
        case 2:    // F2 数据区初始化
        {
            // 数据区初始化
            ret = ACK_RIGHT;    //
        }
        break;
        case 3:    // F3 参数及全体数据区初始化（即恢复至出厂配置）
        {
            ret = ACK_RIGHT;    //
        }
        break;
        case 4:    // F4 参数（除与系统主站通信有关的）及全体数据区初始化
        {
            ret = ACK_RIGHT;    //
        }
        break;
        default: {
            ret = ACK_ERR;    //
        }
    }
    logd("AFN 1 Reset: fn=%d, ret=%d", req->fn, ret);
    return ret;    // 返回确认结果
}

const gdw376_table_s afn01_table = {
    .afn    = AFN_RESET,     ///< 功能码
    .reset  = afn1_reset,    ///< 复位函数
    .verify = NULL,          ///< 验证函数
    .get    = NULL,          ///< 获取函数
    .set    = NULL,          ///< 设置函数
};
