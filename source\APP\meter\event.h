/********************************************************************************
  * @file    event.h
  * <AUTHOR> @date    2024
  * @brief   事件
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
 
#ifndef __EVENT_H__
#define __EVENT_H__
#include "..\config\app_config.h"
#include "typedef.h"
#include "timeapp.h"




/// @brief 事件类型枚举,
typedef enum
{
#if EVENT_LOSS_VOL_EN             /// 失压事件
    EVENT_TYPE_LOSS_VOL_A,       
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_LOSS_VOL_B,       
    EVENT_TYPE_LOSS_VOL_C,       
    #endif
#endif
#if EVENT_LOW_VOL_EN              /// 欠压事件
    EVENT_TYPE_LOW_VOL_A,        
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_LOW_VOL_B,        
    EVENT_TYPE_LOW_VOL_C,        
    #endif
#endif
#if EVENT_OVR_VOL_EN              /// 过压事件
    EVENT_TYPE_OVR_VOL_A,        
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_OVR_VOL_B,        
    EVENT_TYPE_OVR_VOL_C,        
    #endif
#endif
#if EVENT_MISS_VOL_EN             /// 断相事件
    EVENT_TYPE_MISS_VOL_A,       
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_MISS_VOL_B,       
    EVENT_TYPE_MISS_VOL_C,       
    #endif
#endif
#if EVENT_ALL_LOSS_VOL_EN         /// 全失压事件
    EVENT_TYPE_ALL_LOSS_VOL,   
#endif
#if EVENT_BAK_PWR_LOS_EN          /// 辅助电源失电事件
    EVENT_TYPE_BAK_PWR_LOS,    
#endif
#if EVENT_V_REV_SQR_EN            /// 电压逆向序事件
    EVENT_TYPE_V_REV_SQR,      
#endif
#if EVENT_I_REV_SQR_EN            /// 电流逆向序事件
    EVENT_TYPE_I_REV_SQR,      
#endif
#if EVENT_V_UNB_EN                /// 电压不平衡事件
    EVENT_TYPE_V_UNB,          
#endif
#if EVENT_I_UNB_EN            /// 电流不平衡事件
    EVENT_TYPE_I_UNB,      
#endif
#if EVENT_LOS_CUR_EN              /// 失流事件
    EVENT_TYPE_LOS_CUR_A,        
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_LOS_CUR_B,        
    EVENT_TYPE_LOS_CUR_C,        
    #endif
#endif
#if EVENT_OVR_CUR_EN              /// 过流事件
    EVENT_TYPE_OVR_CUR_A,        
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_OVR_CUR_B,        
    EVENT_TYPE_OVR_CUR_C,        
    #endif
#endif
#if EVENT_MISS_CUR_EN             /// 断流事件
    EVENT_TYPE_MISS_CUR_A,       
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_MISS_CUR_B,       
    EVENT_TYPE_MISS_CUR_C,       
    #endif
#endif
#if EVENT_REV_EN                  /// 潮流反向事件
    EVENT_TYPE_REV_A,            
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_REV_B,            
    EVENT_TYPE_REV_C,            
    #endif
#endif
#if EVENT_OVR_LOAD_EN             /// 过载事件
    EVENT_TYPE_OVR_LOAD_A,       
    #if defined(POLYPHASE_METER)
    EVENT_TYPE_OVR_LOAD_B,       
    EVENT_TYPE_OVR_LOAD_C,       
    #endif
#endif
#if EVENT_LOW_PF_EN  
    EVENT_TYPE_LOW_PF,                
#endif
#if EVENT_CONTROL_EN 
    EVENT_TYPE_DISCONNECT,
    EVENT_TYPE_RECONNECT,
#endif
#if EVENT_PWR_DOWN_EN             /// 掉电事件
    EVENT_TYPE_PWR_DOWN,
#endif
#if EVENT_OVR_DM_EN               /// 超需量事件
    EVENT_TYPE_OVR_DM_POS_kW,     /// 正向   有功需量超限记录
    // EVENT_TYPE_OVR_DM_NEG_kW,  /// 反向   有功需量超限记录
    // EVENT_TYPE_OVR_DM_Q1_kvar, /// 第1象限无功需量超限记录
    // EVENT_TYPE_OVR_DM_Q2_kvar, /// 第2象限无功需量超限记录
    // EVENT_TYPE_OVR_DM_Q3_kvar, /// 第3象限无功需量超限记录
    // EVENT_TYPE_OVR_DM_Q4_kvar, /// 第4象限无功需量超限记录
#endif
#if EVENT_PROGRAM_EN              /// 编程事件
    EVENT_TYPE_PROGRAM,
#endif
#if EVENT_METER_CLEAN_EN          /// 电表清零事件
    EVENT_TYPE_METER_CLEAN,
#endif
#if EVENT_DEMAND_CLEAN_EN         /// 需量清零事件
    EVENT_TYPE_DEMAND_CLEAN,
#endif
#if EVENT_EVENT_CLEAN_EN          /// 事件清零事件
    EVENT_TYPE_EVENT_CLEAN,
#endif
#if EVENT_SHITFT_TIME_EN          /// 校时事件
    EVENT_TYPE_SHITFT_TIME,
#endif
#if EVENT_BC_TIME_EN              /// 广播校时事件
    EVENT_TYPE_BC_TIME,
#endif
#if EVENT_SCHEDULE_EN             /// 时段表事件
    EVENT_TYPE_SCHEDULE,
#endif
#if EVENT_ZONE_TAB_EN             /// 时区表事件
    EVENT_TYPE_ZONE_TAB,
#endif
#if EVENT_WEEKENDS_PGM_EN         /// 周休日编程事件
    EVENT_TYPE_WEEKENDS_PGM,
#endif
#if EVENT_HOLIDAY_PGM_EN          /// 节假日表编程事件
    EVENT_TYPE_HOLIDAY_PGM,
#endif
#if EVENT_COMB_kWh_PGM_EN         /// 有功组合方式编程事件
    EVENT_TYPE_COMB_kWh_PGM,
#endif
#if EVENT_COMB1_kvarh_PGM_EN      /// 无功组合方式1编程事件
    EVENT_TYPE_COMB1_kvarh_PGM,
#endif
#if EVENT_COMB2_kvarh_PGM_EN      /// 无功组合方式2编程事件
    EVENT_TYPE_COMB2_kvarh_PGM,
#endif
#if EVENT_BL_DAY_PGM_EN           /// 结算日编程事件
    EVENT_TYPE_BL_DAY_PGM,
#endif
#if EVENT_METER_COVER_EN          /// 开表盖事件
    EVENT_TYPE_METER_COVER,
#endif
#if EVENT_TEM_COVER_EN            /// 开端盖事件
    EVENT_TYPE_TEM_COVER,      
#endif
    EVENT_TYPE_NUM
} evt_type_t;


typedef struct 
{
    uint32_t id;
    uint32_t opt_code;
    uint32_t time;
}evt_data_s;


/// @brief 事件捕获对象定义


/* Exported defines ----------------------------------------------------------*/
#define EVT_STUS    uint16_t

#define EVT_CLEAR_STATUS                    (EVT_STUS)(0 + (1 << 0))  //事件清零



/* Exported macro ------------------------------------------------------------*/
/* Exported variables --------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
struct event_s
{
    void (*reset)(uint8_t type);
    bool (*state_query)(uint16_t state);
    void (*state_clr)(void);
    void (*group_clr)(evt_type_t et);
    void (*group_clr_645)(uint32_t id, uint32_t opt_code);
    uint32_t (*rcd_cnt_get)(uint32_t id);
};
extern const struct event_s event;

#endif /* __EVENT_H__ */

