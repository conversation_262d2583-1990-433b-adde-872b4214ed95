
import sys
import os
import argparse

APP_SIZE_POS = 4
CHECKSUM_POS = 20
VERIFYTAG_POS = 24
CRC32_START_VALUE = 12345678

def crc32_update_revbit(data, len, initval = 0):
    crc = initval ^ 0xFFFFFFFF
    for i in range(len):
        tmp = data[i]
        for j in range(8):
            if (crc ^ tmp) & 1:
                crc = (crc >> 1) ^ 0xEDB88320
            else:
                crc = (crc >> 1)
            tmp = tmp >> 1
    return ~crc & 0xffffffff


if __name__ == '__main__':

    parser = argparse.ArgumentParser(description = '计算二进制文件的CRC校验并填充')
    parser.add_argument('--file', '-f', type = str, required = True, help = "input a binnary file name")
    args = parser.parse_args()

    f = open(args.file, 'rb')
    buffer = bytearray(f.read())
    f.close()
    f_size = len(buffer)

    if( "app1.bin" in args.file):
        #修改文件实际长度
        buffer[APP_SIZE_POS : APP_SIZE_POS + 4] = f_size.to_bytes(length=4, byteorder='little') #使用更优雅的写法

        #计算输入的文件校验值
        buffer[CHECKSUM_POS : CHECKSUM_POS + 8] = bytes(8) #计算前先清空该位置的内容
        crc32 = crc32_update_revbit(buffer, f_size)
        buffer[CHECKSUM_POS : CHECKSUM_POS + 4] = crc32.to_bytes(length=4, byteorder='little')

        print('the binary file: %s; file_size: %d bytes; file_checksum: 0x%x;'%(os.path.basename(args.file), f_size, crc32))

    #计算APP头的校验
    crc32 = crc32_update_revbit(buffer, VERIFYTAG_POS, CRC32_START_VALUE)
    buffer[VERIFYTAG_POS: VERIFYTAG_POS + 4] = crc32.to_bytes(length=4, byteorder='little')
    print('the binary file: %s; file_size: %d bytes; APP_header_checksum: 0x%x;'%(os.path.basename(args.file), f_size, crc32))

    with open(args.file, 'wb') as f: #使用with语句执行完成后会自行关闭文件
        f.write(buffer)


