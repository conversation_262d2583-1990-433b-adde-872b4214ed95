::ff

@echo off
@setlocal ENABLEDELAYEDEXPANSION

echo [INFO] file_copy.bat start!!!
::获取当前项目文件夹以及上一级目录
pushd %~dp0 & for %%i in (.) do set "target=%%~nxi"
cd ..\
pushd %~dp1 & for %%i in (.) do set "proj=%%~nxi"
cd %~dp0

::获取传参
@set "MODE=%~1%"
@set "ver=%~2%"
echo [INFO] get time: %time%
::获取日期 
@set year=%date:~0,4%
@set month=%date:~5,2%
@set day=%date:~8,2%

if "%time:~0,1%"==" " (set hour=0"%time:~1,1%") else (set hour=%time:~0,2%)
@set minute=%time:~3,2%
@set second=%time:~6,2%
 
@REM  chcp 65001 >nul
::设置脚本编码为UTF-8

::查找固件版本号
for /f tokens^=3 %%a in ('findstr "#define.METER_SOFT_VER"     ..\..\source\ver.h')      do set "soft_ver_tmp=%%a"
@set "soft_ver=%soft_ver_tmp:~1,5%"

echo [INFO] mkdir_output: %target%
::建立项目输出文件夹
@set "DIR_OUT=..\..\output\%target%"
@set "hex_path=%DIR_OUT%"

if not exist "%hex_path%" mkdir "%hex_path%"

@set "out_file=.\Release\Exe"
@set "name=%MODE%-(%soft_ver%-%year%%month%%day%)"

echo [INFO] del_histoury: %hex_path%
del "%hex_path%\*.*" /Q
::生成APP bin 文件，直接输出到output
ielftool.exe --verbose --bin=__ICFEDIT_APP_start__-__ICFEDIT_APP_end__ "%out_file%\app.out" "%hex_path%\%MODE%-app(%soft_ver%-%year%%month%%day%).bin"
ielftool.exe --verbose --ihex "%out_file%\app.out" "%out_file%\boot_app.hex"
echo [INFO] app_bin="%hex_path%\%MODE%-app(%soft_ver%-%year%%month%%day%).bin"
echo [INFO] app_hex="%out_file%\boot_app.hex"

echo [INFO] copy_hex: %hex_path%
::复制HEX文件到输出目录
if exist "%out_file%\boot_app.hex" (
    copy "%out_file%\boot_app.hex" "%hex_path%\%name%.hex"
    echo [DEBUG] HEX file detected at %time%
    goto copy_success
) else (
    echo [ERROR] HEX file missing after retries!
    echo file_copy.bat file.
    exit /b 1
)
::echo %soft_ver% >%hex_path%\name.txt
::echo %soft_ver_tmp% >%hex_path%\name2.txt

:copy_success
echo [INFO] file_copy.bat success.
exit /b 0
