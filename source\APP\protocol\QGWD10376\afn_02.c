/**
 ******************************************************************************
 * @file    afn_02.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 14 请求 3 类数据，事件
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t afn2_verify(req_obj_s *req, rsp_obj_s *rsp)
{
    if(req->fn == 3)
    {
        if(req->apdu_len < 1) return ACK_ERR;                  // 数据长度不足
        if(*req->req_apdu != ACK_RIGHT) return ACK_ERR;    // 确认结果
    }
    else if(req->verify == false)
    {
        return ACK_ERR;    // 如果不是确认请求，返回错误
    }

    if(req->son_fn == afn2_f1p0_login)    // 确认登录
    {
        dcu.link_ack(1);    // 调用登录确认函数
        logt();
        logd("Q/GWD 10376 login success ...\r\n");
    }
    else if(req->son_fn == afn2_f3p0_heartbeat)    // 确认心跳
    {
        dcu.link_ack(0);    // 调用心跳确认函数
        logt();
        logd("Q/GWD 10376 heartbeat success...\r\n");
    }
    return ACK_RIGHT;    // 返回确认结果
}


rsp_err_t afn2_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t *ptr = rsp->apdu;
    switch(req->fn)
    {
        case afn2_f1p0_login:    // 登录
        {
            rsp->rsp_len = 0;
        }
        break;
        case afn2_f2p0_logout:
        {
            rsp->rsp_len = 0;
        }
        case afn2_f3p0_heartbeat:    // 心跳 帧数据域体 目前为4G模组 RSQ 
        {
            *ptr = remote_m.info->rssi;
            rsp->rsp_len = 1;
        }
        break;
        default:
            return ACK_ERR;    // 未知的功能码
    }
    return ACK_RIGHT;
}

const gdw376_table_s afn02_table = {
    .afn    = AFN_LINK_TEST,    ///< 功能码
    .reset  = NULL,             ///< 复位函数
    .verify = afn2_verify,      ///< 验证函数
    .get    = afn2_get,         ///< 获取函数
    .set    = NULL,             ///< 设置函数
};

// end of file
