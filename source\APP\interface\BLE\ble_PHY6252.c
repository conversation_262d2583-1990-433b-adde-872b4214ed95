/*
 * @description: phy6252蓝牙soc (Final Architecture: Unified Context Struct)
 * @date: Do not edit
 * @lastAuthor:
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */
#ifdef __INTELLISENSE__
#include "bsp_cfg.h"
#include "hal_def.h"
#include "hal_uart.h"
#include "hal_gpio.h"
#endif

#include "bsp.h"
#include "api.h"
#include <string.h>     // For strcmp, strncpy, memset, sprintf
#include <stdio.h>      // For sprintf
#include <stdbool.h>    // For bool type

// Forward declarations
static void ble_send(uint8_t *msg, uint16_t len);

// ======================== 事件与状态定义 ===========================
typedef enum
{
    EVENT_NONE,
    EVENT_STATE_ENTER,
    EVENT_BLE_RX_DATA,
    EVENT_TIMEOUT
} event_id_t;
typedef struct
{
    event_id_t id;
    char      *recv_buf;
} event_t;

typedef enum
{
    PHY6252_STATE_NONE,          // 代表“无状态”或“无任务”
    PHY6252_CHECK_AT,            // 启动检查
    PHY6252_SET_ATE0,            // 设置ATE0
    PHY6252_SET_ADV_NAME,        // 设置名称
    PHY6252_SET_UUID,            // 设置UUID
    PHY6252_SET_RF,              // 设置频率（需要复位模组，0，125khz 1，500khz 3，1mhz）
    PHY6252_SET_RECV_MODE,       // 设置透传模式（0透传,1接收标识符，读蓝牙缓存）
    PHY6252_CHECK_MESH,          // 检查Mesh配网状态
    PHY6252_TRANSPARENT,         // 透传模式
    PHY6252_CLR_MESH_INFO,       // 清除Mesh信息
    PHY6252_SEND_DATA,           // 主动发送数据
    PHY6252_READ_BUFFER,         // 读取蓝牙缓存数据
    PHY6252_GET_CONN_STATUS,     // 获取当前连接状态和RSSI
    PHY6252_GET_FW_VERSION,      // 获取模块固件版本
    PHY6252_ENTER_SLEEP_MODE,    // 命令模块进入低功耗模式
    PHY6252_ENTER_OTA_MODE,      // 命令模块进入固件升级模式
    PHY6252_SOFT_RESET,          // 软件复位
} ble_thread_s;

// ======================== 队列定义 ===========================
// --- 事件队列 ---
#define EVENT_QUEUE_SIZE 4
static event_t          g_event_queue[EVENT_QUEUE_SIZE];
static volatile uint8_t g_event_queue_head = 0, g_event_queue_tail = 0;

// --- 注入任务队列 ---
#define THREAD_ACTION_QUEUE_SIZE 4
typedef struct
{
    ble_thread_s current_state;    // 当前正在执行的状态ID
    ble_thread_s success_next;     // 成功后的下一状态 (可选, NONE则继承)
    ble_thread_s failure_next;     // 失败后的下一状态 (可选, NONE则继承)
} state_transition_t;
static state_transition_t g_state_queue[THREAD_ACTION_QUEUE_SIZE];
static volatile uint8_t   g_state_queue_head = 0, g_state_queue_tail = 0;

static void event_queue_init(void)
{
    memset(g_event_queue, 0, sizeof(g_event_queue));
    g_event_queue_head = g_event_queue_tail = 0;
}
static bool event_queue_put(event_t event)
{
    uint8_t next_head = (g_event_queue_head + 1) % EVENT_QUEUE_SIZE;
    if(next_head == g_event_queue_tail) return false;
    g_event_queue[g_event_queue_head] = event;
    g_event_queue_head                = next_head;
    return true;
}
static event_t event_queue_get(void)
{
    event_t event = {.id = EVENT_NONE};
    if(g_event_queue_head != g_event_queue_tail)
    {
        event              = g_event_queue[g_event_queue_tail];
        g_event_queue_tail = (g_event_queue_tail + 1) % EVENT_QUEUE_SIZE;
    }
    return event;
}

static void state_queue_init(void)
{
    memset(g_state_queue, 0, sizeof(g_state_queue));
    g_state_queue_head = g_state_queue_tail = 0;
}
static bool state_queue_put(state_transition_t transition)
{
    uint8_t next_head = (g_state_queue_head + 1) % THREAD_ACTION_QUEUE_SIZE;
    if(next_head == g_state_queue_tail) return false;
    g_state_queue[g_state_queue_head] = transition;
    g_state_queue_head                = next_head;
    return true;
}
static state_transition_t state_queue_get(void)
{
    state_transition_t transition = {.current_state = PHY6252_STATE_NONE};
    if(g_state_queue_head != g_state_queue_tail)
    {
        transition         = g_state_queue[g_state_queue_tail];
        g_state_queue_tail = (g_state_queue_tail + 1) % THREAD_ACTION_QUEUE_SIZE;
    }
    return transition;
}

// ===================== 表驱动状态机定义 =====================
typedef const char *(*pfn_get_command)(char *buffer, size_t buffer_size);
typedef bool (*pfn_ack_validator)(const char *response);
typedef union
{
    const char     *static_cmd;
    pfn_get_command get_command;
} command_source_t;
typedef struct
{
    command_source_t   command;              // 指令
    uint32_t           timeout_ms;           // 超时时间
    pfn_ack_validator  success_validator;    // 成功验证函数，包含数据处理
    pfn_ack_validator  failure_validator;    // 失败验证函数
    state_transition_t state_flow;           // 状态流转定义
    uint8_t            max_retries;          // 最大重试次数
    bool               is_dynamic_cmd;       // 是否为动态指令
} state_config_t;

// --- 指令与验证函数 ---
static const char *set_cmd_uuid(char *buffer, size_t buffer_size)
{
    uint8_t addr[6] = {0};
    api.meter_sn_get(addr);
    snprintf(buffer, buffer_size, "AT+UUID=%02x%02x%02x%02x%02x%02x", addr[5], addr[4], addr[3], addr[2], addr[1], addr[0]);
    return buffer;
}

// --- 验证函数 ---
static bool common_success_validator(const char *response)
{
    return strstr(response, "OK") != NULL;
}
static bool common_failure_validator(const char *response)
{
    return strstr(response, "ERROR") != NULL;
}
static bool transparent_success_validator(const char *response)
{
    return true;
}
static bool transparent_failure_validator(const char *response)
{
    return true;
}
// --- 状态配置表 ---
static const state_config_t g_state_configs[] = {
    {{.static_cmd = "AT"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_state = PHY6252_CHECK_AT, .success_next = PHY6252_TRANSPARENT, .failure_next = PHY6252_SOFT_RESET},
     3,
     false},

    {{.static_cmd = "ATE0"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_state = PHY6252_SET_ATE0, .success_next = PHY6252_SET_ADV_NAME, .failure_next = PHY6252_SOFT_RESET},
     3,
     false},

    {{.static_cmd = "AT+NAME=DDS1980_T1"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_state = PHY6252_SET_ADV_NAME, .success_next = PHY6252_SET_UUID, .failure_next = PHY6252_SOFT_RESET},
     3,
     false},

    {{.get_command = set_cmd_uuid},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_state = PHY6252_SET_UUID, .success_next = PHY6252_TRANSPARENT, .failure_next = PHY6252_SOFT_RESET},
     3,
     true},

    {NULL,
     0,
     transparent_success_validator,
     transparent_failure_validator,
     {.current_state = PHY6252_TRANSPARENT, .success_next = PHY6252_TRANSPARENT, .failure_next = PHY6252_SOFT_RESET},
     0,
     false},

    {{.static_cmd = "AT+RESET"},
     2000,
     common_success_validator,
     NULL,
     {.current_state = PHY6252_SOFT_RESET, .success_next = PHY6252_CHECK_AT, .failure_next = PHY6252_CHECK_AT},
     1,
     false},
};

static const state_config_t *find_config_for_state(ble_thread_s state)
{
    for(size_t i = 0; i < sizeof(g_state_configs) / sizeof(g_state_configs[0]); ++i)
    {
        if(g_state_configs[i].state_flow.current_state == state) return &g_state_configs[i];
    }
    return NULL;
}

// ======================== 运行时参数 ===========================
#define BLE_TX_BUF_SIZE 128
typedef struct
{
    // 运行时状态
    state_config_t current_config;    // 当前正在执行的状态配置
    uint8_t        retry_count;       // 当前重试次数
    bool           timer_active;      // 定时器激活标志

    // 通信缓冲区
    SwTimer_s timer;                         // 软件定时器实例
    uint8_t   tx_buffer[BLE_TX_BUF_SIZE];    // 发送缓冲区
    uint16_t  tx_length;                     // 发送长度
    uint8_t  *rx_buffer;                     // 接收缓冲区指针 (由外部传入)
    uint16_t  rx_length;                     // 接收长度

} ble_runtime_context_t;

static ble_runtime_context_t g_ble_context;

// ===================== 定时器管理 ===========================
#define BLE_TIMEOUT_CHECK() hal_timer.expired(&g_ble_context.timer)
void ble_timeout_start(uint32_t timeout)
{
    hal_timer.interval(&g_ble_context.timer, timeout);
    g_ble_context.timer_active = true;
}
void ble_timeout_stop(void)
{
    g_ble_context.timer_active = false;
}

// ===================== 状态机核心与调度器 =====================

/**
 * @brief 状态转换函数，作为状态机的核心调度器。
 * @param add_state 期望进入的下一个状态（最低优先级）。
 */
static bool ble_state_transition(ble_runtime_context_t *context)
{
    // 获取当前状态转换
    state_transition_t next_transition = state_queue_get();

    // 检查队列是否为空
    if(next_transition.current_state == PHY6252_STATE_NONE)
    {
        return false;    // 队列为空，无需状态转换
    }

    // 获取状态配置
    const state_config_t *config_ptr = find_config_for_state(next_transition.current_state);

    // 添加空指针检查
    if(config_ptr == NULL)
    {
        // 如果找不到对应的状态配置，设置为软复位状态
        next_transition.current_state = PHY6252_SOFT_RESET;
        config_ptr                    = find_config_for_state(PHY6252_SOFT_RESET);
        if(config_ptr == NULL) return false;    // 如果连软复位都找不到，返回失败
    }

    state_config_t current_config = *config_ptr;

    // 合并状态流转，假如是插入的任务，不要影响插入任务的下一步
    if(next_transition.failure_next == PHY6252_STATE_NONE) { next_transition.failure_next = current_config.state_flow.failure_next; }
    if(next_transition.success_next == PHY6252_STATE_NONE) { next_transition.success_next = current_config.state_flow.success_next; }

    // 更新当前配置
    current_config.state_flow = next_transition;
    context->current_config   = current_config;

    // 重置重试计数器
    context->retry_count = 0;

    // 触发状态进入事件
    event_queue_put((event_t){.id = EVENT_STATE_ENTER});

    return true;
}

/**
 * @description: 切换状态
 * @param {ble_context_t} *context
 * @param {bool} is_success
 * @return {*}
 */
static void ble_handle_state(ble_runtime_context_t *context, bool is_success)
{
    ble_timeout_stop();

    if(is_success)
    {
        // 成功时，将下一个状态加入队列，通过事件循环处理状态转换
        state_transition_t next_transition = {.current_state = context->current_config.state_flow.success_next};
        if(next_transition.current_state != PHY6252_STATE_NONE) { state_queue_put(next_transition); }
        // 不直接调用状态转换，让主循环处理
    }
    else
    {
        if(context->retry_count < context->current_config.max_retries)
        {
            context->retry_count++;
            /// @attention 重试的情况不要切换状态，直接重发就好
            event_queue_put((event_t){.id = EVENT_STATE_ENTER});
        }
        else
        {
            // 失败时，将失败状态加入队列
            state_transition_t next_transition = {.current_state = context->current_config.state_flow.failure_next};
            if(next_transition.current_state != PHY6252_STATE_NONE) { state_queue_put(next_transition); }
            // 不直接调用状态转换，让主循环处理
        }
    }
}

/**
 * @description: 状态机事件处理函数
 * @param {event_t} event
 * @return {*}
 */
static void ble_state_machine_handle(event_t event, ble_runtime_context_t *context)
{
    static char g_send_buffer[BLE_TX_BUF_SIZE];

    switch(event.id)
    {
        case EVENT_STATE_ENTER: {
            const char *cmd =
                context->current_config.is_dynamic_cmd ? context->current_config.command.get_command(g_send_buffer, BLE_TX_BUF_SIZE) : context->current_config.command.static_cmd;
            if(cmd)
            {
                context->tx_length = snprintf((char *)context->tx_buffer, BLE_TX_BUF_SIZE, "%s\r\n", cmd);
                ble_send(context->tx_buffer, context->tx_length);
                ble_timeout_start(context->current_config.timeout_ms);
            }
            break;
        }

        case EVENT_BLE_RX_DATA: {
            if(context->current_config.failure_validator && context->current_config.failure_validator(event.recv_buf))
            {
                ble_handle_state(context, false);
                break;
            }

            if(context->current_config.success_validator && context->current_config.success_validator(event.recv_buf))
            {
                ble_handle_state(context, true);
                break;
            }
            break;
        }

        case EVENT_TIMEOUT:
            ble_handle_state(context, false);
            break;

        default:
            break;
    }
}

/**
 * @description: 事件发生器
 * @return {*}
 */
static void ble_poll_and_generate_events(void)
{
    g_ble_context.rx_length = hal_uart.recv(COM_BLE);
    if(g_ble_context.rx_length > 0)
    {
        event_t new_event = {.id = EVENT_BLE_RX_DATA, .recv_buf = (char *)g_ble_context.rx_buffer};
        event_queue_put(new_event);
    }
    if(g_ble_context.timer_active && BLE_TIMEOUT_CHECK() != 0)
    {
        ble_timeout_stop();
        event_t new_event = {.id = EVENT_TIMEOUT};
        event_queue_put(new_event);
    }
}

/**
 * @description: 任务调度器，通过队列插入一个待办任务
 * @param {ble_thread_s} current_task 要执行的任务
 * @param {ble_thread_s} next_override 成功后覆盖的下一状态 (可选, NONE则继承)
 * @param {ble_thread_s} fail_override 失败后覆盖的下一状态 (可选, NONE则继承)
 * @return {bool} 是否成功将任务放入队列
 */
bool ble_inject_state(ble_thread_s target_state, ble_thread_s success_next, ble_thread_s failure_next)
{
    state_transition_t transition = {
        .current_state = target_state,
        .success_next  = success_next,
        .failure_next  = failure_next,
    };

    // 如果是紧急状态（如复位），则强制结束当前状态
    // 否则让当前状态正常完成
    if(target_state == PHY6252_SOFT_RESET || target_state == PHY6252_CHECK_AT) { g_ble_context.retry_count = g_ble_context.current_config.max_retries; }

    return state_queue_put(transition);
}

ble_thread_s ble_get_current_state(void)
{
    return g_ble_context.current_config.state_flow.current_state;
}

// ===================== 适配层接口 ===========================
static uint16_t ble_recv(void)
{
    ble_poll_and_generate_events();
    event_t event;
    while((event = event_queue_get()).id != EVENT_NONE) { ble_state_machine_handle(event, &g_ble_context); }

    // 处理完事件后，检查是否需要状态转换
    ble_state_transition(&g_ble_context);

    return (g_ble_context.current_config.state_flow.current_state == PHY6252_TRANSPARENT) ? g_ble_context.rx_length : 0;
}

static void ble_send(uint8_t *msg, uint16_t len)
{
    hal_uart.send(COM_BLE, msg, len);
}

static void ble_init(uint8_t *rx_buf, uint16_t bufsize)
{
    event_queue_init();
    state_queue_init();
    hal_gpio.ext_ble(GPIO_OPEN);
    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_115200BPS, rx_buf, bufsize);
    memset(&g_ble_context, 0, sizeof(g_ble_context));
    g_ble_context.rx_buffer = rx_buf;

    /// @attention 启动检查状态，需要提前放入队列
    ble_inject_state(PHY6252_CHECK_AT, PHY6252_STATE_NONE, PHY6252_STATE_NONE);

    // 不在初始化时阻塞延时，改为非阻塞方式
    // 让主循环来处理状态转换和延时
}

static bool ble_send_over_query(void)
{
    return hal_uart.send_over_query(COM_BLE);
}