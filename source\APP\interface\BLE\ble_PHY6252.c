/*
 * @description: phy6252蓝牙soc (Final Architecture: Unified Context Struct)
 * @date: Do not edit
 * @lastAuthor:
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */
#ifdef __INTELLISENSE__
#include "bsp_cfg.h"
#include "hal_def.h"
#include "hal_uart."
#include "hal_gpio.h"
#endif

#include "bsp.h"
#include "api.h"
#include <string.h>     // For strcmp, strncpy, memset, sprintf
#include <stdio.h>      // For sprintf
#include <stdbool.h>    // For bool type

// Forward declarations
static void ble_send(uint8_t *msg, uint16_t len);

// ======================== 事件与状态定义 ===========================
typedef enum
{
    EVENT_NONE,
    EVENT_STATE_ENTER,
    EVENT_BLE_RX_DATA,
    EVENT_TIMEOUT
} event_id_t;
typedef struct
{
    event_id_t id;
    char      *recv_buf;
} event_t;

typedef enum
{
    PHY6252_STATE_NONE,          // 代表“无状态”或“无任务”
    PHY6252_CHECK_AT,            // 启动检查
    PHY6252_SET_ATE0,            // 设置ATE0
    PHY6252_SET_ADV_NAME,        // 设置名称
    PHY6252_SET_UUID,            // 设置UUID
    PHY6252_SET_RF,              // 设置频率（需要复位模组，0，125khz 1，500khz 3，1mhz）
    PHY6252_SET_RECV_MODE,       // 设置透传模式（0透传,1接收标识符，读蓝牙缓存）
    PHY6252_CHECK_MESH,          // 检查Mesh配网状态
    PHY6252_TRANSPARENT,         // 透传模式
    PHY6252_CLR_MESH_INFO,       // 清除Mesh信息
    PHY6252_SEND_DATA,           // 主动发送数据
    PHY6252_READ_BUFFER,         // 读取蓝牙缓存数据
    PHY6252_GET_CONN_STATUS,     // 获取当前连接状态和RSSI
    PHY6252_GET_FW_VERSION,      // 获取模块固件版本
    PHY6252_ENTER_SLEEP_MODE,    // 命令模块进入低功耗模式
    PHY6252_ENTER_OTA_MODE,      // 命令模块进入固件升级模式
    PHY6252_SOFT_RESET,          // 软件复位
} ble_thread_s;

// ======================== 队列定义 ===========================
// --- 事件队列 ---
#define EVENT_QUEUE_SIZE 4
static event_t          g_event_queue[EVENT_QUEUE_SIZE];
static volatile uint8_t g_event_queue_head = 0, g_event_queue_tail = 0;

// --- 注入任务队列 ---
#define THREAD_ACTION_QUEUE_SIZE 4
typedef struct
{
    ble_thread_s current_task;    // 当前正在执行的任务ID
    ble_thread_s succ_task;       // 成功后覆盖的下一状态 (可选, NONE则继承)
    ble_thread_s fail_task;       // 失败后覆盖的下一状态 (可选, NONE则继承)
} thread_action_t;
static thread_action_t  g_thread_queue[THREAD_ACTION_QUEUE_SIZE];
static volatile uint8_t g_thread_queue_head = 0, g_thread_queue_tail = 0;

static void event_queue_init(void)
{
    memset(g_event_queue, 0, sizeof(g_event_queue));
    g_event_queue_head = g_event_queue_tail = 0;
}
static bool event_queue_put(event_t event)
{
    uint8_t next_head = (g_event_queue_head + 1) % EVENT_QUEUE_SIZE;
    if(next_head == g_event_queue_tail) return false;
    g_event_queue[g_event_queue_head] = event;
    g_event_queue_head                = next_head;
    return true;
}
static event_t event_queue_get(void)
{
    event_t event = {.id = EVENT_NONE};
    if(g_event_queue_head != g_event_queue_tail)
    {
        event              = g_event_queue[g_event_queue_tail];
        g_event_queue_tail = (g_event_queue_tail + 1) % EVENT_QUEUE_SIZE;
    }
    return event;
}

static void thread_queue_init(void)
{
    memset(g_thread_queue, 0, sizeof(g_thread_queue));
    g_thread_queue_head = g_thread_queue_tail = 0;
}
static bool thread_queue_put(thread_action_t action)
{
    uint8_t next_head = (g_thread_queue_head + 1) % THREAD_ACTION_QUEUE_SIZE;
    if(next_head == g_thread_queue_tail) return false;
    g_thread_queue[g_thread_queue_head] = action;
    g_thread_queue_head                 = next_head;
    return true;
}
static thread_action_t thread_queue_get(void)
{
    thread_action_t action = {.current_task = PHY6252_STATE_NONE};
    if(g_thread_queue_head != g_thread_queue_tail)
    {
        action              = g_thread_queue[g_thread_queue_tail];
        g_thread_queue_tail = (g_thread_queue_tail + 1) % THREAD_ACTION_QUEUE_SIZE;
    }
    return action;
}

// ===================== 表驱动状态机定义 =====================
typedef const char *(*pfn_get_command)(char *buffer, size_t buffer_size);
typedef bool (*pfn_ack_validator)(const char *response);
typedef union
{
    const char     *static_cmd;
    pfn_get_command get_command;
} command_source_t;
typedef struct
{
    command_source_t  command;              // 指令
    uint32_t          timeout_ms;           // 超时时间
    pfn_ack_validator success_validator;    // 成功验证函数，包含数据处理
    pfn_ack_validator failure_validator;    // 失败验证函数
    thread_action_t   thread;               // 注入任务上下文
    uint8_t           max_retries;          // 最大重试次数
    bool              is_dynamic_cmd;       // 是否为动态指令
} state_action_t;

// --- 指令与验证函数 ---
static const char *set_cmd_uuid(char *buffer, size_t buffer_size)
{
    uint8_t addr[6] = {0};
    api.meter_sn_get(addr);
    snprintf(buffer, buffer_size, "AT+UUID=%02x%02x%02x%02x%02x%02x", addr[5], addr[4], addr[3], addr[2], addr[1], addr[0]);
    return buffer;
}

// --- 验证函数 ---
static bool common_success_validator(const char *response)
{
    return strstr(response, "OK") != NULL;
}
static bool common_failure_validator(const char *response)
{
    return strstr(response, "ERROR") != NULL;
}
static bool transparent_success_validator(const char *response)
{
    return true;
}
static bool transparent_failure_validator(const char *response)
{
    return true;
}
// --- 状态表 ---
static const state_action_t g_at_sequence[] = {
    {{.static_cmd = "AT"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_task = PHY6252_CHECK_AT, .succ_task = PHY6252_TRANSPARENT, .fail_task = PHY6252_SOFT_RESET},
     3,
     false},

    {{.static_cmd = "ATE0"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_task = PHY6252_SET_ATE0, .succ_task = PHY6252_SET_ADV_NAME, .fail_task = PHY6252_SOFT_RESET},
     3,
     false},

    {{.static_cmd = "AT+NAME=DDS1980_T1"},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_task = PHY6252_SET_ADV_NAME, .succ_task = PHY6252_SET_UUID, .fail_task = PHY6252_SOFT_RESET},
     3,
     false},

    {{.get_command = set_cmd_uuid},
     1000,
     common_success_validator,
     common_failure_validator,
     {.current_task = PHY6252_SET_UUID, .succ_task = PHY6252_TRANSPARENT, .fail_task = PHY6252_SOFT_RESET},
     3,
     true},

    {NULL,
     0,
     transparent_success_validator,
     transparent_failure_validator,
     {.current_task = PHY6252_TRANSPARENT, .succ_task = PHY6252_TRANSPARENT, .fail_task = PHY6252_SOFT_RESET},
     0,
     false},

    {{.static_cmd = "AT+RESET"},
     2000,
     common_success_validator,
     NULL,
     {.current_task = PHY6252_SOFT_RESET, .succ_task = PHY6252_CHECK_AT, .fail_task = PHY6252_CHECK_AT},
     1,
     false},
};

static const state_action_t *find_action_for_state(ble_thread_s state)
{
    for(size_t i = 0; i < sizeof(g_at_sequence) / sizeof(g_at_sequence[0]); ++i)
    {
        if(g_at_sequence[i].thread.current_task == state) return &g_at_sequence[i];
    }
    return NULL;
}

// ======================== 运行时参数 ===========================
#define BLE_TX_BUF_SIZE 128
typedef struct
{
    // 运行时状态
    state_action_t run_action;             // 当前正在执行的任务的上下文
    uint8_t        current_retry_count;    // 当前重试次数
    bool           is_timer_active;        // 定时器激活标志

    // 定时器与缓冲区
    SwTimer_s timer;                      // 软件定时器实例
    uint8_t   tx_buf[BLE_TX_BUF_SIZE];    // 发送缓冲区
    uint16_t  tx_len;                     // 发送长度
    uint8_t  *rx_buf;                     // 接收缓冲区指针 (由外部传入)
    uint16_t  rx_len;                     // 接收长度

} ble_context_t;

static ble_context_t g_ble_context;

// ===================== 定时器管理 ===========================
#define BLE_TIMEOUT_CHECK() hal_timer.expired(&g_ble_context.timer)
void ble_timeout_start(uint32_t timeout)
{
    hal_timer.interval(&g_ble_context.timer, timeout);
    g_ble_context.is_timer_active = true;
}
void ble_timeout_stop(void)
{
    g_ble_context.is_timer_active = false;
}

// ===================== 状态机核心与调度器 =====================

/**
 * @brief 状态转换函数，作为状态机的核心调度器。
 * @param add_state 期望进入的下一个状态（最低优先级）。
 */
static void ble_state_transition(ble_context_t *context)
{
    // 获取当前任务
    thread_action_t next_action = thread_queue_get();

    // 获取默认动作
    state_action_t default_action = *find_action_for_state(next_action.current_task);


    // 合并默认动作，假如是插入的任务，不要影响插入任务的
    if(next_action.fail_task == PHY6252_STATE_NONE) { next_action.fail_task = default_action.thread.fail_task; }
    if(next_action.succ_task == PHY6252_STATE_NONE) { next_action.succ_task = default_action.thread.succ_task; }


    // 参数获取
    context->run_action = default_action;

    // 保存合并后的上下文
    context->run_action.thread = next_action;
}

/**
 * @description: 切换状态
 * @param {ble_context_t} *context
 * @param {bool} is_success
 * @return {*}
 */
static void ble_handle_state(ble_context_t *context, bool is_success)
{
    ble_timeout_stop();

    if(is_success)
    {
        context->current_retry_count = 0;
        thread_action_t next_state   = {.current_task = context->run_action.thread.succ_task};
        thread_queue_put(next_state);
        ble_state_transition(context);
    }
    else
    {
        if(context->current_retry_count < context->run_action.max_retries)
        {
            context->current_retry_count++;
            /// @attention 重试的情况不要切换状态，直接重发就好
            event_queue_put((event_t){.id = EVENT_STATE_ENTER});
        }
        else
        {
            thread_action_t next_state = {.current_task = context->run_action.thread.fail_task};
            thread_queue_put(next_state);
            ble_state_transition(context);
        }
    }
}

/**
 * @description: 状态机事件处理函数
 * @param {event_t} event
 * @return {*}
 */
static void ble_state_machine_handle(event_t event, ble_context_t *context)
{
    static char g_send_buffer[BLE_TX_BUF_SIZE];

    switch(event.id)
    {
        case EVENT_STATE_ENTER: {
            const char *cmd = context->run_action.is_dynamic_cmd ? context->run_action.command.get_command(g_send_buffer, BLE_TX_BUF_SIZE) : context->run_action.command.static_cmd;
            if(cmd)
            {
                context->tx_len = snprintf((char *)context->tx_buf, BLE_TX_BUF_SIZE, "%s\r\n", cmd);
                ble_send(context->tx_buf, context->tx_len);
                ble_timeout_start(context->run_action.timeout_ms);
            }
            break;
        }

        case EVENT_BLE_RX_DATA: {
            if(context->run_action.failure_validator && context->run_action.failure_validator(event.recv_buf))
            {
                ble_handle_state(context, false);
                break;
            }

            if(context->run_action.success_validator && context->run_action.success_validator(event.recv_buf))
            {
                ble_handle_state(context, true);
                break;
            }
        }

        case EVENT_TIMEOUT:
            ble_handle_state(context, false);
            break;

        default:
            break;
    }
}

/**
 * @description: 事件发生器
 * @return {*}
 */
static void ble_poll_and_generate_events(void)
{
    g_ble_context.rx_len = hal_uart.recv(COM_BLE);
    if(g_ble_context.rx_len > 0)
    {
        event_t new_event = {.id = EVENT_BLE_RX_DATA, .recv_buf = (char *)g_ble_context.rx_buf};
        event_queue_put(new_event);
    }
    if(g_ble_context.is_timer_active && BLE_TIMEOUT_CHECK() != 0)
    {
        ble_timeout_stop();
        event_t new_event = {.id = EVENT_TIMEOUT};
        event_queue_put(new_event);
    }
}

/**
 * @description: 任务调度器，通过队列插入一个待办任务
 * @param {ble_thread_s} current_task 要执行的任务
 * @param {ble_thread_s} next_override 成功后覆盖的下一状态 (可选, NONE则继承)
 * @param {ble_thread_s} fail_override 失败后覆盖的下一状态 (可选, NONE则继承)
 * @return {bool} 是否成功将任务放入队列
 */
bool ble_inject_task(ble_thread_s current_task, ble_thread_s next_override, ble_thread_s fail_override)
{
    thread_action_t action = {
        .current_task = current_task,
        .succ_task    = next_override,
        .fail_task    = fail_override,
    };
    /// 插入任务的话，尽快结束当前任务
    g_ble_context.current_retry_count = g_ble_context.run_action.max_retries;
    return thread_queue_put(action);
}

// ===================== 适配层接口 ===========================
static uint16_t ble_recv(void)
{
    ble_poll_and_generate_events();
    event_t event;
    while((event = event_queue_get()).id != EVENT_NONE) ble_state_machine_handle(event, &g_ble_context);

    return (g_ble_context.run_action.thread.current_task == PHY6252_TRANSPARENT) ? g_ble_context.rx_len : 0;
}

static void ble_send(uint8_t *msg, uint16_t len)
{
    hal_uart.send(COM_BLE, msg, len);
}

static void ble_init(uint8_t *rx_buf, uint16_t bufsize)
{
    event_queue_init();
    thread_queue_init();
    hal_gpio.ext_ble(GPIO_OPEN);
    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_115200BPS, rx_buf, bufsize);
    memset(&g_ble_context, 0, sizeof(g_ble_context));
    g_ble_context.rx_buf = rx_buf;

    /// @attention 启动检查任务，需要提前放入队列
    ble_inject_task(PHY6252_CHECK_AT, PHY6252_STATE_NONE, PHY6252_STATE_NONE);
    ble_state_transition(&g_ble_context);

    hal_timer.msdly(1000 * 5);
}

static bool ble_send_over_query(void)
{
    return hal_uart.send_over_query(COM_BLE);
}