/*
 * @description: phy6252蓝牙soc，收发不能是同一块空间
 * @date: Do not edit
 * @lastAuthor: 
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */
#ifdef __INTELLISENSE__
#include "bsp_cfg.h"
#include "hal_def.h"
#include "hal_uart.h"
#include "hal_gpio.h"

#endif

#include "bsp.h"

typedef enum
{
    BLE_PHY6252_STATE_IDLE,        // 空闲状态
    BLE_PHY6252_STATE_LOGIN,       // 登陆状态
    BLE_PHY6252_STATE_REGISTER,    // 注册状态
    BLE_PHY6252_STATE_WORK,        // 工作状态
    BLE_PHY6252_STATE_MAX,
}ble_signal;

struct ble_PHY6252_s
{
    uint8_t *rx_buf;
    uint16_t rx_bufsize;
    uint16_t rx_len;
    uint8_t *tx_buf;
    uint16_t tx_bufsize;
    uint16_t tx_len;
};
#define BLE_PHY6252_TX_BUF_SIZE (256)
static uint8_t ble_phy6252_tx[BLE_PHY6252_TX_BUF_SIZE];

/**
 * @description: 主任务处理函数，状态机处理登陆注册等信息
 * @param {void} *arg
 * @return {*}
 */
static int ble_phy6252_main_thread(void *arg)
{
    return 0;
}

static int ble_process(void *arg)
{
    return 0;
}

/// @brief  初始化蓝牙模块
static void ble_init(uint8* rx_buf, uint16_t bufsize)
{
    hal_gpio.ext_ble(GPIO_OPEN);

    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_115200BPS, rx_buf, bufsize);

    /// @brief 默认参数写的位置
}

/// @brief  蓝牙模块接收数据
static uint16_t ble_recv(void)
{
    return 0;
}
/// @brief  蓝牙模块发送数据
static void ble_send(uint8_t *msg, uint16_t len)
{
    hal_uart.send(COM_BLE, msg, len);
}

/// @brief  蓝牙模块是否发送完成
static bool ble_send_over_query(void)
{
    return 0;
}