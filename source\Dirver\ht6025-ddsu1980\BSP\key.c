
/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      key.c
*    Describe:      按键驱动
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "key.h"


/* Private typedef -----------------------------------------------------------*/
/* 按键模式定义 */
typedef enum
{
    MODE_BTN_LEVEL,    // 电平有效按键
    MODE_BTN_EDG       // 边沿触发有效按键
} BTN_MODE_t;

typedef struct
{
    bool (*io)(void);  // 目标动作电平发生,更新按键电路时必须检查是否一致!!!
    BTN_MODE_t    mode;
} Button_s;

/* Private define ------------------------------------------------------------*/
#define   BTN_POR_SHORT      80    // 上电 按键短按确认时间,单位ms
#define   BTN_POR_LONG       3000  // 上电 按键长按确认时间,单位ms
#define   BTN_PDR_SHORT      100   // 掉电 按键短按确认时间,单位ms
#define   BTN_PDR_LONG       500   // 掉电 按键长按确认时间,单位ms

/* Private function prototypes -------------------------------------------------------------*/
static bool io_btn_tcover_get(void);
static bool io_btn_fcover_get(void);
static bool io_btn_disp_up_get(void);
static bool io_btn_disp_dn_get(void);

/* Private variables ---------------------------------------------------------*/
static bool btn_last_level[BTN_NUM];
static BTN_STATE_t btn_cap[BTN_NUM]; // 按键状态捕获寄存器
static uint16_t btn_cnt[BTN_NUM];    // 按键计时器
static int16_t key_ins_cnt[BTN_NUM];  // 按键有效瞬时计时器,用于滤波判断

static const Button_s button[] =
{
#if USE_FCOVER
    { .io = io_btn_fcover_get,  .mode = MODE_BTN_EDG },
#endif
#if USE_TCOVER
    { .io = io_btn_tcover_get,  .mode = MODE_BTN_EDG },
#endif
#if USE_BTN_DISP_UP
    { .io = io_btn_disp_up_get, .mode = MODE_BTN_LEVEL },
#endif
#if USE_BTN_DISP_DN
    { .io = io_btn_disp_dn_get, .mode = MODE_BTN_LEVEL },
#endif
};

#if USE_BTN_DISP_DN || USE_BTN_DISP_UP
typedef struct
{
    BTN_TYPE_t           btn;
    KEYBOARD_CHAR_TYPE_t btn_ch;
}disp_btn_s;

static const disp_btn_s disp_btn[] =
{
#if USE_BTN_DISP_DN
    {TYPE_BTN_DISP_DN, TYPE_KEY_ENTER,},     /// 下翻键映射到回车键
#endif
#if USE_BTN_DISP_UP
    {TYPE_BTN_DISP_UP, TYPE_KEY_BACKSPACE,}  /// 上翻键映射到退格键
#endif
};
#endif

#if USE_TCOVER
static bool io_btn_tcover_get(void)
{
    return IS_IO_BTN_TCOVER_OPEN();
}
#endif
#if USE_FCOVER
static bool io_btn_fcover_get(void)
{
    return IS_IO_BTN_FCOVER_OPEN();
}
#endif

#if USE_BTN_DISP_UP
static bool io_btn_disp_up_get(void)
{
    return IS_IO_BTN_DISP_UP_PRESS();
}
#endif

#if USE_BTN_DISP_DN
static bool io_btn_disp_dn_get(void)
{
    return IS_IO_BTN_DISP_DN_PRESS();
}
#endif

/// @brief 按键的电平有效状态捕获
/// @param btn  预检测的按键
/// @param filter_time 是否开启滤波处理
/// @param max_time  按键长按时间
static void key_level_capture(BTN_TYPE_t btn, uint8_t filter_time, uint16_t max_time)
{
    const Button_s* bt = &button[btn];

    //if(!(boolof(bt->mode) ^ bt->io())) // 按键按下时
    if(bt->io()) // 按键按下时
    {
        if(btn_cnt[btn] < max_time)
        {
            btn_cnt[btn]++;
        }
        else if(btn_cnt[btn] == max_time)
        { // 长按键动作只允许触发一次
            btn_cap[btn] = STA_BTN_LONG;
            btn_cnt[btn] = max_time + 1;
        }
    }
    else // 按键松开时
    {
        if(btn_cnt[btn] >= filter_time
        && btn_cnt[btn] < max_time + 1
        /*&& btn_cap[btn] == STA_BTN_NULL*/)
        {
            btn_cap[btn] = STA_BTN_SHORT;
        }
        btn_cnt[btn] = 0;
    }
}

/// @brief 按键的边沿变化有效状态捕获
/// @param btn 预检测的按键
/// @param filter_time  是否开启滤波处理
static void key_edge_capture(BTN_TYPE_t btn, uint8_t filter_time)
{
    bool cur_level = (button[btn].io)();
    if((btn_last_level[btn] ^ cur_level))     // 电平有变化 (Enable or Disable)
    {
        if(++btn_cnt[btn] >= filter_time)
        {
            btn_last_level[btn] = cur_level;
            btn_cap[btn] = (cur_level == 1) ? STA_BTN_START : STA_BTN_END;
            btn_cnt[btn] = 0;
        }
    }
    else btn_cnt[btn] = 0;
}
/// @brief 按键扫描,插入到systick中断中
/// @param  
SYSTICKCALL(key_normal_scan)
{
    for(uint16_t i = 0; i < BTN_NUM; i++)
    {
        if(button[i].mode != MODE_BTN_EDG)
        {
            key_level_capture((BTN_TYPE_t)i, BTN_POR_SHORT, BTN_POR_LONG);
        }
        else
        {
            key_edge_capture((BTN_TYPE_t)i, BTN_POR_SHORT);
        }
        if(button[i].io()) 
        {
            if(key_ins_cnt[i] < 0) key_ins_cnt[i] = 0;
            else if(key_ins_cnt[i] < 30000) key_ins_cnt[i]++;
        }
        else
        {
            if(key_ins_cnt[i] > 0) key_ins_cnt[i] = 0;
            else if(key_ins_cnt[i] > -30000) key_ins_cnt[i]--;
        }
    }
}

/// @brief 按键初始化
/// @param 
void bsp_key_init(void)
{
    for(uint16_t i = 0; i < BTN_NUM; i++)
    {
        btn_last_level[i] = button[i].io();
        if(button[i].mode == MODE_BTN_LEVEL) btn_cnt[i] = 0xFFFF; // 防止按键损坏长期按着上下电异常
    }

    hal_timer.systick_insert(&key_normal_scan);
}

/// @brief 掉电时按键唤醒后扫描
/// @param btn - 按键类型
/// @param filter_cnt - 是否开启滤波处理
void bsp_key_wakeup_scan(BTN_TYPE_t btn, uint8_t filter_cnt)
{
    uint16_t valid_cnt = 0;

    for(uint16_t i = 0; i < filter_cnt; i++)
    {
        if(button[btn].io()) valid_cnt++;
    }

    if(btn_cap[btn] == STA_BTN_NULL && valid_cnt > (filter_cnt/2))
    {
        btn_cap[btn] = STA_BTN_SHORT;
    }
    
    hal_mcu.wait_us(600); // 防止按键抖动循环进入中断问题！！！
}

/// @brief           // 获取按键的状态
/// @param btn       // 按键类型
/// @return          // 按键状态
///                     STA_BTN_NULL   --- 未触发过按键
///                     STA_BTN_SHORT  --- 短按键
///                     STA_BTN_LONG   --- 长按键
///                     STA_BTN_START  --- 开始沿
///                     STA_BTN_END    --- 结束沿
BTN_STATE_t bsp_key_state_get(BTN_TYPE_t btn)
{
    if(btn >= BTN_NUM) return STA_BTN_NULL;
    
    BTN_STATE_t btnState = btn_cap[btn];
    if(btnState != STA_BTN_NULL) btn_cap[btn] = STA_BTN_NULL;
    return btnState;
}

/// @brief           // 获取按键的瞬时动作状态
/// @param btn       // 按键类型
/// @param filter_ms // 是否开启滤波处理, 秒任务处理建议滤波不要超过1S
/// @return          // 按键是否瞬时动作, STA_BTN_YES-按键滤波filter_ms后确认有效，STA_BTN_NO-按键滤波filter_ms后确认无效，STA_BTN_NULL-按键未动作
BTN_STATE_t bsp_key_action_get(BTN_TYPE_t btn, uint16_t filter_ms)
{
    int16_t cnt = key_ins_cnt[btn];
    if((cnt > 0) && (cnt    >= filter_ms)) return STA_BTN_YES;
    if((cnt < 0) && ((-cnt) >= filter_ms)) return STA_BTN_NO;
    return STA_BTN_NULL;
    ///return (button[btn].io() && key_ins_cnt[btn] >= filter_ms);
}


/**
 * @brief  获取当前键盘输入字符
 * @param  无
 * @retval 键字符, 返回TYPE_KEY_NONE表示无输入字符
 */

/// @brief 显示按键状态获取
/// @param  
/// @return 
keyboard_type_s bsp_disp_key_indent(void)
{
    keyboard_type_s key_none = {TYPE_KEY_NONE, TYPE_KEY_SHORT};
#if USE_BTN_DISP_DN
    for(uint8_t i = 0; i < eleof(disp_btn); i++)
    {
        BTN_STATE_t mode = key.state_get(disp_btn[i].btn);
        if(mode != STA_BTN_NULL)
        {
            key_none.value = disp_btn[i].btn_ch;
            if(mode == STA_BTN_LONG)  // 默认短按
            {
                key_none.mode = TYPE_KEY_LONG;   //
            }
            break;
        }
    }
#endif
    return key_none;
}

//
void bsp_key_disp_btn_pwrdn_scan(void)
{
#if USE_BTN_DISP_DN || USE_BTN_DISP_UP    
    for(uint8_t i = 0; i < eleof(disp_btn); i++)
    {
        key.wakeup_scan(disp_btn[i].btn, 200);
    }
#endif
}

/// @brief 声明btn子模块对象
const struct key_s key = 
{
    .init               = bsp_key_init,
    .wakeup_scan        = bsp_key_wakeup_scan,
    .state_get          = bsp_key_state_get,
    .action_get         = bsp_key_action_get,
#if USE_BTN_DISP_DN
    .indent             = bsp_disp_key_indent,
    .disp_btn_pwrdn_scan = bsp_key_disp_btn_pwrdn_scan,
#endif
};

