/**
 ******************************************************************************
* @file    hal_spi_sw0.c
* <AUTHOR> @version V1.0.0
* @date    2024
* @brief   本模块完成MASTER SPI总线的软件模拟驱动. HT6025-22MHz-开启指令预取，实测530KHz以下的SPI速率可达到，高于530KHz的SPI速率受限于时钟周期。
*          主要用于flash： SCLK常态低电平 CPOL-0，CPHA-0(第一个沿采数据)，
*          1，SCLK常态低电平 CPOL-0，CPHA-0(第一个沿采数据)
*          2，SCLK常态低电平 CPOL-0，CPHA-1(第二个沿采数据)
*          3，SCLK常态高电平 CPOL-1，CPHA-0(第一个沿采数据)
*          4，SCLK常态高电平 CPOL-1，CPHA-0(第二个沿采数据)
*          常用FLASH主要支持1和4
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
* 
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_spi.h"
#include "hal_gpio.h"


/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/
#define SPI_SW_SDO_GET()     gpio_input_get(PIN_FLASH_MISO)
#define SPI_SW_SDI_CLR()     gpio_out_L(PIN_FLASH_MOSI)
#define SPI_SW_SDI_SET()     gpio_out_H(PIN_FLASH_MOSI)
#define SPI_SW_SCK_CLR()     gpio_out_L(PIN_FLASH_SCLK)
#define SPI_SW_SCK_SET()     gpio_out_H(PIN_FLASH_SCLK)
#define SPI_SW_CS_ON()       gpio_out_L(PIN_FLASH_CS)
#define SPI_SW_CS_OFF()      gpio_out_H(PIN_FLASH_CS)
#define SPI_SW_NOP()         {__no_operation();__no_operation();} 


/// @brief SPI_SW0数据传输
/// @param ch 发送的字符，8位数据
/// @param mod 1-发送一字节，0-接收一字节
/// @return 
char hal_spiSW0_trans(uint8_t ch, uint8_t mod)
{
    uint8_t read = 0;
    uint8_t i;
    if(mod)
    {
        for(i = 0; i < 8; i++)
        {
            SPI_SW_SCK_CLR();
            if(ch & 0x80){SPI_SW_SDI_SET();}
            else{SPI_SW_SDI_CLR();}
            ch <<= 1;
            SPI_SW_SCK_SET();
        }
        SPI_SW_SCK_CLR();
        return 0xff;
    }
    else
    {
        for(i = 0; i < 8; i++)
        {
            SPI_SW_SCK_CLR();
            read <<= 1;
            if(SPI_SW_SDO_GET())
            {
                read |= 0x01;
            }
            else
            {
                read &= 0xfe;
            }
            SPI_SW_SCK_SET();
        }
        //SPI_SW_SCK_CLR();
        return read;        
    }
}

char hal_spiSW0_write(uint8_t ch)
{
    uint8_t i;
    for(i = 0; i < 8; i++)
    {
        SPI_SW_SCK_CLR();
        if(ch & 0x80){SPI_SW_SDI_SET();}
        else{SPI_SW_SDI_CLR();}
        ch <<= 1;
        SPI_SW_SCK_SET();
    }
    SPI_SW_SCK_CLR();
    return 0xff;
}

char hal_spiSW0_read(void)
{
    uint8_t read = 0;
    uint8_t i;
    
    for(i = 0; i < 8; i++)
    {
        SPI_SW_SCK_CLR();
        read <<= 1;
        if(SPI_SW_SDO_GET())
        {
            read |= 0x01;
        }
        else
        {
            read &= 0xfe;
        }
        SPI_SW_SCK_SET();
    }
    SPI_SW_SCK_CLR();
    return read;
}


void hal_spiSW0_open(uint16_t kbps)
{
    hal_gpio.data_flash(GPIO_OPEN);
    for(uint16_t i = 0; i < 500; i++){}
}

/* @brief Close 一个SPI总线 */
void hal_spiSW0_close(void)
{
    SPI_SW_CS_OFF();
}

/* @brief SPI_CS使能 */
void hal_spiSW0_deviceon(void)
{
    SPI_SW_CS_OFF();
    SPI_SW_SCK_CLR();
    SPI_SW_CS_ON(); 
}

/* @brief SPI_CS关闭 */
void hal_spiSW0_deviceoff(void)
{
    SPI_SW_CS_OFF();
}


/** @} */
