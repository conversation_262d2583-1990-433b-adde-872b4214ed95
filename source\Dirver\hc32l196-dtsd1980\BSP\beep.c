/**
 ******************************************************************************
* @file    beep.c
* <AUTHOR> @date     2024
* @brief   本模块完成对蜂鸣器的控制逻辑。蜂鸣器驱动，移植只需改头文件。
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "beep.h"
#include "hal_timer.h"

/* Private typedef -----------------------------------------------------------*/
typedef struct
{
    uint16_t tm_cnt;
    uint16_t freq;

    uint16_t cycleontimer;
    uint16_t cycleofftimer;
    uint32_t durationontimer;
    uint32_t durationofftimer;

    uint16_t cycleontimeout;
    uint16_t cycleofftimeout;
    uint32_t durationontimeout;
    uint32_t durationofftimeout;
}tBuzzCtrl;

/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static tBuzzCtrl beep_ctrl;

/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/// @brief Beep duration process. 蜂鸣器信号持续时间处理
/// @param  
#if USE_BUZZER
void beep_duration_pro(void)
{
    if (beep_ctrl.durationontimeout == 0) return;

    if (beep_ctrl.durationontimer < (beep_ctrl.durationontimeout - 1))
    {
        beep_ctrl.durationontimer++;
    }
    else
    {
        if (beep_ctrl.durationontimer != 0xFFFFFFFF)
        {
            BEEP_OFF();
            beep_ctrl.durationontimer = 0xFFFFFFFF;
            beep_ctrl.durationofftimer = 0;
            beep_ctrl.cycleontimer = 0xFFFF;
            beep_ctrl.cycleofftimer = 0xFFFF;

            if (beep_ctrl.durationofftimeout == 0)
            {
                beep_ctrl.cycleontimeout = 0;
                beep_ctrl.durationontimeout = 0;
                return;
            }
        }
    }

    if (beep_ctrl.durationofftimer < beep_ctrl.durationofftimeout)
    {
        beep_ctrl.durationofftimer++;
    }
    else
    {
        if (beep_ctrl.durationofftimer != 0xFFFFFFFF)
        {
            beep_ctrl.durationofftimer = 0xFFFFFFFF;
            beep_ctrl.durationontimer = 0;
            beep_ctrl.cycleontimer = 0;
            beep_ctrl.cycleofftimer = 0xFFFF;
            BEEP_ON(beep_ctrl.freq);
        }
    }
}

/**
 * @brief Beep cycle process. 蜂鸣器信号周期处理
 * @param None
 * @retval None
 */
SYSTICKCALL(beep_process)
{
    if(beep_ctrl.tm_cnt < 250)
    {
        beep_ctrl.tm_cnt++;
    }
    else
    {
        beep_ctrl.tm_cnt = 0;
        beep_duration_pro();
    }

    if(beep_ctrl.cycleontimeout == 0) return;

    if (beep_ctrl.cycleontimer < beep_ctrl.cycleontimeout)
    {
        beep_ctrl.cycleontimer++;
        return;
    }
    else
    {
        if (beep_ctrl.cycleontimer != 0xFFFF) 
        {
            BEEP_OFF();
            beep_ctrl.cycleontimer = 0xFFFF;
            beep_ctrl.cycleofftimer = 0;

            if (beep_ctrl.cycleofftimeout == 0)
            {
                beep_ctrl.cycleontimeout = 0;
                return;
            }
        }
    }

    if (beep_ctrl.cycleofftimer< beep_ctrl.cycleofftimeout)
    {
        beep_ctrl.cycleofftimer++;
    }
    else
    {
        if (beep_ctrl.cycleofftimer != 0xFFFF)
        {
            beep_ctrl.cycleofftimer = 0xFFFF;
            beep_ctrl.cycleontimer = 0;
            BEEP_ON(beep_ctrl.freq);
        }
    }
}
#endif
/// @brief 打开蜂鸣器,///响cycleon ms，关cycleoff ms，持续durationon秒，durationoff秒循环，频率4000+0*100 Hz 
/// @param cycleon       鸣叫时间，单位-ms
/// @param cycleoff      关闭时间，单位-ms
/// @param durationon    本次开启开启时间，单位-1/4s
/// @param durationoff   开启周期，单位-1/4s
/// @param freq          声音频率
void beep_start(uint16_t cycleon, uint16_t cycleoff, uint32_t durationon, uint32_t durationoff, uint16_t freq)
{
#if USE_BUZZER
    if(cycleon == beep_ctrl.cycleontimeout
    && cycleoff == beep_ctrl.cycleofftimeout
    && durationon == beep_ctrl.durationontimeout
    && durationoff == beep_ctrl.durationofftimeout
    && freq == beep_ctrl.freq)
        return;

    HAL_CRITICAL_STATEMENT
    (
        beep_ctrl.tm_cnt = 0;

        beep_ctrl.cycleontimer = 0;
        beep_ctrl.cycleofftimer = 0xFFFF;
        beep_ctrl.durationontimer = 0;
        beep_ctrl.durationofftimer = 0xFFFF;
        beep_ctrl.freq = freq;

        beep_ctrl.cycleontimeout = cycleon;
        beep_ctrl.cycleofftimeout = cycleoff;
        beep_ctrl.durationontimeout = durationon;
        beep_ctrl.durationofftimeout = durationoff;
        BEEP_ON(beep_ctrl.freq);
    );
#endif
}

/**
 * @brief Stop the beep.
 * @param None
 * @retval None
 */
void beep_stop(void)
{
#if USE_BUZZER
    HAL_CRITICAL_STATEMENT
    (
        BEEP_OFF();
        beep_ctrl.cycleontimeout = 0;
        beep_ctrl.durationontimeout = 0;
    );
#endif
}

/// @brief 初始化蜂鸣器
/// @param  
void beep_init(void)
{
#if USE_BUZZER
    hal_timer.systick_insert(&beep_process);
#endif
}



/// @brief 声明beep子模块对象
const struct beep_s beep =
{
    .init           = beep_init,
    .start          = beep_start,
    .stop           = beep_stop,
};

/** @} */
/** @} */
/** @} */
