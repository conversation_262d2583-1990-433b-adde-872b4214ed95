/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   bgr.c
 *
 * @brief  Source file for BGR functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "bgr.h"
/**
 *******************************************************************************
 ** \addtogroup FlashGroup
 ******************************************************************************/
//@{

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 *****************************************************************************
 ** \brief BGR 使能
 **
 ** 
 ** \retval Null                                      
 *****************************************************************************/
void Bgr_BgrEnable(void)
{    
    M0P_BGR->CR |= 0x1u;
    
    delay10us(2);
}

/**
 *****************************************************************************
 ** \brief BGR 禁止
 **
 ** 
 ** \retval Null                                     
 *****************************************************************************/
void Bgr_BgrDisable(void)
{
    M0P_BGR->CR &= 0x2u;
}

/**
 *****************************************************************************
 ** \brief BGR 温度传感器使能(需要先开启BGR)
 **
 ** 
 ** \retval Null                                      
 *****************************************************************************/
void Bgr_TempSensorEnable(void)
{
    M0P_BGR->CR |= 0x2u;
    
    delay10us(2);  
}

/**
 *****************************************************************************
 ** \brief BGR 温度传感器禁止
 **
 ** 
 ** \retval Null                                      
 *****************************************************************************/
void Bgr_TempSensorDisable(void)
{
    M0P_BGR->CR &= 0x1u;
}


//@} // BgrGroup

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
