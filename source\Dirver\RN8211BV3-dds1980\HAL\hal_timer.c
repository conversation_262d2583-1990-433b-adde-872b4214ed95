
#include "typedef.h"
#include "hal_timer.h"
#include "hal_mcu.h"
#include "rn8xxx_ll_sysc.h"
#include "rn8xxx_ll_simptc.h"

#define TMRx_cnt M0P_TIM1_MODE0    /// 定义毫秒计数器定时器通道
#define TMRx_cnt_timern 1          /// 定义毫秒计数器定时器通道号
#define PWM_TIMER_NO 0             /// 定义PWM定时器通道号

static void (*hal_systick_proc)(void);
static CallChain_s      *systick_call = NULL;    // 系统节拍中断调用函数指针链表
volatile static uint32_t systick_cnt;

extern uint32_t SystemCoreClock;

#if PWM_TIMER_EN
typedef union
{
#if PWM_TIMER0_EN
    M0P_TIM0_MODE0_TypeDef *TIM0_MODE0;
#endif
#if PWM_TIMER1_EN
    M0P_TIM1_MODE0_TypeDef *TIM1_MODE0;
#endif
#if PWM_TIMER2_EN
    M0P_TIM2_MODE0_TypeDef *TIM2_MODE0;
#endif
} TimerModePtr;
/* PWM定时器实例 */
static const TimerModePtr PWM_TMRx[PWM_TIMER_NUM] = {
#if PWM_TIMER0_EN
    {.TIM0_MODE0 = M0P_TIM0_MODE0},
#endif
#if PWM_TIMER1_EN
    {.TIM1_MODE0 = M0P_TIM1_MODE0},
#endif
#if PWM_TIMER2_EN
    {.TIM2_MODE0 = M0P_TIM2_MODE0},
#endif
};
#endif

/// @brief 系统节拍定时器服务函数，1ms
/// @param
void SysTick_Handler(void)
{
    systick_cnt++;

    if(hal_systick_proc != NULL) (hal_systick_proc)();
    /* 遍询系统节拍调用函数链 */
    for(CallChain_s *c = systick_call; c != NULL; c = c->next) { c->func(); }
}

/// @brief 定时器计数功能，不用开中断
#define SIMP_TCx SIMP_TC0
static inline void mcu_timer_cnt_start(uint8_t restart)
{
    if(restart == 0)
    {
        LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID, ERN_DISABLE);

        return;
    }

    LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID, ERN_ENABLE);

    sLL_SIMPTC_InitTypeDef simptcCfg;
    simptcCfg.Ctrl.bitSimpTcCtrl.EN    = SIMPTC_CTRL_DIS;      /* 计数器使能位，初始化时先不开启 */
    simptcCfg.Ctrl.bitSimpTcCtrl.OV    = SIMPTC_CTRL_OV_DIS;   /*计数器溢出标志位，写1清零 */
    simptcCfg.Ctrl.bitSimpTcCtrl.MODE  = SIMPTC_CTRL_MODE_DIS; /* 循环计数 */
    simptcCfg.Ctrl.bitSimpTcCtrl.IRQEN = SIMPTC_CTRL_IRQ_DIS;  /* 使能中断 */
    simptcCfg.mS                       = 1;
    simptcCfg.uS                       = 0;

    LL_SIMPTC_Init(SIMP_TCx, &simptcCfg);
}

/// @brief 启动系统节拍定时器
/// @param func
void hal_systick_start(void func(void))
{
    SysTick_Config(SystemCoreClock / 1000);
    NVIC_EnableIRQ(SysTick_IRQn);
    hal_systick_proc = func;
}

/// @brief 插入系统节拍回调函数
/// @param call  要插入的回调函数结构体指针
void hal_systick_call_insert(CallChain_s *call)
{
    /* 遍询系统节拍调用函数链 */
    for(CallChain_s *c = systick_call; c != NULL; c = c->next)
    {
        if(c == call) return;    // call已经在链表中了
    }

    /* 添加到链首位置 */
    call->next   = systick_call;
    systick_call = call;
}

/// @brief 删除某个后台系统节拍回调函数
/// @param call 要删除的回调函数结构体指针
void hal_systick_call_remove(CallChain_s *call)
{
    if(call == systick_call)
    {
        systick_call = systick_call->next;    // 如果在链首，则直接去掉
    }
    else
    {
        for(CallChain_s *c = systick_call; c != NULL; c = c->next)
        {
            if(c->next == call)
            {
                c->next = call->next;    // 下一个链就是call, 则改指向再下一个
                break;
            }
        }
    }
    call->next = NULL;
}
/// @brief 获取计数值
/// @return
uint32_t systick_cnt_get()
{
    return systick_cnt;
}
/// @brief 计算从tickOld到当前时间的间隔
/// @param tickOld
/// @return
uint32_t systick_interval_get(uint32_t tickOld)
{
    uint32_t tick = systick_cnt_get();

    // if(tick < tickOld) { tick = UINT32_MAX - tick + tickOld; }
    if(tick < tickOld) { tick = (UINT32_MAX - tickOld) + tick + 1; }
    else { tick = tick - tickOld; }

    return tick;
}

/// @brief 软定时器定时间隔设置
/// @param [in]  t-软定时器
/// @param [in]  interval-定时间隔，单位MS
void hal_timer_interval(SwTimer_s *t, uint32_t interval)
{
    t->interval = interval;
    t->start    = systick_cnt;
}

/// @brief 软定时器定时重新开始
/// @param [in]  t-软定时器
void hal_timer_restart(SwTimer_s *t)
{
    t->start = systick_cnt;
}

/// @brief 查询软定时器是否定时到期
/// @param [in]  t-软定时器
/// @return 0-定时未到期 1-定时已到期
bool hal_timer_expired(const SwTimer_s *t)
{
    volatile uint32_t diff;
    volatile uint32_t cnt_tmp = systick_cnt;

    if(cnt_tmp < t->start) { diff = (UINT32_MAX - t->start) + cnt_tmp + 1; }
    else { diff = cnt_tmp - t->start; }

    return boolof(t->interval < diff);
    // uint32_t diff = (systick_cnt - t->start) + 1;
    // return boolof(t->interval < diff);
}

/// 以下为定时器相关函数
/// @brief  开启PWM定时器
/// @param  [in]  t-定时器类型 HAL_PWM_TIMER_TYPE_0 ~ HAL_PWM_TIMER_TYPE_2
/// @param  [in]  hz-频率 100 - 1000000 Hz
/// @param  [in]  div-分频系数
/// @retval None
//__INLINE static void hal_pwm_start(HAL_PWM_TIMER_TYPE t, uint32_t hz)
static void hal_pwm_start(HAL_PWM_TIMER_TYPE t, uint32_t hz)
{
#if PWM_TIMER_EN
    en_bt_cr_timclkdiv_t prs;
    uint16_t             arr;

    if(hz < 100) hz = 100;
    hz %= 100000;

    if(hz <= 1000)
    {
        // 低频段 100Hz-1kHz
        prs = BtPCLKDiv64;    // PCLK 64分频
        arr = WORK_APB_HZ / 64 / hz;
    }
    else
    {
        // 高频段 1kHz-1MHz
        prs = BtPCLKDiv1;    // PCLK 不分频
        arr = WORK_APB_HZ / hz;
    }

    volatile M0P_TIM0_MODE23_TypeDef *pstcM0PBt = (M0P_TIM0_MODE23_TypeDef *)((uint32_t)M0P_TIM0_MODE23 + 0x100 * t);

    // stc_bt_mode23_cfg_t        stcBtBaseCfg;
    // stc_bt_m23_compare_cfg_t   stcBtPortCmpCfg;

    // 结构体初始化清零
    // DDL_ZERO_STRUCT(stcBtBaseCfg);
    // DDL_ZERO_STRUCT(stcBtPortCmpCfg);

    SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralBaseTim,
           TRUE);    // Base Timer外设时钟使能

    // TIMx 的模式23功能初始化
    pstcM0PBt->M23CR_f.MODE    = BtWorkMode2;         // 锯齿波模式
    pstcM0PBt->M23CR_f.PRS     = prs;                 // PCLK 不分频
    pstcM0PBt->M23CR_f.CT      = BtTimer;             // 定时器功能，计数时钟为内部PCLK
    pstcM0PBt->M23CR_f.COMP    = BtIndependentPWM;    // 独立输出PWM
    pstcM0PBt->M23CR_f.PWM2S   = BtSinglePointCmp;    // 单点比较功能
    pstcM0PBt->M23CR_f.ONESHOT = FALSE;               // 循环计数
    pstcM0PBt->M23CR_f.URS     = FALSE;               // 上下溢更新
    pstcM0PBt->M23CR_f.DIR     = BtCntUp;             // 向上计数

    pstcM0PBt->ARR_f.ARR      = arr;    // 设置重载值,并使能缓存
    pstcM0PBt->M23CR_f.BUFPEN = TRUE;
    pstcM0PBt->CCR0A_f.CCR0A  = arr / 2;    // 设置比较值A

    // 比较输出端口配置
    pstcM0PBt->CRCH0_f.CSA        = 0;
    pstcM0PBt->FLTR_f.OCMA0_FLTA0 = BtPWMMode2;        // OCREFA输出控制OCMA:PWM模式2
    pstcM0PBt->FLTR_f.CCPA0       = BtPortPositive;    // 正常输出
    pstcM0PBt->CRCH0_f.BUFEA      = TRUE;              // A通道缓存控制
    pstcM0PBt->M23CR_f.CIS        = BtCmpIntNone;      // A通道比较控制:无

    pstcM0PBt->CRCH0_f.CSB        = 0;
    pstcM0PBt->FLTR_f.OCMB0_FLTB0 = BtPWMMode2;        // OCREFA输出控制OCMA:PWM模式2
    pstcM0PBt->FLTR_f.CCPB0       = BtPortPositive;    // 正常输出
    pstcM0PBt->CRCH0_f.BUFEB      = TRUE;              // A通道缓存控制
    pstcM0PBt->CRCH0_f.CISB       = BtCmpIntNone;      // A通道比较控制:无

    pstcM0PBt->RCR_f.RCR = 0;    // 间隔周期设置
    pstcM0PBt->CNT_f.CNT = 0;    // 设置计数初值

    pstcM0PBt->DTR_f.MOE    = TRUE;    // TIMx 端口输出使能
    pstcM0PBt->DTR_f.AOE    = FALSE;
    pstcM0PBt->M23CR_f.CTEN = TRUE;    // TIMx 运行
#endif
}
/// @brief   关闭PWM定时器
/// @param t t-定时器类型 HAL_PWM_TIMER_TYPE_0 ~ HAL_PWM_TIMER_TYPE_3
/// @return  None
//__INLINE static void hal_pwm_stop(HAL_PWM_TIMER_TYPE t)
static void hal_pwm_stop(HAL_PWM_TIMER_TYPE t)
{
#if PWM_TIMER_EN
    volatile M0P_TIM0_MODE23_TypeDef *pstcM0PBt = (M0P_TIM0_MODE23_TypeDef *)((uint32_t)M0P_TIM0_MODE23 + 0x100 * t);
    pstcM0PBt->DTR_f.MOE                        = FALSE;    // TIMx 端口输出使能
    pstcM0PBt->M23CR_f.CTEN                     = FALSE;    // TIMx 关闭
    SetBit((uint32_t)(&(M0P_SYSCTRL->PERI_CLKEN0)), SysctrlPeripheralBaseTim,
           FALSE);    // 关闭Base Timer外设时钟
#endif
}

void hal_pwm_out(HAL_PWM_TIMER_TYPE ch, uint32_t hz)
{
    hal_pwm_stop(ch);
    if(hz != 0) { hal_pwm_start(ch, hz); }
}

///

/**
 * @brief  系统精确毫秒延时，才用定时器，无需开中断也可用
 * @param  [in]  ms-延时毫秒数
 * @retval None
 */
void hal_delay_ms(uint32_t ms)
{
    mcu_timer_cnt_start(1);

    while(ms--)
    {
        SIMP_TCx->VAL = 0;
        LL_SIMPTC_START(SIMP_TCx);
        while((SIMP_TCx->CTRL & (1 << 1)) == 0);    // 等待计数器溢出标志置位，即计数完成
        LL_SIMPTC_STOP(SIMP_TCx);                  // 停止计数器，清除计数器溢出标志位
        SIMP_TCx->CTRL |= 1 << 1;                  // 写1清零计数器溢出标志位
    }
    mcu_timer_cnt_start(0);
}

/// @brief //
/// @param count
void hal_delay_xms(uint16_t count)
{
    while(count--) { SystemDelayUs(1000); }
}

const struct hal_delay_s hal_timer = {
    .systick_start  = hal_systick_start,
    .systick_insert = hal_systick_call_insert,
    .systick_remove = hal_systick_call_remove,
    .systick_cnt    = systick_cnt_get,
    .msdly          = hal_delay_ms,
    .interval       = hal_timer_interval,
    .restart        = hal_timer_restart,
    .expired        = hal_timer_expired,
    .xms            = hal_delay_xms,
    .pwm_out        = hal_pwm_out,
};