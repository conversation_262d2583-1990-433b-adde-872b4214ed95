/********************************************************************************
  * @file    power_event.h
  * <AUTHOR> @date    2024
  * @brief   事件记录处理
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifndef __POWER_QUALITY_H__
#define __POWER_QUALITY_H__
#include "typedef.h"
#include "../config/app_config.h"
#include "timeapp.h"

typedef uint16_t TYPE_PE_EVT_OUT;
#define PE_EVT_OUT_LEN 8
/// bit 0-2 不可用
#define EVT_LOS_V_A_S           (0 + (1U<<3))   // A相失压发生
#define EVT_LOS_V_B_S           (0 + (1U<<4))   // B相失压发生
#define EVT_LOS_V_C_S           (0 + (1U<<5))   // C相失压发生
#define EVT_LOW_V_A_S           (0 + (1U<<6))   // A相低压发生
#define EVT_LOW_V_B_S           (0 + (1U<<7))   // B相低压发生
#define EVT_LOW_V_C_S           (0 + (1U<<8))   // C相低压发生
#define EVT_OVR_V_A_S           (0 + (1U<<9))   // A相高压发生
#define EVT_OVR_V_B_S           (0 + (1U<<10))  // B相高压发生
#define EVT_OVR_V_C_S           (0 + (1U<<11))  // C相高压发生
#define EVT_MIS_PH_A_S          (0 + (1U<<12))  // A相断相发生
#define EVT_MIS_PH_B_S          (0 + (1U<<13))  // B相断相发生
#define EVT_MIS_PH_C_S          (0 + (1U<<14))  // C相断相发生

#define EVT_LOS_V_A_E           (1 + (1U<<3))   // A相失压结束
#define EVT_LOS_V_B_E           (1 + (1U<<4))   // B相失压结束
#define EVT_LOS_V_C_E           (1 + (1U<<5))   // C相失压结束
#define EVT_LOW_V_A_E           (1 + (1U<<6))   // A相低压结束
#define EVT_LOW_V_B_E           (1 + (1U<<7))   // B相低压结束
#define EVT_LOW_V_C_E           (1 + (1U<<8))   // C相低压结束
#define EVT_OVR_V_A_E           (1 + (1U<<9))   // A相高压结束
#define EVT_OVR_V_B_E           (1 + (1U<<10))  // B相高压结束
#define EVT_OVR_V_C_E           (1 + (1U<<11))  // C相高压结束
#define EVT_MIS_PH_A_E          (1 + (1U<<12))  // A相断相结束
#define EVT_MIS_PH_B_E          (1 + (1U<<13))  // B相断相结束
#define EVT_MIS_PH_C_E          (1 + (1U<<14))  // C相断相结束

#define EVT_ALL_LOS_V_S         (2 + (1U<<3))   // 全失压发生
#define EVT_PWR_DOWN_S          (2 + (1U<<4))   // 掉电事件发生
#define EVT_UNB_V_S             (2 + (1U<<5))   // 电压不平衡发生
#define EVT_REV_V_SEQ_S         (2 + (1U<<6))   // 电压逆序发生
#define EVT_PWDN_ABNORMAL       (2 + (1U<<7))   // 异常重启

#define EVT_ALL_LOS_V_E         (2 + (1U<<8))   // 全失压结束
#define EVT_PWR_DOWN_E          (2 + (1U<<9))   // 掉电事件结束
#define EVT_UNB_V_E             (2 + (1U<<10))  // 电压不平衡结束
#define EVT_REV_V_SEQ_E         (2 + (1U<<11))  // 电压逆序结束

#define EVT_LOS_I_A_S           (3 + (1U<<3))   // A相失流发生
#define EVT_LOS_I_B_S           (3 + (1U<<4))   // B相失流发生
#define EVT_LOS_I_C_S           (3 + (1U<<5))   // C相失流发生
#define EVT_OVR_I_A_S           (3 + (1U<<6))   // A相过流发生
#define EVT_OVR_I_B_S           (3 + (1U<<7))   // B相过流发生
#define EVT_OVR_I_C_S           (3 + (1U<<8))   // C相过流发生
#define EVT_MIS_I_A_S           (3 + (1U<<9))   // A相断流发生
#define EVT_MIS_I_B_S           (3 + (1U<<10))  // B相断流发生
#define EVT_MIS_I_C_S           (3 + (1U<<11))  // C相断流发生
#define EVT_UNB_I_S             (3 + (1U<<12))  // 电流不平衡发生
#define EVT_REV_I_SEQ_S         (3 + (1U<<13))  // 电流逆序  发生

#define EVT_LOS_I_A_E           (4 + (1U<<3))   // A相失流结束
#define EVT_LOS_I_B_E           (4 + (1U<<4))   // B相失流结束
#define EVT_LOS_I_C_E           (4 + (1U<<5))   // C相失流结束
#define EVT_OVR_I_A_E           (4 + (1U<<6))   // A相过流结束
#define EVT_OVR_I_B_E           (4 + (1U<<7))   // B相过流结束
#define EVT_OVR_I_C_E           (4 + (1U<<8))   // C相过流结束
#define EVT_MIS_I_A_E           (4 + (1U<<9))   // A相断流结束
#define EVT_MIS_I_B_E           (4 + (1U<<10))  // B相断流结束
#define EVT_MIS_I_C_E           (4 + (1U<<11))  // C相断流结束
#define EVT_UNB_I_E             (4 + (1U<<12))  // 电流不平衡结束
#define EVT_REV_I_SEQ_E         (4 + (1U<<13))  // 电流逆序  结束

#define EVT_REV_P_A_S           (5 + (1U<<3))   // A相潮流反向功率发生
#define EVT_REV_P_B_S           (5 + (1U<<4))   // B相潮流反向功率发生
#define EVT_REV_P_C_S           (5 + (1U<<5))   // C相潮流反向功率发生
#define EVT_OVR_P_S             (5 + (1U<<6))   // 总 有功功率过载发生
#define EVT_OVR_P_A_S           (5 + (1U<<7))   // A相有功功率过载发生
#define EVT_OVR_P_B_S           (5 + (1U<<8))   // B相有功功率过载发生
#define EVT_OVR_P_C_S           (5 + (1U<<9))   // C相有功功率过载发生
#define EVT_LOW_PF_S            (5 + (1U<<10))  // 总   功率因素低发生

#define EVT_REV_P_A_E           (6 + (1U<<3))   // A相潮流反向功率结束
#define EVT_REV_P_B_E           (6 + (1U<<4))   // B相潮流反向功率结束
#define EVT_REV_P_C_E           (6 + (1U<<5))   // C相潮流反向功率结束
#define EVT_OVR_P_E             (6 + (1U<<6))   // 总 有功功率过载结束
#define EVT_OVR_P_A_E           (6 + (1U<<7))   // A相有功功率过载结束
#define EVT_OVR_P_B_E           (6 + (1U<<8))   // B相有功功率过载结束
#define EVT_OVR_P_C_E           (6 + (1U<<9))   // C相有功功率过载结束
#define EVT_LOW_PF_E            (6 + (1U<<10))  // 总   功率因素低结束

#define EVT_OVR_MD_PA_S         (7 + (1U<<3))   // 正向有功需量过载发生
#define EVT_OVR_MD_NA_S         (7 + (1U<<4))   // 反向有功需量过载发生
#define EVT_OVR_MD_Q1_S         (7 + (1U<<5))   // Q1  无功需量过载发生
#define EVT_OVR_MD_Q2_S         (7 + (1U<<6))   // Q2  无功需量过载发生
#define EVT_OVR_MD_Q3_S         (7 + (1U<<7))   // Q3  无功需量过载发生
#define EVT_OVR_MD_Q4_S         (7 + (1U<<8))   // Q4  无功需量过载发生

#define EVT_OVR_MD_PA_E         (7 + (1U<<9))   // 正向有功需量过载结束
#define EVT_OVR_MD_NA_E         (7 + (1U<<10))  // 反向有功需量过载结束
#define EVT_OVR_MD_Q1_E         (7 + (1U<<11))  // Q1  无功需量过载结束
#define EVT_OVR_MD_Q2_E         (7 + (1U<<12))  // Q2  无功需量过载结束
#define EVT_OVR_MD_Q3_E         (7 + (1U<<13))  // Q3  无功需量过载结束
#define EVT_OVR_MD_Q4_E         (7 + (1U<<14))  // Q4  无功需量过载结束

typedef union
{
    struct
    {
        uint32_t vol_loss     :3; // 失压，bit0~2 - A~C相
        uint32_t vol_ovr      :3; // 过压，bit0~2 - A~C相
        uint32_t vol_low      :3; // 欠压，bit0~2 - A~C相
        uint32_t ph_miss      :3; // 断相，bit0~2 - A~C
        uint32_t v_all_miss   :1; // 全失压
        uint32_t pwdn         :1; // 掉电
        uint32_t vol_unb      :1; // 电压不平衡
        uint32_t vol_rev_seq  :1; // 电压逆序
    };
    uint32_t lword;
}v_status_s;

typedef union
{
    struct
    {
        uint32_t i_loss       :3; // 失流，bit0~2 - A~C相
        uint32_t i_high       :3; // 过流，bit0~2 - A~C相
        uint32_t i_miss       :3; // 断流，bit0~2 - A~C相
        uint32_t i_unb        :1; // 电流不平衡
        uint32_t i_unb2       :1; // 电流严重不平衡
        uint32_t i_rev_seq    :1; // 电流逆序
    };
    uint32_t lword;
}i_status_s;

typedef union
{
    struct
    {
        uint32_t p_rev        :3; // 潮流反向，bit0~2 - A~C相
        uint32_t p_over       :4; // 过载，    bit0~3 - 总，A~C相
        uint32_t md_over      :6; // 超需量，  bit0~3 - 总，正向有功需量，反向有功需量 Q1-Q4 需量
        uint32_t pf_low       :1; // 总功率因素低
        uint32_t q_rev        :3; // 无功潮流反向，bit0~3 - A-C
    };
    uint32_t lword;
}p_status_s;

typedef struct
{
    v_status_s v;
    i_status_s i;
    p_status_s p;
}pe_status_s;


/// @brief 参数
typedef struct 
{
    uint16_t crc; // 校验码
    uint16_t chk;
/// 判断条件
#if EVENT_LOSS_VOL_EN
    uint16_t vol_loss_thd_v_h;  // 失压事件电压触发上限
    uint16_t vol_loss_thd_v_l;  // 失压事件电压恢复下限
    uint32_t vol_loss_thd_i;    // 失压事件电流触发下限
#endif

#if EVENT_LOW_VOL_EN
    uint16_t vol_low_thd_v;     // 欠压事件电压触发上限
#endif

#if EVENT_OVR_VOL_EN
    uint16_t vol_ovr_thd_v;     // 过压事件电压触发上限
#endif

#if EVENT_MISS_VOL_EN
    uint16_t vol_miss_thd_v;    // 断相事件电压触发上限
    uint32_t vol_miss_thd_i;    // 断相事件电流触发上限
#endif
#if EVENT_ALL_LOSS_VOL_EN
    uint16_t vol_all_mis_thd_v; // 全失压事件电压触发上限
    uint32_t vol_all_mis_thd_i; // 全失压事件电流触发上限
#endif

#if EVENT_V_UNB_EN           
    uint16_t vol_unb_thd;       // 电压不平衡率限值
#endif

#if EVENT_I_UNB_EN       
    uint16_t i_unb_thd;         // 电流不平衡率限值
#endif

#if EVENT_LOS_CUR_EN         
    uint16_t i_loss_thd_v;      // 失流事件电压触发上限
    uint32_t i_loss_thd_i_h;    // 失流事件电流触发上限
    uint32_t i_loss_thd_i_l;    // 失流事件电流触发下限
#endif

#if EVENT_OVR_CUR_EN         
    uint32_t i_ovr_thd_i;       // 过流事件电流触发上限
#endif

#if EVENT_MISS_CUR_EN        
    uint16_t i_miss_thd_v;      // 断流事件电压触发下限
    uint32_t i_miss_thd_i;      // 断流事件电流触发上限
#endif

#if EVENT_REV_EN             
    uint32_t p_rev_thd_p;       // 潮流反向功率触发下限
#endif

#if EVENT_OVR_LOAD_EN        
    uint32_t p_ovr_thd_p;       // 过载功率触发下限
#endif

#if EVENT_LOW_PF_EN          
    uint16_t pf_low_thd_pf;     // 总功率因素低触发下限
#endif

// 判定延时时间
#if EVENT_LOSS_VOL_EN
    uint8_t  vol_loss_thd_time; // 失压事件判定延时时间
#endif
#if EVENT_LOW_VOL_EN
    uint8_t  vol_low_thd_time;  // 欠压事件判定延时时间
#endif
#if EVENT_OVR_VOL_EN
    uint8_t  vol_ovr_thd_time;  // 过压事件判定延时时间
#endif
#if EVENT_MISS_VOL_EN
    uint8_t  vol_miss_thd_time; // 断相事件判定延时时间
#endif
#if EVENT_ALL_LOSS_VOL_EN
    uint8_t  vol_all_mis_thd_time; // 全失压事件判定延时时间
#endif
#if EVENT_V_REV_SQR_EN
    uint8_t  vol_rev_sqr_thd_time; // 电压逆序判断延时时间
#endif
#if EVENT_I_REV_SQR_EN
    uint8_t  i_rev_sqr_thd_time;   // 电流逆序判断延时时间
#endif
#if EVENT_V_UNB_EN           
    uint8_t  vol_unb_thd_time;  // 电压不平衡判定延时时间
#endif
#if EVENT_I_UNB_EN       
    uint8_t  i_unb_thd_time;    // 电流不平衡判定延时时间
#endif
#if EVENT_LOS_CUR_EN         
    uint8_t  i_loss_thd_time;   // 失流事件判定延时时间
#endif
#if EVENT_OVR_CUR_EN         
    uint8_t  i_ovr_thd_time;    // 过流事件判定延时时间
#endif
#if EVENT_MISS_CUR_EN        
    uint8_t  i_miss_thd_time;   // 断流事件判定延时时间
#endif
#if EVENT_REV_EN             
    uint8_t  p_rev_thd_time;    // 潮流反向功率判定延时时间
#endif
#if EVENT_OVR_LOAD_EN        
    uint8_t  p_ovr_thd_time;    // 过载功率判定延时时间
#endif
#if EVENT_LOW_PF_EN          
    uint8_t  pf_low_thd_time;   // 总功率因素低判定延时时间
#endif
}pe_para_s;

typedef struct
{
    uint32_t rcd_num; // 记录数
    uint32_t time_s;  // 发生时间
    uint32_t time_e;  // 结束时间
}pe_rcd_s;

/// @brief 
typedef struct 
{
    uint16_t crc; // 校验码
    uint16_t chk;

    pe_status_s confirm; // 确认状态

    //事件
#if EVENT_LOSS_VOL_EN
    pe_rcd_s  vol_loss;
#endif
#if EVENT_LOW_VOL_EN
    pe_rcd_s  vol_low; 
#endif
#if EVENT_OVR_VOL_EN
    pe_rcd_s  vol_ovr; 
#endif
#if EVENT_MISS_VOL_EN
    pe_rcd_s  vol_miss;
#endif
#if EVENT_V_UNB_EN     
    pe_rcd_s  vol_unb; 
#endif
#if EVENT_I_UNB_EN 
    pe_rcd_s  i_unb;   
#endif
#if EVENT_LOS_CUR_EN   
    pe_rcd_s  i_loss;  
#endif
#if EVENT_OVR_CUR_EN   
    pe_rcd_s  i_ovr;   
#endif
#if EVENT_MISS_CUR_EN  
    pe_rcd_s  i_miss;  
#endif
#if EVENT_REV_EN       
    pe_rcd_s  p_rev;   
#endif
#if EVENT_OVR_LOAD_EN  
    pe_rcd_s  p_ovr;   
#endif
#if EVENT_LOW_PF_EN    
    pe_rcd_s  pf_low;  
#endif

}pe_data_s;



/* !!!上下电数据，需要掉电保存 */
#define TYPE_PWDN_STUS  uint8_t
#define STUS_PWR_UNKNOW             0x00
#define STUS_PWR_DOWN_CNF           0x01        // 掉电确认
#define STUS_PWR_ON_CNF             0x02        // 上电确认

typedef struct
{
    uint16_t crc;
    uint16_t chk;
    TYPE_PWDN_STUS pwdn_state;    // 上下电状态
    uint32_t cum_pwdn_time;       // 累计掉电时长, unit:秒
    uint32_t cum_pwon_time;       // 累计上电时长, unit:秒
    uint32_t cum_pwdn_cnt;        // 累计掉电次数
    uint32_t lst_lp_duration;     // 上一次掉电时长
    clock_s  pwdn_clock;          // 掉电时间戳
    clock_s  pwon_clock;          // 上电时间戳

#if EVENT_LOSS_VOL_EN
    uint32_t vol_loss_time_s;     // 最近一次失压事件发生时间
    uint32_t vol_loss_time_e;     // 最近一次失压事件结束时间 
    uint32_t vol_loss_cnt[4];     // 总ABC相 失压总次数
    uint32_t vol_loss_time_cnt[4];// 总ABC相 失压总累计时间 单位:秒
    uint32_t vol_loss_ah[3][3];   // ABC相   失压期间ABC相安时-> 最近一次 0.01Ah
#endif

#if EVENT_LOW_VOL_EN
    uint32_t vol_low_time_s;      // 最近一次欠压事件发生时间
    uint32_t vol_low_time_e;      // 最近一次欠压事件结束时间
    uint32_t vol_low_cnt[4];      // 总ABC相 欠压总次数
    uint32_t vol_low_time_cnt[4]; // 总ABC相 欠压总累计时间
    uint32_t vol_low_ah[3][3];    // ABC相   欠压期间ABC相安时-> 最近一次 0.01Ah
#endif
#if EVENT_OVR_VOL_EN
    uint32_t vol_ovr_time_s;      // 最近一次过压事件发生时间
    uint32_t vol_ovr_time_e;      // 最近一次过压事件结束时间
    uint32_t vol_ovr_cnt[4];      // 总ABC相 过压总次数
    uint32_t vol_ovr_time_cnt[4]; // 总ABC相 过压总累计时间
    uint32_t vol_ovr_ah[3][3];    // ABC相   过压期间ABC相安时-> 最近一次 0.01Ah
#endif
#if EVENT_MISS_VOL_EN
    uint32_t ph_miss_time_s;      // 最近一次断相事件发生时间
    uint32_t ph_miss_time_e;      // 最近一次断相事件结束时间
    uint32_t ph_miss_cnt[4];      // 总ABC相 断相总次数
    uint32_t ph_miss_time_cnt[4]; // 总ABC相 断相总累计时间
    uint32_t ph_miss_ah[3][3];    // ABC相   断相期间ABC相安时-> 最近一次 0.01Ah
#endif
#if EVENT_ALL_LOSS_VOL_EN 
    uint32_t all_miss_time_s;      // 最近一次全失压事件发生时间
    uint32_t all_miss_time_e;      // 最近一次全失压事件结束时间
    uint32_t all_miss_i;           // 最近一次全失压事件电流值
    uint32_t all_miss_cnt;         //         全失压总次数
    uint32_t all_miss_time_cnt;    //         全失压总累计时间
    uint32_t all_miss_pwroff;      //         低功耗下检测到全失压
#endif
#if EVENT_V_REV_SQR_EN
    uint32_t vol_rev_sqr_time_s;   // 最近一次电压逆序事件发生时间
    uint32_t vol_rev_sqr_time_e;   // 最近一次电压逆序事件结束时间
    uint32_t vol_rev_sqr_cnt;      // 电压逆序总次数
    uint32_t vol_rev_sqr_time_cnt; // 电压逆序总累计时间
#endif
#if EVENT_I_REV_SQR_EN
    uint32_t i_rev_sqr_time_s;     // 最近一次电流逆序事件发生时间
    uint32_t i_rev_sqr_time_e;     // 最近一次电流逆序事件结束时间
    uint32_t i_rev_sqr_cnt;        // 电流逆序总次数
    uint32_t i_rev_sqr_time_cnt;   // 电流逆序总累计时间
#endif
#if EVENT_V_UNB_EN     
    uint32_t vol_unb_time_s;       // 最近一次电压不平衡事件发生时间
    uint32_t vol_unb_time_e;       // 最近一次电压不平衡事件结束时间
    uint32_t vol_unb_cnt;          // 电压不平衡总次数
    uint32_t vol_unb_time_cnt;     // 电压不平衡总累计时间
    uint32_t vol_unb_max_per;      // 电压不平衡最大值
#endif
#if EVENT_I_UNB_EN 
    uint32_t i_unb_time_s;         // 最近一次电流不平衡事件发生时间
    uint32_t i_unb_time_e;         // 最近一次电流不平衡事件结束时间
    uint32_t i_unb_cnt;            // 电流不平衡总次数  
    uint32_t i_unb_time_cnt;       // 电流不平衡总累计时间
    uint32_t i_unb_max_per;        // 电流不平衡最大值
#endif
#if EVENT_LOS_CUR_EN   
    uint32_t i_loss_time_s;        // 最近一次失流事件发生时间
    uint32_t i_loss_time_e;        // 最近一次失流事件结束时间
    uint32_t i_loss_cnt[3];        // 失流总次数
    uint32_t i_loss_time_cnt[3];   // 失流总累计时间
#endif
#if EVENT_OVR_CUR_EN   
    uint32_t i_ovr_time_s;         // 最近一次过流事件发生时间
    uint32_t i_ovr_time_e;         // 最近一次过流事件结束时间
    uint32_t i_ovr_cnt[3];         // 过流总次数
    uint32_t i_ovr_time_cnt[3];    // 过流总累计时间
#endif
#if EVENT_MISS_CUR_EN  
    uint32_t i_miss_time_s;        // 最近一次断流事件发生时间
    uint32_t i_miss_time_e;        // 最近一次断流事件结束时间
    uint32_t i_miss_cnt[3];        // 断流总次数
    uint32_t i_miss_time_cnt[3];   // 断流总累计时间
#endif
#if EVENT_REV_EN       
    uint32_t p_rev_time_s;         // 最近一次潮流反向功率事件发生时间
    uint32_t p_rev_time_e;         // 最近一次潮流反向功率事件结束时间
    uint32_t p_rev_cnt[3];         // 潮流反向功率总次数
    uint32_t p_rev_time_cnt[3];    // 潮流反向功率总累计时间
#endif
#if EVENT_OVR_LOAD_EN  
    uint32_t p_ovr_time_s;         // 最近一次过载功率事件发生时间
    uint32_t p_ovr_time_e;         // 最近一次过载功率事件结束时间
    uint32_t p_ovr_cnt[3];         // 过载总次数
    uint32_t p_ovr_time_cnt[3];    // 过载总累计时间
#endif
#if EVENT_LOW_PF_EN    
    uint32_t pf_low_time_s;        // 最近一次总功率因素低事件发生时间
    uint32_t pf_low_time_e;        // 最近一次总功率因素低事件结束时间
    uint32_t pf_low_cnt;           // 总功率因素低总次数
    uint32_t pf_low_time_cnt;      // 总功率因素低总累计时间
#endif
}pe_pwrdn_s;



struct power_event_s
{
    void (*reset)(uint8_t type);
    const pe_para_s* (*para_get)(void);
    bool (*para_set)(uint16_t ofst, const void* val, uint16_t len);
    bool (*state_query)(TYPE_PE_EVT_OUT state);
    void (*state_clr)(void);

    pe_status_s* (*status_get)(uint8_t mode);
    void (*power_down_time_get)(clock_s* time);
    void (*power_on_time_get)(clock_s* time);
    pe_data_s* (*data_get)(void);
    pe_pwrdn_s* (*pwrdn_data_get)(void);
};
extern const struct power_event_s power_event;

#endif /* __POWER_QUALITY_H__ */

