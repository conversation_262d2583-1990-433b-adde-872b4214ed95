/**
 *@description:
 *@return {*}
 **/
#include <string.h>
#include "datastore.h"
#include "utils.h"
#include "crc.h"
#include "module_para.h"

// 定义模块参数地址索引，本文件私有
#define MODULE_PARA_ADDR (nvm_addr(NVM_MODULE_PARA))

#define MODULE_PARA_CRC16 3    /// 只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct, len) STRUCT_CRC16_CHK(MODULE_PARA_CRC16, struct, len)
#define CRC16_CAL(struct, len) STRUCT_CRC16_GET(MODULE_PARA_CRC16, struct, len)

static const module_para_s *module_running_para;    /// 模块运行参数

const module_para_s module_default_para = {
    .tcp            = {14, "**************", 8890},    // {11, "***********", 7099},    //   //   //// 能耗测试  // ecs服务器
    .tcp_bak        = {11, "***********", 8090},
    .ntp_server     = {15, "ntp.aliyun.com", 123},    // 默认NTP服务器地址
    .ntp_server_bak = {15, "time.windows.com", 0},
    .apn            = "CMNET",    // 默认APN
};

/// @brief 模块参数保存函数
static bool module_para_save(uint16_t ofst, const void *val, uint16_t len)
{
    module_para_s para;
    if(ofst != 0) memcpy(&para, module_running_para, sizeof(module_para_s));
    memcpy((uint8_t *)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(module_para_s));
    module_running_para = (const module_para_s *)MODULE_PARA_ADDR;
    return nvm.write((uint32_t)module_running_para, &para, sizeof(module_para_s));
}

/// @brief 模块参数指针初始化并检验参数
static void module_para_load(void)
{
    module_running_para = (const module_para_s *)MODULE_PARA_ADDR;
    if(CRC16_CHK(module_running_para, sizeof(module_para_s)) == false)
    {
        module_running_para = &module_default_para;    // 如果CRC校验失败，使用默认参数
    }
}

void module_init(void)
{
    module_para_load();    // 初始化模块参数
}

void module_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        /* 恢复默认参数 */
        module_para_save(0, &module_default_para, sizeof(module_para_s));
    }
    if(type & SYS_DATA_RESET) { /* 恢复默认数据 */ }
}

/// @brief 获取模块参数
/// @param buf 缓冲区指针
/// @param typ 模块参数类型
/// @return 返回数据长度，当为端口号时，返回uint16_t类型的端口号
uint16_t module_para_get(void *buf, MODULE_PARA_t typ)
{
    const module_para_s *para = module_running_para;
    uint8_t             *pbuf = (uint8_t *)buf;
    uint16_t             len  = 0;

    if(pbuf == NULL || typ > MODULE_NTP_SERVER_BAK) return 0;    // 参数错误

    switch(typ)
    {
        case MODULE_TCP_IP:
            len = para->tcp.len;                                                          // 获取IP地址长度
            if(len == 0 || len > sizeof(para->tcp.ip)) { len = sizeof(para->tcp.ip); }    // IP地址长度为0或超过最大长度
            memcpy(pbuf, para->tcp.ip, len);
            break;
        case MODULE_TCP_PORT:
            return para->tcp.port;    // 返回主IP端口号

        case MODULE_TCP_IP_BAK:
            len = para->tcp_bak.len;                                                              // 获取备用IP地址长度
            if(len == 0 || len > sizeof(para->tcp_bak.ip)) { len = sizeof(para->tcp_bak.ip); }    // 备用IP地址长度为0或超过最大长度
            memcpy(pbuf, para->tcp_bak.ip, len);
            break;
        case MODULE_TCP_BAK_PORT:
            return para->tcp_bak.port;    // 返回备用IP端口号
        case MODULE_APN:
            memcpy(pbuf, (uint8_t *)para->apn, MODULE_APN_LEN);    // 复制APN到缓冲区
            return MODULE_APN_LEN;                                 // 返回APN长度

        case MODULE_NTP_SERVER:
            len = para->ntp_server.len;                                                                 // 获取NTP服务器地址长度
            if(len == 0 || len > sizeof(para->ntp_server.ip)) { len = sizeof(para->ntp_server.ip); }    // NTP服务器地址长度为0或超过最大长度
            memcpy(pbuf, para->ntp_server.ip, len);
            break;
        case MODULE_NTP_SERVER_BAK:
            len = para->ntp_server_bak.len;                                                                     // 获取备用NTP服务器地址长度
            if(len == 0 || len > sizeof(para->ntp_server_bak.ip)) { len = sizeof(para->ntp_server_bak.ip); }    // 备用NTP服务器地址长度为0或超过最大长度
            memcpy(pbuf, para->ntp_server_bak.ip, len);
            break;
        default:
            break;
    }
    return len;
}

/// @brief 设置模块参数
/// @param buf 缓冲区指针
/// @param len 长度
/// @param typ 模块参数类型
/// @return 返回true表示设置成功，false表示设置失败
bool module_para_set(uint8_t *buf, uint16_t len, MODULE_PARA_t typ)
{
    uint16_t dlen;
    uint16_t ofst;

    if(buf == NULL || typ > MODULE_NTP_SERVER_BAK || len == 0) return false;    // 参数错误

    switch(typ)
    {
        case MODULE_TCP_IP:
            dlen = member_size(module_para_s, tcp.ip);
            ofst = member_offset(module_para_s, tcp.ip);
            if(len > dlen) { return false; }                                                   // 如果长度超过最大长度
            module_para_save(member_offset(module_para_s, tcp.len), (const void *)&len, 1);    // 保存长度
            break;
        case MODULE_TCP_PORT:
            dlen = member_size(module_para_s, tcp.port);
            ofst = member_offset(module_para_s, tcp.port);
            break;
        case MODULE_TCP_IP_BAK:
            dlen = member_size(module_para_s, tcp_bak.ip);
            ofst = member_offset(module_para_s, tcp_bak.ip);
            if(len > dlen) { return false; }
            module_para_save(member_offset(module_para_s, tcp_bak.len), (const void *)&len, 1);    // 保存长度
            break;
        case MODULE_TCP_BAK_PORT:
            dlen = member_size(module_para_s, tcp_bak.port);
            ofst = member_offset(module_para_s, tcp_bak.port);
            if(len > dlen) { return false; }    // 如果长度超过最大长度
            break;
        case MODULE_APN:
            dlen = member_size(module_para_s, apn);
            ofst = member_offset(module_para_s, apn);
            if(len > dlen) { return false; }    // 如果长度超过最大长度
            break;
        case MODULE_NTP_SERVER:
            dlen = member_size(module_para_s, ntp_server.ip);
            ofst = member_offset(module_para_s, ntp_server.ip);
            if(len > dlen) { return false; }
            module_para_save(member_offset(module_para_s, ntp_server.len), (const void *)&len, 1);    // 保存长度
            break;
        case MODULE_NTP_SERVER_BAK:
            dlen = member_size(module_para_s, ntp_server_bak.ip);
            ofst = member_offset(module_para_s, ntp_server_bak.ip);
            if(len > dlen) { return false; }
            module_para_save(member_offset(module_para_s, ntp_server_bak.len), (const void *)&len, 1);    // 保存长度
            break;
        default:
            return false;    // 不支持的参数类型
    }
    return module_para_save(ofst, buf, len);    // 保存参数
}

const struct module_data_api_s module_para = {
    .init     = module_init,
    .reset    = module_reset,
    .para_get = module_para_get,
    .para_set = module_para_set,
};
