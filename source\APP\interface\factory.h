/*
 * @description: 
 * @date: Do not edit
 * @lastAuthor: 
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */

/**
  ******************************************************************************
  * @file    factory.h
  * <AUTHOR> @date    2024
  * @brief   厂内协议命令定义
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

typedef enum
{
    // 校表命令
    MFCCMD_DO1 = 0x01,          // 三相所有相(单相L N)全部校准
    MFCCMD_DO2,                 // 三相所有相(单相L N)电压，电流校准
    MFCCMD_DO3,                 // 三相所有相(单相L N)功率增益校准
    MFCCMD_DO4,                 // 三相所有相(单相L N)相位校准1
    MFCCMD_DO5,                 // 三相所有相(单相L N)相位校准2
    MFCCMD_DO6,                 // 三相所有相(单相L N)相位校准3
    MFCCMD_DO7,                 // 三相所有相(单相L N)功率偏移校准
    MFCCMD_DO8,                 // 三相所有相(单相L N)电流偏移校准
    MFCCMD_DO9,                 // 三相所有相(单相L N)小信号校准
    MFCCMD_DOA,                 // 三相所有相(单相L N)其它点校准

    MFCCMD_D11 = 0x11,          // A相(单相L)全部校准
    MFCCMD_D12,                 // A相(单相L)电压，电流校准
    MFCCMD_D13,                 // A相(单相L)功率增益校准
    MFCCMD_D14,                 // A相(单相L)相位校准1
    MFCCMD_D15,                 // A相(单相L)相位校准2
    MFCCMD_D16,                 // A相(单相L)相位校准3
    MFCCMD_D17,                 // A相(单相L)功率偏移校准
    MFCCMD_D18,                 // A相(单相L)电流偏移校准
    MFCCMD_D19,                 // A相(单相L)小信号校准
    MFCCMD_D1A,                 // A相(单相L)其它点校准

    MFCCMD_D21 = 0x21,          // B相(单相N)全部校准
    MFCCMD_D22,                 // B相(单相N)电压，电流校准
    MFCCMD_D23,                 // B相(单相N)功率增益校准
    MFCCMD_D24,                 // B相(单相N)相位校准1
    MFCCMD_D25,                 // B相(单相N)相位校准2
    MFCCMD_D26,                 // B相(单相N)相位校准3
    MFCCMD_D27,                 // B相(单相N)功率偏移校准
    MFCCMD_D28,                 // B相(单相N)电流偏移校准
    MFCCMD_D29,                 // B相(单相N)小信号校准
    MFCCMD_D2A,                 // B相(单相N)其它点校准

    MFCCMD_D31 = 0x31,          // C相全部校准
    MFCCMD_D32,                 // C相电压，电流校准
    MFCCMD_D33,                 // C相功率增益校准
    MFCCMD_D34,                 // C相相位校准1
    MFCCMD_D35,                 // C相相位校准2
    MFCCMD_D36,                 // C相相位校准3
    MFCCMD_D37,                 // C相功率偏移校准
    MFCCMD_D38,                 // C相电流偏移校准
    MFCCMD_D39,                 // C相小信号校准
    MFCCMD_D3A,                 // C相其它点校准

    MFCCMD_PPMTSTEN = 0x40,     // 使能PPM输出(晶振频率输出)
    MFCCMD_RDPPM,               // 读PPM值
    MFCCMD_WRPPM,               // 写PPM值用于校准RTC误差
    MFCCMD_RDTEMP,              // 读电表温度值
    MFCCMD_WRTEMP,              // 写当前室温值(用于校准温度)

    MFCCMD_TMPCONST = 0x50,     // 临时脉冲常数设置
    MFCCMD_WRCLDATE,            // 写校表日期-年月日时分秒
    MFCCMD_RDCLDATE,            // 读校表日期
    MFCCMD_WCALPARA1,           // 写校表参数
    MFCCMD_RCALPARA3,           // 读校表参数

    MFCCMD_INIT     = 0x60,     // 系统初始化
    MFCCMD_RDVERSION,           // 读取内部软件版本
    MFCCMD_RDMINFO,             // 读取电表铭牌信息ASCII码 额定电压6、额定电流6、最大电流6、有功精度等级4，无功精度等级4、有功常数3，无功常数3，电表型号10  格式与07协议一致
    MFCCMD_RDSTATUS,            // 读取电表自检状态字
    MFCCMD_WRPRDATE,            // 写生产日期-年月日时分秒
    MFCCMD_RDPRDATE,            // 读生产日期
    MFCCMD_BEEP_OFF,            // 蜂鸣器响

    MFCCMD_RDTOTALEN = 0x70,    // 读总用电量、总无功电量 4位小数
    MFCCMD_INSTANTPARA,         // 读当前ABC相电压、ABC电流、总ABC有功功率、无功功率、视在功率、零线电流，电压2位小数，电流3位小数，功率4位小数

    MFCCMD_RDASSETID = 0x80,    // 读序列号
    MFCCMD_WRASSETID,           // 写序列号
    MFCCMD_SYNCTIME,            // 设置时间（年月日分时秒）
    MFCCMD_USER_MODE,           // 切换工厂模式，用户模式 1字节 1：用户模式 0：工厂模式
    MFCCMD_FACTORY_MODE,        // 电表数据清零

    MFCCMD_RES_OK = 0xF0,       // 回应OK // 应答
    MFCCMD_RES_ERR,             // 回应Err
}MFCCMD_t;