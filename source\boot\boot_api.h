/*
 * @Author:
 * @Date: 2024-12-17 08:59:57
 * @LastEditTime: 2025-01-06 17:03:53
 * @Description:
 */
/**
 ******************************************************************************
 * @file    boot_api.h
 * <AUTHOR> @date    2025
 * @brief   boot区函数调用接口
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __BOOT_API_H__
#define __BOOT_API_H__

#include "boot_entry.h"

#define bootapi ((const struct boot_api_s *)BOOT_API_ADDR)
#define boot_identifier bootapi->identifier
#define boot_information bootapi->boof_info
#define irq_vector_set bootapi->intvec_set
#define mcu_flash_r bootapi->mcu_flash_read
#define mcu_flash_w bootapi->mcu_flash_write

#endif /* __BOOT_API_H__ */
