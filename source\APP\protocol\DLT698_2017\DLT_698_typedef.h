/**
 ******************************************************************************
* @file    typedef.h
* <AUTHOR> @date    2024
* @brief   类型定义
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __DLT698_TYPEDEF_H__
#define __DLT698_TYPEDEF_H__
#include "typedef.h"



/// @brief DLT698 对象数据类型 ASN.1编码信息
typedef enum
{
    DT_NULL_DATA                = 0,
    DT_ARRAY                    = 1,    // 数组的元素在对象属性或方法的描述中定义 SEQUENCE OF Data
    DT_STRUCTURE                = 2,    // 结构的元素在对象属性或方法的描述中定义 SEQUENCE OF Data
    DT_BOOLEAN                  = 3,    // 布尔值
    DT_BIT_STRING               = 4,    // 位串
    DT_DOUBLE_LONG              = 5,    // int32
    DT_DOUBLE_LONG_UNSIGNED     = 6,    // uint32
    DT_OCTET_STRING             = 9,    // 8位字符串
    DT_VISIBLE_STRING           = 10,   // ASCII字符串
    DT_UTF8_STRING              = 12,   // UTF-8字符串
    DT_INTEGER                  = 15,   // int8
    DT_LONG                     = 16,   // int16
    DT_INTEGER_UNSIGNED         = 17,   // uint8
    DT_LONG_UNSIGNED            = 18,   // uint16
    DT_LONG64                   = 20,   // int64
    DT_LONG64_UNSIGNED          = 21,   // uint64
    DT_ENUM                     = 22,   // 枚举值
    DT_FLOAT32                  = 23,   // float
    DT_FLOAT64                  = 24,   // double
    DT_DATE_TIME                = 25,   // 日期时间 octet-string(SIZE(10))
    DT_DATE                     = 26,   // 日期     octet-string(SIZE(5))
    DT_TIME                     = 27,   // 时间     octet-string(SIZE(3))
    DT_DATE_TIME_S              = 28,   // 日期时间 octet-string(SIZE(7))

    DT_OI                       = 80,   // 对象标识数据类型OI
    DT_OAD                      = 81,   // 对象属性描述符OAD（Object Attribute Descriptor）
    DT_ROAD                     = 82,   // 记录型对象属性描述符ROAD Record Object Attribute Descriptor 用于描述记录型对象中的一个或若干个关联对象属性。
    DT_OMD                      = 83,   // 对象方法描述符OMD(Object Method Descriptor) 用于描述对象的方法。
    DT_TI                       = 84,   // 时间间隔TI（Time Interval）TI 用于表示时间间隔的间隔值及时间单位，间隔值为 0 表示无间隔。
    DT_TSA                      = 85,   // 目标服务器地址TSA（Target Server Address）
    DT_MAC                      = 86,   // 数据安全MAC: 消息鉴别码 Message Authentication Code
    DT_RN                       = 87,   // 随机数RN（Rrandom Nnumber）  ESAM 生成的用于加密的信息串。
    DT_Region                   = 88,   // 区间类型Region  用于描述数据的区间范围，包括以下四种:(起始值，结束值),[起始值，结束值),(起始值，结束值],[起始值，结束值]。
    DT_Scaler_Unit              = 89,   // 换算及单位Scaler_Unit
    DT_RSD                      = 90,   // 记录行选择描述符RSD（Record Selection Descriptor）
    DT_CSD                      = 91,   // CSD 用于描述记录型对象中记录的列关联对象属性
    DT_MS                       = 92,   // 表计集合MS（Meter Set）
    DT_SID                      = 93,   // ESAM 所属安全标识（Security Identifier）
    DT_SID_MAC                  = 94,   // ESAM 所属安全标识以及消息鉴别码。
    DT_COMDCB                   = 95,   // 串口控制块COMDCB
    DT_RCSD                     = 96,   // 记录列选择描述符RCSD（Record Column Selection Descriptor）

    DT_UNDEF                    = 255,  // 未定义的
}DLT698_data_type_t;

typedef enum
{
    UNIT_YEAR         = 1,
    UNIT_MONTH        = 2,
    UNIT_WEEK         = 3,
    UNIT_DAY          = 4,
    UNIT_HOUR         = 5,
    UNIT_MINUTE       = 6,
    UNIT_SECOND       = 7,
    UNIT_ANGLE        = 8,    // 度
    UNIT_TEMP         = 9,    // 温度
    UNIT_CURRENCY     = 10,   // 货币(local)
    UNIT_m3           = 14,   // 用水量(m³)
    UNIT_W            = 27,   // 有功功率,W
    UNIT_kW           = 28,   // 有功功率,kW
    UNIT_VA           = 29,   // 视在功率,VA
    UNIT_kVA          = 30,   // 视在功率,kVA
    UNIT_var          = 31,   // 无功功率,var
    UNIT_kvar         = 32,   // 无功功率,kvar
    UNIT_kWh          = 33,   // 有功电能,kWh
    UNIT_kVAh         = 34,   // 视在电能,kVAh
    UNIT_kvarh        = 35,   // 无功电能,kvarh
    UNIT_A            = 36,   // A
    UNIT_V            = 38,   // V
    UNIT_Hz           = 47,   // Hz
    UNIT_100          = 51,   // %
    UNIT_Pf           = 60,   // Hz 
    UNIT_RESERVED     = 253,  // reserved
    UNIT_CONFIG       = 254,  // other unit
    UNIT_UNDEF        = 255   // no unit, unitless, count
} class_unit_t;



#endif /* __DLT698_TYPEDEF_H__ */

