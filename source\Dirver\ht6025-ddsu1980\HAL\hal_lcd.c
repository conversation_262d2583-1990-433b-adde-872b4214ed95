/**
  ******************************************************************************
  * @file    hal_lcd.c
  * <AUTHOR> @date    2024
  * @brief   lcd驱动头文件
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  ******************************************************************************/
#include <string.h>
#include "hal_lcd.h"
#include "hal_mcu.h"


static union {uint8_t LcdBuffer[MCU_LCD_MEM_LEN]; uint32_t LcdData[MCU_LCD_MEM_LEN / 4]; };

/* Private constants ---------------------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/* Public functions ----------------------------------------------------------*/
/* @brief 正常供电下，打开LCD设备，LCD显示处理初始化 */
bool hal_lcd_open(void)
{
    HAL_SAFETY_WR(HT_CMU->CLKCTRL0 |= CMU_CLKCTRL0_LCDEN;);
    HT_LCD->LCDCLK = LCD_LCDCLK_BIASCTL_3|LCD_LCDCLK_DUTY_COM4|LCD_LCDCLK_LCLK_FRAME128HZ;  // 1/3bias, 4duty, 128Hz
    HT_LCD->LCDCON = 0x00;  //大电流充电模式，对比度00最大
    return TRUE;
}

/* @brief 电池供电下，打开LCD设备，LCD显示处理初始化 */
bool hal_lcd_open_nopower(void)
{
    return hal_lcd_open();
}

///@brief Action: 关闭LCD设备，在低功耗时使用
bool hal_lcd_close(void)
{
    //LCD->CR &= ~LCD_CR_EN_Msk;                    // 先关闭LCD使能再关闭总线时钟
    HAL_SAFETY_WR(HT_CMU->CLKCTRL0 &= ~CMU_CLKCTRL0_LCDEN;);
    return TRUE;
}

///@brief Action:  点亮所有SEG
void hal_lcd_all_seg_light(void)
{
	register uint8_t i;
	for(i = 0; i < MCU_LCD_MEM_LEN; i++) LcdBuffer[i] = 0xFF;
}

///@brief Action:  熄灭所有SEG
void hal_lcd_all_seg_clear(void)
{
	register uint8_t i;
	for(i = 0; i < MCU_LCD_MEM_LEN; i++) LcdBuffer[i] = 0x00;
}

///@brief Action: LCD段码位填充
///@brief Input:  com_seg-段码位索引, mask 0-灭 1-亮
///@brief Output:
void hal_lcd_fill_com_seg(uint16_t com_seg, uint8_t mask)
{
    if(com_seg == 0) return;
    com_seg = com_seg - 1; // 显示段数值定义做了加1的非0处理
	uint8_t x = 1 << (com_seg % 8);
	uint8_t y = com_seg / 8;

    if(y >= sizeof(LcdBuffer)) return;
	if(mask & 0x01)
	{
		LcdBuffer[y] |= x;
	}
	else
	{
		LcdBuffer[y] &= ~x;
	}
}

///@brief Action: 刷新LCD显存
void hal_lcd_refresh(void)
{
    for(uint16_t i = 0; i < MCU_LCD_MEM_LEN; i++)
    {
        MCU_LCD_MEM_PTR[i] = LcdBuffer[i];
    }
}

/// @brief 声明hal_lcd子模块对象
const struct hal_lcd_s hal_lcd =
{
    .open               = hal_lcd_open,
    .open_nopower       = hal_lcd_open_nopower,
    .close              = hal_lcd_close,
    .all_seg_set        = hal_lcd_all_seg_light,
    .all_seg_clr        = hal_lcd_all_seg_clear,
    .light              = hal_lcd_fill_com_seg,
    .refresh            = hal_lcd_refresh,
};

// end of hal_lcd.c
