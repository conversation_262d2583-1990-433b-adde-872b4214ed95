/********************************************************************************
* @file    billing.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __BILLING_H
#define __BILLING_H
#include "app.h"
#include "..\config\app_config.h"
#include "demand.h"
#include "mic.h"

typedef uint16_t BL_STUS;
#define STUS_BL_AUTO_BILLING    ((BL_STUS)1 << 1 )   // 自动月结算
#define STUS_BL_PATCH           ((BL_STUS)1 << 2 )   // 调整时钟/掉电补月结算
#define STUS_BL_COMMAND         ((BL_STUS)1 << 3 )   // 指令
#define STUS_BL_MONTH_BILLING   ((BL_STUS)1 << 4 )   // 月结算
#define STUS_BL_PRG_BLM_DATE    ((BL_STUS)1 << 5 )   // 更改结算日
#define STUS_BL_STEP_BILLING    ((BL_STUS)1 << 6 )   // 阶梯结算
#define STUS_BL_CLR_MDATA       ((BL_STUS)1 << 7 )   // 清除月结数据
#define STUS_BL_CLR_SDATA       ((BL_STUS)1 << 8 )   // 清除阶梯结算数据
#define STUS_BL_CLR_DDATA       ((BL_STUS)1 << 9 )   // 清除日结结算数据

typedef enum
{
    bl_monthly,
    bl_daily,
    bl_step,

    bl_num
}bl_type_t;


typedef struct
{
    ENERGY_DEF_FORMAT cum_en[1][BL_EN_TYPE_NUM][1 + TARIFF_RATE_NUM];
#if ENERGY_PHASE_ENABLE
    ENERGY_DEF_FORMAT cum_en_ph[PHASE_NUM][BL_EN_TYPE_NUM][1 + TARIFF_RATE_NUM*ENERGY_PHASE_TARIFF_EN]; // 累积电能 Unit: 10Wh
#endif
#if SW_PAYMENT_EN
    PAY_CREDIT_DATA_TYPE cum_paid; // 累计消费信用, 单位同预付费模块信用单位
#endif   
}bl_energy_s;

typedef struct
{   
    clock_s     stamp;
    bl_energy_s eng;
    MD_reg_s    md[DEMAND_TYPE_NUM][1 + DEMAND_TARIFF_RATE_NUM]; // 单位1w,1var,1va
#if DEMAND_CUM_SUM_ENABLE
    MD_reg_s    cum_md[DEMAND_TYPE_NUM][1 + DEMAND_TARIFF_RATE_NUM]; // 累计总量, 单位1w,1var,1va
#endif
}bl_monthly_s;

typedef struct
{   
    clock_s     stamp;
    bl_energy_s eng;
    MD_reg_s    md[DEMAND_TYPE_NUM][1 + DEMAND_TARIFF_RATE_NUM]; // 单位1w,1var,1va
}bl_step_s;

typedef struct
{
    clock_s     stamp;
    bl_energy_s eng;
    MD_reg_s    md[DEMAND_TYPE_NUM][1 + DEMAND_TARIFF_RATE_NUM]; // 单位1w,1var,1va
}bl_daily_s;


typedef struct
{
    uint16_t crc;
    uint16_t chk;

    uint8_t month_billing_time[BILLING_MONTHLY_DAY_NUM][2]; ///3 个月结算日,DDhh
    uint8_t daily_frozen_time[2];     ///日冻结时间,  hhmm
//     uint8_t hour_frozen_start_time[5];///整点冻结开始时间,YYMMDDhhmm
//     uint8_t hour_frozen_interval;     ///整点冻结间隔, 分钟
// // bit 7 -变量 
// // bit 6 -反向有功最大需量及发生时间
// // bit 5 -正向有功最大需量及发生时间
// // bit 4 -四象限无功电能
// // bit 3 -组合无功 2 电能
// // bit 2 -组合无功 1 电能
// // bit 1 -反向有功电能
// // bit 0 -正向有功电能
//     uint8_t frozen_mode;              ///冻结模式字     bit0-bit7
//     uint8_t timing_frozen_mode;       ///定时冻结模式字 bit0-bit7
//     uint8_t ins_forzen_mode;          ///瞬时冻结模式字 bit0-bit7
//     uint8_t arrange_forzen_mode;      ///约定冻结模式字 bit0-bit7
//     uint8_t hour_frozen_mode;         ///整点冻结模式字 bit0-bit1
//     uint8_t daily_frozen_mode;        ///日冻结模式字   bit0-bit7
}billing_para_s;



struct billing_s
{
    void (*reset)(uint8_t type);
    bool (*state_query)(BL_STUS state);
    void (*state_clr)(void);
    void (*month_profile_clr)(void);
    void (*day_profile_clr)(void);
    void (*step_profile_clr)(void);
    uint8_t (*is_happen)(void);
    const billing_para_s* (*para_get)(void);
    bool (*para_set)(uint16_t ofst, const void* val, uint16_t len);
    bool (*last_month_billing_time_get)(uint8_t point,    clock_s* lst_time);
    bool (*next_month_billing_time_get)(clock_s lst_time, clock_s* nxt_time);
    bool (*last_day_billing_time_get)(uint8_t point, clock_s* lst_time);
    bool (*next_day_billing_time_get)(clock_s lst_time, clock_s* nxt_time);
    BL_EN_TYPE_t (*energy_type_get)(uint8_t typ);
    ENERGY_DEF_FORMAT (*phs_cum_energy_get)(bl_type_t bl_type, uint8_t ph, uint8_t bl_lst, BL_EN_TYPE_t bl_en, uint8_t rate);
    ENERGY_DEF_FORMAT (*phs_inc_energy_get)(bl_type_t bl_type, uint8_t ph, uint8_t bl_lst, BL_EN_TYPE_t bl_en, uint8_t rate);
    /// @brief 获取结算最大需量
    MD_reg_s (*max_demand_get)(bl_type_t bl_type, uint8_t bl_lst, demand_type_t demand_type, uint8_t rate);
#if DEMAND_CUM_SUM_ENABLE
    MD_reg_s (*cum_max_demand_get)(uint8_t bl_lst, demand_type_t demand_type, uint8_t rate);
#endif
    void (*command_month_billing)(void);
    uint32_t (*month_billing_cnt_get)(void);
#if SW_PAYMENT_EN
    PAY_CREDIT_DATA_TYPE (*cum_paid_get)(bl_type_t bl_type, uint8_t bl_lst);
    PAY_CREDIT_DATA_TYPE (*inc_paid_get)(bl_type_t bl_type, uint8_t bl_lst);
    PAY_CREDIT_DATA_TYPE (*last_day_avg_paid)(uint16_t last_day);
    uint16_t (*credit_residual_days_get)(void);
#endif
};

extern const struct billing_s billing;

#endif /* __BILLING_H */

