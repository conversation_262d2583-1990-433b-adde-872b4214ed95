
/**
 ******************************************************************************
* @file    ext_lcd_driver.c
* <AUTHOR> @date    2024
* @brief   lcd -> lcd驱动映射
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __EXT_LCD_DRIVER_H__
#define __EXT_LCD_DRIVER_H__


///液晶com  -> 驱动com 映射 
#define LCD_COM1            0 
#define LCD_COM2            1
#define LCD_COM3            2
#define LCD_COM4            3
#define LCD_COM5            4
#define LCD_COM6            5
#define LCD_COM7            6
#define LCD_COM8            7   

/* lcd seg -> 驱动seg段 映射 */
#define LCD_SEG01           31
#define LCD_SEG02           25
#define LCD_SEG03           24
#define LCD_SEG04           23
#define LCD_SEG05           22
#define LCD_SEG06           21
#define LCD_SEG07           20
#define LCD_SEG08           19
#define LCD_SEG09           18
#define LCD_SEG10           17
#define LCD_SEG11           16
#define LCD_SEG12           15
#define LCD_SEG13           26
#define LCD_SEG14           27
#define LCD_SEG15           28
#define LCD_SEG16           29
#define LCD_SEG17           30
#define LCD_SEG18           0
#define LCD_SEG19           1
#define LCD_SEG20           2
#define LCD_SEG21           3
#define LCD_SEG22           4
#define LCD_SEG23           5
#define LCD_SEG24           13
#define LCD_SEG25           12
#define LCD_SEG26           11
#define LCD_SEG27           10
#define LCD_SEG28           9
#define LCD_SEG29           8
#define LCD_SEG30           7
#define LCD_SEG31           6
#define LCD_SEG32           14

/// 内存地址偏移计算
#define COM0(x)             (x * 8 + LCD_COM1 + 1)
#define COM1(x)             (x * 8 + LCD_COM2 + 1)
#define COM2(x)             (x * 8 + LCD_COM3 + 1)
#define COM3(x)             (x * 8 + LCD_COM4 + 1)
#define COM4(x)             (x * 8 + LCD_COM5 + 1)
#define COM5(x)             (x * 8 + LCD_COM6 + 1)
#define COM6(x)             (x * 8 + LCD_COM7 + 1)
#define COM7(x)             (x * 8 + LCD_COM8 + 1)


#endif //__EXT_LCD_DRIVER_H__
