/*
*********************************************************************************************************
*                                              HT6XXX
*                                           Library Function
*
*                                   Copyright 2013, Hi-Trend Tech, Corp.
*                                        All Rights Reserved
*
*
* Project      : HT6xxx
* File         : systerm_ht6xxx.c
* By           : Hitrendtech_SocTeam
* Version      : V1.0.2
* Description  :
*********************************************************************************************************
*/

#define  __SYSTEM_HT6XXX_C


#include "system_ht6xxx.h"



#define VECT_TAB_OFFSET  0x2000
#define FLASH_BASE  0x00000000
/*
*********************************************************************************************************
*                                           本地宏/结构体
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                             本地变量
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                           本地函数申明
*********************************************************************************************************
*/


/*
*********************************************************************************************************
*                                        INITIAL THE SYSTERM
*
* 函数说明：此函数在main()之前被调用
*
* 入口参数：无
*
* 返回参数：无
*
*********************************************************************************************************
*/
void SystemInit (void)
{
  // SCB->VTOR = FLASH_BASE | VECT_TAB_OFFSET; 
}

// /*
// *********************************************************************************************************
// *                                        IAR 上电变量是否初始化
// *
// * 函数说明：只有IAR编译器时才会使用（修改返回值就能决定初始化选项）
// *
// * 入口参数：无
// *
// * 返回参数：返回值“0”代表RAM不初始化，返回值“1”代表RAM初始化
// *
// *********************************************************************************************************
// */
// #if defined ( __ICCARM__ )

// int __low_level_init(void)
// {
//     return 1;
// }
// #endif
