/**
 ******************************************************************************
* @file    DLT645_2007_id.h
* <AUTHOR> @date    2024
* @brief   根据DL/T645-2007 标准定义。
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef _DLT645_2007_H
#define _DLT645_2007_H

#define DAY_TARIFF(D,T) (((uint32_t)T << 8) | (uint32_t)D)
#define LAST_INDEX(N)   ((uint32_t)N)

/// 电能类
// 注 1: 组合有功、无功电能最高位是符号位，0正1负。取值范围：0.00～799999.99。
// 注 2: ZZ代表本字节所列数值的任意一个取值，ZZ不能取值为FF。
// 注 3: 电能测量四象限的定义见附录D
// 注 4: 正向视在总电能是与正向有功电能相对应的视在电能，即位于一、四象限；反向视在总电能是与反向有功电能相对应的视在电能，即位于二、三象限。
// 注 5: 谐波潮流方向与基波同向，关联电能为基波电能减谐波电能；谐波潮流方向与基波反向，关联电能为基波电能加谐波电能。
// 注 6: 在传输某结算日电能量数据块时，数据块中包含的费率电能以实际设置的费率数为准。
#define C0_CMB_kWh(D,T)                             (0x00000000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合有功总       电能(kWh)
#define C0_POS_kWh(D,T)                             (0x00010000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 正向有功总       电能(kWh)
#define C0_NEG_kWh(D,T)                             (0x00020000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 反向有功总       电能(kWh)
#define C0_CMB1_kvarh(D,T)                          (0x00030000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功1总      电能(kvarh)
#define C0_CMB2_kvarh(D,T)                          (0x00040000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功2总      电能(kvarh)
#define C0_Q1_kvarh(D,T)                            (0x00050000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q1无功总         电能 (kvarh)
#define C0_Q2_kvarh(D,T)                            (0x00060000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q2无功总         电能 (kvarh)
#define C0_Q3_kvarh(D,T)                            (0x00070000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q3无功总         电能 (kvarh)
#define C0_Q4_kvarh(D,T)                            (0x00080000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q4无功总         电能 (kvarh)
#define C0_A_POS_kWh(D,T)                           (0x00150000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相正向有功总    电能(kWh)
#define C0_A_NEG_kWh(D,T)                           (0x00160000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相反向有功总    电能(kWh)
#define C0_A_CMB1_kvarh(D,T)                        (0x00170000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功1总   电能(kvarh)
#define C0_A_CMB2_kvarh(D,T)                        (0x00180000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功2总   电能(kvarh)
#define C0_A_Q1_kvarh(D,T)                          (0x00190000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q1无功总      电能 (kvarh)
#define C0_A_Q2_kvarh(D,T)                          (0x001A0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q2无功总      电能 (kvarh)
#define C0_A_Q3_kvarh(D,T)                          (0x001B0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q3无功总      电能 (kvarh)
#define C0_A_Q4_kvarh(D,T)                          (0x001C0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q4无功总      电能 (kvarh)
#define C0_B_POS_kWh(D,T)                           (0x00290000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相正向有功总    电能(kWh)
#define C0_B_NEG_kWh(D,T)                           (0x002A0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相反向有功总    电能(kWh)
#define C0_B_CMB1_kvarh(D,T)                        (0x002B0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功1总   电能(kvarh)
#define C0_B_CMB2_kvarh(D,T)                        (0x002C0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功2总   电能(kvarh)
#define C0_B_Q1_kvarh(D,T)                          (0x002D0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q1无功总      电能 (kvarh)
#define C0_B_Q2_kvarh(D,T)                          (0x002E0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q2无功总      电能 (kvarh)
#define C0_B_Q3_kvarh(D,T)                          (0x002F0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q3无功总      电能 (kvarh)
#define C0_B_Q4_kvarh(D,T)                          (0x00300000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q4无功总      电能 (kvarh)
#define C0_C_POS_kWh(D,T)                           (0x003D0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相正向有功总    电能(kWh)
#define C0_C_NEG_kWh(D,T)                           (0x003E0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相反向有功总    电能(kWh)
#define C0_C_CMB1_kvarh(D,T)                        (0x003F0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功1总   电能(kvarh)
#define C0_C_CMB2_kvarh(D,T)                        (0x00400000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功2总   电能(kvarh)
#define C0_C_Q1_kvarh(D,T)                          (0x00410000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q1无功总      电能 (kvarh)
#define C0_C_Q2_kvarh(D,T)                          (0x00420000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q2无功总      电能 (kvarh)
#define C0_C_Q3_kvarh(D,T)                          (0x00430000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q3无功总      电能 (kvarh)
#define C0_C_Q4_kvarh(D,T)                          (0x00440000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q4无功总      电能 (kvarh)

#define C0_REMAIN_kWh                               0x00900100                                  /// （当前）剩余电量
#define C0_OVERDRAFT_kWh                            0x00900101                                  /// （当前）透支电量
#define C0_REMAIN_yuan                              0x00900200                                  /// （当前）剩余金额
#define C0_OVERDRAFT_yuan                           0x00900201                                  /// （当前）透支金额



/// @最大需量类
// 注 1: 组合无功最大需量的最高位是符号位，0正1负。取值范围：0.0000～79.0000。
// 注 2: 在传输某结算日最大需量及发生时间数据块时，数据块中包含的费率最大需量及发生时间以实际设置的费率数为准。
// 注 3: ZZ代表本字节所列数值的任意一个取值，ZZ不能取值为FF。 
// 01 ZZ ZZ FF XX.XXXXYYMMDDhhmm 8×13 * 某项当前和 12 个结算日最大需量及发生时间数据块
#define C1_MD_CMB_kW(D,T)                           (0x01000000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合有功总       最大需量(kW)
#define C1_MD_POS_kW(D,T)                           (0x01010000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 正向有功总       最大需量(kW)
#define C1_MD_NEG_kW(D,T)                           (0x01020000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 反向有功总       最大需量(kW)
#define C1_MD_CMB1_kvar(D,T)                        (0x01030000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功1总      最大需量(kvar)
#define C1_MD_CMB2_kvar(D,T)                        (0x01040000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功2总      最大需量(kvar)
#define C1_MD_Q1_kvar(D,T)                          (0x01050000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q1无功总         最大需量(kvar)
#define C1_MD_Q2_kvar(D,T)                          (0x01060000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q2无功总         最大需量(kvar)
#define C1_MD_Q3_kvar(D,T)                          (0x01070000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q3无功总         最大需量(kvar)
#define C1_MD_Q4_kvar(D,T)                          (0x01080000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q4无功总         最大需量(kvar)
#define C1_MD_A_POS_kW(D,T)                         (0x01150000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相正向有功总    最大需量(kW)
#define C1_MD_A_NEG_kW(D,T)                         (0x01160000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相反向有功总    最大需量(kW)
#define C1_MD_A_CMB1_kvar(D,T)                      (0x01170000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功1总   最大需量(kvar)
#define C1_MD_A_CMB2_kvar(D,T)                      (0x01180000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功2总   最大需量(kvar)
#define C1_MD_A_Q1_kvar(D,T)                        (0x01190000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q1无功总      最大需量(kvar)
#define C1_MD_A_Q2_kvar(D,T)                        (0x011A0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q2无功总      最大需量(kvar)
#define C1_MD_A_Q3_kvar(D,T)                        (0x011B0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q3无功总      最大需量(kvar)
#define C1_MD_A_Q4_kvar(D,T)                        (0x011C0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q4无功总      最大需量(kvar)
#define C1_MD_B_POS_kW(D,T)                         (0x01290000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相正向有功总    最大需量(kW)
#define C1_MD_B_NEG_kW(D,T)                         (0x012A0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相反向有功总    最大需量(kW)
#define C1_MD_B_CMB1_kvar(D,T)                      (0x012B0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功1总   最大需量(kvar)
#define C1_MD_B_CMB2_kvar(D,T)                      (0x012C0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功2总   最大需量(kvar)
#define C1_MD_B_Q1_kvar(D,T)                        (0x012D0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q1无功总      最大需量(kvar)
#define C1_MD_B_Q2_kvar(D,T)                        (0x012E0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q2无功总      最大需量(kvar)
#define C1_MD_B_Q3_kvar(D,T)                        (0x012F0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q3无功总      最大需量(kvar)
#define C1_MD_B_Q4_kvar(D,T)                        (0x01300000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q4无功总      最大需量(kvar)
#define C1_MD_C_POS_kW(D,T)                         (0x013D0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相正向有功总    最大需量(kW)
#define C1_MD_C_NEG_kW(D,T)                         (0x013E0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相反向有功总    最大需量(kW)
#define C1_MD_C_CMB1_kvar(D,T)                      (0x013F0000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功1总   最大需量(kvar)
#define C1_MD_C_CMB2_kvar(D,T)                      (0x01400000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功2总   最大需量(kvar)
#define C1_MD_C_Q1_kvar(D,T)                        (0x01410000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q1无功总      最大需量(kvar)
#define C1_MD_C_Q2_kvar(D,T)                        (0x01420000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q2无功总      最大需量(kvar)
#define C1_MD_C_Q3_kvar(D,T)                        (0x01430000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q3无功总      最大需量(kvar)
#define C1_MD_C_Q4_kvar(D,T)                        (0x01440000 | DAY_TARIFF(D,T))              /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q4无功总      最大需量(kvar)


/// 变量数据标识
// 注 1: 三相三线电表电压A相为Uab，B相为0，C相为Ucb；电流A相为Ia，B相为0，C相为Ic；功率因数A相为Uab与Ia的夹
//       角余弦，B相为0，C相为Ucb与Ic的夹角余弦；相角A相为Uab与Ia的夹角，B相为0，C相为Ucb与Ic的夹角。
// 注 2: 瞬时功率及当前需量最高位表示方向，0正，1负，三相三线B相为0。取值范围：0.0000～79.9999。
// 注 3: 表内温度最高位0表示零上，1表示零下。取值范围：0.0～799.9。
// 注 4: 相角测量范围是0～360度。
// 注 5: 当前有功需量、当前无功需量、当前视在需量是最近一段时间的平均功率。
// 注 6: 电流最高位表示方向，0正，1负，取值范围：0.000～799.999。功率因数最高位表示方向，0正，1负，取值范围：0.000～1.000。
#define C2_A_VOL                                    0x02010100                                 /// A相电压
#define C2_B_VOL                                    0x02010200                                 /// B相电压
#define C2_C_VOL                                    0x02010300                                 /// C相电压
#define C2_M_VOL                                    0x0201FF00                                 /// 所有相电压

#define C2_A_CUR                                    0x02020100                                 /// A相   电流
#define C2_B_CUR                                    0x02020200                                 /// B相   电流
#define C2_C_CUR                                    0x02020300                                 /// C相   电流
#define C2_M_CUR                                    0x0202FF00                                 /// 所有相电流

#define C2_T_INS_P                                  0x02030000                                 /// 总    有功功率
#define C2_A_INS_P                                  0x02030100                                 /// A相   有功功率
#define C2_B_INS_P                                  0x02030200                                 /// B相   有功功率
#define C2_C_INS_P                                  0x02030300                                 /// C相   有功功率
#define C2_M_INS_P                                  0x0203FF00                                 /// 数据块有功功率

#define C2_T_INS_Q                                  0x02040000                                 /// 总    无功功率
#define C2_A_INS_Q                                  0x02040100                                 /// A相   无功功率
#define C2_B_INS_Q                                  0x02040200                                 /// B相   无功功率
#define C2_C_INS_Q                                  0x02040300                                 /// C相   无功功率
#define C2_M_INS_Q                                  0x0204FF00                                 /// 数据块无功功率

#define C2_T_INS_S                                  0x02050000                                 /// 总    视在功率
#define C2_A_INS_S                                  0x02050100                                 /// A相   视在功率
#define C2_B_INS_S                                  0x02050200                                 /// B相   视在功率
#define C2_C_INS_S                                  0x02050300                                 /// C相   视在功率
#define C2_M_INS_S                                  0x0205FF00                                 /// 数据块视在功率

#define C2_T_PF                                     0x02060000                                 /// 总    功率因素
#define C2_A_PF                                     0x02060100                                 /// A相   功率因素
#define C2_B_PF                                     0x02060200                                 /// B相   功率因素
#define C2_C_PF                                     0x02060300                                 /// C相   功率因素
#define C2_M_PF                                     0x0206FF00                                 /// 数据块功率因素

#define C2_A_ANGEL                                  0x02070100                                 /// A相   相角
#define C2_B_ANGEL                                  0x02070200                                 /// B相   相角
#define C2_C_ANGEL                                  0x02070300                                 /// C相   相角
#define C2_M_ANGEL                                  0x0207FF00                                 /// 数据块相角

#define C2_A_VOL_THD                                0x02080100                                 /// A相   电压波形失真度
#define C2_B_VOL_THD                                0x02080200                                 /// B相   电压波形失真度
#define C2_C_VOL_THD                                0x02080300                                 /// C相   电压波形失真度
#define C2_M_VOL_THD                                0x0208FF00                                 /// 数据块电压波形失真度

#define C2_A_CUR_THD                                0x02090100                                 /// A相   电流波形失真度
#define C2_B_CUR_THD                                0x02090200                                 /// B相   电流波形失真度
#define C2_C_CUR_THD                                0x02090300                                 /// C相   电流波形失真度
#define C2_M_CUR_THD                                0x0209FF00                                 /// 数据块电流波形失真度

#define C2_A_VOL_HARMONIC(N)                        (0x020A0100 | LAST_INDEX(N))               /// A相电压谐波含量 1-21
#define C2_A_VOL_HARMONIC_M                         0x020A01FF                                 /// A相电压谐波含量数据块          
#define C2_B_VOL_HARMONIC(N)                        (0x020A0200 | LAST_INDEX(N))               /// B相电压谐波含量 1-21
#define C2_B_VOL_HARMONIC_M                         0x020A02FF                                 /// B相电压谐波含量数据块  
#define C2_C_VOL_HARMONIC(N)                        (0x020A0300 | LAST_INDEX(N))               /// C相电压谐波含量 1-21
#define C2_C_VOL_HARMONIC_M                         0x020A03FF                                 /// C相电压谐波含量数据块  

#define C2_A_CUR_HARMONIC(N)                        (0x020B0100 | LAST_INDEX(N))               /// A相电流谐波含量 1-21
#define C2_A_CUR_HARMONIC_M                         0x020B01FF                                 /// A相电流谐波含量数据块
#define C2_B_CUR_HARMONIC(N)                        (0x020B0200 | LAST_INDEX(N))               /// B相电流谐波含量 1-21
#define C2_B_CUR_HARMONIC_M                         0x020B02FF                                 /// B相电流谐波含量数据块
#define C2_C_CUR_HARMONIC(N)                        (0x020B0300 | LAST_INDEX(N))               /// C相电流谐波含量 1-21
#define C2_C_CUR_HARMONIC_M                         0x020B03FF                                 /// C相电流谐波含量数据块

#define C2_N_CUR                                    0x02800001                                 /// 零线电流
#define C2_FREQUENCY                                0x02800002                                 /// 电网频率
#define C2_AVR_POWER_1M                             0x02800003                                 /// 一分钟有功平均功率
#define C2_CUR_DM_kW                                0x02800004                                 /// 当前有功需量
#define C2_CUR_DM_kva                               0x02800005                                 /// 当前无功需量
#define C2_CUR_DM_kV                                0x02800006                                 /// 当前视在需量
#define C2_TEMPARETURE                              0x02800007                                 /// 温度
#define C2_INTBAT_VOL                               0x02800008                                 /// 内部时钟电池电压
#define C2_EXTBAT_VOL                               0x02800009                                 /// 外部抄表电池电压
#define C2_INTBAT_RUN_TIME                          0x0280000A                                 /// 内部电池工作时间
#define C2_CUR_STEP_PRICE                           0x0280000B                                 /// 当前阶梯电价

/// 变量数据标识(自定义)
#define C2_LINE_VOL_AB                              0x02A00100                                 /// 当前AB线电压
#define C2_LINE_VOL_BC                              0x02A00200                                 /// 当前BC线电压
#define C2_LINE_VOL_AC                              0x02A00300                                 /// 当前AC线电压


/// 事件记录数据标识
/// 部分事件记录在 0x10000000~0x1F 区间，
#define C3_LOSS_VOL_NUM                             0x03010000                                 /// 失压总次数
#define C3_A_LOSS_VOL_RECORD(N)                     (0x03010100 | LAST_INDEX(N))               /// 上N次A相失压记录N 1-10
#define C3_B_LOSS_VOL_RECORD(N)                     (0x03010200 | LAST_INDEX(N))               /// 上N次B相失压记录N 1-10
#define C3_C_LOSS_VOL_RECORD(N)                     (0x03010300 | LAST_INDEX(N))               /// 上N次C相失压记录N 1-10

#define C3_LOW_VOL_NUM                              0x03020000                                 /// 欠压总次数
#define C3_A_LOW_VOL_RECORD(N)                      (0x03020100 | LAST_INDEX(N))               /// 上N次A相欠压记录N 1-10
#define C3_B_LOW_VOL_RECORD(N)                      (0x03020200 | LAST_INDEX(N))               /// 上N次B相欠压记录N 1-10
#define C3_C_LOW_VOL_RECORD(N)                      (0x03020300 | LAST_INDEX(N))               /// 上N次C相欠压记录N 1-10

#define C3_OVR_VOL_NUM                              0x03030000                                 /// 过压总次数
#define C3_A_OVR_VOL_RECORD(N)                      (0x03030100 | LAST_INDEX(N))               /// 上N次A相过压记录N 1-10
#define C3_B_OVR_VOL_RECORD(N)                      (0x03030200 | LAST_INDEX(N))               /// 上N次B相过压记录N 1-10
#define C3_C_OVR_VOL_RECORD(N)                      (0x03030300 | LAST_INDEX(N))               /// 上N次C相过压记录N 1-10

#define C3_MISS_VOL_NUM                             0x03040000                                 /// 断相总次数
#define C3_A_MISS_VOL_RECORD(N)                     (0x03040100 | LAST_INDEX(N))               /// 上N次A相断相记录N 1-10
#define C3_B_MISS_VOL_RECORD(N)                     (0x03040200 | LAST_INDEX(N))               /// 上N次B相断相记录N 1-10
#define C3_C_MISS_VOL_RECORD(N)                     (0x03040300 | LAST_INDEX(N))               /// 上N次C相断相记录N 1-10

#define C3_ALL_LOSS_VOL_NUM                         0x03050000                                 /// 全失压总次数
#define C3_ALL_LOSS_VOL_RECORD(N)                   (0x03050000 | LAST_INDEX(N))               /// 上N次全失压记录N 1-10

#define C3_BAK_PWR_LOS_NUM                          0x03060000                                 ///      辅助电源失电总次数
#define C3_BAK_PWR_LOS_RECORD(N)                    (0x03060000 | LAST_INDEX(N))               /// 上N次辅助电源失电记录N 1-10

#define C3_V_REV_SQR_NUM                            0x03070000                                 ///      电压逆向序总次数
#define C3_V_REV_SQR_RECORD(N)                      (0x03070000 | LAST_INDEX(N))               /// 上N次电压逆向序记录N 1-10

#define C3_I_REV_SQR_NUM                            0x03080000                                 ///      电流逆向序总次数
#define C3_I_REV_SQR_RECORD(N)                      (0x03080000 | LAST_INDEX(N))               /// 上N次电流逆向序记录N 1-10

#define C3_V_UNB_NUM                                0x03090000                                 ///      电压不平衡总次数
#define C3_V_UNB_RECORD(N)                          (0x03090000 | LAST_INDEX(N))               /// 上N次电压不平衡记录N 1-10

#define C3_I_UNB_SQR_NUM                            0x030A0000                                 ///      电流不平衡总次数
#define C3_I_UNB_SQR_RECORD(N)                      (0x030A0000 | LAST_INDEX(N))               /// 上N次电流不平衡记录N 1-10

#define C3_LOS_CUR_NUM                              0x030B0000                                 ///       失流总次数
#define C3_A_LOS_CUR_RECORD(N)                      (0x030B0100 | LAST_INDEX(N))               /// 上N次A失流相记录N 1-10
#define C3_B_LOS_CUR_RECORD(N)                      (0x030B0200 | LAST_INDEX(N))               /// 上N次B失流相记录N 1-10
#define C3_C_LOS_CUR_RECORD(N)                      (0x030B0300 | LAST_INDEX(N))               /// 上N次C失流相记录N 1-10

#define C3_OVR_CUR_NUM                              0x030C0000                                 ///       过流总次数
#define C3_A_OVR_CUR_RECORD(N)                      (0x030C0100 | LAST_INDEX(N))               /// 上N次A过流相记录N 1-10
#define C3_B_OVR_CUR_RECORD(N)                      (0x030C0200 | LAST_INDEX(N))               /// 上N次B过流相记录N 1-10
#define C3_C_OVR_CUR_RECORD(N)                      (0x030C0300 | LAST_INDEX(N))               /// 上N次C过流相记录N 1-10

#define C3_MISS_CUR_NUM                             0x030D0000                                 ///       断流总次数
#define C3_A_MISS_CUR_RECORD(N)                     (0x030D0100 | LAST_INDEX(N))               /// 上N次A相断流相记录N 1-10
#define C3_B_MISS_CUR_RECORD(N)                     (0x030D0200 | LAST_INDEX(N))               /// 上N次B相断流相记录N 1-10
#define C3_C_MISS_CUR_RECORD(N)                     (0x030D0300 | LAST_INDEX(N))               /// 上N次C相断流相记录N 1-10

#define C3_REV_NUM                                  0x030E0000                                 ///         潮流反向总次数
#define C3_A_REV_RECORD(N)                          (0x030E0100 | LAST_INDEX(N))               /// 上N次A相潮流反向记录N 1-10
#define C3_B_REV_RECORD(N)                          (0x030E0200 | LAST_INDEX(N))               /// 上N次B相潮流反向记录N 1-10
#define C3_C_REV_RECORD(N)                          (0x030E0300 | LAST_INDEX(N))               /// 上N次C相潮流反向记录N 1-10

#define C3_OVR_LOAD_NUM                             0x030F0000                                 ///         过载总次数
#define C3_A_OVR_LOAD_RECORD(N)                     (0x030F0100 | LAST_INDEX(N))               /// 上N次A相过载记录N 1-10
#define C3_B_OVR_LOAD_RECORD(N)                     (0x030F0200 | LAST_INDEX(N))               /// 上N次B相过载记录N 1-10
#define C3_C_OVR_LOAD_RECORD(N)                     (0x030F0300 | LAST_INDEX(N))               /// 上N次C相过载记录N 1-10

#define C3_PWR_DOWN_NUM                             0x03110000                                 ///      掉电总次数
#define C3_PWR_DOWN_CUM_TIME                        0x03110100                                 /// 掉电累计时间
#define C3_PWR_DOWN_RECORD(N)                       (0x03110000 | LAST_INDEX(N))               /// 上N次掉电记录N 1-10

#define C3_OVR_DM_NUM                               0x03120000                                 ///                需量总次数
#define C3_OVR_DM_POS_kW_RECORD(N)                  (0x03120100 | LAST_INDEX(N))               /// 上N次正向   有功需量超限记录N 1-10
#define C3_OVR_DM_NEG_kW_RECORD(N)                  (0x03120200 | LAST_INDEX(N))               /// 上N次反向   有功需量超限记录N 1-10
#define C3_OVR_DM_Q1_kvar_RECORD(N)                 (0x03120300 | LAST_INDEX(N))               /// 上N次第1象限无功需量超限记录N 1-10
#define C3_OVR_DM_Q2_kvar_RECORD(N)                 (0x03120400 | LAST_INDEX(N))               /// 上N次第2象限无功需量超限记录N 1-10
#define C3_OVR_DM_Q3_kvar_RECORD(N)                 (0x03120500 | LAST_INDEX(N))               /// 上N次第3象限无功需量超限记录N 1-10
#define C3_OVR_DM_Q4_kvar_RECORD(N)                 (0x03120600 | LAST_INDEX(N))               /// 上N次第4象限无功需量超限记录N 1-10

#define C3_PROGRAM_NUM                              0x03300000                                 ///      编程总次数
#define C3_PROGRAM_RECORD(N)                        (0x03300000 | LAST_INDEX(N))               /// 上N次编程记录N 1-10

#define C3_METER_CLEAN_NUM                          0x03300100                                 ///      电表清零总次数
#define C3_METER_CLEAN_RECORD(N)                    (0x03300100 | LAST_INDEX(N))               /// 上N次电表清零记录N 1-10

#define C3_DEMAND_CLEAN_NUM                         0x03300200                                 ///      需量清零总次数
#define C3_DEMAND_CLEAN_RECORD(N)                   (0x03300200 | LAST_INDEX(N))               /// 上N次需量清零记录N 1-10

#define C3_EVENT_CLEAN_NUM                          0x03300300                                 ///      事件清零总次数
#define C3_EVENT_CLEAN_RECORD(N)                    (0x03300300 | LAST_INDEX(N))               /// 上N次事件清零记录N 1-10

#define C3_SHITFT_TIME_NUM                          0x03300400                                 ///      校时总次数
#define C3_SHITFT_TIME_RECORD(N)                    (0x03300400 | LAST_INDEX(N))               /// 上N次校时记录N 1-10

#define C3_SCHEDULE_NUM                             0x03300500                                 ///      时段表总次数
#define C3_SCHEDULE_RECORD(N)                       (0x03300500 | LAST_INDEX(N))               /// 上N次时段表记录N 1-10

#define C3_ZONE_TAB_NUM                             0x03300600                                 ///      时区表总次数
#define C3_ZONE_TAB_RECORD(N)                       (0x03300600 | LAST_INDEX(N))               /// 上N次时区表记录N 1-10

#define C3_WEEKENDS_PGM_NUM                         0x03300700                                 ///      周休日编程总次数
#define C3_WEEKENDS_PGM_RECORD(N)                   (0x03300700 | LAST_INDEX(N))               /// 上N次周休日编程记录N 1-10

#define C3_HOLIDAY_PGM_NUM                          0x03300800                                 ///      节假日表编程总次数
#define C3_HOLIDAY_PGM_RECORD(N)                    (0x03300800 | LAST_INDEX(N))               /// 上N次节假日表编程记录N 1-10

#define C3_COMB_kWh_PGM_NUM                         0x03300900                                 ///      有功组合方式编程总次数
#define C3_COMB_kWh_PGM_RECORD(N)                   (0x03300900 | LAST_INDEX(N))               /// 上N次有功组合方式编程记录N 1-10

#define C3_COMB1_kvarh_PGM_NUM                      0x03300A00                                 ///      无功组合方式1编程总次数
#define C3_COMB1_kvarh_PGM_RECORD(N)                (0x03300A00 | LAST_INDEX(N))               /// 上N次无功组合方式1编程记录N 1-10

#define C3_COMB2_kvarh_PGM_NUM                      0x03300B00                                 ///      无功组合方式2编程总次数
#define C3_COMB2_kvarh_PGM_RECORD(N)                (0x03300B00 | LAST_INDEX(N))               /// 上N次无功组合方式2编程记录N 1-10

#define C3_BL_DAY_PGM_NUM                           0x03300C00                                 ///      结算日编程总次数
#define C3_BL_DAY_PGM_RECORD(N)                     (0x03300C00 | LAST_INDEX(N))               /// 上N次结算日编程记录N 1-10

#define C3_METER_COVER_NUM                          0x03300D00                                 ///      开表盖总次数
#define C3_METER_COVER_RECORD(N)                    (0x03300D00 | LAST_INDEX(N))               /// 上N次开表盖记录N 1-10

#define C3_TEM_COVER_NUM                            0x03300E00                                 ///      开端盖总次数
#define C3_TEM_COVER_RECORD(N)                      (0x03300E00 | LAST_INDEX(N))               /// 上N次开端盖记录N 1-10

//注：购电日期为预购电量（金额）输入电表的时间。
#define C3_PURCHASE_DATE(N)                         (0x03320100 | LAST_INDEX(N))               ///  上N次购电日期           N 1-10
#define C3_PURCHASE_NUM(N)                          (0x03320200 | LAST_INDEX(N))               ///  上N次购电后总购电次数   N 1-10
#define C3_PURCHASE_kWh(N)                          (0x03320300 | LAST_INDEX(N))               ///  上N次购电电量           N 1-10
#define C3_REMAIN_kWh_BEFORE(N)                     (0x03320400 | LAST_INDEX(N))               ///  上N次购电前剩余电量     N 1-10
#define C3_REMAIN_kWh_AFTER(N)                      (0x03320500 | LAST_INDEX(N))               ///  上N次购电购电后剩余电量 N 1-10
#define C3_CUM_PURCHASE_kWh(N)                      (0x03320600 | LAST_INDEX(N))               ///  上N次购电累计购电量     N 1-10

#define C3_PURCHASE_MONEY_DATE(N)                   (0x03330100 | LAST_INDEX(N))               ///  上N次购电日期 金额           N 1-10
#define C3_PURCHASE_MONEY_NUM(N)                    (0x03330200 | LAST_INDEX(N))               ///  上N次购电后总购电次数 金额   N 1-10
#define C3_PURCHASE_MONEY(N)                        (0x03330300 | LAST_INDEX(N))               ///  上N次购电金额           N 1-10
#define C3_REMAIN_MONEY_BEFORE(N)                   (0x03330400 | LAST_INDEX(N))               ///  上N次购电前剩余金额     N 1-10
#define C3_REMAIN_MONEY_AFTER(N)                    (0x03330500 | LAST_INDEX(N))               ///  上N次购电购电后剩余金额 N 1-10
#define C3_CUM_PURCHASE_MONEY(N)                    (0x03330600 | LAST_INDEX(N))               ///  上N次购电累计购金额     N 1-10


///参变量数据标识
// 注 1: 日时段表号和费率号的起始值为1。
// 注 2: 时区表数据不足设置时区数补最后一个时区数据，日时段表数据不足设置日时段数补最后一个日时段数据。
// 注 3: 以ASCII传输的数据项，不足字节后补NUL。
// 注 4: 厂家编号建议用企业代码。
// 注 5: 每月结算日数值如果为9999代表未设置此结算日。
// 注 6: 循环显示设置中NNNNNNNN代表每个显示项对应的数据标识。
// 注 7:单费率电费表，规定使用第一套费率和第二套费率中的费率 1。
// 注 8:费率修改属于电表操作类事件，按编程事件记录保存。
// 注 9. 无线通信参变量NN最高位bit7代表网络是否在线，0代表不在线，1代表在线。低三位bit0～bit2代表信号强度0～4，0为无信号，4为信号最强。
// 注10. 整点冻结时间间隔默认为60分钟。
#define C4_DATE_TIME                                 0x04000100                                 ///  日期时间             年月日时分秒  
#define C4_DATE_WEEK                                 0x04000101                                 ///  日期星期             年月日星期
#define C4_TIME                                      0x04000102                                 ///  时间                 时分秒     
#define C4_MD_PERIOD                                 0x04000103                                 ///  最大需量周期         分   
#define C4_MD_SLIP_PERIOD                            0x04000104                                 ///  需量滑差时间         分 
#define C4_PLUSE_WIDTH                               0x04000105                                 ///  校表脉冲宽度         毫秒 
#define C4_ZONE_SWITCH_TIME                          0x04000106                                 ///  两套时区表切换时间   年月日时分 
#define C4_SCHEDULE_SWITCH_TIME                      0x04000107                                 ///  两套日时段表切换时间 年月日时分 
#define C4_TARIFF_SWITCH_TIME                        0x04000108                                 ///  两套费率电价切换时间 年月日时分 
#define C4_STEP_SWITCH_TIME                          0x04000109                                 ///  两套阶梯度切换时间   年月日时分

#define C4_ZONE_NUM                                  0x04000201                                 ///  年时区数                
#define C4_SCHEDULE_TAB_NUM                          0x04000202                                 ///  日时段表数         
#define C4_SCHEDULE_NUM                              0x04000203                                 ///  日时段数      
#define C4_TARIFF_NUM                                0x04000204                                 ///  费率数        
#define C4_HOLIDAY_NUM                               0x04000205                                 ///  公共假日数          
#define C4_HARMONIC_NUM                              0x04000206                                 ///  谐波分析次数      
#define C4_STEP_NUM                                  0x04000207                                 ///  梯度数

#define C4_DISP_ITEM_NUM                             0x04000301                                 ///  自动循环显示屏数          NN
#define C4_DISP_TIME                                 0x04000302                                 ///  每屏显示时间              NN
#define C4_ENG_DISP_DECI_NUM                         0x04000303                                 ///  显示电能小数位数          NN
#define C4_POWER_DISP_DECI_NUM                       0x04000304                                 ///  显示功率(最大需量)小数位数 NN
#define C4_KEY_DISP_ITEM_NUM                         0x04000305                                 ///  按键循环显示屏数           NN
#define C4_CT                                        0x04000306                                 ///  电流互感器变比            NNNNNN
#define C4_PT                                        0x04000307                                 ///  电压互感器变比             NNNNNN
#define C4_CT_N                                      0x04000308                                 ///  电流互感器变比整数
#define C4_CT_D                                      0x04000309                                 ///  电流互感器变比小数
#define C4_PT_N                                      0x0400030A                                 ///  电压互感器变比整数
#define C4_PT_D                                      0x0400030B                                 ///  电压互感器变比小数

#define C4_COMM_ADDR                                 0x04000401                                 ///  通信地址
#define C4_METER_NO                                  0x04000402                                 ///  表号
#define C4_AMC_NO                                    0x04000403                                 ///  资产管理编码(ASCII 码)
#define C4_BASE_VOLTAGE                              0x04000404                                 ///  额定电压(ASCII 码)
#define C4_BASE_CURRENT                              0x04000405                                 ///  额定电流/基本电流(ASCII 码)
#define C4_MAX_CURRENT                               0x04000406                                 ///  最大电流(ASCII 码)
#define C4_ACT_ACCURACY                              0x04000407                                 ///  有功准确度等级(ASCII 码)
#define C4_REA_ACCURACY                              0x04000408                                 ///  无功准确度等级(ASCII 码)
#define C4_ACT_CONST                                 0x04000409                                 ///  电表有功常数
#define C4_REA_CONST                                 0x0400040A                                 ///  电表无功常数
#define C4_METER_TYPE                                0x0400040B                                 ///  电表型号(ASCII 码)
#define C4_PRODUCT_DATE                              0x0400040C                                 ///  生产日期(ASCII 码)
#define C4_PROTOCOL_VER                              0x0400040D                                 ///  协议版本号(ASCII 码)
#define C4_CUSTOMER_CODE                             0x0400040E                                 ///  客户编号 BCD

#define C4_METER_STATUS_1                            0x04000501                                 ///  电表状态1
#define C4_METER_STATUS_2                            0x04000502                                 ///  电表状态2
#define C4_METER_STATUS_3                            0x04000503                                 ///  电表状态3
#define C4_METER_STATUS_4                            0x04000504                                 ///  电表状态4
#define C4_METER_STATUS_5                            0x04000505                                 ///  电表状态5
#define C4_METER_STATUS_6                            0x04000506                                 ///  电表状态6
#define C4_METER_STATUS_7                            0x04000507                                 ///  电表状态7
#define C4_METER_STATUS_FF                           0x040005FF                                 ///  电表状态数据块

#define C4_kWh_COMB_TYPE                             0x04000601                                 ///  有功组合方式
#define C4_kvar_COMB1_TYPE                           0x04000602                                 ///  无功组合1方式
#define C4_kvar_COMB2_TYPE                           0x04000603                                 ///  无功组合2方式

#define C4_IR_38K                                    0x04000701                                 ///  远红外通讯速率特征字
#define C4_IR                                        0x04000702                                 ///  近红外通讯速率特征字
#define C4_COM_1                                     0x04000703                                 ///  通讯口1通讯速率特征字
#define C4_COM_2                                     0x04000704                                 ///  通讯口2通讯速率特征字
#define C4_COM_3                                     0x04000705                                 ///  通讯口3通讯速率特征字

#define C4_WEEKEND_STATUS                            0x04000801                                 ///  周休日特征字
#define C4_WEEKEND_SCH_TAB_NO                        0x04000802                                 ///  周休日采用的日时段表序号

#define C4_LOAD_PROFILE_MODE                         0x04000901                                 ///  负荷记录模式字
#define C4_FIXED_TIME_FRZ_MODE                       0x04000902                                 ///  定时冻结数据模式字
#define C4_INS_FRZ_MODE                              0x04000903                                 ///  瞬时冻结数据模式字
#define C4_CONTRACT_FRZ_MODE                         0x04000904                                 ///  约定冻结数据模式字
#define C4_HOUR_FRZ_MODE                             0x04000905                                 ///  整点冻结数据模式字
#define C4_DAY_FRZ_MODE                              0x04000906                                 ///  日  冻结数据模式字

#define C4_LOAD_PROFILE_START                        0x04000A01                                 ///  负荷记录起始时间
#define C4_LOAD_PROFILE1_PERIOD                      0x04000A02                                 ///  第1类负荷记录周期
#define C4_LOAD_PROFILE2_PERIOD                      0x04000A03                                 ///  第2类负荷记录周期
#define C4_LOAD_PROFILE3_PERIOD                      0x04000A04                                 ///  第3类负荷记录周期
#define C4_LOAD_PROFILE4_PERIOD                      0x04000A05                                 ///  第4类负荷记录周期
#define C4_LOAD_PROFILE5_PERIOD                      0x04000A06                                 ///  第5类负荷记录周期
#define C4_LOAD_PROFILE6_PERIOD                      0x04000A07                                 ///  第6类负荷记录周期

#define C4_BL1_DATE                                  0x04000B01                                 ///  每月第1结算日
#define C4_BL2_DATE                                  0x04000B02                                 ///  每月第2结算日
#define C4_BL3_DATE                                  0x04000B03                                 ///  每月第3结算日

#define C4_PASSWORD1                                 0x04000C01                                 ///  0级密码
#define C4_PASSWORD2                                 0x04000C02                                 ///  1级密码
#define C4_PASSWORD3                                 0x04000C03                                 ///  2级密码
#define C4_PASSWORD4                                 0x04000C04                                 ///  3级密码
#define C4_PASSWORD5                                 0x04000C05                                 ///  4级密码
#define C4_PASSWORD6                                 0x04000C06                                 ///  5级密码
#define C4_PASSWORD7                                 0x04000C07                                 ///  6级密码
#define C4_PASSWORD8                                 0x04000C08                                 ///  7级密码
#define C4_PASSWORD9                                 0x04000C09                                 ///  8级密码
#define C4_PASSWORDA                                 0x04000C0A                                 ///  9级密码

#define C4_LOW_kWh_ALARM1_VALUE                      0x04000F01                                 ///  低电量报警值1
#define C4_LOW_kWh_ALARM2_VALUE                      0x04000F02                                 ///  低电量报警值2
#define C4_STORE_kWh_MAX                             0x04000F03                                 ///  囤积电量限值
#define C4_OVERTRAFT_MAX                             0x04000F04                                 ///  透支电量限值

#define C4_LOW_MONE_ALARM1_VALUE                     0x04001001                                 ///  低金额报警值1
#define C4_LOW_MONE_ALARM2_VALUE                     0x04001002                                 ///  低金额报警值2
#define C4_OVERTRAFT_MONEY_MAX                       0x04001003                                 ///  透支金额限值
#define C4_STORE_MONE_MAX                            0x04001004                                 ///  囤积金额限值
#define C4_CONNECT_THRESHOLD                         0x04001005                                 ///  合闸允许电量阈值

#define C4_hour_FRZ_START_TIME                       0x04001201                                 ///  整点冻结开始时间
#define C4_hour_FRZ_END_TIME                         0x04001202                                 ///  整点冻结结束时间
#define C4_day_FRZ_TIME                              0x04001203                                 ///  日冻结开始时间

#define C4_GPRS_SIGNAL_STRENGTH                      0x04001301                                 ///  信号强度
#define C4_RELAY_OFF_DELAY                           0x04001302                                 ///  继电器跳闸延时 跳闸告警时间

#define C4_ZONE1_TABLE                               0x04010000                                 ///  第1套时区表数据 起始日期及日时段表号
#define C4_ZONE1_SCH1_TAB                            0x04010001                                 ///  第1套时区第1日时段表  起始时间及费率号
#define C4_ZONE1_SCH2_TAB                            0x04010002                                 ///  第1套时区第2日时段表  起始时间及费率号
#define C4_ZONE1_SCH3_TAB                            0x04010003                                 ///  第1套时区第3日时段表  起始时间及费率号
#define C4_ZONE1_SCH4_TAB                            0x04010004                                 ///  第1套时区第4日时段表  起始时间及费率号
#define C4_ZONE1_SCH5_TAB                            0x04010005                                 ///  第1套时区第5日时段表  起始时间及费率号
#define C4_ZONE1_SCH6_TAB                            0x04010006                                 ///  第1套时区第6日时段表  起始时间及费率号
#define C4_ZONE1_SCH7_TAB                            0x04010007                                 ///  第1套时区第7日时段表  起始时间及费率号
#define C4_ZONE1_SCH8_TAB                            0x04010008                                 ///  第1套时区第8日时段表  起始时间及费率号

#define C4_ZONE2_TABLE                               0x04020000                                 ///  第2套时区表数据 起始日期及日时段表号
#define C4_ZONE2_SCH1_TAB                            0x04020001                                 ///  第2套时区第1日时段表  起始时间及费率号
#define C4_ZONE2_SCH2_TAB                            0x04020002                                 ///  第2套时区第2日时段表  起始时间及费率号
#define C4_ZONE2_SCH3_TAB                            0x04020003                                 ///  第2套时区第3日时段表  起始时间及费率号
#define C4_ZONE2_SCH4_TAB                            0x04020004                                 ///  第2套时区第4日时段表  起始时间及费率号
#define C4_ZONE2_SCH5_TAB                            0x04020005                                 ///  第2套时区第5日时段表  起始时间及费率号
#define C4_ZONE2_SCH6_TAB                            0x04020006                                 ///  第2套时区第6日时段表  起始时间及费率号
#define C4_ZONE2_SCH7_TAB                            0x04020007                                 ///  第2套时区第7日时段表  起始时间及费率号
#define C4_ZONE2_SCH8_TAB                            0x04020008                                 ///  第2套时区第8日时段表  起始时间及费率号

#define C4_HOLIDAY_TABLE(N)                          (0x04030000 | LAST_INDEX(N))               ///  第 N 公共假日日期及日时段表号 1-50

#define C4_AUTO_DISP_ITEM(N)                         (0x04040100 | LAST_INDEX(N))               ///  自动循环显示第 N 屏显示数据项(是否分屏) 1- 254
#define C4_KEY_DISP_ITEM(N)                          (0x04040200 | LAST_INDEX(N))               ///  按键循环显示第 N 屏显示数据项(是否分屏) 1- 254

#define C4_TARIFF_TAB1_PRICE(N)                      (0x04050100 | LAST_INDEX(N))               ///  第1套费率电价 N 1-12
#define C4_TARIFF_TAB2_PRICE(N)                      (0x04050200 | LAST_INDEX(N))               ///  第2套费率电价 N 1-12

#define C4_STEP_TAB1_VALUE(N)                        (0x04060000 | LAST_INDEX(N))               ///  第1套阶梯值   N 1-7
#define C4_STEP_TAB1_PRICE(N)                        (0x04060100 | LAST_INDEX(N))               ///  第1套阶梯电价 N 1-7
#define C4_STEP_TAB2_VALUE(N)                        (0x04060200 | LAST_INDEX(N))               ///  第2套阶梯值   N 1-7
#define C4_STEP_TAB2_PRICE(N)                        (0x04060300 | LAST_INDEX(N))               ///  第2套阶梯电价 N 1-7

#define C4_LOSS_VOL_LIMIT_MAX                        0x04090101                                 ///  失压事件电压触发上限
#define C4_LOSS_VOL_LIMIT_MIN                        0x04090102                                 ///  失压事件电压恢复下限
#define C4_LOSS_VOL_LIMIT_CUR                        0x04090103                                 ///  失压事件电流触发下限
#define C4_LOSS_VOL_LIMIT_time                       0x04090104                                 ///  失压事件判定延时时间

#define C4_LOW_VOL_LIMIT_MAX                         0x04090201                                 ///  欠压事件电压触发上限
#define C4_LOW_VOL_LIMIT_time                        0x04090202                                 ///  欠压事件判定延时时间

#define C4_OVR_VOL_LIMIT_MAX                         0x04090301                                 ///  过压事件电压触发上限
#define C4_OVR_VOL_LIMIT_time                        0x04090302                                 ///  过压事件判定延时时间

#define C4_MISS_PH_LIMIT_VOL                         0x04090401                                 ///  断相事件电压触发上限
#define C4_MISS_PH_LIMIT_CUR                         0x04090402                                 ///  断相事件电流触发上限
#define C4_MISS_PH_LIMIT_TIME                        0x04090403                                 ///  断相事件判定延时时间

#define C4_VOL_UNB_PER                               0x04090501                                 ///  电压不平衡率限值
#define C4_VOL_UNB_TIME                              0x04090502                                 ///  电压不平衡率判定延时时间

#define C4_CUR_UNB_PER                               0x04090601                                 ///  电流不平衡率限值
#define C4_CUR_UNB_TIME                              0x04090602                                 ///  电流不平衡率判定延时时间
#define C4_LOS_CUR_V                                 0x04090701                                 ///  失流事件电压触发下限
#define C4_LOS_CUR_I_MAX                             0x04090702                                 ///  失流事件电流触发上限
#define C4_LOS_CUR_I_MIN                             0x04090703                                 ///  失流事件电流触发下限
#define C4_LOS_CUR_TIME                              0x04090704                                 ///  失流事件判定延时时间
#define C4_OVR_CUR_I                                 0x04090801                                 ///  过流事件电流触发下限
#define C4_OVR_CUR_TIME                              0x04090802                                 ///  过流事件判定延时时间
#define C4_MISS_CUR_V                                0x04090901                                 ///  断流事件电压触发下限
#define C4_MISS_CUR_I                                0x04090902                                 ///  断流事件电流触发上限
#define C4_MISS_CUR_TIME                             0x04090903                                 ///  断流事件判定延时时间
#define C4_REV_POWER                                 0x04090A01                                 ///  潮流反向事件有功功率触发下限
#define C4_REV_TIME                                  0x04090A02                                 ///  潮流反向事件判定延时时间
#define C4_OVER_LOAD_POWER                           0x04090B01                                 ///  过载事件有功功率触发下限
#define C4_OVER_LOAD_TIME                            0x04090B02                                 ///  过载事件判定延时时间
#define C4_VOL__MAX                                  0x04090C01                                 ///  电压考核上限
#define C4_VOL__MIN                                  0x04090C02                                 ///  电压考核下限
#define C4_OVR_MD_kW_THD                             0x04090D01                                 ///  有功需量超限事件需量触发下限
#define C4_OVR_MD_kvar_THD                           0x04090D02                                 ///  无功需量超限事件需量触发下限
#define C4_OVR_MD_TIME                               0x04090D03                                 ///  需量超限事件判定延时时间
#define C4_LOW_PF_THD                                0x04090E01                                 ///  总功率因数超下限阀值
#define C4_LOW_PF_TIME                               0x04090E02                                 ///  总功率因数超下限判定延时时间
#define C4_CUR_UNB2_THD                              0x04090F01                                 ///  电流严重不平衡限值
#define C4_CUR_UNB2_TIME                             0x04090F02                                 ///  电流严重不平衡触发延时时间

// 参变量数据标识(自定义)
#define C4_DEVICE_SOFT                               0x04A00100                                 ///  设备软件版本号


/// @冻结数据
// 注 1: 按照电能表实际设置的费率数冻结费率电能和最大需量及发生时间。
// 注 2: n的值为从站实际冻结的费率数加1（1为总量）。
// 注 3. 电能表上电后对停电期间数据不做补冻。
#define C5_TIMER_FRZ_TIME(N)                         (0x05000000 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结时间
#define C5_TIMER_FRZ_POS_kWh(N)                      (0x05000100 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结正向有功电能数据：
#define C5_TIMER_FRZ_NEG_kWh(N)                      (0x05000200 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结反向有功电能数据：
#define C5_TIMER_FRZ_CMB1_kvar(N)                    (0x05000300 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结组合无功 1 电能数据：
#define C5_TIMER_FRZ_CMB2_kvar(N)                    (0x05000400 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结组合无功 2 电能数据：
#define C5_TIMER_FRZ_Q1_kvar(N)                      (0x05000500 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结第一象限无功电能数据：
#define C5_TIMER_FRZ_Q2_kvar(N)                      (0x05000600 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结第二象限无功电能数据
#define C5_TIMER_FRZ_Q3_kvar(N)                      (0x05000700 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结第三象限无功电能数据
#define C5_TIMER_FRZ_Q4_kvar(N)                      (0x05000800 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结第四象限无功电能数据
#define C5_TIMER_FRZ_POS_kW_MD(N)                    (0x05000900 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结正向有功最大需量及发生时间数据
#define C5_TIMER_FRZ_NEG_kW_MD(N)                    (0x05000A00 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结反向有功最大需量及发生时间数据
#define C5_TIMER_FRZ_INS(N)                          (0x05001000 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结变量数据
#define C5_TIMER_FRZ_BLK(N)                          (0x0500FF00 | LAST_INDEX(N))               /// （上 N 1-12 次）定时冻结数据块

#define C5_INS_FRZ_TIME(N)                          (0x05010000 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结时间
#define C5_INS_FRZ_POS_kWh(N)                       (0x05010100 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结正向有功电能数据：
#define C5_INS_FRZ_NEG_kWh(N)                       (0x05010200 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结反向有功电能数据：
#define C5_INS_FRZ_CMB1_kvar(N)                     (0x05010300 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结组合无功 1 电能数据：
#define C5_INS_FRZ_CMB2_kvar(N)                     (0x05010400 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结组合无功 2 电能数据：
#define C5_INS_FRZ_Q1_kvar(N)                       (0x05010500 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结第一象限无功电能数据：
#define C5_INS_FRZ_Q2_kvar(N)                       (0x05010600 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结第二象限无功电能数据
#define C5_INS_FRZ_Q3_kvar(N)                       (0x05010700 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结第三象限无功电能数据
#define C5_INS_FRZ_Q4_kvar(N)                       (0x05010800 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结第四象限无功电能数据
#define C5_INS_FRZ_POS_kW_MD(N)                     (0x05010900 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结正向有功最大需量及发生时间数据
#define C5_INS_FRZ_NEG_kW_MD(N)                     (0x05010A00 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结反向有功最大需量及发生时间数据
#define C5_INS_FRZ_INS(N)                           (0x05011000 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结变量数据
#define C5_INS_FRZ_BLK(N)                           (0x0501FF00 | LAST_INDEX(N))                /// （上 N 1-3 次）瞬时冻结数据块

#define C5_ZONE_SW_FRZ_TIME(N)                      (0x05020000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结时间
#define C5_ZONE_SW_FRZ_POS_kWh(N)                   (0x05020100 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结正向有功电能数据：
#define C5_ZONE_SW_FRZ_NEG_kWh(N)                   (0x05020200 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结反向有功电能数据：
#define C5_ZONE_SW_FRZ_CMB1_kvar(N)                 (0x05020300 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结组合无功 1 电能数据：
#define C5_ZONE_SW_FRZ_CMB2_kvar(N)                 (0x05020400 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结组合无功 2 电能数据：
#define C5_ZONE_SW_FRZ_Q1_kvar(N)                   (0x05020500 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结第一象限无功电能数据：
#define C5_ZONE_SW_FRZ_Q2_kvar(N)                   (0x05020600 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结第二象限无功电能数据
#define C5_ZONE_SW_FRZ_Q3_kvar(N)                   (0x05020700 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结第三象限无功电能数据
#define C5_ZONE_SW_FRZ_Q4_kvar(N)                   (0x05020800 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结第四象限无功电能数据
#define C5_ZONE_SW_FRZ_POS_kW_MD(N)                 (0x05020900 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结正向有功最大需量及发生时间数据
#define C5_ZONE_SW_FRZ_NEG_kW_MD(N)                 (0x05020A00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结反向有功最大需量及发生时间数据
#define C5_ZONE_SW_FRZ_INS(N)                       (0x05021000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结变量数据
#define C5_ZONE_SW_FRZ_BLK(N)                       (0x0502FF00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套时区表切换冻结数据块

#define C5_SCH_SW_FRZ_TIME(N)                       (0x05030000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结时间
#define C5_SCH_SW_FRZ_POS_kWh(N)                    (0x05030100 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结正向有功电能数据：
#define C5_SCH_SW_FRZ_NEG_kWh(N)                    (0x05030200 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结反向有功电能数据：
#define C5_SCH_SW_FRZ_CMB1_kvar(N)                  (0x05030300 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结组合无功 1 电能数据：
#define C5_SCH_SW_FRZ_CMB2_kvar(N)                  (0x05030400 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结组合无功 2 电能数据：
#define C5_SCH_SW_FRZ_Q1_kvar(N)                    (0x05030500 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结第一象限无功电能数据：
#define C5_SCH_SW_FRZ_Q2_kvar(N)                    (0x05030600 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结第二象限无功电能数据
#define C5_SCH_SW_FRZ_Q3_kvar(N)                    (0x05030700 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结第三象限无功电能数据
#define C5_SCH_SW_FRZ_Q4_kvar(N)                    (0x05030800 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结第四象限无功电能数据
#define C5_SCH_SW_FRZ_POS_kW_MD(N)                  (0x05030900 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结正向有功最大需量及发生时间数据
#define C5_SCH_SW_FRZ_NEG_kW_MD(N)                  (0x05030A00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结反向有功最大需量及发生时间数据
#define C5_SCH_SW_FRZ_INS(N)                        (0x05031000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结变量数据
#define C5_SCH_SW_FRZ_BLK(N)                        (0x0503FF00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套日时段表切换冻结数据块

#define C5_hour_FRZ_TIME(N)                         (0x05040000 | LAST_INDEX(N))                /// (上N 1- 254 次）整点冻结时间
#define C5_hour_FRZ_POS_kWh(N)                      (0x05040100 | LAST_INDEX(N))                /// (上N 1- 254 次）整点冻结正向有功总电能
#define C5_hour_FRZ_NEG_kWh(N)                      (0x05040200 | LAST_INDEX(N))                /// (上N 1- 254 次）整点冻结反向有功总电能
#define C5_hour_FRZ_BLK(N)                          (0x0504FF00 | LAST_INDEX(N))                /// (上N 1- 254 次）整点冻结数据块

#define C5_DAY_FRZ_TIME(N)                          (0x05060000 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结时间
#define C5_DAY_FRZ_POS_kWh(N)                       (0x05060100 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结正向有功电能数据：
#define C5_DAY_FRZ_NEG_kWh(N)                       (0x05060200 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结反向有功电能数据：
#define C5_DAY_FRZ_CMB1_kvar(N)                     (0x05060300 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结组合无功 1 电能数据：
#define C5_DAY_FRZ_CMB2_kvar(N)                     (0x05060400 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结组合无功 2 电能数据：
#define C5_DAY_FRZ_Q1_kvar(N)                       (0x05060500 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结第一象限无功电能数据：
#define C5_DAY_FRZ_Q2_kvar(N)                       (0x05060600 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结第二象限无功电能数据
#define C5_DAY_FRZ_Q3_kvar(N)                       (0x05060700 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结第三象限无功电能数据
#define C5_DAY_FRZ_Q4_kvar(N)                       (0x05060800 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结第四象限无功电能数据
#define C5_DAY_FRZ_POS_kW_MD(N)                     (0x05060900 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结正向有功最大需量及发生时间数据
#define C5_DAY_FRZ_NEG_kW_MD(N)                     (0x05060A00 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结反向有功最大需量及发生时间数据
#define C5_DAY_FRZ_INS(N)                           (0x05061000 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结变量数据
#define C5_DAY_FRZ_BLK(N)                           (0x0506FF00 | LAST_INDEX(N))                /// （上 N 1-62 次）日冻结数据块

#define C5_STEP_SW_FRZ_TIME(N)                      (0x05070000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结时间
#define C5_STEP_SW_FRZ_POS_kWh(N)                   (0x05070100 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结正向有功电能数据：
#define C5_STEP_SW_FRZ_NEG_kWh(N)                   (0x05070200 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结反向有功电能数据：
#define C5_STEP_SW_FRZ_CMB1_kvar(N)                 (0x05070300 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结组合无功 1 电能数据：
#define C5_STEP_SW_FRZ_CMB2_kvar(N)                 (0x05070400 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结组合无功 2 电能数据：
#define C5_STEP_SW_FRZ_Q1_kvar(N)                   (0x05070500 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结第一象限无功电能数据：
#define C5_STEP_SW_FRZ_Q2_kvar(N)                   (0x05070600 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结第二象限无功电能数据
#define C5_STEP_SW_FRZ_Q3_kvar(N)                   (0x05070700 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结第三象限无功电能数据
#define C5_STEP_SW_FRZ_Q4_kvar(N)                   (0x05070800 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结第四象限无功电能数据
#define C5_STEP_SW_FRZ_POS_kW_MD(N)                 (0x05070900 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结正向有功最大需量及发生时间数据
#define C5_STEP_SW_FRZ_NEG_kW_MD(N)                 (0x05070A00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结反向有功最大需量及发生时间数据
#define C5_STEP_SW_FRZ_INS(N)                       (0x05071000 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结变量数据
#define C5_STEP_SW_FRZ_BLK(N)                       (0x0507FF00 | LAST_INDEX(N))                /// （上 N 1-2 次）两套阶梯切换冻结数据块


// 负荷记录数据标识编码表
// 表格中的数据格式为主站下行格式说明，从站上行的数据域负荷记录格式、结构定义见附录B。
#define LP_INDEX(N)  ((uint32_t)N << 16)
#define C6_EARLIEST_GET(N)                          (0x06000000 | LP_INDEX(N))                  ///  第N 0-6类负荷最早记录块
#define C6_TIME_GET(N)                              (0x06000001 | LP_INDEX(N))                  ///  第N 0-6类负荷给定时间记录块
#define C6_LAST_GET(N)                              (0x06000002 | LP_INDEX(N))                  ///  第N 0-6类负荷最近一个记录块

/// 安全认证专用读数据的数据标识编码表
#define C7_DATA_GET                                 0x07800101                                  ///  数据回抄
#define C7_STATUS_GET                               0x07800201                                  ///  状态查询

// 注：
// 随机数：系统中存在有随机数1和随机数2，随机数1是主站获取的随机数，8字节；随机数2是电能表获取的随机数，4字节。
// MAC1是根据购电金额和购电次数计算的的MAC；MAC2是根据客户编号计算的的MAC。
// 开户时，不需要验证客户编号，直接将客户编号写到ESAM的对应文件中；再进行充值操作。
// 充值时，先比对客户编号是否相同，相同再将客户编号写到ESAM的相应区，进行MAC校验，如果验证通过再进行充值操作。
#define C7_ID_VERIFY_PSW                            0x07000001                                  ///  密文1身份认证指令
#define C7_ID_VERIFY_RN                             0x07000002                                  ///  随机数1身份认证指令
#define C7_ID_VERIFY_FACTOR                         0x07000003                                  ///  分散因子身份认证指令
#define C7_ID_VERIFY                                0x070000FF                                  ///  分散因子身份认证指令

#define C7_ID_VERIFY_TIMEOUT_SET                    0x07000101                                  ///  身份认证有效时长 身份认证时效设置
#define C7_ID_VERIFY_MAC                            0x07000103                                  ///  MAC   身份认证时效设置
#define C7_ID_VERIFY_SET                            0x070001FF                                  ///  身份认证时效设置

#define C7_ID_VERIFY_UNVOLID                        0x07000201                                  ///  身份认证失效
#define C7_ID_VERIFY_UNVOLID_FF                     0x070002ff                                  ///  身份认证失效时刻

#define C7_OPEN_ACCOUNT                             0x07010101                                  ///  开户指令
#define C7_OPEN_ACCOUNT_FF                          0x070101ff                                  ///  开户指令
#define C7_PURCHASE                                 0x07010201                                  ///  充值
#define C7_PURCHASE_FF                              0x070102ff                                  ///  充值

#define C7_CLOSE_ACCOUNT                            0x070103ff                                  ///  销户

#define C7_REFUND                                   0x070104ff                                  ///  退费

#define C7_CTRL_KEY_UPDATE                          0x07020101                                  ///  控制命令密钥更新
#define C7_CTRL_KEY_UPDATE_FF                       0x070201ff                                  ///  控制命令密钥更新
#define C7_PARA_KEY_UPDATE                          0x07020201                                  ///  参数密钥更新
#define C7_PARA_KEY_UPDATE_FF                       0x070202ff                                  ///  参数密钥更新

#define C7_CHECK_STATUS                             0x078102ff                                  ///  检查ESAM状态

#define C8_ENTRY_FACTORY                            0x08000001                                  ///  自定义进入工厂模式
#define C8_ENTRY_PROGRAM                            0x08000002                                  ///  自定义进入编程模式

/// 07 协议 （续）事件记录数据标识编码
#define C10_LOS_VOL_CNT                             0x10000001                                 /// 失压总次数
#define C10_LOS_VOL_TMR                             0x10000002                                 /// 失压总累计时间
#define C10_LAST_LOS_VOL_TMR_S                      0x10000101                                 /// 最近 1 次失压发生时刻
#define C10_LAST_LOS_VOL_TMR_E                      0x10000201                                 /// 最近 1 次失压结束时刻
#define C10_LOS_VOL_A_CNT                           0x10010001                                 /// A相失压总次数
#define C10_LOS_VOL_A_TMR                           0x10010002                                 /// A相失压总累计时间
#define C10_LOS_VOL_B_CNT                           0x10020001                                 /// B相失压总次数
#define C10_LOS_VOL_B_TMR                           0x10020002                                 /// B相失压总累计时间
#define C10_LOS_VOL_C_CNT                           0x10030001                                 /// C相失压总次数
#define C10_LOS_VOL_C_TMR                           0x10030002                                 /// C相失压总累计时间

#define PH_N_INDEX(ph,N)  ((uint32_t)(ph) << 16 + N)     /// ph 相序号，N 记录序号 
/// 失压，过压，欠压，断相 数据结构一致
#define C10_t_s(ph,N)                               (0x10000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻
#define C10_0pkWh(ph,N)                             (0x10000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻正向有功总电能
#define C10_0nkWh(ph,N)                             (0x10000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻反向有功总电能
#define C10_0pkvarh(ph,N)                           (0x10000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻组合无功 1 总电能
#define C10_0nkvarh(ph,N)                           (0x10000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻组合无功 2 总电能
#define C10_1pkWh(ph,N)                             (0x10000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相正向有功电能
#define C10_1nkWh(ph,N)                             (0x10000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相反向有功电能
#define C10_1pkvarh(ph,N)                           (0x10000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相组合无功 1 电能
#define C10_1nkvarh(ph,N)                           (0x10000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相组合无功 2 电能
#define C10_1v(ph,N)                                (0x10000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相电压
#define C10_1i(ph,N)                                (0x10000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相电流
#define C10_1kW(ph,N)                               (0x10000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相有功功率
#define C10_1kvar(ph,N)                             (0x10000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相无功功率
#define C10_1pf(ph,N)                               (0x10000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 A 相功率因数
#define C10_2pkWh(ph,N)                             (0x10000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相正向有功电能
#define C10_2nkWh(ph,N)                             (0x10001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相反向有功电能
#define C10_2pkvarh(ph,N)                           (0x10001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相组合无功 1 电能
#define C10_2nkvarh(ph,N)                           (0x10001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相组合无功 2 电能
#define C10_2v(ph,N)                                (0x10001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相电压
#define C10_2i(ph,N)                                (0x10001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相电流
#define C10_2kW(ph,N)                               (0x10001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相有功功率
#define C10_2kvar(ph,N)                             (0x10001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相无功功率
#define C10_2pf(ph,N)                               (0x10001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 B 相功率因数
#define C10_3pkWh(ph,N)                             (0x10001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相正向有功电能
#define C10_3nkWh(ph,N)                             (0x10001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相反向有功电能
#define C10_3pkvarh(ph,N)                           (0x10001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相组合无功 1 电能
#define C10_3nkvarh(ph,N)                           (0x10001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相组合无功 2 电能
#define C10_3v(ph,N)                                (0x10001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相电压
#define C10_3i(ph,N)                                (0x10001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相电流
#define C10_3kW(ph,N)                               (0x10001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相有功功率
#define C10_3kvar(ph,N)                             (0x10001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相无功功率
#define C10_3pf(ph,N)                               (0x10002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压发生时刻 C 相功率因数
#define C10_0ah(ph,N)                               (0x10002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压期间总安时数
#define C10_1ah(ph,N)                               (0x10002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压期间 A 相安时数
#define C10_2ah(ph,N)                               (0x10002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压期间 B 相安时数
#define C10_3ah(ph,N)                               (0x10002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压期间 C 相安时数
#define C10_t_e(ph,N)                               (0x10002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻
#define C10_0pkWh_e(ph,N)                           (0x10002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻正向有功总电能
#define C10_0nkWh_e(ph,N)                           (0x10002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻反向有功总电能
#define C10_0pkvarh_e(ph,N)                         (0x10002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻组合无功 1 总电能
#define C10_0nkvarh_e(ph,N)                         (0x10002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻组合无功 2 总电能
#define C10_1pkWh_e(ph,N)                           (0x10002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 A 相正向有功电能
#define C10_1nkWh_e(ph,N)                           (0x10002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 A 相反向有功电能
#define C10_1pkvarh_e(ph,N)                         (0x10002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 A 相组合无功 1 电能
#define C10_1nkvarh_e(ph,N)                         (0x10002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 A 相组合无功 2 电能
#define C10_2pkWh_e(ph,N)                           (0x10002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 B 相正向有功电能
#define C10_2nkWh_e(ph,N)                           (0x10002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 B 相反向有功电能
#define C10_2pkvarh_e(ph,N)                         (0x10003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 B 相组合无功 1 电能
#define C10_2nkvarh_e(ph,N)                         (0x10003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 B 相组合无功 2 电能
#define C10_3pkWh_e(ph,N)                           (0x10003200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 C 相正向有功电能
#define C10_3nkWh_e(ph,N)                           (0x10003300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 C 相反向有功电能
#define C10_3pkvarh_e(ph,N)                         (0x10003400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 C 相组合无功 1 电能
#define C10_3nkvarh_e(ph,N)                         (0x10003500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压结束时刻 C 相组合无功 2 电能
#define C10_ALL(ph,N)                               (0x1000FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失压数据块

#define C11_LOW_VOL_A_CNT                           0x11010001                                    /// A相欠压总次数
#define C11_LOW_VOL_A_TMR                           0x11010002                                    /// A相欠压总累计时间
#define C11_LOW_VOL_B_CNT                           0x11020001                                    /// B相欠压总次数
#define C11_LOW_VOL_B_TMR                           0x11020002                                    /// B相欠压总累计时间
#define C11_LOW_VOL_C_CNT                           0x11030001                                    /// C相欠压总次数
#define C11_LOW_VOL_C_TMR                           0x11030002                                    /// C相欠压总累计时间

#define C11_t_s(ph,N)                               (0x11000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻
#define C11_0pkWh(ph,N)                             (0x11000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻正向有功总电能
#define C11_0nkWh(ph,N)                             (0x11000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻反向有功总电能
#define C11_0pkvarh(ph,N)                           (0x11000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻组合无功 1 总电能
#define C11_0nkvarh(ph,N)                           (0x11000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻组合无功 2 总电能
#define C11_1pkWh(ph,N)                             (0x11000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相正向有功电能
#define C11_1nkWh(ph,N)                             (0x11000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相反向有功电能
#define C11_1pkvarh(ph,N)                           (0x11000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相组合无功 1 电能
#define C11_1nkvarh(ph,N)                           (0x11000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相组合无功 2 电能
#define C11_1v(ph,N)                                (0x11000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相电压
#define C11_1i(ph,N)                                (0x11000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相电流
#define C11_1kW(ph,N)                               (0x11000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相有功功率
#define C11_1kvar(ph,N)                             (0x11000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相无功功率
#define C11_1pf(ph,N)                               (0x11000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 A 相功率因数
#define C11_2pkWh(ph,N)                             (0x11000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相正向有功电能
#define C11_2nkWh(ph,N)                             (0x11001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相反向有功电能
#define C11_2pkvarh(ph,N)                           (0x11001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相组合无功 1 电能
#define C11_2nkvarh(ph,N)                           (0x11001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相组合无功 2 电能
#define C11_2v(ph,N)                                (0x11001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相电压
#define C11_2i(ph,N)                                (0x11001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相电流
#define C11_2kW(ph,N)                               (0x11001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相有功功率
#define C11_2kvar(ph,N)                             (0x11001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相无功功率
#define C11_2pf(ph,N)                               (0x11001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 B 相功率因数
#define C11_3pkWh(ph,N)                             (0x11001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相正向有功电能
#define C11_3nkWh(ph,N)                             (0x11001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相反向有功电能
#define C11_3pkvarh(ph,N)                           (0x11001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相组合无功 1 电能
#define C11_3nkvarh(ph,N)                           (0x11001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相组合无功 2 电能
#define C11_3v(ph,N)                                (0x11001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相电压
#define C11_3i(ph,N)                                (0x11001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相电流
#define C11_3kW(ph,N)                               (0x11001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相有功功率
#define C11_3kvar(ph,N)                             (0x11001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相无功功率
#define C11_3pf(ph,N)                               (0x11002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压发生时刻 C 相功率因数
#define C11_0ah(ph,N)                               (0x11002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压期间总安时数
#define C11_1ah(ph,N)                               (0x11002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压期间 A 相安时数
#define C11_2ah(ph,N)                               (0x11002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压期间 B 相安时数
#define C11_3ah(ph,N)                               (0x11002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压期间 C 相安时数
#define C11_t_e(ph,N)                               (0x11002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻
#define C11_0pkWh_e(ph,N)                           (0x11002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻正向有功总电能
#define C11_0nkWh_e(ph,N)                           (0x11002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻反向有功总电能
#define C11_0pkvarh_e(ph,N)                         (0x11002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻组合无功 1 总电能
#define C11_0nkvarh_e(ph,N)                         (0x11002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻组合无功 2 总电能
#define C11_1pkWh_e(ph,N)                           (0x11002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 A 相正向有功电能
#define C11_1nkWh_e(ph,N)                           (0x11002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 A 相反向有功电能
#define C11_1pkvarh_e(ph,N)                         (0x11002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 A 相组合无功 1 电能
#define C11_1nkvarh_e(ph,N)                         (0x11002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 A 相组合无功 2 电能
#define C11_2pkWh_e(ph,N)                           (0x11002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 B 相正向有功电能
#define C11_2nkWh_e(ph,N)                           (0x11002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 B 相反向有功电能
#define C11_2pkvarh_e(ph,N)                         (0x11003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 B 相组合无功 1 电能
#define C11_2nkvarh_e(ph,N)                         (0x11003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 B 相组合无功 2 电能
#define C11_3pkWh_e(ph,N)                           (0x11003200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 C 相正向有功电能
#define C11_3nkWh_e(ph,N)                           (0x11003300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 C 相反向有功电能
#define C11_3pkvarh_e(ph,N)                         (0x11003400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 C 相组合无功 1 电能
#define C11_3nkvarh_e(ph,N)                         (0x11003500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压结束时刻 C 相组合无功 2 电能
#define C11_ALL(ph,N)                               (0x1100FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相欠压数据块

#define C12_OVR_VOL_A_CNT                           0x12010001                                    /// A相过压总次数
#define C12_OVR_VOL_A_TMR                           0x12010002                                    /// A相过压总累计时间
#define C12_OVR_VOL_B_CNT                           0x12020001                                    /// B相过压总次数
#define C12_OVR_VOL_B_TMR                           0x12020002                                    /// B相过压总累计时间
#define C12_OVR_VOL_C_CNT                           0x12030001                                    /// C相过压总次数
#define C12_OVR_VOL_C_TMR                           0x12030002                                    /// C相过压总累计时间

#define C12_t_s(ph,N)                               (0x12000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻
#define C12_0pkWh(ph,N)                             (0x12000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻正向有功总电能
#define C12_0nkWh(ph,N)                             (0x12000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻反向有功总电能
#define C12_0pkvarh(ph,N)                           (0x12000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻组合无功 1 总电能
#define C12_0nkvarh(ph,N)                           (0x12000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻组合无功 2 总电能
#define C12_1pkWh(ph,N)                             (0x12000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相正向有功电能
#define C12_1nkWh(ph,N)                             (0x12000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相反向有功电能
#define C12_1pkvarh(ph,N)                           (0x12000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相组合无功 1 电能
#define C12_1nkvarh(ph,N)                           (0x12000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相组合无功 2 电能
#define C12_1v(ph,N)                                (0x12000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相电压
#define C12_1i(ph,N)                                (0x12000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相电流
#define C12_1kW(ph,N)                               (0x12000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相有功功率
#define C12_1kvar(ph,N)                             (0x12000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相无功功率
#define C12_1pf(ph,N)                               (0x12000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 A 相功率因数
#define C12_2pkWh(ph,N)                             (0x12000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相正向有功电能
#define C12_2nkWh(ph,N)                             (0x12001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相反向有功电能
#define C12_2pkvarh(ph,N)                           (0x12001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相组合无功 1 电能
#define C12_2nkvarh(ph,N)                           (0x12001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相组合无功 2 电能
#define C12_2v(ph,N)                                (0x12001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相电压
#define C12_2i(ph,N)                                (0x12001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相电流
#define C12_2kW(ph,N)                               (0x12001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相有功功率
#define C12_2kvar(ph,N)                             (0x12001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相无功功率
#define C12_2pf(ph,N)                               (0x12001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 B 相功率因数
#define C12_3pkWh(ph,N)                             (0x12001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相正向有功电能
#define C12_3nkWh(ph,N)                             (0x12001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相反向有功电能
#define C12_3pkvarh(ph,N)                           (0x12001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相组合无功 1 电能
#define C12_3nkvarh(ph,N)                           (0x12001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相组合无功 2 电能
#define C12_3v(ph,N)                                (0x12001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相电压
#define C12_3i(ph,N)                                (0x12001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相电流
#define C12_3kW(ph,N)                               (0x12001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相有功功率
#define C12_3kvar(ph,N)                             (0x12001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相无功功率
#define C12_3pf(ph,N)                               (0x12002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压发生时刻 C 相功率因数
#define C12_0ah(ph,N)                               (0x12002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压期间总安时数
#define C12_1ah(ph,N)                               (0x12002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压期间 A 相安时数
#define C12_2ah(ph,N)                               (0x12002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压期间 B 相安时数
#define C12_3ah(ph,N)                               (0x12002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压期间 C 相安时数
#define C12_t_e(ph,N)                               (0x12002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻
#define C12_0pkWh_e(ph,N)                           (0x12002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻正向有功总电能
#define C12_0nkWh_e(ph,N)                           (0x12002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻反向有功总电能
#define C12_0pkvarh_e(ph,N)                         (0x12002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻组合无功 1 总电能
#define C12_0nkvarh_e(ph,N)                         (0x12002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻组合无功 2 总电能
#define C12_1pkWh_e(ph,N)                           (0x12002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 A 相正向有功电能
#define C12_1nkWh_e(ph,N)                           (0x12002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 A 相反向有功电能
#define C12_1pkvarh_e(ph,N)                         (0x12002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 A 相组合无功 1 电能
#define C12_1nkvarh_e(ph,N)                         (0x12002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 A 相组合无功 2 电能
#define C12_2pkWh_e(ph,N)                           (0x12002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 B 相正向有功电能
#define C12_2nkWh_e(ph,N)                           (0x12002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 B 相反向有功电能
#define C12_2pkvarh_e(ph,N)                         (0x12003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 B 相组合无功 1 电能
#define C12_2nkvarh_e(ph,N)                         (0x12003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 B 相组合无功 2 电能
#define C12_3pkWh_e(ph,N)                           (0x12003200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 C 相正向有功电能
#define C12_3nkWh_e(ph,N)                           (0x12003300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 C 相反向有功电能
#define C12_3pkvarh_e(ph,N)                         (0x12003400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 C 相组合无功 1 电能
#define C12_3nkvarh_e(ph,N)                         (0x12003500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压结束时刻 C 相组合无功 2 电能
#define C12_ALL(ph,N)                               (0x1200FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过压数据块

#define C13_MIS_VOL_A_CNT                           0x13010001                                    /// A相断相总次数
#define C13_MIS_VOL_A_TMR                           0x13010002                                    /// A相断相总累计时间
#define C13_MIS_VOL_B_CNT                           0x13020001                                    /// B相断相总次数
#define C13_MIS_VOL_B_TMR                           0x13020002                                    /// B相断相总累计时间
#define C13_MIS_VOL_C_CNT                           0x13030001                                    /// C相断相总次数
#define C13_MIS_VOL_C_TMR                           0x13030002                                    /// C相断相总累计时间

#define C13_t_s(ph,N)                               (0x13000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻
#define C13_0pkWh(ph,N)                             (0x13000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻正向有功总电能
#define C13_0nkWh(ph,N)                             (0x13000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻反向有功总电能
#define C13_0pkvarh(ph,N)                           (0x13000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻组合无功 1 总电能
#define C13_0nkvarh(ph,N)                           (0x13000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻组合无功 2 总电能
#define C13_1pkWh(ph,N)                             (0x13000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相正向有功电能
#define C13_1nkWh(ph,N)                             (0x13000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相反向有功电能
#define C13_1pkvarh(ph,N)                           (0x13000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相组合无功 1 电能
#define C13_1nkvarh(ph,N)                           (0x13000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相组合无功 2 电能
#define C13_1v(ph,N)                                (0x13000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相电压
#define C13_1i(ph,N)                                (0x13000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相电流
#define C13_1kW(ph,N)                               (0x13000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相有功功率
#define C13_1kvar(ph,N)                             (0x13000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相无功功率
#define C13_1pf(ph,N)                               (0x13000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 A 相功率因数
#define C13_2pkWh(ph,N)                             (0x13000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相正向有功电能
#define C13_2nkWh(ph,N)                             (0x13001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相反向有功电能
#define C13_2pkvarh(ph,N)                           (0x13001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相组合无功 1 电能
#define C13_2nkvarh(ph,N)                           (0x13001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相组合无功 2 电能
#define C13_2v(ph,N)                                (0x13001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相电压
#define C13_2i(ph,N)                                (0x13001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相电流
#define C13_2kW(ph,N)                               (0x13001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相有功功率
#define C13_2kvar(ph,N)                             (0x13001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相无功功率
#define C13_2pf(ph,N)                               (0x13001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 B 相功率因数
#define C13_3pkWh(ph,N)                             (0x13001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相正向有功电能
#define C13_3nkWh(ph,N)                             (0x13001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相反向有功电能
#define C13_3pkvarh(ph,N)                           (0x13001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相组合无功 1 电能
#define C13_3nkvarh(ph,N)                           (0x13001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相组合无功 2 电能
#define C13_3v(ph,N)                                (0x13001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相电压
#define C13_3i(ph,N)                                (0x13001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相电流
#define C13_3kW(ph,N)                               (0x13001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相有功功率
#define C13_3kvar(ph,N)                             (0x13001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相无功功率
#define C13_3pf(ph,N)                               (0x13002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相发生时刻 C 相功率因数
#define C13_0ah(ph,N)                               (0x13002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相期间总安时数
#define C13_1ah(ph,N)                               (0x13002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相期间 A 相安时数
#define C13_2ah(ph,N)                               (0x13002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相期间 B 相安时数
#define C13_3ah(ph,N)                               (0x13002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相期间 C 相安时数
#define C13_t_e(ph,N)                               (0x13002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻
#define C13_0pkWh_e(ph,N)                           (0x13002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻正向有功总电能
#define C13_0nkWh_e(ph,N)                           (0x13002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻反向有功总电能
#define C13_0pkvarh_e(ph,N)                         (0x13002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻组合无功 1 总电能
#define C13_0nkvarh_e(ph,N)                         (0x13002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻组合无功 2 总电能
#define C13_1pkWh_e(ph,N)                           (0x13002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 A 相正向有功电能
#define C13_1nkWh_e(ph,N)                           (0x13002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 A 相反向有功电能
#define C13_1pkvarh_e(ph,N)                         (0x13002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 A 相组合无功 1 电能
#define C13_1nkvarh_e(ph,N)                         (0x13002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 A 相组合无功 2 电能
#define C13_2pkWh_e(ph,N)                           (0x13002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 B 相正向有功电能
#define C13_2nkWh_e(ph,N)                           (0x13002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 B 相反向有功电能
#define C13_2pkvarh_e(ph,N)                         (0x13003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 B 相组合无功 1 电能
#define C13_2nkvarh_e(ph,N)                         (0x13003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 B 相组合无功 2 电能
#define C13_3pkWh_e(ph,N)                           (0x13003200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 C 相正向有功电能
#define C13_3nkWh_e(ph,N)                           (0x13003300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 C 相反向有功电能
#define C13_3pkvarh_e(ph,N)                         (0x13003400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 C 相组合无功 1 电能
#define C13_3nkvarh_e(ph,N)                         (0x13003500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相结束时刻 C 相组合无功 2 电能
#define C13_ALL(ph,N)                               (0x1300FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断相数据块

#define C14_VOL_REV_SEQ_CNT                         0x14000001                                    /// 电压逆相序总次数
#define C14_VOL_REV_SEQ_TMR                         0x14000002                                    /// 电压逆相序总累计时间

#define C15_CUR_REV_SEQ_CNT                         0x15000001                                    /// 电流逆相序总次数
#define C15_CUR_REV_SEQ_TMR                         0x15000002                                    /// 电流逆相序总累计时间

#define C16_VOL_UNB_CNT                             0x16000001                                    /// 电压不平衡总次数
#define C16_VOL_UNB_TMR                             0x16000002                                    /// 电压不平衡总累计时间

#define C17_CUR_UNB_CNT                             0x17000001                                    /// 电流不平衡总次数
#define C17_CUR_UNB_TMR                             0x17000002                                    /// 电流不平衡总累计时间

/// 失流，过流，断流数据结构一致
#define C18_LOS_CUR_A_CNT                           0x18010001                                    /// A相失流总次数
#define C18_LOS_CUR_A_TMR                           0x18010002                                    /// A相失流总累计时间
#define C18_LOS_CUR_B_CNT                           0x18020001                                    /// B相失流总次数
#define C18_LOS_CUR_B_TMR                           0x18020002                                    /// B相失流总累计时间
#define C18_LOS_CUR_C_CNT                           0x18030001                                    /// C相失流总次数
#define C18_LOS_CUR_C_TMR                           0x18030002                                    /// C相失流总累计时间

#define C18_t_s(ph,N)                               (0x18000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻
#define C18_0pkWh(ph,N)                             (0x18000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻正向有功总电能
#define C18_0nkWh(ph,N)                             (0x18000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻反向有功总电能
#define C18_0pkvarh(ph,N)                           (0x18000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻组合无功 1 总电能
#define C18_0nkvarh(ph,N)                           (0x18000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻组合无功 2 总电能
#define C18_1pkWh(ph,N)                             (0x18000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相正向有功电能
#define C18_1nkWh(ph,N)                             (0x18000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相反向有功电能
#define C18_1pkvarh(ph,N)                           (0x18000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相组合无功 1 电能
#define C18_1nkvarh(ph,N)                           (0x18000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相组合无功 2 电能
#define C18_1v(ph,N)                                (0x18000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相电压
#define C18_1i(ph,N)                                (0x18000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相电流
#define C18_1kW(ph,N)                               (0x18000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相有功功率
#define C18_1kvar(ph,N)                             (0x18000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相无功功率
#define C18_1pf(ph,N)                               (0x18000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 A 相功率因数
#define C18_2pkWh(ph,N)                             (0x18000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相正向有功电能
#define C18_2nkWh(ph,N)                             (0x18001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相反向有功电能
#define C18_2pkvarh(ph,N)                           (0x18001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相组合无功 1 电能
#define C18_2nkvarh(ph,N)                           (0x18001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相组合无功 2 电能
#define C18_2v(ph,N)                                (0x18001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相电压
#define C18_2i(ph,N)                                (0x18001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相电流
#define C18_2kW(ph,N)                               (0x18001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相有功功率
#define C18_2kvar(ph,N)                             (0x18001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相无功功率
#define C18_2pf(ph,N)                               (0x18001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 B 相功率因数
#define C18_3pkWh(ph,N)                             (0x18001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相正向有功电能
#define C18_3nkWh(ph,N)                             (0x18001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相反向有功电能
#define C18_3pkvarh(ph,N)                           (0x18001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相组合无功 1 电能
#define C18_3nkvarh(ph,N)                           (0x18001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相组合无功 2 电能
#define C18_3v(ph,N)                                (0x18001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相电压
#define C18_3i(ph,N)                                (0x18001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相电流
#define C18_3kW(ph,N)                               (0x18001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相有功功率
#define C18_3kvar(ph,N)                             (0x18001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相无功功率
#define C18_3pf(ph,N)                               (0x18002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流发生时刻 C 相功率因数
#define C18_t_e(ph,N)                               (0x18002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻
#define C18_0pkWh_e(ph,N)                           (0x18002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻正向有功总电能
#define C18_0nkWh_e(ph,N)                           (0x18002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻反向有功总电能
#define C18_0pkvarh_e(ph,N)                         (0x18002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻组合无功 1 总电能
#define C18_0nkvarh_e(ph,N)                         (0x18002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻组合无功 2 总电能
#define C18_1pkWh_e(ph,N)                           (0x18002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 A 相正向有功电能
#define C18_1nkWh_e(ph,N)                           (0x18002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 A 相反向有功电能
#define C18_1pkvarh_e(ph,N)                         (0x18002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 A 相组合无功 1 电能
#define C18_1nkvarh_e(ph,N)                         (0x18002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 A 相组合无功 2 电能
#define C18_2pkWh_e(ph,N)                           (0x18002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 B 相正向有功电能
#define C18_2nkWh_e(ph,N)                           (0x18002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 B 相反向有功电能
#define C18_2pkvarh_e(ph,N)                         (0x18002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 B 相组合无功 1 电能
#define C18_2nkvarh_e(ph,N)                         (0x18002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 B 相组合无功 2 电能
#define C18_3pkWh_e(ph,N)                           (0x18002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 C 相正向有功电能
#define C18_3nkWh_e(ph,N)                           (0x18002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 C 相反向有功电能
#define C18_3pkvarh_e(ph,N)                         (0x18003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 C 相组合无功 1 电能
#define C18_3nkvarh_e(ph,N)                         (0x18003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流结束时刻 C 相组合无功 2 电能
#define C18_ALL(ph,N)                               (0x1800FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相失流数据块


#define C19_OVR_CUR_A_CNT                           0x19010001                                    /// A相过流总次数
#define C19_OVR_CUR_A_TMR                           0x19010002                                    /// A相过流总累计时间
#define C19_OVR_CUR_B_CNT                           0x19020001                                    /// B相过流总次数
#define C19_OVR_CUR_B_TMR                           0x19020002                                    /// B相过流总累计时间
#define C19_OVR_CUR_C_CNT                           0x19030001                                    /// C相过流总次数
#define C19_OVR_CUR_C_TMR                           0x19030002                                    /// C相过流总累计时间

#define C19_t_s(ph,N)                               (0x19000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻
#define C19_0pkWh(ph,N)                             (0x19000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻正向有功总电能
#define C19_0nkWh(ph,N)                             (0x19000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻反向有功总电能
#define C19_0pkvarh(ph,N)                           (0x19000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻组合无功 1 总电能
#define C19_0nkvarh(ph,N)                           (0x19000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻组合无功 2 总电能
#define C19_1pkWh(ph,N)                             (0x19000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相正向有功电能
#define C19_1nkWh(ph,N)                             (0x19000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相反向有功电能
#define C19_1pkvarh(ph,N)                           (0x19000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相组合无功 1 电能
#define C19_1nkvarh(ph,N)                           (0x19000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相组合无功 2 电能
#define C19_1v(ph,N)                                (0x19000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相电压
#define C19_1i(ph,N)                                (0x19000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相电流
#define C19_1kW(ph,N)                               (0x19000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相有功功率
#define C19_1kvar(ph,N)                             (0x19000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相无功功率
#define C19_1pf(ph,N)                               (0x19000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 A 相功率因数
#define C19_2pkWh(ph,N)                             (0x19000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相正向有功电能
#define C19_2nkWh(ph,N)                             (0x19001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相反向有功电能
#define C19_2pkvarh(ph,N)                           (0x19001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相组合无功 1 电能
#define C19_2nkvarh(ph,N)                           (0x19001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相组合无功 2 电能
#define C19_2v(ph,N)                                (0x19001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相电压
#define C19_2i(ph,N)                                (0x19001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相电流
#define C19_2kW(ph,N)                               (0x19001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相有功功率
#define C19_2kvar(ph,N)                             (0x19001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相无功功率
#define C19_2pf(ph,N)                               (0x19001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 B 相功率因数
#define C19_3pkWh(ph,N)                             (0x19001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相正向有功电能
#define C19_3nkWh(ph,N)                             (0x19001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相反向有功电能
#define C19_3pkvarh(ph,N)                           (0x19001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相组合无功 1 电能
#define C19_3nkvarh(ph,N)                           (0x19001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相组合无功 2 电能
#define C19_3v(ph,N)                                (0x19001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相电压
#define C19_3i(ph,N)                                (0x19001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相电流
#define C19_3kW(ph,N)                               (0x19001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相有功功率
#define C19_3kvar(ph,N)                             (0x19001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相无功功率
#define C19_3pf(ph,N)                               (0x19002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流发生时刻 C 相功率因数
#define C19_t_e(ph,N)                               (0x19002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻
#define C19_0pkWh_e(ph,N)                           (0x19002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻正向有功总电能
#define C19_0nkWh_e(ph,N)                           (0x19002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻反向有功总电能
#define C19_0pkvarh_e(ph,N)                         (0x19002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻组合无功 1 总电能
#define C19_0nkvarh_e(ph,N)                         (0x19002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻组合无功 2 总电能
#define C19_1pkWh_e(ph,N)                           (0x19002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 A 相正向有功电能
#define C19_1nkWh_e(ph,N)                           (0x19002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 A 相反向有功电能
#define C19_1pkvarh_e(ph,N)                         (0x19002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 A 相组合无功 1 电能
#define C19_1nkvarh_e(ph,N)                         (0x19002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 A 相组合无功 2 电能
#define C19_2pkWh_e(ph,N)                           (0x19002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 B 相正向有功电能
#define C19_2nkWh_e(ph,N)                           (0x19002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 B 相反向有功电能
#define C19_2pkvarh_e(ph,N)                         (0x19002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 B 相组合无功 1 电能
#define C19_2nkvarh_e(ph,N)                         (0x19002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 B 相组合无功 2 电能
#define C19_3pkWh_e(ph,N)                           (0x19002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 C 相正向有功电能
#define C19_3nkWh_e(ph,N)                           (0x19002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 C 相反向有功电能
#define C19_3pkvarh_e(ph,N)                         (0x19003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 C 相组合无功 1 电能
#define C19_3nkvarh_e(ph,N)                         (0x19003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流结束时刻 C 相组合无功 2 电能
#define C19_ALL(ph,N)                               (0x1900FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相过流数据块

#define C1A_MIS_VOL_A_CNT                           0x1A010001                                    /// A相断流总次数
#define C1A_MIS_VOL_A_TMR                           0x1A010002                                    /// A相断流总累计时间
#define C1A_MIS_VOL_B_CNT                           0x1A020001                                    /// B相断流总次数
#define C1A_MIS_VOL_B_TMR                           0x1A020002                                    /// B相断流总累计时间
#define C1A_MIS_VOL_C_CNT                           0x1A030001                                    /// C相断流总次数
#define C1A_MIS_VOL_C_TMR                           0x1A030002                                    /// C相断流总累计时间

#define C1A_t_s(ph,N)                               (0x1A000100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻
#define C1A_0pkWh(ph,N)                             (0x1A000200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻正向有功总电能
#define C1A_0nkWh(ph,N)                             (0x1A000300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻反向有功总电能
#define C1A_0pkvarh(ph,N)                           (0x1A000400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻组合无功 1 总电能
#define C1A_0nkvarh(ph,N)                           (0x1A000500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻组合无功 2 总电能
#define C1A_1pkWh(ph,N)                             (0x1A000600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相正向有功电能
#define C1A_1nkWh(ph,N)                             (0x1A000700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相反向有功电能
#define C1A_1pkvarh(ph,N)                           (0x1A000800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相组合无功 1 电能
#define C1A_1nkvarh(ph,N)                           (0x1A000900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相组合无功 2 电能
#define C1A_1v(ph,N)                                (0x1A000A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相电压
#define C1A_1i(ph,N)                                (0x1A000B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相电流
#define C1A_1kW(ph,N)                               (0x1A000C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相有功功率
#define C1A_1kvar(ph,N)                             (0x1A000D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相无功功率
#define C1A_1pf(ph,N)                               (0x1A000E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 A 相功率因数
#define C1A_2pkWh(ph,N)                             (0x1A000F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相正向有功电能
#define C1A_2nkWh(ph,N)                             (0x1A001000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相反向有功电能
#define C1A_2pkvarh(ph,N)                           (0x1A001100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相组合无功 1 电能
#define C1A_2nkvarh(ph,N)                           (0x1A001200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相组合无功 2 电能
#define C1A_2v(ph,N)                                (0x1A001300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相电压
#define C1A_2i(ph,N)                                (0x1A001400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相电流
#define C1A_2kW(ph,N)                               (0x1A001500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相有功功率
#define C1A_2kvar(ph,N)                             (0x1A001600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相无功功率
#define C1A_2pf(ph,N)                               (0x1A001700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 B 相功率因数
#define C1A_3pkWh(ph,N)                             (0x1A001800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相正向有功电能
#define C1A_3nkWh(ph,N)                             (0x1A001900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相反向有功电能
#define C1A_3pkvarh(ph,N)                           (0x1A001A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相组合无功 1 电能
#define C1A_3nkvarh(ph,N)                           (0x1A001B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相组合无功 2 电能
#define C1A_3v(ph,N)                                (0x1A001C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相电压
#define C1A_3i(ph,N)                                (0x1A001D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相电流
#define C1A_3kW(ph,N)                               (0x1A001E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相有功功率
#define C1A_3kvar(ph,N)                             (0x1A001F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相无功功率
#define C1A_3pf(ph,N)                               (0x1A002000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流发生时刻 C 相功率因数
#define C1A_t_e(ph,N)                               (0x1A002100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻
#define C1A_0pkWh_e(ph,N)                           (0x1A002200 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻正向有功总电能
#define C1A_0nkWh_e(ph,N)                           (0x1A002300 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻反向有功总电能
#define C1A_0pkvarh_e(ph,N)                         (0x1A002400 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻组合无功 1 总电能
#define C1A_0nkvarh_e(ph,N)                         (0x1A002500 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻组合无功 2 总电能
#define C1A_1pkWh_e(ph,N)                           (0x1A002600 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 A 相正向有功电能
#define C1A_1nkWh_e(ph,N)                           (0x1A002700 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 A 相反向有功电能
#define C1A_1pkvarh_e(ph,N)                         (0x1A002800 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 A 相组合无功 1 电能
#define C1A_1nkvarh_e(ph,N)                         (0x1A002900 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 A 相组合无功 2 电能
#define C1A_2pkWh_e(ph,N)                           (0x1A002A00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 B 相正向有功电能
#define C1A_2nkWh_e(ph,N)                           (0x1A002B00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 B 相反向有功电能
#define C1A_2pkvarh_e(ph,N)                         (0x1A002C00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 B 相组合无功 1 电能
#define C1A_2nkvarh_e(ph,N)                         (0x1A002D00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 B 相组合无功 2 电能
#define C1A_3pkWh_e(ph,N)                           (0x1A002E00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 C 相正向有功电能
#define C1A_3nkWh_e(ph,N)                           (0x1A002F00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 C 相反向有功电能
#define C1A_3pkvarh_e(ph,N)                         (0x1A003000 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 C 相组合无功 1 电能
#define C1A_3nkvarh_e(ph,N)                         (0x1A003100 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流结束时刻 C 相组合无功 2 电能
#define C1A_ALL(ph,N)                               (0x1A00FF00 | PH_N_INDEX(ph,N))               /// （上 1-10 次）A B C 相断流数据块


// 集社扩展数据标识编码表
#define C73_A_LOSS_VOL_RECORD                       0x73010102                                 /// A相失压记录 buff
#define C73_B_LOSS_VOL_RECORD                       0x73010202                                 /// B相失压记录 buff
#define C73_C_LOSS_VOL_RECORD                       0x73010302                                 /// C相失压记录 buff
#define C73_A_LOW_VOL_RECORD                        0x73020102                                 /// A相欠压记录 buff
#define C73_B_LOW_VOL_RECORD                        0x73020202                                 /// B相欠压记录 buff
#define C73_C_LOW_VOL_RECORD                        0x73020302                                 /// C相欠压记录 buff
#define C73_A_OVR_VOL_RECORD                        0x73030102                                 /// A相过压记录 buff
#define C73_B_OVR_VOL_RECORD                        0x73030202                                 /// B相过压记录 buff
#define C73_C_OVR_VOL_RECORD                        0x73030302                                 /// C相过压记录 buff
#define C73_A_MISS_VOL_RECORD                       0x73040102                                 /// A相断相记录 buff
#define C73_B_MISS_VOL_RECORD                       0x73040202                                 /// B相断相记录 buff
#define C73_C_MISS_VOL_RECORD                       0x73040302                                 /// C相断相记录 buff
#define C73_ALL_LOSS_VOL_RECORD                     0x73050002                                 /// 全失压记录 buff
#define C73_BAK_PWR_LOS_RECORD                      0x73060002                                 /// 辅助电源失电记录 buff
#define C73_V_REV_SQR_RECORD                        0x73070002                                 /// 电压逆向序记录 buff
#define C73_I_REV_SQR_RECORD                        0x73080002                                 /// 电流逆向序记录 buff
#define C73_V_UNB_RECORD                            0x73090002                                 /// 电压不平衡记录 buff
#define C73_I_UNB_RECORD                            0x730A0002                                 /// 电流不平衡记录 buff
#define C73_A_LOS_CUR_RECORD                        0x730B0102                                 /// A失流相记录 buff
#define C73_B_LOS_CUR_RECORD                        0x730B0202                                 /// B失流相记录 buff
#define C73_C_LOS_CUR_RECORD                        0x730B0302                                 /// C失流相记录 buff
#define C73_A_OVR_CUR_RECORD                        0x730C0102                                 /// A过流相记录 buff
#define C73_B_OVR_CUR_RECORD                        0x730C0202                                 /// B过流相记录 buff
#define C73_C_OVR_CUR_RECORD                        0x730C0302                                 /// C过流相记录 buff
#define C73_A_MISS_CUR_RECORD                       0x730D0102                                 /// A相断流相记录 buff
#define C73_B_MISS_CUR_RECORD                       0x730D0202                                 /// B相断流相记录 buff
#define C73_C_MISS_CUR_RECORD                       0x730D0302                                 /// C相断流相记录 buff
#define C73_A_REV_RECORD                            0x730E0102                                 /// A相潮流反向记录 buff
#define C73_B_REV_RECORD                            0x730E0202                                 /// B相潮流反向记录 buff
#define C73_C_REV_RECORD                            0x730E0302                                 /// C相潮流反向记录 buff
#define C73_A_OVR_LOAD_RECORD                       0x730F0102                                 /// A相过载记录 buff
#define C73_B_OVR_LOAD_RECORD                       0x730F0202                                 /// B相过载记录 buff
#define C73_C_OVR_LOAD_RECORD                       0x730F0302                                 /// C相过载记录 buff
#define C73_PWR_DOWN_RECORD                         0x73110002                                 /// 掉电记录 buff
#define C73_OVR_DM_POS_kW_RECORD                    0x73120102                                 /// 正向   有功需量超限记录 buff
#define C73_OVR_DM_NEG_kW_RECORD                    0x73120202                                 /// 反向   有功需量超限记录 buff
#define C73_OVR_DM_Q1_kvar_RECORD                   0x73120302                                 /// 第1象限无功需量超限记录 buff
#define C73_OVR_DM_Q2_kvar_RECORD                   0x73120402                                 /// 第2象限无功需量超限记录 buff
#define C73_OVR_DM_Q3_kvar_RECORD                   0x73120502                                 /// 第3象限无功需量超限记录 buff
#define C73_OVR_DM_Q4_kvar_RECORD                   0x73120602                                 /// 第4象限无功需量超限记录 buff
#define C73_PROGRAM_RECORD                          0x73300002                                 /// 编程记录 buff
#define C73_METER_CLEAN_RECORD                      0x73300102                                 /// 电表清零记录 buff
#define C73_DEMAND_CLEAN_RECORD                     0x73300202                                 /// 需量清零记录 buff
#define C73_EVENT_CLEAN_RECORD                      0x73300302                                 /// 事件清零记录 buff
#define C73_SHITFT_TIME_RECORD                      0x73300402                                 /// 校时记录 buff
#define C73_SCHEDULE_RECORD                         0x73300502                                 /// 时段表记录 buff
#define C73_ZONE_TAB_RECORD                         0x73300602                                 /// 时区表记录 buff
#define C73_WEEKENDS_PGM_RECORD                     0x73300702                                 /// 周休日编程记录 buff
#define C73_HOLIDAY_PGM_RECORD                      0x73300802                                 /// 节假日表编程记录 buff
#define C73_COMB_kWh_PGM_RECORD                     0x73300902                                 /// 有功组合方式编程记录 buff
#define C73_COMB1_kvarh_PGM_RECORD                  0x73300A02                                 /// 无功组合方式1编程记录 buff
#define C73_COMB2_kvarh_PGM_RECORD                  0x73300B02                                 /// 无功组合方式2编程记录 buff
#define C73_BL_DAY_PGM_RECORD                       0x73300C02                                 /// 结算日编程记录 buff
#define C73_METER_COVER_RECORD                      0x73300D02                                 /// 开表盖记录 buff
#define C73_TEM_COVER_RECORD                        0x73300E02                                 /// 开端盖记录 buff
#define C73_BC_TIME_RECORD                          0x73300F02                                 /// 广播校时记录 buff

// 事件结束标识编码表，与C73一一对应
#define C83_A_LOSS_VOL_RECORD                       0x83010102                                 /// A相失压记录 buff
#define C83_B_LOSS_VOL_RECORD                       0x83010202                                 /// B相失压记录 buff
#define C83_C_LOSS_VOL_RECORD                       0x83010302                                 /// C相失压记录 buff
#define C83_A_LOW_VOL_RECORD                        0x83020102                                 /// A相欠压记录 buff
#define C83_B_LOW_VOL_RECORD                        0x83020202                                 /// B相欠压记录 buff
#define C83_C_LOW_VOL_RECORD                        0x83020302                                 /// C相欠压记录 buff
#define C83_A_OVR_VOL_RECORD                        0x83030102                                 /// A相过压记录 buff
#define C83_B_OVR_VOL_RECORD                        0x83030202                                 /// B相过压记录 buff
#define C83_C_OVR_VOL_RECORD                        0x83030302                                 /// C相过压记录 buff
#define C83_A_MISS_VOL_RECORD                       0x83040102                                 /// A相断相记录 buff
#define C83_B_MISS_VOL_RECORD                       0x83040202                                 /// B相断相记录 buff
#define C83_C_MISS_VOL_RECORD                       0x83040302                                 /// C相断相记录 buff
#define C83_ALL_LOSS_VOL_RECORD                     0x83050002                                 /// 全失压记录 buff
#define C83_BAK_PWR_LOS_RECORD                      0x83060002                                 /// 辅助电源失电记录 buff
#define C83_V_REV_SQR_RECORD                        0x83070002                                 /// 电压逆向序记录 buff
#define C83_I_REV_SQR_RECORD                        0x83080002                                 /// 电流逆向序记录 buff
#define C83_V_UNB_RECORD                            0x83090002                                 /// 电压不平衡记录 buff
#define C83_I_UNB_SQR_RECORD                        0x830A0002                                 /// 电流不平衡记录 buff
#define C83_A_LOS_CUR_RECORD                        0x830B0102                                 /// A失流相记录 buff
#define C83_B_LOS_CUR_RECORD                        0x830B0202                                 /// B失流相记录 buff
#define C83_C_LOS_CUR_RECORD                        0x830B0302                                 /// C失流相记录 buff
#define C83_A_OVR_CUR_RECORD                        0x830C0102                                 /// A过流相记录 buff
#define C83_B_OVR_CUR_RECORD                        0x830C0202                                 /// B过流相记录 buff
#define C83_C_OVR_CUR_RECORD                        0x830C0302                                 /// C过流相记录 buff
#define C83_A_MISS_CUR_RECORD                       0x830D0102                                 /// A相断流相记录 buff
#define C83_B_MISS_CUR_RECORD                       0x830D0202                                 /// B相断流相记录 buff
#define C83_C_MISS_CUR_RECORD                       0x830D0302                                 /// C相断流相记录 buff
#define C83_A_REV_RECORD                            0x830E0102                                 /// A相潮流反向记录 buff
#define C83_B_REV_RECORD                            0x830E0202                                 /// B相潮流反向记录 buff
#define C83_C_REV_RECORD                            0x830E0302                                 /// C相潮流反向记录 buff
#define C83_A_OVR_LOAD_RECORD                       0x830F0102                                 /// A相过载记录 buff
#define C83_B_OVR_LOAD_RECORD                       0x830F0202                                 /// B相过载记录 buff
#define C83_C_OVR_LOAD_RECORD                       0x830F0302                                 /// C相过载记录 buff
#define C83_PWR_DOWN_RECORD                         0x83110002                                 /// 掉电记录 buff
#define C83_OVR_DM_POS_kW_RECORD                    0x83120102                                 /// 正向   有功需量超限记录 buff
#define C83_OVR_DM_NEG_kW_RECORD                    0x83120202                                 /// 反向   有功需量超限记录 buff
#define C83_OVR_DM_Q1_kvar_RECORD                   0x83120302                                 /// 第1象限无功需量超限记录 buff
#define C83_OVR_DM_Q2_kvar_RECORD                   0x83120402                                 /// 第2象限无功需量超限记录 buff
#define C83_OVR_DM_Q3_kvar_RECORD                   0x83120502                                 /// 第3象限无功需量超限记录 buff
#define C83_OVR_DM_Q4_kvar_RECORD                   0x83120602                                 /// 第4象限无功需量超限记录 buff
#define C83_PROGRAM_RECORD                          0x83300002                                 /// 编程记录 buff
#define C83_METER_CLEAN_RECORD                      0x83300102                                 /// 电表清零记录 buff
#define C83_DEMAND_CLEAN_RECORD                     0x83300202                                 /// 需量清零记录 buff
#define C83_EVENT_CLEAN_RECORD                      0x83300302                                 /// 事件清零记录 buff
#define C83_SHITFT_TIME_RECORD                      0x83300402                                 /// 校时记录 buff
#define C83_SCHEDULE_RECORD                         0x83300502                                 /// 时段表记录 buff
#define C83_ZONE_TAB_RECORD                         0x83300602                                 /// 时区表记录 buff
#define C83_WEEKENDS_PGM_RECORD                     0x83300702                                 /// 周休日编程记录 buff
#define C83_HOLIDAY_PGM_RECORD                      0x83300802                                 /// 节假日表编程记录 buff
#define C83_COMB_kWh_PGM_RECORD                     0x83300902                                 /// 有功组合方式编程记录 buff
#define C83_COMB1_kvarh_PGM_RECORD                  0x83300A02                                 /// 无功组合方式1编程记录 buff
#define C83_COMB2_kvarh_PGM_RECORD                  0x83300B02                                 /// 无功组合方式2编程记录 buff
#define C83_BL_DAY_PGM_RECORD                       0x83300C02                                 /// 结算日编程记录 buff
#define C83_METER_COVER_RECORD                      0x83300D02                                 /// 开表盖记录 buff
#define C83_TEM_COVER_RECORD                        0x83300E02                                 /// 开端盖记录 buff

//
#define C94_METER_COV_CLOSE_TIME                    0x94010102                                 /// 表盖关闭时间
#define C94_TEM_COV_CLOSE_TIME                      0x94010202                                 /// 端盖关闭时间
#define C94_LAST_POWER_OFF_TIME                     0x94010302                                 /// 上次断电时间
#define C94_LAST_POWER_ON_TIME                      0x94010402                                 /// 上次开机时间
#define C94_LAST_ADJUST_TIME                        0x94010502                                 /// 上次校时时间
#define C94_LAST_BC_TIME                            0x94010602                                 /// 上次广播校时时间

#define C94_OPT_CODE                                0x94020102                                 /// 操作者代码


#define CA0_AH_T                                    0xA0000000                                 /// 总安时
#define CA0_AH_A                                    0xA0000100                                 /// A相安时
#define CA0_AH_B                                    0xA0000200                                 /// B相安时
#define CA0_AH_C                                    0xA0000300                                 /// C相安时

// 高精度电能，4位小数点
#define CA1_CMB_kWh(D,T)                             (0xA1000000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合有功总       电能(kWh)
#define CA1_POS_kWh(D,T)                             (0xA1010000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 正向有功总       电能(kWh)
#define CA1_NEG_kWh(D,T)                             (0xA1020000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 反向有功总       电能(kWh)
#define CA1_CMB1_kvarh(D,T)                          (0xA1030000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功1总      电能(kvarh)
#define CA1_CMB2_kvarh(D,T)                          (0xA1040000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) 组合无功2总      电能(kvarh)
#define CA1_Q1_kvarh(D,T)                            (0xA1050000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q1无功总         电能 (kvarh)
#define CA1_Q2_kvarh(D,T)                            (0xA1060000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q2无功总         电能 (kvarh)
#define CA1_Q3_kvarh(D,T)                            (0xA1070000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q3无功总         电能 (kvarh)
#define CA1_Q4_kvarh(D,T)                            (0xA1080000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) Q4无功总         电能 (kvarh)
#define CA1_A_POS_kWh(D,T)                           (0xA1150000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相正向有功总    电能(kWh)
#define CA1_A_NEG_kWh(D,T)                           (0xA1160000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相反向有功总    电能(kWh)
#define CA1_A_CMB1_kvarh(D,T)                        (0xA1170000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功1总   电能(kvarh)
#define CA1_A_CMB2_kvarh(D,T)                        (0xA1180000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相组合无功2总   电能(kvarh)
#define CA1_A_Q1_kvarh(D,T)                          (0xA1190000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q1无功总      电能 (kvarh)
#define CA1_A_Q2_kvarh(D,T)                          (0xA11A0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q2无功总      电能 (kvarh)
#define CA1_A_Q3_kvarh(D,T)                          (0xA11B0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q3无功总      电能 (kvarh)
#define CA1_A_Q4_kvarh(D,T)                          (0xA11C0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) A相Q4无功总      电能 (kvarh)
#define CA1_B_POS_kWh(D,T)                           (0xA1290000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相正向有功总    电能(kWh)
#define CA1_B_NEG_kWh(D,T)                           (0xA12A0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相反向有功总    电能(kWh)
#define CA1_B_CMB1_kvarh(D,T)                        (0xA12B0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功1总   电能(kvarh)
#define CA1_B_CMB2_kvarh(D,T)                        (0xA12C0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相组合无功2总   电能(kvarh)
#define CA1_B_Q1_kvarh(D,T)                          (0xA12D0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q1无功总      电能 (kvarh)
#define CA1_B_Q2_kvarh(D,T)                          (0xA12E0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q2无功总      电能 (kvarh)
#define CA1_B_Q3_kvarh(D,T)                          (0xA12F0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q3无功总      电能 (kvarh)
#define CA1_B_Q4_kvarh(D,T)                          (0xA1300000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) B相Q4无功总      电能 (kvarh)
#define CA1_C_POS_kWh(D,T)                           (0xA13D0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相正向有功总    电能(kWh)
#define CA1_C_NEG_kWh(D,T)                           (0xA13E0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相反向有功总    电能(kWh)
#define CA1_C_CMB1_kvarh(D,T)                        (0xA13F0000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功1总   电能(kvarh)
#define CA1_C_CMB2_kvarh(D,T)                        (0xA1400000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相组合无功2总   电能(kvarh)
#define CA1_C_Q1_kvarh(D,T)                          (0xA1410000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q1无功总      电能 (kvarh)
#define CA1_C_Q2_kvarh(D,T)                          (0xA1420000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q2无功总      电能 (kvarh)
#define CA1_C_Q3_kvarh(D,T)                          (0xA1430000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q3无功总      电能 (kvarh)
#define CA1_C_Q4_kvarh(D,T)                          (0xA1440000 | DAY_TARIFF(D,T))            /// 上D结算日(0为当前，FF为全部)费率T(0为总, FF全部) C相Q4无功总      电能 (kvarh)


#define ID_LCD_ALL                                  0xFF010001                                 /// 液晶全显
#define ID_LCD_NONE                                 0xFF010002                                 /// 不显示

#define MENU_NO(num)   ((uint32_t)(num))
#define ID_LCD_MENU(num)                            (0xFF020000 | MENU_NO(num))                /// 液晶菜单显示内容                            



#define ITEM(id)    (uint16_t)(id >> 8) 

#endif /* _DLT645_2007_H */



