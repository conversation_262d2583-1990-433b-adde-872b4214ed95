/**
 ******************************************************************************
 * @file    hal_gpio.c
 * <AUTHOR> @version V1.0.0
 * @date    2024
 * @brief   本模块完成MCU GPIO的初始化过程.
 * @note    中断中操作GPIO只能用PTCLR，PTSET方式，不能操作其它寄存器！！！！！
 *          如果MCU没有PTCLR\PTSET类似寄存器操作IO口输出，应当使用bit band 方式操作GPIO输出。！！！！！！
 *
 ******************************************************************************
 *
 *
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_gpio.h"
#include "../MCU/RN8211B-V3/driver/inc/rn8xxx_ll_gpio.h"
#include "../MCU/RN8211B-V3/common/rn821x_rn721x_soc_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* 数字IO口功能定义 */
#define GPIO_Mode_AFx 0x08                // GPIO复用功能被使用
#define GPIO_Mode_IN 0x00                 // GPIO输入
#define GPIO_Mode_OUT 0x01                // GPIO输出
#define GPIO_Mode_ANin_0 (0x00 | 0x08)    // 模拟端口
#define GPIO_Mode_AFin_1 (0x10 | 0x08)    // 复用功能1 输入
#define GPIO_Mode_AFin_2 (0x20 | 0x08)    // 复用功能2 输入
#define GPIO_Mode_AFin_3 (0x30 | 0x08)    // 复用功能3 输入
#define GPIO_Mode_AFin_4 (0x40 | 0x08)    // 复用功能4 输入
#define GPIO_Mode_AFin_5 (0x50 | 0x08)    // 复用功能5 输入
#define GPIO_Mode_AFin_6 (0x60 | 0x08)    // 复用功能6 输入
#define GPIO_Mode_AFin_7 (0x70 | 0x08)    // 复用功能7 输入

#define GPIO_Mode_AFo_1 (0x11 | 0x08)    // 复用功能1 输出
#define GPIO_Mode_AFo_2 (0x21 | 0x08)    // 复用功能2 输出
#define GPIO_Mode_AFo_3 (0x31 | 0x08)    // 复用功能3 输出
#define GPIO_Mode_AFo_4 (0x41 | 0x08)    // 复用功能4 输出
#define GPIO_Mode_AFo_5 (0x51 | 0x08)    // 复用功能5 输出
#define GPIO_Mode_AFo_6 (0x61 | 0x08)    // 复用功能6 输出
#define GPIO_Mode_AFo_7 (0x71 | 0x08)    // 复用功能7 输出

#define GPIO_In_Floating 0    // 输入浮空
#define GPIO_In_Up 1          // 输入上拉
#define GPIO_In_Down 2        // 输入下拉

#define PULL_UP 0x80
#define PULL_DOWN 0x40
#define GPIO_Out_PE 0                   // 输出推挽 禁止、下拉
#define GPIO_Out_PP (0 | PULL_UP)       // 输出推挽 上拉
#define GPIO_Out_PD (0 | PULL_DOWN)     // 输出推挽 下拉
#define GPIO_Out_ODE 1                  // 输出开漏 禁止、下拉
#define GPIO_Out_ODP (1 | PULL_UP)      // 输出开漏 上拉
#define GPIO_Out_ODD (1 | PULL_DOWN)    // 输出开漏 下拉

#define GPIO_Output_H 1    // 输出高电平
#define GPIO_Output_L 0    // 输出低电平
#define GPIO_OD_DIS 1      // 关闭开漏
#define GPIO_OD_EN 0       //
/// @brief 这里列出有唤醒(外部)中断功能的GPIO
#define EXTI_RIE 0x0001     // 上升沿中断
#define EXTI_FIE 0x0002     // 下升沿中断
#define EXTI_RFIE 0x0003    // 双边沿中断

void hal_gpio_exti_set(uint8_t irq, void func(void));
void irq_handler_extiA(void);
void irq_handler_extiB(void);
void irq_handler_extiC(void);
void irq_handler_extiD(void);
void irq_handler_extiE(void);
void irq_handler_extiF(void);

/**
 * @brief  配置MCU GPIO功能类型. 利用宏函数定义形式，编译器会自动优化无效内容。另注意效率，复位过的状态则无须配置
 * @param  [in]  x-指定的GPIO
 * @param  [in]  m-GPIO_Mode_IOIN      // 输入
 *                 GPIO_Mode_IOOUT     // 输出
 *                 GPIO_Mode_ANin_0    // 模拟端口
 *                 GPIO_Mode_AFin_1    // 复用功能1 输入
 *                 GPIO_Mode_AFin_2    // 复用功能2 输入
 *                 GPIO_Mode_AFin_3    // 复用功能3 输入
 *                 GPIO_Mode_AFin_4    // 复用功能4 输入
 *                 GPIO_Mode_AFin_5    // 复用功能5 输入
 *                 GPIO_Mode_AFin_6    // 复用功能6 输入
 *                 GPIO_Mode_AFin_7    // 复用功能7 输入
 *                 GPIO_Mode_AFo_1     // 复用功能1 输出
 *                 GPIO_Mode_AFo_2     // 复用功能2 输出
 *                 GPIO_Mode_AFo_3     // 复用功能3 输出
 *                 GPIO_Mode_AFo_4     // 复用功能4 输出
 *                 GPIO_Mode_AFo_5     // 复用功能5 输出
 *                 GPIO_Mode_AFo_6     // 复用功能6 输出
 *                 GPIO_Mode_AFo_7     // 复用功能7 输出
 *
 * @param  [in]  p-GPIO_In_Floating    // 输入浮空 --- 设置输入时有效
 *                 GPIO_Input_Up       // 输入上拉
 *                 GPIO_Input_PD       // 输入下拉
 *
 *                 GPIO_Out_PE         // 输出推挽 禁止、下拉 --- 设置输出时有效
 *                 GPIO_Out_PP         // 输出推挽 上拉
 *                 GPIO_Out_PD         // 输出推挽 下拉
 *                 GPIO_Out_ODE        // 输出开漏 禁止、下拉
 *                 GPIO_Out_ODP        // 输出开漏 上拉
 *                 GPIO_Out_ODD        // 输出开漏 下拉
 *
 * @param  [in]  l-GPIO_Output_H       // 输出高电平 --- 设置输出时有效
 *                 GPIO_Output_L       // 输出低电平
 *
 *                 GPIO_OD_DIS  // 关闭开漏--- 设置输入时有效
 *
 * HC32L19x GPIO寄存器默认值
 * PxDIR = 0xFFFFFFFF; // 所有GPIO口默认配置为输入
 * PxADS = 0x00000000; // 所有GPIO口默认配置为数字端口
 * PxPU = 0x00000000;  // 所有GPIO口默认配置为禁止上拉
 * PxPD = 0x00000000;  // 所有GPIO口默认配置为禁止下拉
 * PxDR = 0x00000000;  // 所有GPIO口默认配置为高驱动
 * PxOD = 0x00000000;  // 所有GPIO口默认配置为禁止开漏
 * PxSEL = 0x00000000; // 所有GPIO口默认配置为GPIO模式
 */
// #define _halGpioConfig(port, pin, m, p, l)                                                                        \
//     {                                                                                                             \
//         if(m == GPIO_Mode_ANin_0) { port->PAADS |= (1UL << (pin)); }                                              \
//         else { port->PAADS &= ~(1UL << (pin)); }                                                                  \
//         if(m & GPIO_Mode_AFx) { *((uint32_t *)((uint32_t)(&(port->PA00_SEL)) + (pin) * 4)) = ((m & 0xF0) >> 4); } \
//         if((m & 0x01) == GPIO_Mode_IN)                                                                            \
//         {                                                                                                         \
//             port->PADIR |= (1UL << (pin));                                                                        \
//             if(p == GPIO_In_Up) { port->PAPU |= (1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                  \
//             else if(p == GPIO_In_Down) { port->PAPU &= ~(1UL << (pin)), port->PAPD |= (1UL << (pin)); }           \
//             else { port->PAPU &= ~(1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                                \
//             if(l) { port->PAOD &= ~(1UL << (pin)); }                                                              \
//             else { port->PAOD |= (1UL << (pin)); }                                                                \
//         }                                                                                                         \
//         else                                                                                                      \
//         {                                                                                                         \
//             port->PADIR &= ~(1UL << (pin));                                                                       \
//             if(p & 0x01) { port->PAOD |= (1UL << (pin)); }                                                        \
//             else { port->PAOD &= ~(1UL << (pin)); }                                                               \
//             if((p & PULL_UP)) { port->PAPU |= (1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                    \
//             else if((p & PULL_DOWN)) { port->PAPU &= ~(1UL << (pin)), port->PAPD |= (1UL << (pin)); }             \
//             else { port->PAPU &= ~(1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                                \
//             if((uint32_t)l == GPIO_Output_H) { port->PAOUT |= (1UL << (pin)); }                                   \
//             else { port->PAOUT &= ~(1UL << (pin)); }                                                              \
//         }                                                                                                         \
//     }

void _halGpioConfig(uint8_t pin, eGPIOFunction_TypeDef af, eGPIOOutputLevel_TypeDef out, eGPIOPull_TypeDef pull, eGPIODir_TypeDef dir, uint8_t mode)
{
    GPIO_InitTypeDef GPIO_InitStruct;
    memset(&GPIO_InitStruct, 0, sizeof(GPIO_InitStruct));

    GPIO_InitStruct.Pin         = pin;
    GPIO_InitStruct.Mode        = af;
    GPIO_InitStruct.OutputLevel = out;
    GPIO_InitStruct.Pull        = pull;
    GPIO_InitStruct.Dir         = dir;
    (dir == GPIO_MODE_OUT) ? (GPIO_InitStruct.OutputMode = (eGPIOOutputMode_TypeDef)mode) : (GPIO_InitStruct.InputMode = (eGPIOInputMode_TypeDef)mode);

    LL_GPIO_Init(&GPIO_InitStruct);
}

#define gpio_config(pin, af, out, pull, dir, mode) _halGpioConfig(pin, af, out, pull, dir, (uint8_t)mode)

// /// @brief 配置外部中断. 利用宏函数定义形，编译器会自动优化无效内容。
// /// @param x-GPIO
// /// @param active_level-EXTI_RIE, EXTI_FIE, EXTI_RFIE
// #define _gpio_exti_config(port, pin, rfie)                                                               \
//     {                                                                                                    \
//         port->PAHIE &= ~(1UL << (pin));                                                                  \
//         port->PALIE &= ~(1UL << (pin));                                                                  \
//         if(rfie & EXTI_RIE) { port->PARIE |= (1UL << (pin)); }                                           \
//         if(rfie & EXTI_FIE) { port->PAFIE |= (1UL << (pin)); }                                           \
//         if(port == HC_GPIOA) { irq_vector_set(irq_handler_extiA), EnableNvic(PORTA_IRQn, 3, 1); }        \
//         else if(port == HC_GPIOB) { irq_vector_set(irq_handler_extiB), EnableNvic(PORTB_IRQn, 3, 1); }   \
//         else if(port == HC_GPIOC) { irq_vector_set(irq_handler_extiC), EnableNvic(PORTC_E_IRQn, 3, 1); } \
//         else if(port == HC_GPIOD) { irq_vector_set(irq_handler_extiD), EnableNvic(PORTD_F_IRQn, 3, 1); } \
//         else if(port == HC_GPIOE) { irq_vector_set(irq_handler_extiE), EnableNvic(PORTC_E_IRQn, 3, 1); } \
//         else if(port == HC_GPIOF) { irq_vector_set(irq_handler_extiF), EnableNvic(PORTD_F_IRQn, 3, 1); } \
//     }
// #define gpio_exti_config(x, rfie) _gpio_exti_config(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x), rfie)

// #define _gpio_exti_deConfig(port, pin)                           \
//     {                                                            \
//         port->PARIE &= ~(1UL << (pin));                          \
//         port->PAFIE &= ~(1UL << (pin));                          \
//         port->PAHIE &= ~(1UL << (pin));                          \
//         port->PALIE &= ~(1UL << (pin));                          \
//         if(port == HC_GPIOA) { DisableNvic(PORTA_IRQn); }        \
//         else if(port == HC_GPIOB) { DisableNvic(PORTB_IRQn); }   \
//         else if(port == HC_GPIOC) { DisableNvic(PORTC_E_IRQn); } \
//         else if(port == HC_GPIOD) { DisableNvic(PORTD_F_IRQn); } \
//         else if(port == HC_GPIOE) { DisableNvic(PORTC_E_IRQn); } \
//         else if(port == HC_GPIOF) { DisableNvic(PORTD_F_IRQn); } \
//     }
// #define gpio_exti_deconfig(x) _gpio_exti_deConfig(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x))

// static funcPointer exti_fun[TYPE_EXTI_NUM];    // typ 1-上升沿，0-下降沿

/// @brief IAR编译环境下的启动文件初始化调用接口,在main函数之前运行，保证在系统初始化之前完成重要GPIO配置
/// @param
/// @return 返回值“0”代表RAM不初始化，返回值“1”代表RAM初始化
#if 0    // defined(__ICCARM__)
int __low_level_init(void)
{
    /// 配置电源检测口
    // gpio_config(PIN_CF2_IN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // gpio_config(PIN_CF1_IN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // gpio_config(PIN_RELAY_LED, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
    // gpio_config(PIN_LVDIN0, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);

    return 1;
}
#endif

/// @brief  提供给串口驱动调用。有些MCU必须先初始化串口后再打开GPIO，所以不在hal_gpio.c中提前初始化。
void hal_gpio_uart_init(uint8_t com)
{
    switch(com)
    {
        case 0:
#if defined(PIN_485_UART0_TXD) && defined(PIN_485_UART0_RXD)
            gpio_config(PIN_485_UART0_TXD, _UART0, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
            gpio_config(PIN_485_UART0_RXD, _UART0, Low_Level, Pull_OFF, GPIO_MODE_IN, TTL_MODE);
#endif
            break;
        case 1:
#if defined(PIN_UART1_TXD) && defined(PIN_UART1_RXD)
            gpio_config(PIN_UART1_TXD, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
            gpio_config(PIN_UART1_RXD, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Output_H);
#endif
            break;
        case 2:
#if defined(PIN_UART2_TXD) && defined(PIN_UART2_RXD)
            gpio_config(PIN_UART2_TXD, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
            gpio_config(PIN_UART2_RXD, GPIO_Mode_AFin_1, GPIO_In_Floating, GPIO_Out_ODP);
            // gpio_config(PIN_IR_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
#endif
            break;
        case 3:
#if defined(PIN_UART3_RXD) && defined(PIN_UART3_TXD)
            gpio_config(PIN_UART3_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
            gpio_config(PIN_UART3_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
#endif
            break;
        case 4:
#if defined(PIN_UART4_TXD) && defined(PIN_UART4_RXD)
            gpio_config(PIN_UART4_TXD, GPIO_Mode_AFo_6, GPIO_In_Floating, GPIO_Out_PP);
            gpio_config(PIN_UART4_RXD, GPIO_Mode_AFin_6, GPIO_In_Floating, GPIO_Out_PP);
#endif
            break;
        case 5:
#if defined(PIN_PHY_UART5_TXD) && defined(PIN_PHY_UART5_RXD)
            gpio_config(PIN_PHY_UART5_TXD, _UART5, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
            gpio_config(PIN_PHY_UART5_RXD, _UART5, Low_Level, Pull_OFF, GPIO_MODE_IN, TTL_MODE);
#endif
            break;
    }
}

/// @brief 所有IO口寄存器监控
/// @param typ :0只扫描电源检测IO口, 1: 扫描所有IO口
void hal_gpio_monitor(uint8_t typ)
{
    // if(!M0P_SYSCTRL->PERI_CLKEN0_f.GPIO) { M0P_SYSCTRL->PERI_CLKEN0_f.GPIO = 1; }    // 打开GPIO外设时钟门控
    // if(0xE000 != (M0P_GPIO->PCADS & 0xE000)) { M0P_GPIO->PCADS |= 0xE000; }          // LVD-PC13, XTL-PC14 PC15
}

// #define SWD_EN
void hal_gpio_reset(void)
{
#ifdef SWD_EN
    GPIO->PCA0 = (1 << 29); /* keep SWD */
    GPIO->PUA  = 0x00300000;
    GPIO->PIEA = 0xfeffffff; /* enable p30 input */
#else
    GPIO->PCA0 = 0; /* disable SWD */
    GPIO->PUA  = 0;
    GPIO->PIEA = 0xffffffff; /* PA input disable */
#endif
    GPIO->PCA1  = 0x0;        /* GPIO mode */
    GPIO->PCA2  = 0x0;        /* GPIO mode */
    GPIO->PCA3  = 0x0;        /* GPIO mode */
    GPIO->PMA   = 0xffffff1f; /* PA input mode */
    GPIO->PIMA  = 0;
    GPIO->PIMA2 = 0;

    GPIO->PCB  = 0x0; /* p60~67,p70~71,p74~77 as LCD */
    GPIO->PCB2 = 0;
    GPIO->PCB3 = 0;
    GPIO->PCB5 = 0;
    GPIO->PUB  = 0;          /*  */
    GPIO->PMB  = 0xffffffff; /*  */
    GPIO->PIEB = 0xffffffff; /*  */
    GPIO->PIMB = 0;

    GPIO->PCC  = 0x0;        /*  */
    GPIO->PCC2 = 0x0;        /*  */
    GPIO->PCC3 = 0x0;        /*  */
    GPIO->PCC4 = 0x0;        /*  */
    GPIO->PCC5 = 0x0;        /*  */
    GPIO->PUC  = 0x0;        /*  */
    GPIO->PMC  = 0xffffffff; /*  */
    GPIO->PIEC = 0xffffffff;
    GPIO->PIMC = 0;

    GPIO->PCD  = 0x3fff;
    GPIO->PCD2 = 0x0;
    GPIO->PUD  = (1 << 17); /*PU141=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */
    GPIO->PIED = 0xffffffff;
    GPIO->PIMD = 0x0;

    GPIO->PCE = 0x0;
}

/// @brief 配置GPIO外部中断, 开启NVIC中断
/// @param mode 0开启中断，1关闭中断
void hal_gpio_exti_config(uint8_t mode)
{
    if(!mode)
    {
#ifdef PIN_METER_COVER
        gpio_exti_config(PIN_METER_COVER, EXTI_RFIE);    // 开盖检测
#endif
#ifdef PIN_TEM_COVER
        gpio_exti_config(PIN_TEM_COVER, EXTI_RFIE);
#endif
#ifdef PIN_KEY_DWN
        gpio_exti_config(PIN_KEY_DWN, EXTI_RIE);
#endif
#ifdef PIN_KEY_UP
        gpio_exti_config(PIN_KEY_UP, EXTI_RIE);
#endif
    }
    else
    {
#ifdef PIN_METER_COVER
        gpio_exti_deconfig(PIN_METER_COVER);
#endif
#ifdef PIN_TEM_COVER
        gpio_exti_deconfig(PIN_TEM_COVER);
#endif
#ifdef PIN_KEY_DWN
        gpio_exti_deconfig(PIN_KEY_DWN);
#endif
#ifdef PIN_KEY_UP
        gpio_exti_deconfig(PIN_KEY_UP);
#endif
    }
}

/// @brief 正常上电初始化GPIO
/// 1，输出IO的高低电平依据电路控制逻辑相应选择GPIO_Output_H或者GPIO_Output_L。
/// 2，外设功能IO配置一律采用GPIO_In_Floating，GPIO_Out_PP。
/// 3，不建议上电情况下使用GPIO中断功能,脉冲除外。
/// 4，这里只需配置用到的IO口即可，其他IO口默认配置为GPIO模式，并且关闭输入和输出使能，避免引脚浮空漏电。
/// 5，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_config(1)开启中断。
/// 6，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_set(irq, func)设置中断回调函数。
/// 7，如果需要使用按键GPIO中断功能，则在中断回调函数中调用hal_gpio_exti_clear(irq)清除中断标志。
/// 8，应用中IICSDA设置为
/// @param
void hal_gpio_init(void)
{
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); /* 开启GPIO的APB时钟 */

    GPIO->PCA0 = (1 << 29); /* keep SWD */
    GPIO->PUA  = 0x00300000;
    GPIO->PIEA = 0xfeffffff; /* enable p30 input */
}

/**
 * @brief  低功耗运行环境GPIO初始化.
 *         默认所有功能引脚被配置为GPIO模式，开漏输出高。
 */
void hal_gpio_init_nopower(void)
{
    // 打开GPIO外设时钟门控
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); /* 开启GPIO的APB时钟 */
    /// 统一设置低功耗，关闭了swd
    hal_gpio_reset();
    hal_gpio.ext_led_init(GPIO_CLOSE);
    gpio_config(PIN_BT_PWR, _NORMALIO, Low_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
}

/// @brief FLASH片选、时钟、命令、数据引脚初始化
/// @param
void hal_gpio_flash_init(GPIO_INIT_TYPE_t type)
{
    // // gpio_config(FLASH_PW_PIN, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
    // if(type == GPIO_OPEN)
    // {
    //     // gpio_out_L(FLASH_PW_PIN);    // 电源控制
    //     gpio_config(PIN_FLASH_CS, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_SCLK, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_L);    // SCLK常态低电平 CPOL-0，CPHA-0(第一个沿采数据)
    //     gpio_config(PIN_FLASH_MOSI, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_MISO, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Out_ODE);    /// 没有外部上拉
    // }
    // else if(type == GPIO_CLOSE)
    // {
    //     // gpio_config(FLASH_PW_PIN, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_CS, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_SCLK, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_MOSI, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    //     gpio_config(PIN_FLASH_MISO, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    // }
}

/// @brief EEPROM时钟、数据引脚初始化
/// @param
void hal_gpio_eeprom_init(GPIO_INIT_TYPE_t type)
{
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
    gpio_config(PIN_3V3_EP, _NORMALIO, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
    gpio_config(PIN_I2C_SCL, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
    gpio_config(PIN_I2C_SDA, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
}

/// @brief LCD电源，时钟、数据引脚初始化
/// @param type
void hal_gpio_lcd_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_LCD_COM0, _COM, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_COM1, _COM, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_COM2, _COM, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_COM3, _COM, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG1, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG2, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG3, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG4, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG5, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG6, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG7, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG8, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG9, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG10, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG11, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG12, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG13, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG14, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG15, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG16, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG17, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_SEG18, _SEG, Low_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);

        gpio_config(PIN_LCD_VA, _LCDV, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_VB, _LCDV, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_VC, _LCDV, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
        // gpio_config(PIN_LCD_VD, _LCDV, High_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_VP1, _LCDV, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
        gpio_config(PIN_LCD_VP2, _LCDV, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
    }
    else if(type == GPIO_CLOSE)    /// @bug 关闭lcd，不需要特殊操作，直接全关闭就能低功耗
    {}
}

void hal_gpio_mic_init(GPIO_INIT_TYPE_t type)
{
    gpio_config(PIN_PF_CF, _PF, High_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
}

/// @brief 设置秒脉冲输出
/// @param mode mode=1:输出秒脉冲，0- 关闭
void hal_pulse_out_mode(uint8_t mode)
{
    // if(mode) { gpio_config(PIN_SECOND_TOUT, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP); }
    // else { gpio_config(PIN_SECOND_TOUT, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H); }
}

static void hal_gpio_remote_module_init(GPIO_INIT_TYPE_t type)
{}

void hal_gpio_exti_set(uint8_t irq, void func(void))
{
    // exti_fun[irq] = func;
}

static void hal_gpio_led_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_TZ_LED, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
        gpio_config(PIN_PHY_LED, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
    }
    else
    {
        gpio_config(PIN_TZ_LED, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
        gpio_config(PIN_PHY_LED, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, N_ch_MODE);
    }
}

static void hal_ble_init(GPIO_INIT_TYPE_t type)
{
    gpio_config(PIN_BT_PWR, _NORMALIO, High_Level, Pull_OFF, GPIO_MODE_OUT, PushPll_MODE);

    if(type == GPIO_OPEN) { LL_GPIO_SetPin(PIN_BT_PWR, Low_Level); }
}

static void hal_gpio_relay_init(GPIO_INIT_TYPE_t type)
{
    gpio_config(PIN_RELAY_ON, _NORMALIO, Low_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
    gpio_config(PIN_RELAY_OFF, _NORMALIO, Low_Level, Pull_ON, GPIO_MODE_OUT, PushPll_MODE);
}

/// @brief 声明hal_gpio子模块对象
const struct hal_gpio_t hal_gpio = {.init           = hal_gpio_init,
                                    .init_nopower   = hal_gpio_init_nopower,
                                    .uart_init      = hal_gpio_uart_init,
                                    .pulse_out_mode = hal_pulse_out_mode,
                                    .monitor        = hal_gpio_monitor,
                                    .exti_set       = hal_gpio_exti_set,
                                    .data_flash     = hal_gpio_flash_init,
                                    .ext_eeprom     = hal_gpio_eeprom_init,
                                    .ext_lcd        = hal_gpio_lcd_init,
                                    .mic_init       = hal_gpio_mic_init,
                                    .remote_module  = hal_gpio_remote_module_init,
                                    .ext_led_init   = hal_gpio_led_init,
                                    .ext_relay_init = hal_gpio_relay_init,
                                    .ext_ble        = hal_ble_init};

/** @} */
/** @} */
/** @} */
