@echo off
@setlocal ENABLEDELAYEDEXPANSION
pushd %~dp0
echo [FILE] project_buile.bat
if exist ".\boot.ewp" (
    "C:\Program Files\IAR Systems\Embedded Workbench 9.2\common\bin\iarbuild.exe" ".\boot.ewp" -build Release
    if not !ERRORLEVEL! == 0 goto ERROR
)

if exist ".\app.ewp" (
    "C:\Program Files\IAR Systems\Embedded Workbench 9.2\common\bin\iarbuild.exe" ".\app.ewp" -build Release
    if not !ERRORLEVEL! == 0 goto ERROR
)

::call .\file_copy.bat


goto END

:ERROR
popd
exit /b 1

:END
@REM pause
::echo success...
popd
exit /b 0