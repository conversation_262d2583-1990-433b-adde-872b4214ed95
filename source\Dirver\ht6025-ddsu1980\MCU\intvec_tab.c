/**
 ******************************************************************************
* @file    intvec_tab.c
* <AUTHOR> @date    2024
* @brief   中断重定向表
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*       /// HT6x2x系列中断向量表
        ; External Interrupts
        DCD     PMU_IRQHandler            ; 16+ 0: PMU
        DCD     AES_IRQHandler            ; 16+ 1: AES
        DCD     EXTI0_IRQHandler          ; 16+ 2: EXTI0
        DCD     EXTI1_IRQHandler          ; 16+ 3: EXTI1
        DCD     EXTI2_IRQH<PERSON>ler          ; 16+ 4: EXTI2
        DCD     EXTI3_IRQHandler          ; 16+ 5: EXTI3
        DCD     EXTI4_IRQHandler          ; 16+ 6: EXTI4
        DCD     EXTI5_IRQHandler          ; 16+ 7: EXTI5
        DCD     EXTI6_IRQHandler          ; 16+ 8: EXTI6
        DCD     UART0_IRQHandler          ; 16+ 9: UART0
        DCD     UART1_IRQHandler          ; 16+10: UART1
        DCD     UART2_IRQHandler          ; 16+11: UART2
        DCD     UART3_IRQHandler          ; 16+12: UART3
        DCD     UART4_IRQHandler          ; 16+13: UART4
        DCD     UART5_IRQHandler          ; 16+14: UART5
        DCD     TIMER_0_IRQHandler        ; 16+15: Timer0
        DCD     TIMER_1_IRQHandler        ; 16+16: Timer1
        DCD     TIMER_2_IRQHandler        ; 16+17: Timer2
        DCD     TIMER_3_IRQHandler        ; 16+18: Timer3
        DCD     TBS_IRQHandler            ; 16+19: TBS
        DCD     RTC_IRQHandler            ; 16+20: RTC
        DCD     I2C_IRQHandler            ; 16+21: I2C
        DCD     SPI0_IRQHandler           ; 16+22: SPI0
        DCD     SPI1_IRQHandler           ; 16+23: SPI1
        DCD     SelfTestFreq_IRQHandler   ; 16+24: SelfTestFreq
        DCD     TIMER_4_IRQHandler        ; 16+25: Timer4
        DCD     TIMER_5_IRQHandler        ; 16+26: Timer5
        DCD     UART6_IRQHandler          ; 16+27: UART6
        DCD     EXTI7_IRQHandler          ; 16+28: EXTI7
        DCD     EXTI8_IRQHandler          ; 16+29: EXTI8
        DCD     EXTI9_IRQHandler          ; 16+30: EXTI9
        DCD     DMA_IRQHandler            ; 16+31: DMA

******************************************************************************/


#pragma section = ".intvec"
/* Includes ------------------------------------------------------------------*/
#include "intvec_tab.h"


extern void systick_handler(void);


#ifndef NULL
#define NULL   (void*)0
#endif

#pragma location = ".boot_ram"
static void(*(int_vector_tab[INT_NUM]))(); // 中断请求向量

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
__weak void NMI_Handler(void)
{
    while (1){}
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
__weak void HardFault_Handler(void)
{
    while (1){}
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
__weak void SVC_Handler(void)
{
	while (1){}
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
__weak void PendSV_Handler(void)
{
	while (1){}
}

// /**
//   * @brief  This function handles SysTick Handler.
//   * @param  None
//   * @retval None
//   */
// __weak void SysTick_Handler(void)
// {
//     systick_handler();
// }


// 宏定义用于定义中断处理函数的声明
#define _PRAGMA(x)  _Pragma(#x)
#define IRQ_FUN_DECLARATION(v,f) \
void f(void) \
{ \
	if(int_vector_tab[v] != NULL) int_vector_tab[v](); \
}
// 宏定义用于定义中断处理函数的定义
IRQ_FUN_DECLARATION(INT_PMU,            PMU_IRQHandler         )
IRQ_FUN_DECLARATION(INT_AES,            AES_IRQHandler         )
IRQ_FUN_DECLARATION(INT_EXTI0,          EXTI0_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI1,          EXTI1_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI2,          EXTI2_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI3,          EXTI3_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI4,          EXTI4_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI5,          EXTI5_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI6,          EXTI6_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART0,          UART0_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART1,          UART1_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART2,          UART2_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART3,          UART3_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART4,          UART4_IRQHandler       )
IRQ_FUN_DECLARATION(INT_UART5,          UART5_IRQHandler       )
IRQ_FUN_DECLARATION(INT_TIMER_0,        TIMER_0_IRQHandler     )    
IRQ_FUN_DECLARATION(INT_TIMER_1,        TIMER_1_IRQHandler     )    
IRQ_FUN_DECLARATION(INT_TIMER_2,        TIMER_2_IRQHandler     )    
IRQ_FUN_DECLARATION(INT_TIMER_3,        TIMER_3_IRQHandler     )    
IRQ_FUN_DECLARATION(INT_TBS,            TBS_IRQHandler         )    
IRQ_FUN_DECLARATION(INT_RTC,            RTC_IRQHandler         )    
IRQ_FUN_DECLARATION(INT_I2C,            I2C_IRQHandler         )    
IRQ_FUN_DECLARATION(INT_SPI0,           SPI0_IRQHandler        )    
IRQ_FUN_DECLARATION(INT_SPI1,           SPI1_IRQHandler        )    
IRQ_FUN_DECLARATION(INT_SelfTestFreq,   SelfTestFreq_IRQHandler)
IRQ_FUN_DECLARATION(INT_TIMER_4,        TIMER_4_IRQHandler     )
IRQ_FUN_DECLARATION(INT_TIMER_5,        TIMER_5_IRQHandler     )
IRQ_FUN_DECLARATION(INT_UART6,          UART6_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI7,          EXTI7_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI8,          EXTI8_IRQHandler       )
IRQ_FUN_DECLARATION(INT_EXTI9,          EXTI9_IRQHandler       )
IRQ_FUN_DECLARATION(INT_DMA,            DMA_IRQHandler         )


/// 配置中断向量调用的处理函数.
/// @param  irq-中断类型
/// @param  vec-用户处理函数(或者清中断标志函数)指针.
/// @note   本函数允许设置空函数指针NULL用于取消中断调用处理函数.但对于必须清零
///         中断标志的中断向量，必须设置包含清中断标志的函数指针。
void int_vector_set(int irq, void vec(void))
{
    int_vector_tab[irq] = vec;
}


/** @} */
/** @} */
/** @} */
