/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   wdt.c
 *
 * @brief  Source file for WDT functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

/******************************************************************************/
/* Include files                                                              */
/******************************************************************************/
#include "wdt.h"

/**
 ******************************************************************************
 ** \defgroup WdtGroup
 **
 ******************************************************************************/
//@{

/******************************************************************************/
/* Local function prototypes ('static')                                       */
/******************************************************************************/

/**
 ******************************************************************************
 ** \brief  WDT溢出时间设置函数
 **
 ** \param [in] u8LoadValue 溢出时间
 **
 ** \retval 无
 **
 ******************************************************************************/
void Wdt_WriteWdtLoad(uint8_t u8LoadValue)
{
    M0P_WDT->CON_f.WOV = u8LoadValue;
}
/**
 ******************************************************************************
 ** \brief  WDT初始化函数
 **
 ** \param [in] enFunc @ref en_wdt_func_t
 ** \param [in] enTime @ref en_wdt_time_t
 **
 ** \retval Ok
 **
 ******************************************************************************/
en_result_t Wdt_Init(en_wdt_func_t enFunc, en_wdt_time_t enTime)
{
    en_result_t enRet = Error;
    
    Wdt_WriteWdtLoad(enTime);
    M0P_WDT->CON_f.WINT_EN = enFunc;
    enRet = Ok;
    return enRet;
}
/**
 ******************************************************************************
 ** \brief  WDT复位及启动函数
 **
 ** \param [in] 无
 **
 ** \retval 无
 **
 ******************************************************************************/
void Wdt_Start(void)
{ 
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 ******************************************************************************
 ** \brief  WDT喂狗
 **
 ** \param [in] 无
 **
 ** \retval Ok
 **
 ******************************************************************************/
void Wdt_Feed(void)
{ 
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 ******************************************************************************
 ** \brief  WDT中断标志清除
 **
 ** \param [in] 无
 **
 ** \retval Ok
 **
 ******************************************************************************/
void Wdt_IrqClr(void)
{ 
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 ******************************************************************************
 ** \brief  WDT读取当前计数值函数
 **
 ** \param [in] 无
 **
 ** \retval 计数值
 **
 ******************************************************************************/
uint8_t Wdt_ReadWdtValue(void)
{
    uint8_t u8Count;
    
    u8Count = M0P_WDT->CON_f.WCNTL;
    
    return u8Count;
}
/**
 ******************************************************************************
 ** \brief  WDT读取当前运行状态
 **
 ** \param [in] 无
 **
 ** \retval 状态值
 **
 ******************************************************************************/
boolean_t Wdt_ReadwdtStatus(void)
{
    if(M0P_WDT->CON&0x10u)
    {   
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 *******************************************************************************
 ** \brief WDT 中断状态标记获取
 **
 **
 ** \retval  中断状态
 ******************************************************************************/
boolean_t Wdt_GetIrqStatus(void)
{
    if(M0P_WDT->CON&0x80u)
    {   
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}


//@} // WdtGroup
