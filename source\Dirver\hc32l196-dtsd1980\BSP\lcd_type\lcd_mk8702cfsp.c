/**
 ******************************************************************************
* @file    lcd_jsh209105y.c
* <AUTHOR> @date    2024
* @brief   lcd_jsh209105y 显示屏定义
* @note
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"
#if USE_LCD_EXT_DRIVER
#include "ext_lcd_driver.h"
#else
#include "hal_lcd.h"
#endif
/* 定义LCD主屏, 辅屏数显个数 */
#define LCD_MS_DIGITS       8  ///定义主屏第一行数显个数  注意修改 DISP_MAX_VAL 定义
#define LCD_LS_DIGITS       0   ///定义主屏未尾小8的个数
#define LCD_PS_DIGITS       5   ///定义辅屏0数显的个数   注意修改 DISP_MAX_VAL_2 定义
#define LCD_P1S_DIGITS      5   ///定义辅屏1数显的个数
#define LCD_P2S_DIGITS      5   ///定义辅屏2数显的个数

#define LCD_MS_MINUS_SIGN   1   ///定义主屏前是否有单独的负号



/* 定义LCD COM0~8的SEG */
/*****************************************
 * 
 * 统一格式  
 * SEG_XY   数码8， X-序号，Y-8段 如：第一个数的G段-SEG_1G
 *          符号，  参考真值表定义 // X-固定为S，Y-按照液晶符号序号
 *          小数点，X-序号，Y-8段 如：第一个数的小数点-SEG_1H  // X-固定为DP，Y-按照液晶小数点序号 
 * 
******************************************/
#define SEG_THD             COM0(LCD_SEG01)
#define SEG_MD              COM1(LCD_SEG01)
#define SEG_Max             COM2(LCD_SEG01)
#define SEG_Min             COM3(LCD_SEG01)
#define SEG_X2              COM4(LCD_SEG01)
#define SEG_T1              COM5(LCD_SEG01)
// #define SEG_             COM6(LCD_SEG01)
#define SEG_a1              COM7(LCD_SEG01)

#define SEG_c2              COM0(LCD_SEG02)
#define SEG_QiuHe           COM1(LCD_SEG02)    // Σ
#define SEG_c1              COM2(LCD_SEG02)
#define SEG_b2              COM3(LCD_SEG02)
#define SEG_Avg             COM4(LCD_SEG02)
#define SEG_X4              COM5(LCD_SEG02)
#define SEG_T2              COM6(LCD_SEG02)
#define SEG_b1              COM7(LCD_SEG02)

#define SEG_6D              COM0(LCD_SEG03)
#define SEG_6E              COM1(LCD_SEG03)
#define SEG_6F              COM2(LCD_SEG03)
#define SEG_6A              COM3(LCD_SEG03)
#define SEG_1D              COM4(LCD_SEG03)
#define SEG_1E              COM5(LCD_SEG03)
#define SEG_1F              COM6(LCD_SEG03)
#define SEG_1A              COM7(LCD_SEG03)

#define SEG_6H              COM0(LCD_SEG04)
#define SEG_6C              COM1(LCD_SEG04)
#define SEG_6G              COM2(LCD_SEG04)
#define SEG_6B              COM3(LCD_SEG04)
#define SEG_1H              COM4(LCD_SEG04)
#define SEG_1C              COM5(LCD_SEG04)
#define SEG_1G              COM6(LCD_SEG04)
#define SEG_1B              COM7(LCD_SEG04)

#define SEG_7D              COM0(LCD_SEG05)
#define SEG_7E              COM1(LCD_SEG05)
#define SEG_7F              COM2(LCD_SEG05)
#define SEG_7A              COM3(LCD_SEG05)
#define SEG_2D              COM4(LCD_SEG05)
#define SEG_2E              COM5(LCD_SEG05)
#define SEG_2F              COM6(LCD_SEG05)
#define SEG_2A              COM7(LCD_SEG05)

#define SEG_7H              COM0(LCD_SEG06)
#define SEG_7C              COM1(LCD_SEG06)
#define SEG_7G              COM2(LCD_SEG06)
#define SEG_7B              COM3(LCD_SEG06)
#define SEG_2H              COM4(LCD_SEG06)
#define SEG_2C              COM5(LCD_SEG06)
#define SEG_2G              COM6(LCD_SEG06)
#define SEG_2B              COM7(LCD_SEG06)

#define SEG_8D              COM0(LCD_SEG07)
#define SEG_8E              COM1(LCD_SEG07)
#define SEG_8F              COM2(LCD_SEG07)
#define SEG_8A              COM3(LCD_SEG07)
#define SEG_3D              COM4(LCD_SEG07)
#define SEG_3E              COM5(LCD_SEG07)
#define SEG_3F              COM6(LCD_SEG07)
#define SEG_3A              COM7(LCD_SEG07)

#define SEG_8H              COM0(LCD_SEG08)
#define SEG_8C              COM1(LCD_SEG08)
#define SEG_8G              COM2(LCD_SEG08)
#define SEG_8B              COM3(LCD_SEG08)
#define SEG_3H              COM4(LCD_SEG08)
#define SEG_3C              COM5(LCD_SEG08)
#define SEG_3G              COM6(LCD_SEG08)
#define SEG_3B              COM7(LCD_SEG08)

#define SEG_9D              COM0(LCD_SEG09)
#define SEG_9E              COM1(LCD_SEG09)
#define SEG_9F              COM2(LCD_SEG09)
#define SEG_9A              COM3(LCD_SEG09)
#define SEG_4D              COM4(LCD_SEG09)
#define SEG_4E              COM5(LCD_SEG09)
#define SEG_4F              COM6(LCD_SEG09)
#define SEG_4A              COM7(LCD_SEG09)

#define SEG_9H              COM0(LCD_SEG10)
#define SEG_9C              COM1(LCD_SEG10)
#define SEG_9G              COM2(LCD_SEG10)
#define SEG_9B              COM3(LCD_SEG10)
#define SEG_4H              COM4(LCD_SEG10)
#define SEG_4C              COM5(LCD_SEG10)
#define SEG_4G              COM6(LCD_SEG10)
#define SEG_4B              COM7(LCD_SEG10)

#define SEG_10D             COM0(LCD_SEG11)
#define SEG_10E             COM1(LCD_SEG11)
#define SEG_10F             COM2(LCD_SEG11)
#define SEG_10A             COM3(LCD_SEG11)
#define SEG_5D              COM4(LCD_SEG11)
#define SEG_5E              COM5(LCD_SEG11)
#define SEG_5F              COM6(LCD_SEG11)
#define SEG_5A              COM7(LCD_SEG11)

#define SEG_V3              COM0(LCD_SEG12)
#define SEG_10C             COM1(LCD_SEG12)
#define SEG_10G             COM2(LCD_SEG12)
#define SEG_10B             COM3(LCD_SEG12)
#define SEG_X5              COM4(LCD_SEG12)
#define SEG_5C              COM5(LCD_SEG12)
#define SEG_5G              COM6(LCD_SEG12)
#define SEG_5B              COM7(LCD_SEG12)

#define SEG_X8              COM0(LCD_SEG13)
#define SEG_24C             COM1(LCD_SEG13)
#define SEG_24G             COM2(LCD_SEG13)
#define SEG_24B             COM3(LCD_SEG13)
#define SEG_16A             COM4(LCD_SEG13)
#define SEG_16F             COM5(LCD_SEG13)
#define SEG_16E             COM6(LCD_SEG13)
#define SEG_16D             COM7(LCD_SEG13)

#define SEG_24D             COM0(LCD_SEG14)
#define SEG_24E             COM1(LCD_SEG14)
#define SEG_24F             COM2(LCD_SEG14)
#define SEG_24A             COM3(LCD_SEG14)
#define SEG_16B             COM4(LCD_SEG14)
#define SEG_16G             COM5(LCD_SEG14)
#define SEG_16C             COM6(LCD_SEG14)
#define SEG_X12             COM7(LCD_SEG14)

#define SEG_X11             COM0(LCD_SEG15)
#define SEG_X10             COM1(LCD_SEG15)
#define SEG_T               COM2(LCD_SEG15)
#define SEG_RiLi            COM3(LCD_SEG15)
#define SEG_17A             COM4(LCD_SEG15)
#define SEG_17F             COM5(LCD_SEG15)
#define SEG_17E             COM6(LCD_SEG15)
#define SEG_17D             COM7(LCD_SEG15)

#define SEG_a2              COM0(LCD_SEG16)
#define SEG_n               COM1(LCD_SEG16)
#define SEG_LingXu          COM2(LCD_SEG16)    // 零序
#define SEG_Demand          COM3(LCD_SEG16)    // 需量
#define SEG_17B             COM4(LCD_SEG16)
#define SEG_17G             COM5(LCD_SEG16)
#define SEG_17C             COM6(LCD_SEG16)
#define SEG_DOcolin         COM7(LCD_SEG16)    // DO:

#define SEG_11A             COM0(LCD_SEG17)
#define SEG_11F             COM1(LCD_SEG17)
#define SEG_11E             COM2(LCD_SEG17)
#define SEG_11D             COM3(LCD_SEG17)
#define SEG_18A             COM4(LCD_SEG17)
#define SEG_18F             COM5(LCD_SEG17)
#define SEG_18E             COM6(LCD_SEG17)
#define SEG_18D             COM7(LCD_SEG17)

#define SEG_11B             COM0(LCD_SEG18)
#define SEG_11G             COM1(LCD_SEG18)
#define SEG_11C             COM2(LCD_SEG18)
#define SEG_11H             COM3(LCD_SEG18)
#define SEG_18B             COM4(LCD_SEG18)
#define SEG_18G             COM5(LCD_SEG18)
#define SEG_18C             COM6(LCD_SEG18)
#define SEG_Mcolon          COM7(LCD_SEG18)    // M:

#define SEG_12A             COM0(LCD_SEG19)
#define SEG_12F             COM1(LCD_SEG19)
#define SEG_12E             COM2(LCD_SEG19)
#define SEG_12D             COM3(LCD_SEG19)
#define SEG_19A             COM4(LCD_SEG19)
#define SEG_19F             COM5(LCD_SEG19)
#define SEG_19E             COM6(LCD_SEG19)
#define SEG_19D             COM7(LCD_SEG19)

#define SEG_12B             COM0(LCD_SEG20)
#define SEG_12G             COM1(LCD_SEG20)
#define SEG_12C             COM2(LCD_SEG20)
#define SEG_12H             COM3(LCD_SEG20)
#define SEG_19B             COM4(LCD_SEG20)
#define SEG_19G             COM5(LCD_SEG20)
#define SEG_19C             COM6(LCD_SEG20)
#define SEG_U1              COM7(LCD_SEG20)

#define SEG_U6              COM0(LCD_SEG21)
#define SEG_U5              COM1(LCD_SEG21)
#define SEG_T3              COM2(LCD_SEG21)
#define SEG_U4              COM3(LCD_SEG21)
#define SEG_19I             COM4(LCD_SEG21)
#define SEG_19H             COM5(LCD_SEG21)
#define SEG_U2              COM6(LCD_SEG21)
#define SEG_U3              COM7(LCD_SEG21)

#define SEG_13A             COM0(LCD_SEG22)
#define SEG_13F             COM1(LCD_SEG22)
#define SEG_13E             COM2(LCD_SEG22)
#define SEG_13D             COM3(LCD_SEG22)
#define SEG_20A             COM4(LCD_SEG22)
#define SEG_20F             COM5(LCD_SEG22)
#define SEG_20E             COM6(LCD_SEG22)
#define SEG_20D             COM7(LCD_SEG22)

#define SEG_13B             COM0(LCD_SEG23)
#define SEG_13G             COM1(LCD_SEG23)
#define SEG_13C             COM2(LCD_SEG23)
#define SEG_13H             COM3(LCD_SEG23)
#define SEG_20B             COM4(LCD_SEG23)
#define SEG_20G             COM5(LCD_SEG23)
#define SEG_20C             COM6(LCD_SEG23)
#define SEG_20H             COM7(LCD_SEG23)

#define SEG_14A             COM0(LCD_SEG24)
#define SEG_14F             COM1(LCD_SEG24)
#define SEG_14E             COM2(LCD_SEG24)
#define SEG_14D             COM3(LCD_SEG24)
#define SEG_21A             COM4(LCD_SEG24)
#define SEG_21F             COM5(LCD_SEG24)
#define SEG_21E             COM6(LCD_SEG24)
#define SEG_21D             COM7(LCD_SEG24)

#define SEG_14B             COM0(LCD_SEG25)
#define SEG_14G             COM1(LCD_SEG25)
#define SEG_14C             COM2(LCD_SEG25)
#define SEG_14H             COM3(LCD_SEG25)
#define SEG_21B             COM4(LCD_SEG25)
#define SEG_21G             COM5(LCD_SEG25)
#define SEG_21C             COM6(LCD_SEG25)
#define SEG_DIcolon         COM7(LCD_SEG25)    // DI:

#define SEG_Y4              COM0(LCD_SEG26)
#define SEG_Y3              COM1(LCD_SEG26)
#define SEG_T4              COM2(LCD_SEG26)
#define SEG_Y2              COM3(LCD_SEG26)
#define SEG_21I             COM4(LCD_SEG26)
#define SEG_21H             COM5(LCD_SEG26)
#define SEG_Tcolon          COM6(LCD_SEG26)    // T:
#define SEG_Y1              COM7(LCD_SEG26)

#define SEG_15A             COM0(LCD_SEG27)
#define SEG_15F             COM1(LCD_SEG27)
#define SEG_15E             COM2(LCD_SEG27)
#define SEG_15D             COM3(LCD_SEG27)
#define SEG_22A             COM4(LCD_SEG27)
#define SEG_22F             COM5(LCD_SEG27)
#define SEG_22E             COM6(LCD_SEG27)
#define SEG_22D             COM7(LCD_SEG27)

#define SEG_15B             COM0(LCD_SEG28)
#define SEG_15G             COM1(LCD_SEG28)
#define SEG_15C             COM2(LCD_SEG28)
#define SEG_M4              COM3(LCD_SEG28)
#define SEG_22B             COM4(LCD_SEG28)
#define SEG_22G             COM5(LCD_SEG28)
#define SEG_22C             COM6(LCD_SEG28)
#define SEG_22H             COM7(LCD_SEG28)

#define SEG_K3              COM0(LCD_SEG29)
#define SEG_M3              COM1(LCD_SEG29)
#define SEG_K41             COM2(LCD_SEG29)
#define SEG_M5              COM3(LCD_SEG29)
#define SEG_23A             COM4(LCD_SEG29)
#define SEG_23F             COM5(LCD_SEG29)
#define SEG_23E             COM6(LCD_SEG29)
#define SEG_23D             COM7(LCD_SEG29)

#define SEG_VA              COM0(LCD_SEG30)
#define SEG_K31             COM1(LCD_SEG30)
#define SEG_Wh              COM2(LCD_SEG30)
#define SEG_K51             COM3(LCD_SEG30)
#define SEG_23B             COM4(LCD_SEG30)
#define SEG_23G             COM5(LCD_SEG30)
#define SEG_23C             COM6(LCD_SEG30)
#define SEG_varh            COM7(LCD_SEG30)

#define SEG_A3              COM0(LCD_SEG31)
#define SEG_K21             COM1(LCD_SEG31)
#define SEG_var             COM2(LCD_SEG31)
#define SEG_A2              COM3(LCD_SEG31)
#define SEG_Hz              COM4(LCD_SEG31)
#define SEG_W               COM5(LCD_SEG31)
#define SEG_A1              COM6(LCD_SEG31)
#define SEG_PF              COM7(LCD_SEG31)

#define SEG_X6              COM0(LCD_SEG32)
#define SEG_M2              COM1(LCD_SEG32)
#define SEG_K2              COM2(LCD_SEG32)
#define SEG_V2              COM3(LCD_SEG32)
#define SEG_M1              COM4(LCD_SEG32)
#define SEG_K11             COM5(LCD_SEG32)
#define SEG_K1              COM6(LCD_SEG32)
#define SEG_V1              COM7(LCD_SEG32)

#define SEG_NULL            0

#define I_A                 0x0001
#define I_B                 0x0002
#define I_C                 0x0004
#define I_D                 0x0008
#define I_E                 0x0010
#define I_F                 0x0020
#define I_G                 0x0040
#define I_I                 0x0080

/*  lcd digit and char macro define */
#define SPACE           0  //all the SEGments of the char off
#define CHAR_0          (I_A|I_B|I_C|I_D|I_E|I_F)
#define CHAR_1          (I_B|I_C)
#define CHAR_2          (I_A|I_B|I_D|I_E|I_G)
#define CHAR_3          (I_A|I_B|I_C|I_D|I_G)
#define CHAR_4          (I_B|I_C|I_F|I_G)
#define CHAR_5          (I_A|I_C|I_D|I_F|I_G)
#define CHAR_6          (I_A|I_C|I_D|I_E|I_F|I_G)
#define CHAR_7          (I_A|I_B|I_C)
#define CHAR_8          (I_A|I_B|I_C|I_D|I_E|I_F|I_G)
#define CHAR_9          (I_A|I_B|I_C|I_D|I_F|I_G)
#define CHAR_A          (I_A|I_B|I_C|I_E|I_F|I_G)    // 'A'
#define CHAR_b          (I_C|I_D|I_E|I_F|I_G)        // 'b'
#define CHAR_C          (I_A|I_D|I_E|I_F)            // 'C'
#define CHAR_d          (I_B|I_C|I_D|I_E|I_G)        // 'd'
#define CHAR_E          (I_A|I_D|I_E|I_F|I_G)        // 'E'
#define CHAR_F          (I_A|I_E|I_F|I_G)            // 'F'
#define CHAR_G          (I_A|I_C|I_D|I_E|I_F)        // 'G'
#define CHAR_H          (I_C|I_E|I_F|I_G)            // 'h'
#define CHAR_I          (I_E|I_F)                    // 'I'
#define CHAR_J          (I_B|I_C|I_D|I_E)            // 'J'
#define CHAR_k          (I_E|I_F|I_G)                // 'k'
#define CHAR_L          (I_D|I_E|I_F)                // 'L'
#define CHAR_m          (I_A|I_C|I_E)                // 'm'
#define CHAR_N          (I_A|I_B|I_C|I_E|I_F)        // 'N'
#define CHAR_O          (I_A|I_B|I_C|I_D|I_E|I_F)    // 'O'
#define CHAR_P          (I_A|I_B|I_E|I_F|I_G)        // 'P'
#define CHAR_q          (I_A|I_B|I_C|I_F|I_G)        // 'q'
#define CHAR_r          (I_E|I_G)                    // 'r'
#define CHAR_S          (I_A|I_C|I_D|I_F|I_G)        // 'S'
#define CHAR_t          (I_D|I_E|I_F|I_G)            // 't'
#define CHAR_u          (I_B|I_C|I_D|I_E|I_F)        // 'u'
#define CHAR_V          (I_C|I_D|I_E)                // 'V'
#define CHAR_w          (I_B|I_D|I_F)                // 'w'
#define CHAR_x          (I_B|I_C|I_E|I_F|I_G)        // 'x'
#define CHAR_y          (I_B|I_C|I_D|I_F|I_G)        // 'y'
#define CHAR_Z          (I_A|I_B|I_D|I_E|I_G)        // 'Z'
#define CHAR_c          (I_D|I_E|I_G)                // 'c'
#define CHAR_n          (I_C|I_E|I_G)                // 'n'
#define CHAR_o          (I_C|I_D|I_E|I_G)            // 'o'
#define CHAR_           (I_G)                        // '-'  短杠 
#define CHAR__          (I_D)                        // '_'  下划线


/* Private typedef -----------------------------------------------------------*/
typedef uint16_t          SEG_TYPE_t;     ///
typedef struct
{
    const SEG_TYPE_t* seg_tab;
    const SEG_TYPE_t* dot_tab;
    uint8_t seg_num;
    uint8_t dot_num;
    uint8_t digit_num;
} Screen_s;

/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t ms_digit_segs[][7] =
{
    //数据显示
    {SEG_16A, SEG_16B, SEG_16C, SEG_16D, SEG_16E, SEG_16F, SEG_16G},
    {SEG_17A, SEG_17B, SEG_17C, SEG_17D, SEG_17E, SEG_17F, SEG_17G},
    {SEG_18A, SEG_18B, SEG_18C, SEG_18D, SEG_18E, SEG_18F, SEG_18G},
    {SEG_19A, SEG_19B, SEG_19C, SEG_19D, SEG_19E, SEG_19F, SEG_19G},
    {SEG_20A, SEG_20B, SEG_20C, SEG_20D, SEG_20E, SEG_20F, SEG_20G},
    {SEG_21A, SEG_21B, SEG_21C, SEG_21D, SEG_21E, SEG_21F, SEG_21G},
    {SEG_22A, SEG_22B, SEG_22C, SEG_22D, SEG_22E, SEG_22F, SEG_22G},
    {SEG_23A, SEG_23B, SEG_23C, SEG_23D, SEG_23E, SEG_23F, SEG_23G},
};
/// @brief 主屏小数点段址表
static const SEG_TYPE_t ms_dot_segs[] =
{
    SEG_NULL, SEG_NULL, SEG_NULL, SEG_19H, SEG_20H, SEG_21H, SEG_22H
};


#if LCD_PS_DIGITS != 0
/// @brief 副屏数字段址表, 从左到右
static const SEG_TYPE_t ps_digit_segs[][7] =
{
    //ID 数据标识
    {SEG_1A,   SEG_1B,   SEG_1C,   SEG_1D,   SEG_1E,   SEG_1F,   SEG_1G},
    {SEG_2A,   SEG_2B,   SEG_2C,   SEG_2D,   SEG_2E,   SEG_2F,   SEG_2G},
    {SEG_3A,   SEG_3B,   SEG_3C,   SEG_3D,   SEG_3E,   SEG_3F,   SEG_3G},
    {SEG_4A,   SEG_4B,   SEG_4C,   SEG_4D,   SEG_4E,   SEG_4F,   SEG_4G},
    {SEG_5A,   SEG_5B,   SEG_5C,   SEG_5D,   SEG_5E,   SEG_5F,   SEG_5G},
};

/// @brief 副屏小数点段址表
static const SEG_TYPE_t ps_dot_segs[] =
{
    SEG_1H, SEG_2H, SEG_3H, SEG_4H
};
#endif

#if LCD_P1S_DIGITS != 0
/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t p1s_digit_segs[][7] =
{
    //数据显示
    {SEG_6A,   SEG_6B,   SEG_6C,   SEG_6D,   SEG_6E,   SEG_6F,   SEG_6G},
    {SEG_7A,   SEG_7B,   SEG_7C,   SEG_7D,   SEG_7E,   SEG_7F,   SEG_7G},
    {SEG_8A,   SEG_8B,   SEG_8C,   SEG_8D,   SEG_8E,   SEG_8F,   SEG_8G},
    {SEG_9A,   SEG_9B,   SEG_9C,   SEG_9D,   SEG_9E,   SEG_9F,   SEG_9G},
    {SEG_10A,  SEG_10B,  SEG_10C,  SEG_10D,  SEG_10E,  SEG_10F,  SEG_10G},
};
/// @brief 主屏小数点段址表
static const SEG_TYPE_t p1s_dot_segs[] =
{
    SEG_6H, SEG_7H, SEG_8H, SEG_9H
};
#endif

#if LCD_P2S_DIGITS != 0
/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t p2s_digit_segs[][7] =
{
    //数据显示
    {SEG_11A,  SEG_11B,  SEG_11C,  SEG_11D,  SEG_11E,  SEG_11F,  SEG_11G},
    {SEG_12A,  SEG_12B,  SEG_12C,  SEG_12D,  SEG_12E,  SEG_12F,  SEG_12G},
    {SEG_13A,  SEG_13B,  SEG_13C,  SEG_13D,  SEG_13E,  SEG_13F,  SEG_13G},
    {SEG_14A,  SEG_14B,  SEG_14C,  SEG_14D,  SEG_14E,  SEG_14F,  SEG_14G},
    {SEG_15A,  SEG_15B,  SEG_15C,  SEG_15D,  SEG_15E,  SEG_15F,  SEG_15G},
};

/// @brief 主屏小数点段址表
static const SEG_TYPE_t p2s_dot_segs[] =
{
    SEG_11H, SEG_12H, SEG_13H, SEG_14H
};
#endif


static const Screen_s digit_screen[] =  // 显示屏的参数
{
    {
        .seg_tab    = ms_digit_segs[0],
        .seg_num    = sizeof(ms_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(ms_digit_segs),
        .dot_tab    = ms_dot_segs,
        .dot_num    = eleof(ms_dot_segs),
    },
#if LCD_PS_DIGITS != 0
    {
        .seg_tab    = ps_digit_segs[0],
        .seg_num    = sizeof(ps_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(ps_digit_segs),
        .dot_tab    = ps_dot_segs,
        .dot_num    = eleof(ps_dot_segs),
    },
#endif
#if LCD_P1S_DIGITS != 0
    {
        .seg_tab    = p1s_digit_segs[0],
        .seg_num    = sizeof(p1s_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(p1s_digit_segs),
        .dot_tab    = p1s_dot_segs,
        .dot_num    = eleof(p1s_dot_segs),
    },
#endif
#if LCD_P2S_DIGITS != 0
    {
        .seg_tab    = p2s_digit_segs[0],
        .seg_num    = sizeof(p2s_digit_segs[0]) / sizeof(SEG_TYPE_t),
        .digit_num  = eleof(p2s_digit_segs),
        .dot_tab    = p2s_dot_segs,
        .dot_num    = eleof(p2s_dot_segs), 
    },
#endif
};

static const uint8_t screen_number = eleof(digit_screen);
/// @brief 单位
static const SEG_TYPE_t unit_none[]          = {SEG_NULL};
static const SEG_TYPE_t unit_sign[]          = {SEG_X8};    // 负号
static const SEG_TYPE_t unit_time[]          = {SEG_19H, SEG_19I, SEG_21H, SEG_21I}; // hh:mm:ss
static const SEG_TYPE_t unit_date[]          = {SEG_19H, SEG_21H};                   //YY.MM.DD

static const SEG_TYPE_t unit_W[]             = {SEG_W};
static const SEG_TYPE_t unit_kW[]            = {SEG_K11, SEG_W};
static const SEG_TYPE_t unit_mW[]            = {SEG_M1, SEG_W};
static const SEG_TYPE_t unit_VA[]            = {SEG_VA};
static const SEG_TYPE_t unit_kVA[]           = {SEG_K31, SEG_VA};
static const SEG_TYPE_t unit_mVA[]           = {SEG_M3, SEG_VA};
static const SEG_TYPE_t unit_var[]           = {SEG_var};
static const SEG_TYPE_t unit_kvar[]          = {SEG_K21, SEG_var};
static const SEG_TYPE_t unit_mvar[]          = {SEG_M2, SEG_var};

static const SEG_TYPE_t unit_Wh[]            = {SEG_Wh};
static const SEG_TYPE_t unit_kWh[]           = {SEG_K41, SEG_Wh};
static const SEG_TYPE_t unit_mWh[]           = {SEG_M4, SEG_Wh};
static const SEG_TYPE_t unit_varh[]          = {SEG_varh};
static const SEG_TYPE_t unit_kvarh[]         = {SEG_K51, SEG_varh};
static const SEG_TYPE_t unit_mvarh[]         = {SEG_M5, SEG_varh};

static const SEG_TYPE_t unit_V1[]            = {SEG_V1};
static const SEG_TYPE_t unit_A1[]            = {SEG_A1};
static const SEG_TYPE_t unit_V2[]            = {SEG_V2};
static const SEG_TYPE_t unit_A2[]            = {SEG_A2};
static const SEG_TYPE_t unit_V3[]            = {SEG_V3};
static const SEG_TYPE_t unit_A3[]            = {SEG_A3};
static const SEG_TYPE_t unit_kV1[]           = {SEG_K1, SEG_V1};
static const SEG_TYPE_t unit_kA1[]           = {SEG_K1, SEG_A1};
static const SEG_TYPE_t unit_kV2[]           = {SEG_K2, SEG_V2};
static const SEG_TYPE_t unit_kA2[]           = {SEG_K2, SEG_A2};
static const SEG_TYPE_t unit_kV3[]           = {SEG_K3, SEG_V3};
static const SEG_TYPE_t unit_kA3[]           = {SEG_K3, SEG_A3};
static const SEG_TYPE_t unit_Hz[]            = {SEG_Hz};

static const SEG_TYPE_t unit_Percent[]       = {SEG_X5};    // %
static const SEG_TYPE_t unit_PF[]            = {SEG_PF};    // PF
static const SEG_TYPE_t unit_Temp[]          = {SEG_X6};    // ℃

/// @brief 图标
static const SEG_TYPE_t icon_P[]             = {SEG_X2};          // 有功指示符号
static const SEG_TYPE_t icon_Q[]             = {SEG_X4};          // 无功指示符号

static const SEG_TYPE_t icon_Jian[]          = {SEG_T1};          // 尖指示符号
static const SEG_TYPE_t icon_Feng[]          = {SEG_T2};          // 峰指示符号
static const SEG_TYPE_t icon_Ping[]          = {SEG_T3};          // 平指示符号
static const SEG_TYPE_t icon_Gu[]            = {SEG_T4};          // 谷指示符号

static const SEG_TYPE_t icon_Max[]           = {SEG_Max};         // Max
static const SEG_TYPE_t icon_Min[]           = {SEG_Min};         // Min
static const SEG_TYPE_t icon_Avg[]           = {SEG_Avg};         // Avg
static const SEG_TYPE_t icon_MD[]            = {SEG_MD};          // MD
static const SEG_TYPE_t icon_THD[]           = {SEG_THD};         // THD

static const SEG_TYPE_t icon_ling_xu[]       = {SEG_LingXu};      // 零序
static const SEG_TYPE_t icon_demand[]        = {SEG_Demand};      // 需量
static const SEG_TYPE_t icon_ri_li[]         = {SEG_RiLi};        // 日历

static const SEG_TYPE_t icon_qiuhe[]         = {SEG_QiuHe};       // 求和符号

static const SEG_TYPE_t icon_ab_a1[]         = {SEG_a1};          // ab a1符号
static const SEG_TYPE_t icon_ca_a2[]         = {SEG_a2};          // ca a2符号
static const SEG_TYPE_t icon_ab_b1[]         = {SEG_b1};          // ab b1符号
static const SEG_TYPE_t icon_bc_b2[]         = {SEG_b2};          // bc b2符号
static const SEG_TYPE_t icon_bc_c1[]         = {SEG_c1};          // bc c1符号
static const SEG_TYPE_t icon_ca_c2[]         = {SEG_c2};          // ca c2符号
static const SEG_TYPE_t icon_n[]             = {SEG_n};           // n 符号

static const SEG_TYPE_t icon_total[]         = {SEG_T};           // 总符号
static const SEG_TYPE_t icon_tariff_1[]      = {SEG_T, SEG_24B, SEG_24C};                                              //费率1
static const SEG_TYPE_t icon_tariff_2[]      = {SEG_T, SEG_24A, SEG_24B, SEG_24D, SEG_24E, SEG_24G};                   //费率2
static const SEG_TYPE_t icon_tariff_3[]      = {SEG_T, SEG_24A, SEG_24B, SEG_24C, SEG_24D, SEG_24G};                   //费率3
static const SEG_TYPE_t icon_tariff_4[]      = {SEG_T, SEG_24B, SEG_24C, SEG_24F, SEG_24G};                            //费率4
static const SEG_TYPE_t icon_tariff_5[]      = {SEG_T, SEG_24A, SEG_24C, SEG_24D, SEG_24F, SEG_24G};                   //费率5
static const SEG_TYPE_t icon_tariff_6[]      = {SEG_T, SEG_24A, SEG_24C, SEG_24D, SEG_24E, SEG_24F, SEG_24G};          //费率6
static const SEG_TYPE_t icon_tariff_7[]      = {SEG_T, SEG_24A, SEG_24B, SEG_24C};                                     //费率7
static const SEG_TYPE_t icon_tariff_8[]      = {SEG_T, SEG_24A, SEG_24B, SEG_24C, SEG_24D, SEG_24E, SEG_24F, SEG_24G}; //费率8
static const SEG_TYPE_t icon_tariff_9[]      = {SEG_T, SEG_24A, SEG_24B, SEG_24C, SEG_24D, SEG_24F, SEG_24G};          //费率9

static const SEG_TYPE_t icon_mudle_comm[]    = {SEG_X10};                                   // 通信模块通讯中
static const SEG_TYPE_t icon_alarm[]         = {SEG_X12};                                   // 报警指示
static const SEG_TYPE_t icon_battery1_low[]  = {SEG_X11};                                   // 电池1低电量

static const SEG_TYPE_t icon_DO_U1[]         = {SEG_DOcolin, SEG_U1};    // DO:U1
static const SEG_TYPE_t icon_DO_U2[]         = {SEG_DOcolin, SEG_U2};    // DO:U2
static const SEG_TYPE_t icon_DO_U3[]         = {SEG_DOcolin, SEG_U3};    // DO:U3
static const SEG_TYPE_t icon_DO_U4[]         = {SEG_DOcolin, SEG_U4};    // DO:U4
static const SEG_TYPE_t icon_DO_U5[]         = {SEG_DOcolin, SEG_U5};    // DO:U5
static const SEG_TYPE_t icon_DO_U6[]         = {SEG_DOcolin, SEG_U6};    // DO:U6
static const SEG_TYPE_t icon_M_U1[]          = {SEG_Mcolon, SEG_U1};     // M:U1
static const SEG_TYPE_t icon_M_U2[]          = {SEG_Mcolon, SEG_U2};     // M:U2
static const SEG_TYPE_t icon_M_U3[]          = {SEG_Mcolon, SEG_U3};     // M:U3
static const SEG_TYPE_t icon_M_U4[]          = {SEG_Mcolon, SEG_U4};     // M:U4
static const SEG_TYPE_t icon_M_U5[]          = {SEG_Mcolon, SEG_U5};     // M:U5
static const SEG_TYPE_t icon_M_U6[]          = {SEG_Mcolon, SEG_U6};     // M:U6
static const SEG_TYPE_t icon_DI_Y1[]         = {SEG_DIcolon, SEG_Y1};    // DI:Y1
static const SEG_TYPE_t icon_DI_Y2[]         = {SEG_DIcolon, SEG_Y2};    // DI:Y2
static const SEG_TYPE_t icon_DI_Y3[]         = {SEG_DIcolon, SEG_Y3};    // DI:Y3
static const SEG_TYPE_t icon_DI_Y4[]         = {SEG_DIcolon, SEG_Y4};    // DI:Y4
static const SEG_TYPE_t icon_T_Y1[]          = {SEG_Tcolon, SEG_Y1};     // T:Y1
static const SEG_TYPE_t icon_T_Y2[]          = {SEG_Tcolon, SEG_Y2};     // T:Y2
static const SEG_TYPE_t icon_T_Y3[]          = {SEG_Tcolon, SEG_Y3};     // T:Y3
static const SEG_TYPE_t icon_T_Y4[]          = {SEG_Tcolon, SEG_Y4};     // T:Y4

/// @brief 数字字段码表
static const uint8_t segs_digit_tab[] =
{
    CHAR_0, CHAR_1, CHAR_2, CHAR_3, CHAR_4,
    CHAR_5, CHAR_6, CHAR_7, CHAR_8, CHAR_9,
};

/// @brief 字母字段码表
static const uint8_t segs_alp_tab[] =
{
    CHAR_A, CHAR_b, CHAR_C, CHAR_d, CHAR_E, CHAR_F, CHAR_G,
    CHAR_H, CHAR_I, CHAR_J, CHAR_k, CHAR_L, CHAR_m, CHAR_N,
    CHAR_o, CHAR_P, CHAR_q, CHAR_r, CHAR_S, CHAR_t,
    CHAR_u, CHAR_V, CHAR_w, CHAR_x, CHAR_y, CHAR_Z,
};


