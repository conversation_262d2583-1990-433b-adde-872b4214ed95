/**
 ******************************************************************************
* @file    hal_rtc.c
* <AUTHOR> @version V1.0.0
* @date    2024
* @brief   本模块主要完成RTC的读取, 处理, 补偿等驱动。
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include <string.h>                 // for memcpy
#include <time.h>
#include "hal_mcu.h"
#include "hal_gpio.h"
#include "hal_rtc.h"
#include "hal_def.h"
#include "hal_adc.h"

#define const_T_OffsetRun    0          // 芯片发热修正-运行用 (非MCU测温可设为0)
#define const_T_OffsetSlp    0          // 芯片发热修正-休眠用 (非MCU测温可设为0)
#define const_KL             0.0309     // 低温晶体曲线系数
#define const_KH             0.03805    // 高温晶体曲线系数

#define const_xtl_top        25.0       // 晶体顶点温度
#define const_KHKL_TrimRange 32         // 晶体曲线修调功能分界范围，25度±7度范围外使用高低温修调值kh，kl


/* Private variables ---------------------------------------------------------*/
static HAL_RTC_STUS rtc_state;
static uint8_t rtc_second;
static void (*second_callback)(void);  ///< 秒中断回调函数
#if (EMU_TYPE==EMU_VIRTUAL)
static void (*energy_integral_callback)(void);  ///< 能量积分回调函数,用于虚拟计量芯片电能积分
#endif
static int16_t rtc_ppm;                                     // RTC手动校准值
/* Private constants ---------------------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/// @brief RTC中断处理函数
/// @param  
void irq_handler_rtc(void)
{
    if(HT_RTC->RTCIF & (RTC_RTCIF_SECIF + RTC_RTCIF_MINIF + RTC_RTCIF_HRIF + RTC_RTCIF_DAYIF + RTC_RTCIF_MTHIF))
    {
        rtc_state.rtc_second = true;
        if(second_callback != NULL) (second_callback)();
#if (EMU_TYPE==EMU_VIRTUAL)
        if(energy_integral_callback != NULL) (energy_integral_callback)();  ///< 能量积分回调函数,用于虚拟计量芯片电能积分
#endif
    }

    /* Clear RTC All Flags */
    HT_RTC->RTCIF = 0;
}


/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */
/**
 * @brief  初始RTC相关参数, 返回时钟状态
 * @retval 参见@ref HAL_RTC_STUS定义
 */
HAL_RTC_STUS hal_rtc_init(void)
{
    //HT_RTC->RTCIE = 0;
    HT_RTC_LoadInfoData();  //从InfoFlash中装载RTC补偿系数

    hal_rtc.sec_pulse_set(1);           // TOUT秒脉冲默认输出
    return rtc_state;
}

void hal_rtc_init_nopower(void)
{

}

/**
 * @brief  获取当前RTC时间，不获取星期
 * @param  [out ] rtc-RTC时间指针
 * @retval 0-RTC发生过复位, 不可信
 * @retval 1-RTC正常
 */
bool hal_rtc_time_get(struct rtc_t* rtc)
{
    uint16_t cnt = 8000;

    HT_RTC->RTCRD = RTC_RTCRD_READFLAG;         ///设置开始读标志
    while(HT_RTC->RTCRD & RTC_RTCRD_READFLAG)   ///等待读标志清除
    {
        if(--cnt == 0) return FALSE;
    }
    /// 开始读RTC寄存器
    //rtc->weekDay = HT_RTC->WEEKR & RTC_WEEKR;
    rtc->YY = HT_RTC->YEARR & RTC_YEARR;
    rtc->MM = HT_RTC->MONTHR& RTC_MONTHR;
    rtc->DD = HT_RTC->DAYR  & RTC_DAYR;
    rtc->hh = HT_RTC->HOURR & RTC_HOURR;
    rtc->mm = HT_RTC->MINR  & RTC_MINR;
    rtc->ss = HT_RTC->SECR  & RTC_SECR;
    rtc_second = rtc->ss;
    return TRUE;
}

/**
 * @brief  设置当前RTC时间，不写星期
 * @param  [in ] rtc-RTC时间指针
 * @retval 0-设置失败
 * @retval 1-设置成功
 * @note   如果RTC时间设置成功，将会清除RTC异常复位标志
 */
bool hal_rtc_time_set(const struct rtc_t* rtc)
{
    HAL_SAFETY_WR(
        HT_RTC->RTCWR = RTC_RTCWR_CLEAR;                   
                                                        
        HT_RTC->YEARR  = rtc->YY & RTC_YEARR;
        HT_RTC->MONTHR = rtc->MM & RTC_MONTHR;
        HT_RTC->DAYR   = rtc->DD & RTC_DAYR;
        HT_RTC->HOURR  = rtc->hh & RTC_HOURR;
        HT_RTC->MINR   = rtc->mm & RTC_MINR;
        HT_RTC->SECR   = rtc->ss & RTC_SECR;
        HT_RTC->WEEKR  = 1;  //rtc->WeekDay & RTC_WEEKR; //固定写入1，读取时算

        HT_RTC->RTCWR = RTC_RTCWR_UPDATE;                 
    );

    rtc_second = rtc->ss;
    rtc_state.rtc_init = false;
    return true;
}

/**
 * @brief  设置RTC闹钟模式
 * @param  [in ] mode-RTC闹钟模式, 可以是以下值:
 *         关闭闹钟            NONE_ALARM
 *         每秒闹钟(秒中断)    SEC_ALARM
 *         整分闹钟(hh:mm:00)  MIN_ALARM
 *         整时闹钟(hh:00:00)  HOUR_ALARM
 *         整天闹钟(00:00:00)  DAY_ALARM
 * @param  [in ] func-闹钟中断回调函数
 * @retval 0-设置失败
 * @retval 1-设置成功
 */
bool hal_rtc_irq_set(HAL_RTC_ALARM_MODE mode, void func(void))
{
    switch(mode)
    {
        case SEC_ALARM: ///秒中断使能
            HT_RTC->RTCIE = RTC_RTCIE_SECIE;
            break;
        case MIN_ALARM: ///分中断使能
            HT_RTC->RTCIE = RTC_RTCIE_MINIE;
            break;
        case HOUR_ALARM: ///时中断使能
            HT_RTC->RTCIE = RTC_RTCIE_HRIE;
            break;
        case DAY_ALARM: ///天中断使能
            HT_RTC->RTCIE = RTC_RTCIE_DAYIE;
            break;
        default:
            HT_RTC->RTCIE = 0;
            return true;
    }
    HT_RTC->RTCIF = 0;

    second_callback = func;
    irq_vector_set(INT_RTC, irq_handler_rtc);
    NVIC_SetPriority(RTC_IRQn, 3);
    NVIC_EnableIRQ(RTC_IRQn);
    return true;
}

/// @brief 查询是否有RTC中断
/// @param  
/// @return 
bool hal_rtc_irq_query(void)
{
    if(rtc_state.rtc_second)
    {
        rtc_state.rtc_second = false;
        return true;
    }
    return false;
}

/**
 * @brief  写入RTC校准配置值，RTC温补
 * @param  [in ] value-生产矫正值
 * @retval none  此函数未调试
 */
void hal_rtc_cali_reg_wr(int16_t value)
{
    float   coefficient;     // 系数
    float   T;               // 温度    
    float   top_rtc_adj;     // 晶体常温顶点误差
    float   rtc_adj_offset;  // 温度变换带来晶体误差变化
    float   rtc_adj;         // 总误差
    uint32_t reg;            // 寄存器值
    // 采温度
    if(!hal_mcu.stus->power_on_rst) { hal_adc.open_nopower(); }
    hal_adc.start(HAL_ADC_TEMP);
    T = hal_adc.temperature();
    if(!hal_mcu.stus->power_on_rst) { hal_adc.close_nopower(); }

    // 顶点误差
    if(value != 0)    
    {
        // 生产矫正值
        top_rtc_adj = (float)value / 100.0; // 顶点误差 ppm
    }
    else 
    {
        // 烧录矫正值
        top_rtc_adj = 0;
    }
    
    // 温度值去除芯片发热影响
    if(hal_mcu.stus->power_on_rst) {T = T + const_T_OffsetRun;}
    else {T = T + const_T_OffsetSlp;}
    
    // 高温低温采用不同系数
    if(T > const_xtl_top)    // 高温 
    {
        coefficient = const_KH;
    }
    else
    { 
        coefficient = const_KL; 
    }
    rtc_adj_offset = coefficient * (T - const_xtl_top) * (T - const_xtl_top); // 计算出不同温度下晶体误差
    rtc_adj        = top_rtc_adj - rtc_adj_offset;       // 常温顶点误差+不同温度对应的晶体误差

    rtc_adj = rtc_adj * 16.6666667;    // DFi 每个 LSB 表示约 0.06pp
    reg = ((uint32_t)(rtc_adj + 0.5) & 0x001FFFFF);

    if(!(HT_RTC->RTCCON & RTC_RTCCON_AUTOC)) {HT_RTC->RTCCON = RTC_RTCCON_AUTOC; }
    HT_RTC->DFIH = (uint16_t)(reg >> 16);
    HT_RTC->DFIL = (uint16_t)(reg & 0xFFFF);
}

/**
 * @brief  写入RTC校准配置PPM值
 * @param  [in ] value-RTC校准配置值,输入值为PPM值
 * @retval none
 */
int16_t hal_rtc_ppm_wr(int16_t value)
{
    
    return rtc_ppm;
}

/**
 * @brief  读取RTC校准配置PPM值
 * @param
 * @retval none
 */
int16_t hal_rtc_ppm_rd(void)
{
    return rtc_ppm;
}

/**
 * @brief  RTC秒脉冲输出使能控制, 返回时钟状态
 * @param  flag, 1-使能秒脉冲输出，0-关闭输出
 * @retval NULL
 */
void hal_rtc_second_out_set(uint8_t flag)
{
    HT_RTC->RTCCON &= ~(uint32_t)RTC_RTCCON_TOUT;
    HT_RTC->RTCCON |= (uint32_t)RTC_RTCCON_TOUT_1HZ;
    hal_gpio.pulse_out_mode(1);
}

#if (EMU_TYPE==EMU_VIRTUAL)
void hal_rtc_energy_integral_set(void func(void))
{
    energy_integral_callback = func;
}
#endif


/// @brief 声明hal_rtc子模块对象
const struct hal_rtc_t hal_rtc =
{
    .second         = &rtc_second,
    .init           = hal_rtc_init,
    .init_nopower   = hal_rtc_init_nopower,
    .time_get       = hal_rtc_time_get,
    .time_set       = hal_rtc_time_set,
    .irq_set        = hal_rtc_irq_set,
    .irq_query      = hal_rtc_irq_query,
    .cali_reg_wr    = hal_rtc_cali_reg_wr,
    .ppm_rd         = hal_rtc_ppm_rd,
    .ppm_wr         = hal_rtc_ppm_wr,
    .sec_pulse_set  = hal_rtc_second_out_set,
#if (EMU_TYPE==EMU_VIRTUAL)
    .energy_integral_set = hal_rtc_energy_integral_set,
#endif
};


/** @} */
/** @} */
/** @} */
