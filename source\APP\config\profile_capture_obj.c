/********************************************************************************
* @file    profile_capture_obj.h
* <AUTHOR> @date    2024
* @brief   事件，曲线，冻结捕获对象定义， 本文件弃用，698协议考虑启用
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "profile_capture_obj.h"
#include "app_config.h"


#if EVENT_LOSS_VOL_EN || EVENT_LOW_VOL_EN || EVENT_OVR_VOL_EN || EVENT_MISS_VOL_EN        
/// 发生时刻
const uint32_t evt1_str_objlist[] =
{
    C4_DATE_TIME,               /// 6 发生时刻
    C0_POS_kWh(0,0),            /// 4 发生时刻正向有功总电能
    C0_NEG_kWh(0,0),            /// 4 发生时刻反向有功总电能
    C0_CMB1_kvarh(0,0),         /// 4 发生时刻组合无功 1 总电能
    C0_CMB2_kvarh(0,0),         /// 4 发生时刻组合无功 2 总电能
    C0_A_POS_kWh(0,0),          /// 4 发生时刻 A 相正向有功电能
    C0_A_NEG_kWh(0,0),          /// 4 发生时刻 A 相反向有功电能
    C0_A_CMB1_kvarh(0,0),       /// 4 发生时刻 A 相组合无功 1 电能
    C0_A_CMB2_kvarh(0,0),       /// 4 发生时刻 A 相组合无功 2 电能
    C2_A_VOL,                   /// 2 发生时刻 A 相电压
    C2_A_CUR,                   /// 3 发生时刻 A 相电流
    C2_A_INS_P,                 /// 3 发生时刻 A 相有功功率
    C2_A_INS_Q,                 /// 3 发生时刻 A 相无功功率
    C2_A_PF,                    /// 2 发生时刻 A 相功率因数
    C0_B_POS_kWh(0,0),          /// 4 发生时刻 B 相正向有功电能
    C0_B_NEG_kWh(0,0),          /// 4 发生时刻 B 相反向有功电能
    C0_B_CMB1_kvarh(0,0),       /// 4 发生时刻 B 相组合无功 1 电能
    C0_B_CMB2_kvarh(0,0),       /// 4 发生时刻 B 相组合无功 2 电能
    C2_B_VOL,                   /// 2 发生时刻 B 相电压
    C2_B_CUR,                   /// 3 发生时刻 B 相电流
    C2_B_INS_P,                 /// 3 发生时刻 B 相有功功率
    C2_B_INS_Q,                 /// 3 发生时刻 B 相无功功率
    C2_B_PF,                    /// 2 发生时刻 B 相功率因数
    C0_C_POS_kWh(0,0),          /// 4 发生时刻 C 相正向有功电能
    C0_C_NEG_kWh(0,0),          /// 4 发生时刻 C 相反向有功电能
    C0_C_CMB1_kvarh(0,0),       /// 4 发生时刻 C 相组合无功 1 电能
    C0_C_CMB2_kvarh(0,0),       /// 4 发生时刻 C 相组合无功 2 电能
    C2_C_VOL,                   /// 2 发生时刻 C 相电压
    C2_C_CUR,                   /// 3 发生时刻 C 相电流
    C2_C_INS_P,                 /// 3 发生时刻 C 相有功功率
    C2_C_INS_Q,                 /// 3 发生时刻 C 相无功功率
    C2_C_PF,                    /// 2 发生时刻 C 相功率因数
};
const uint8_t  evt1_str_objlist_num = eleof(evt1_str_objlist);

/// @brief 结束时刻
const uint32_t evt1_end_objlist[] =
{

    CA0_AH_T,                   /// 4 期间总安时数
    CA0_AH_A,                   /// 4 期间 A 相安时数
    CA0_AH_B,                   /// 4 期间 B 相安时数
    CA0_AH_C,                   /// 4 期间 C 相安时数
    C74_LAST_POWER_OFF_TIME,    /// 6 结束时刻
    C0_POS_kWh(0,0),            /// 4 结束时刻正向有功总电能
    C0_NEG_kWh(0,0),            /// 4 结束时刻反向有功总电能
    C0_CMB1_kvarh(0,0),         /// 4 结束时刻组合无功 1 总电能
    C0_CMB2_kvarh(0,0),         /// 4 结束时刻组合无功 2 总电能
    C0_A_POS_kWh(0,0),          /// 4 结束时刻 A 相正向有功电能
    C0_A_NEG_kWh(0,0),          /// 4 结束时刻 A 相反向有功电能
    C0_A_CMB1_kvarh(0,0),       /// 4 结束时刻 A 相组合无功 1 电能
    C0_A_CMB2_kvarh(0,0),       /// 4 结束时刻 A 相组合无功 2 电能
    C0_B_POS_kWh(0,0),          /// 4 结束时刻 B 相正向有功电能
    C0_B_NEG_kWh(0,0),          /// 4 结束时刻 B 相反向有功电能
    C0_B_CMB1_kvarh(0,0),       /// 4 结束时刻 B 相组合无功 1 电能
    C0_B_CMB2_kvarh(0,0),       /// 4 结束时刻 B 相组合无功 2 电能
    C0_C_POS_kWh(0,0),          /// 4 结束时刻 C 相正向有功电能
    C0_C_NEG_kWh(0,0),          /// 4 结束时刻 C 相反向有功电能
    C0_C_CMB1_kvarh(0,0),       /// 4 结束时刻 C 相组合无功 1 电能
    C0_C_CMB2_kvarh(0,0),       /// 4 结束时刻 C 相组合无功 2 电能
};
const uint8_t  evt1_end_objlist_num = eleof(evt1_end_objlist);
#endif

#if EVENT_ALL_LOSS_VOL_EN         
/// 全失压事件
    EVENT_TYPE_ALL_LOSS_VOL,   
#endif

#if EVENT_BAK_PWR_LOS_EN          
/// 辅助电源失电事件
   
#endif
#if EVENT_V_REV_SQR_EN            
/// 电压逆向序事件
     
#endif
#if EVENT_I_REV_SQR_EN            
/// 电流逆向序事件

#endif
#if EVENT_V_UNB_EN                
/// 电压不平衡事件

#endif
#if EVENT_I_UNB_EN            
/// 电流不平衡事件

#endif
#if EVENT_LOS_CUR_EN              
/// 失流事件

#endif
#if EVENT_OVR_CUR_EN              
/// 过流事件

#endif
#if EVENT_MISS_CUR_EN             
/// 断流事件

#endif
#if EVENT_REV_EN                  
/// 潮流反向事件

#endif
#if EVENT_OVR_LOAD_EN             
/// 过载事件

#endif
#if EVENT_PWR_DOWN_EN             
/// 掉电事件
const uint32_t evt_pwr_down_objlist[] =
{
    C74_LAST_POWER_OFF_TIME,
    C74_LAST_POWER_ON_TIME,
};
const uint8_t  evt_pwr_down_objlist_num = eleof(evt_pwr_down_objlist);
#endif
#if EVENT_OVR_DM_EN               
/// 超需量事件

#endif
#if EVENT_PROGRAM_EN              
/// 编程事件

#endif
#if EVENT_METER_CLEAN_EN          
/// 电表清零事件

#endif
#if EVENT_DEMAND_CLEAN_EN         
/// 需量清零事件

#endif
#if EVENT_EVENT_CLEAN_EN          
/// 事件清零事件

#endif
#if EVENT_SHITFT_TIME_EN          
/// 校时事件
extern const uint32_t evt_shift_time_objlist[] =
{
    C74_OPT_CODE,
    C4_DATE_TIME,
    C74_LAST_ADJUST_TIME,
};
extern const uint8_t  evt_shift_time_objlist_num = eleof(evt_shift_time_objlist);
#endif
#if EVENT_BC_TIME_EN              
/// 广播校时事件
extern const uint32_t evt_bc_time_objlist[] =
{
    C4_DATE_TIME,
    C74_LAST_BC_TIME,
};
extern const uint8_t  evt_bc_time_objlist_num = eleof(evt_bc_time_objlist);
#endif
#if EVENT_SCHEDULE_EN             
/// 时段表事件

#endif
#if EVENT_ZONE_TAB_EN             
/// 时区表事件

#endif
#if EVENT_WEEKENDS_PGM_EN         
/// 周休日编程事件

#endif
#if EVENT_HOLIDAY_PGM_EN          
/// 节假日表编程事件

#endif
#if EVENT_COMB_kWh_PGM_EN         
/// 有功组合方式编程事件

#endif
#if EVENT_COMB1_kvarh_PGM_EN      
/// 无功组合方式1编程事件

#endif
#if EVENT_COMB2_kvarh_PGM_EN      
/// 无功组合方式2编程事件

#endif
#if EVENT_BL_DAY_PGM_EN           
/// 结算日编程事件

#endif

#if EVENT_METER_COVER_EN          
/// 开表盖事件
const uint32_t evt_meter_cover_objlist[] = 
{
    C4_DATE_TIME,
    C74_METER_COV_CLOSE_TIME,
};
const uint8_t  evt_meter_cover_objlist_num = eleof(evt_meter_cover_objlist);
#endif

#if EVENT_TEM_COVER_EN            
/// 开端盖事件
const uint32_t evt_tem_cover_objlist[] =
{
    C4_DATE_TIME,
    C74_TEM_COV_CLOSE_TIME,
};
const uint8_t  evt_tem_cover_objlist_num = eleof(evt_tem_cover_objlist);  
#endif




