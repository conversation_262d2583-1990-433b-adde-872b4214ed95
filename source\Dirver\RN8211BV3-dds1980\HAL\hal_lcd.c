/**
 ******************************************************************************
 * @file    hal_lcd.c
 * <AUTHOR> @date    2024
 * @brief   lcd驱动头文件
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 ******************************************************************************/
#include <string.h>
#include "hal_lcd.h"
#include "hal_mcu.h"
#include "hal_gpio.h"

#include "rn8xxx_ll_lcd.h"
#include "rn821x_rn721x_soc_lcd.h"

static union
{
    uint8_t  LcdBuffer[MCU_LCD_MEM_LEN];
    uint32_t LcdData[MCU_LCD_MEM_LEN / 4];
};

/* Private constants ---------------------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/* Public functions ----------------------------------------------------------*/
/* @brief 正常供电下，打开LCD设备，LCD显示处理初始化 */
bool hal_lcd_open(void)
{
    uLcdCtrl_TypeDef LCD_InitStruct = {0};
    memset(&LCD_InitStruct, 0x00, sizeof(uLcdCtrl_TypeDef));

    hal_gpio.ext_lcd(GPIO_OPEN);

    LCD_InitStruct.bitLcdCtrl.EN = 1;
    LCD_InitStruct.bitLcdCtrl.DUTY = 3;
    LCD_InitStruct.bitLcdCtrl.BIAS = 0;
    LCD_InitStruct.bitLcdCtrl.BIASLVL = 0;
    LCD_InitStruct.bitLcdCtrl.TYPE = 0;
    LCD_InitStruct.bitLcdCtrl.PWD_PUMP = 0;

    LL_LCD_Init(LCD_InitStruct, 300);

    LCD->CLKDIV = 0x3fU;

    return TRUE;
}

/* @brief 电池供电下，打开LCD设备，LCD显示处理初始化 */
bool hal_lcd_open_nopower(void)
{
    return hal_lcd_open();
}

///@brief Action: 关闭LCD设备，在低功耗时使用
bool hal_lcd_close(void)
{
    uLcdCtrl_TypeDef LCD_InitStruct = {0};
    memset(&LCD_InitStruct, 0x00, sizeof(uLcdCtrl_TypeDef));
    LCD_InitStruct.bitLcdCtrl.EN = 0;
    LL_LCD_Init(LCD_InitStruct, 0);
    return TRUE;
}

///@brief Action:  点亮所有SEG
void hal_lcd_all_seg_light(void)
{
    register uint8_t i;
    for(i = 0; i < MCU_LCD_MEM_LEN; i++) LcdBuffer[i] = 0xFF;
}

///@brief Action:  熄灭所有SEG
void hal_lcd_all_seg_clear(void)
{
    register uint8_t i;
    for(i = 0; i < MCU_LCD_MEM_LEN; i++) LcdBuffer[i] = 0x00;
}

///@brief Action: LCD段码位填充
///@brief Input:  com_seg-段码位索引, mask 0-灭 1-亮
///@brief Output:
void hal_lcd_fill_com_seg(uint16_t com_seg, uint8_t mask)
{
    if(com_seg == 0) return;
    com_seg   = com_seg - 1;    // 显示段数值定义做了加1的非0处理
    uint8_t x = 1 << (com_seg % 8);
    uint8_t y = com_seg / 8;

    if(y >= sizeof(LcdBuffer)) return;
    if(mask & 0x01) { LcdBuffer[y] |= x; }
    else { LcdBuffer[y] &= ~x; }
}

///@brief Action: 刷新LCD显存
void hal_lcd_refresh(void)
{
    for(uint16_t i = 0; i < MCU_LCD_MEM_LEN; i++) { MCU_LCD_MEM_PTR[i] = LcdBuffer[i]; }
}

/// @brief 声明hal_lcd子模块对象
const struct hal_lcd_s hal_lcd = {
    .open         = hal_lcd_open,
    .open_nopower = hal_lcd_open_nopower,
    .close        = hal_lcd_close,
    .all_seg_set  = hal_lcd_all_seg_light,
    .all_seg_clr  = hal_lcd_all_seg_clear,
    .light        = hal_lcd_fill_com_seg,
    .refresh      = hal_lcd_refresh,
};

// end of hal_lcd.c
