/**
 ******************************************************************************
 * @file    boot_entry.h
 * <AUTHOR> @date    2025
 * @brief   本文件为boot区的函数入口符号定义
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __BOOT_ENTRY_H
#define __BOOT_ENTRY_H

#include "typedef.h"
#include "boot_cfg.h"

/// BOOT模式定义
#define MODE_RUNING_APP 0xAAAA        /// 运行应用程序Programm
#define MODE_RUNING_ISP 0x5555        /// 运行ISP升级模式
#define MODE_RUNING_IAP 0x3333        /// 运行IAP升级模式
#define MODE_FILE_TR_ERR 0x6666       /// 文件传输错误
#define MODE_FILE_CRC_ERR 0x7777      /// 文件校验错误
#define MODE_IAP_ERR 0xCCCC           /// 编程错误 -- 不可以运行原来程序

/// @brief  APP程序文件头定义
struct image_header_t
{
    uint32_t base_addr;          // 程序起始地址
    uint32_t size;               // 程序大小
    const char* identifier;      // 固件标识地址
    const char* version;         // 程序版本号地址
    union
    {
        uint8_t* checksum_addr;  // 程序校验和地址
        uint32_t checksum;       // 程序校验crc32
    };
    uint32_t verify_tag;         // APP文件头合法性校验
};

/// @brief  BOOT升级信息域
struct boot_info_t
{
    uint16_t mode;               ///升级模式
    uint8_t  stamp[6];           ///升级时间戳
    uint32_t image_size;         ///升级固件大小
    uint8_t  image_signature[SIGNATURE_MAX_LEN];    ///升级固件签名
    char     image_identifier[IDENTIFIER_MAX_LEN];  ///升级固件标识
};

/// @brief  BOOT模块API接口，不可随意改动
#if !defined(__ICC78K__)
#define __near_func
#endif
struct boot_api_s
{
    /// @brief  BOOT模块标识符
    const char* boot_identifier;

    /// @brief  BOOT升级信息域地址
    const struct boot_info_t* boof_info;

    /// @brief  设置中断向量
    void (__near_func *intvec_set)(int irq, void (__near_func *vec)(void));

#if IAP_SUPPORT
    /// @brief  IAP升级，可用于应用层
    char (__near_func *iap_process)(void);
#endif

    /// 
    bool (__near_func *mcu_flash_read)(uint32_t ofst, void* pdat, uint16_t num);
    bool (__near_func *mcu_flash_write)(uint32_t ofst, const void* pdat, uint16_t num);
};

#endif /* __BOOT_ENTRY_H */

