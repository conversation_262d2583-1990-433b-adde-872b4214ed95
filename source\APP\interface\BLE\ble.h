/**
 ******************************************************************************
* @file    ble.h
* <AUTHOR> @date    2024
* @brief  
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __BLE_H__
#define __BLE_H__

#include "typedef.h"

#define BLE_M_AC6368A 1   // 使用AC6368A蓝牙芯片
#define BLE_M_WS8201  2   // 使用WS8201蓝牙芯片
#define BLE_M_PHY6252  3   // 使用WS8201蓝牙芯片

#define BLE_M_TYPE BLE_M_PHY6252  // 当前使用的蓝牙芯片类型

struct ble_s
{
    /// @brief  初始化蓝牙模块
    void (*init)(uint8* rxbuf, uint16_t bufsize);
    /// @brief  蓝牙模块接收数据
    uint16_t (*recv)(void);
    /// @brief  蓝牙模块发送数据
    void (*send)(uint8_t *msg, uint16_t len);
    /// @brief  蓝牙模块是否发送完成
    bool (*send_over_query)(void);
};
extern const struct ble_s ble;

#endif /* __BLE_H__ */
