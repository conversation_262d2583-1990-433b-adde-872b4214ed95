{"folders": [{"path": "."}], "settings": {"files.associations": {"utils.h": "c", "status.h": "c", "typedef.h": "c", "module_para.h": "c", "debug.h": "c", "api.h": "c", "gprs_4g.h": "c", "remote_module.h": "c", "hal_def.h": "c", "timeapp.h": "c", "dcu.h": "c", "qgdw10376.h": "c", "protocol.h": "c", "bsp.h": "c", "ble.h": "c", "app_config.h": "c", "intrinsics.h": "c", "ringbuf.h": "c", "__config": "c", "initializer_list": "c", "type_traits": "c", "stdio.h": "c", "datastore.h": "c", "string.h": "c", "hal_timer.h": "c", "bsp_cfg.h": "c", "boot_cfg.h": "c", "ver.h": "c", "image_transfer_api.h": "c", "crc.h": "c", "boot_api.h": "c", "app.h": "c", "dlt645_2007.h": "c", "hal_gpio.h": "c", "hal_mcu.h": "c", "ddl.h": "c", "interrupts_hc32l19x.h": "c", "hc32l19x.h": "c", "stdint.h": "c", "sysctrl.h": "c", "cmsis_compiler.h": "c", "cmsis_gcc.h": "c", "cmsis_version.h": "c", "cmsis_iccarm.h": "c", "system_hc32l19x.h": "c", "board_stkhc32l19x.h": "c", "base_types.h": "c", "hc32l19x_adc.h": "c", "boot_entry.h": "c", "stdbool.h": "c", "bsp_lcd.h": "c", "lcd.h": "c", "hal_inc.h": "c", "hal_flash.h": "c", "ycheck.h": "c", "yvals.h": "c", "*.inc": "c", "eeprom.h": "c"}}}