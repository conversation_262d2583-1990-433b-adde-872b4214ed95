/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      A_XDR.c
 *    Describe:      A-XDR 编码规则
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#include "utils.h"
#include "A_XDR.h"


/// @brief 编码一个对象
/// @param out 输出缓冲区
/// @param ic  类型
/// @param id  数据id
/// @param attr  
/// @param data_idx 
/// @return 
uint8_t  a_xdr_encode_obj(uint8_t * out, uint16_t ic, const uint8_t * id, uint8_t  attr, uint16_t data_idx)
{
    *out++ = DT_STRUCTURE, *out++ = 4;
    *out++ = DT_LONG_UNSIGNED;
    *out++ = (uint8_t )(ic >> 8), *out++ = (uint8_t )ic;
    *out++ = DT_OCTET_STRING, *out++ = 6;
    memcpy(out, id, 6), out += 6;
    *out++ = DT_INTEGER;
    *out++ = attr;
    *out++ = DT_LONG_UNSIGNED;
    *out++ = (uint8_t )(data_idx >> 8), *out++ = (uint8_t )data_idx;
    return 18;
}

/// @brief 编码一个空对象
/// @param out 输出缓冲区
uint8_t  a_xdr_encode_null_sorta_xdr_encode_null_sort(uint8_t * out)
{
    *out++ = DT_STRUCTURE, *out++ = 4;
    *out++ = DT_LONG_UNSIGNED;
    *out++ = 0, *out++ = 0;
    *out++ = DT_OCTET_STRING, *out++ = 6;
    memset(out, 0, 6), out += 6;
    *out++ = DT_INTEGER;
    *out++ = 0;
    *out++ = DT_LONG_UNSIGNED;
    *out++ = 0, *out++ = 0;
    return 18;
}
/* public function:
    a_xdr_en_scaler_unit()
    Using A-XDR rule to encode a scaler_unit data
    the out buff used to save an encoded data
    in buff value which need be encoded
    export result to the output address, return result length
*/

/// @brief 使用A-XDR规则编码一个scaler unit数据
/// @param out     输出缓冲区
/// @param scaler  换算因子
/// @param unit    单位
/// @return 
uint8_t  a_xdr_en_scaler_unit(uint8_t * out, int8_t scaler, class_unit_t unit)
{
    out[0] = DT_STRUCTURE;
    out[1] = 2;
    out[2] = DT_INTEGER;
    out[3] = scaler;
    out[4] = DT_ENUM;
    out[5] = unit;
    return 6;
}

/// @brief 使用A-XDR规则编码ASN.1数据长度
/// @param out 输出缓冲区
/// @param len  数据长度
/// @return  
uint8_t  a_xdr_en_asn1_len(uint8_t * out, uint16_t len)
{
    if(len < 128)
    {
        out[0] = (uint8_t )len;
        return 1;
    }
    else if(len < 256)
    {
        out[0] = 0x81;
        out[1] = (uint8_t )len;
        return 2;
    }
    else if(len < 32768)
    {
        out[0] = 0x82;
        out[1] = (uint8_t )(len >> 8);
        out[2] = (uint8_t )len;
        return 3;
    }
    return 0;
}

/// @brief 使用A-XDR规则解码ASN.1数据长度
/// @param out_len  输出数据长度
/// @param in       输入缓冲区
/// @return  
uint8_t  a_xdr_de_asn1_len(uint16_t* out_len, const uint8_t * in)
{
    if(in[0] < 128)
    {
        *out_len = in[0];
        return 1;
    }
    else if(0x81 == in[0])
    {
        *out_len = in[1];
        return 2;
    }
    else if(0x82 == in[0])
    {
        *out_len = get_msbdata16(&in[1]);
        return 3;
    }
    return 0;
}

/// @brief 获取数据类型长度
/// @param in         输入缓冲区
/// @param encode_len 编码长度  
/// @return           数据长度
uint16_t data_type_len_get(uint8_t * in, uint8_t * encode_len)
{
    uint16_t length = 0;

    switch(*in)
    {
        case DT_BOOLEAN:              // BOOLEAN
        case DT_ENUM:                 // Enum
        case DT_INTEGER:              // Integer8
        case DT_INTEGER_UNSIGNED:     // Unsigned8
            length = 1;
            break;
        case DT_LONG:                 // Integer16
        case DT_LONG_UNSIGNED:        // Unsigned16
            length = 2;
            break;
        case DT_DOUBLE_LONG:          // Integer32
        case DT_DOUBLE_LONG_UNSIGNED: // Unsigned32
            length = 4;
            break;
        case DT_BIT_STRING:
        case DT_VISIBLE_STRING:
        case DT_OCTET_STRING:
            *encode_len = a_xdr_de_asn1_len(&length, in + 1);
            break;
        case DT_DATE:
            *encode_len = 1;
            length = 6;
            break;
        case DT_TIME:
            *encode_len = 1;
            length = 5;
            break;
        default:
            length = 0;
            break;
    }

    return length;
}

/// @brief 使用A-XDR规则编码紧凑数组
/// @param in_out   输入输出缓冲区
/// @param len      输入输出缓冲区长度
/// @param isheader 是否为头部数据
// /// @return          编码后数据长度
// uint16_t a_xdr_en_compact_array(uint8_t * in_out, uint16_t len, bool isheader)
// {
//     #define COMPACT_ARRAY_HEAD_LEN 256

//     bool first_time = true;
//     uint8_t  remain_buf[COMPACT_ARRAY_HEAD_LEN];     // 考虑从外面传递临时缓冲，可能更安全
//     uint8_t * pdatatype = NULL;
//     uint8_t * phead = remain_buf;
//     uint8_t * out = in_out;
//     uint8_t * in = in_out;
//     int16  tmplen;
//     uint16_t struct_num = 1, array_num = 0;
//     uint32 datalen;

//     if(in_out == NULL) return 0;

//     // the header data must have one complete array element data at least.!!!
//     if(isheader)
//     {
//         if(*in++ != DT_ARRAY) return 0;
//         in += a_xdr_de_asn1_len(&array_num, in);
//         tmplen = len - (int16)(in - in_out);
//         if(array_num == 0 || tmplen < 0) return 0;
//         *phead++ = DT_COMPACT_ARRAY;  // tag
//         *phead++ = *in;               // data type
//     }

//     while(1)
//     {
//         tmplen = len - (uint16_t)(in - in_out);
//         if(tmplen <= 0) break;

//         if(*in == DT_STRUCTURE)
//         {
//             in++;
//             in += a_xdr_de_asn1_len(&struct_num, in);
//             tmplen = len - (uint16_t)(in - in_out);
//             if(struct_num == 0 || tmplen < 0) return 0;

//             if(isheader && first_time)
//             {
//                 phead += a_xdr_en_asn1_len(phead, struct_num);
//                 pdatatype = phead; // record to fill the struct element data type
//                 phead += struct_num;
//                 if((uint16_t)(phead + 5 - remain_buf) > COMPACT_ARRAY_HEAD_LEN)
//                     return 0;     // the struct element num is to large!
//             }
//         }

//         uint16_t i = 0;
//         for(i = 0; i < struct_num; i++)
//         {
//             uint8_t  encode_len = 0;
//             datalen = data_type_len_get(in, &encode_len);
//             if(datalen == 0 && encode_len == 0) return 0; // the error data encode

//             if(pdatatype != NULL) *pdatatype++ = *in;      //  encode the struct element data type

//             memcpy(out, in + 1, datalen + encode_len);     // encode data
//             in += (encode_len + 1 + datalen);
//             out += (encode_len + datalen);

//             tmplen = len - (uint16_t)(in - in_out);
//             if(tmplen < 0) return 0;
//         }
//         if(i != struct_num) return 0;              // the data is not a complete array element data

//         if(isheader && first_time)
//         {
//             datalen = (uint32)(out - in_out);      // one array element data length
//             datalen = datalen * array_num;
//             phead += a_xdr_en_asn1_len(phead, datalen); // encode compact array total data length
//             first_time = false;
//         }
//     }

//     if(isheader)
//     {
//         datalen = (uint32)(out - in_out);
//         uint32 tmp = (uint32)(phead - remain_buf);
//         for(uint16_t j = 1; j <= datalen; j++)
//         {
//             in_out[tmp + datalen - j] = in_out[datalen - j];
//         }
//         memcpy(in_out, remain_buf, tmp);
//         out += tmp;
//     }

//     return (uint16_t)(out - in_out);
// }

/// @brief 使用A-XDR规则解码紧凑数组
/// @param in_out     输入输出缓冲区
/// @param len        输入缓冲区长度
/// @param max_buf_len 最大缓冲区长度 
// /// @return          解码后数据长度
// uint16_t a_xdr_compact_array(uint8_t * in_out, uint16_t len, uint16_t max_buf_len)
// {
// #define DATA_RESULT_BUF_SIZE 1500
//     uint8_t   tmp_buf[DATA_RESULT_BUF_SIZE]; // 搬移数据方式实现，执行复杂度太高
//     uint8_t * out = tmp_buf;
//     uint8_t * in = in_out;
//     uint8_t * pdatatype;
//     uint8_t   data_type;
//     int16  tmplen;
//     uint16_t struct_num = 1, array_num = 0;
//     uint32 datalen = 0, one_entry_len = 0;

//     if(in_out == NULL || len < 3) return 0;

//     if(*in++ != DT_COMPACT_ARRAY) return 0;
//     data_type = *in;               // data type
//     pdatatype = in++;
//     if(data_type == DT_STRUCTURE)
//     {
//         in += a_xdr_de_asn1_len(&struct_num, in);
//         pdatatype = in;
//         in += struct_num;
//         tmplen = len - (uint16_t)(in - in_out);
//         if(struct_num == 0 || tmplen < 0) return 0;
//     }

//     uint16_t tmp1 = 0;
//     in += a_xdr_de_asn1_len(&tmp1, in);
//     if(tmp1 == 0)
//     {
//         if(0x84 == in[0])
//         {
//             datalen = get_msbdata32(&in[1]);
//             in += 5;
//         }
//     }
//     else datalen = tmp1;

//     tmplen = len - (uint16_t)(in + datalen - in_out);
//     if(datalen == 0 || tmplen < 0) return 0;

//     *out++ = DT_ARRAY;
//     out += 5; // 预留编码数组长度

//     while(1)
//     {
//         tmplen = len - (uint16_t)(in - in_out);
//         if(tmplen <= 0) break;

//         if(data_type == DT_STRUCTURE)
//         {
//             if(((uint16_t)(out - tmp_buf) + 6) > DATA_RESULT_BUF_SIZE)
//                 return 0;
//             *out++ = DT_STRUCTURE;
//             out += a_xdr_en_asn1_len(out, struct_num);
//         }

//         uint16_t i = 0;
//         one_entry_len = 0;
//         for(i = 0; i < struct_num; i++)
//         {
//             uint8_t  encode_len = 0;
//             if(pdatatype[i] == DT_BIT_STRING || pdatatype[i] == DT_VISIBLE_STRING
//             || pdatatype[i] == DT_OCTET_STRING)
//             {
//                 uint16_t tmp2 = 0;
//                 encode_len = a_xdr_de_asn1_len(&tmp2, in);
//                 datalen = tmp2;
//             }
//             else
//             {
//                 datalen = data_type_len_get(pdatatype + i, &encode_len);
//             }
//             if(datalen == 0 && encode_len == 0)
//                 return 0; // the error data encode
//             if(((uint16_t)(out - tmp_buf) + (encode_len + datalen + 1)) > DATA_RESULT_BUF_SIZE)
//                 return 0;
//             *out++ = pdatatype[i];
//             memcpy(out, in, datalen + encode_len);     // encode data
//             in += (encode_len + datalen);
//             out += (encode_len + datalen + 1);
//             one_entry_len += (encode_len + datalen + 1);

//             tmplen = len - (uint16_t)(in - in_out);
//             if(tmplen < 0) return 0;
//         }
//         if(i != struct_num) return 0;              // the data is not a complete array element data
//     }

//     array_num = datalen / one_entry_len;
//     one_entry_len = a_xdr_en_asn1_len(&tmp_buf[1], array_num);
//     datalen = 5 - one_entry_len;
//     memcpy(&tmp_buf[datalen + 1], &tmp_buf[6], (uint16_t)(out - &tmp_buf[6]));
//     datalen = one_entry_len + 1 + (uint16_t)(out - &tmp_buf[6]);
//     memcpy(in_out, tmp_buf, datalen);

//     return (uint16_t)datalen;
// }

/// @brief 使用A-XDR规则编码位串
/// @param out     输出缓冲区
/// @param in      输入缓冲区
/// @param bits_len 位串长度
/// @return 编码后数据长度
uint16_t a_xdr_en_bit_string(uint8_t * out, const void* in, uint16_t bits_len)
{
    uint8_t * pout = out;
    const uint8_t * pin = (uint8_t *)in;
    *out++ = DT_BIT_STRING, out += a_xdr_en_asn1_len(out, bits_len);
    bits_len = (bits_len + 7) / 8;
    while(bits_len-- > 0)
    {
        *out++ = byte_reverse(*pin++);
    }
    return (uint16_t)(out - pout);
}

/// @brief 使用A-XDR规则编码八位串
/// @param out 输出缓冲区 
/// @param in  输入缓冲区 
/// @param len 输入缓冲区长度 
/// @return 编码后数据长度 
uint16_t a_xdr_encode_octet_string(uint8_t * out, const void* in, uint16_t len)
{
    uint8_t * pout = out;
    *out++ = DT_OCTET_STRING, out += a_xdr_en_asn1_len(out, len);
    memcpy(out, in, len);
    out += len;
    return (uint16_t)(out - pout);
}

/// @brief 使用A-XDR规则编码可见字符串
/// @param out 输出缓冲区 
/// @param in  输入缓冲区 
/// @param len 输入缓冲区长度 
/// @return     编码后数据长度
uint16_t EncodeVisibleString(uint8_t * out, const void* in, uint16_t len)
{
    uint8_t * pout = out;
    *out++ = DT_VISIBLE_STRING, out += a_xdr_en_asn1_len(out, len);
    memcpy(out, in, len), out += len;
    return (uint16_t)(out - pout);
}

/// @brief 使用A-XDR规则编码日期时间
/// @param out 输出缓冲区 
/// @param in 输入缓冲区 
/// @return 编码后数据长度 
uint8_t  a_xdr_en_datetime(uint8_t * out, const void* in)
{
    *out++ = DT_DATE_TIME;
    memcpy(out, in, 12);
    return 13;
}

/// @brief 使用A-XDR规则编码日期
/// @param out 输出缓冲区 
/// @param in  输入缓冲区 
/// @return    编码后数据长度
uint8_t  a_xdr_en_date(uint8_t * out, const void* in)
{
    *out++ = DT_DATE;
    memcpy(out, in, 5);
    return 6;
}

/// @brief 使用A-XDR规则编码时间
/// @param out 输出缓冲区 
/// @param in  输入缓冲区 
/// @return   编码后数据长度
uint8_t  a_xdr_time(uint8_t * out, const void* in)
{
    *out++ = DT_TIME;
    memcpy(out, in, 4);
    return 5;
}

/// @brief 使用A-XDR规则编码数据
/// @param out 输出缓冲区 
/// @param asn_data_type 数据类型 
/// @param in  输入缓冲区 
/// @return 编码后数据长度
uint16_t a_xdr_en_data(uint8_t * out, DLT698_data_type_t asn_data_type, const void* in)
{
    switch(asn_data_type)
    {
        case DT_BOOLEAN:              // BOOLEAN
        case DT_ENUM:                 // Enum
        case DT_INTEGER:              // Integer8
        case DT_INTEGER_UNSIGNED:     // Unsigned8
        *out++ = asn_data_type;
        *out = *(uint8_t *)in;
        return 2;

        case DT_LONG:                 // Integer16
        case DT_LONG_UNSIGNED:        // Unsigned16
        *out++ = asn_data_type;
        set_msbdata16(out, *(uint16_t*)in);
        return 3;

        case DT_DOUBLE_LONG:          // Integer32
        case DT_DOUBLE_LONG_UNSIGNED: // Unsigned32
        case DT_FLOAT32:
        *out++ = asn_data_type;
        set_msbdata32(out, *(uint32*)in);
        return 5;

        case DT_FLOAT64:              // Double64
        case DT_LONG64:
        case DT_LONG64_UNSIGNED:
        *out++ = asn_data_type;
        set_msbdata32(out, *((uint32*)in + 1));
        set_msbdata32(out + 4, *(uint32*)in);
        return 9;

        case DT_BIT_STRING:
        return a_xdr_en_bit_string(out, (uint8_t *)in + 1, *(uint8_t *)in);

        case DT_OCTET_STRING:
        return a_xdr_encode_octet_string(out, (uint8_t *)in + 1, *(uint8_t *)in);

        case DT_VISIBLE_STRING:
        return EncodeVisibleString(out, (uint8_t *)in + 1, *(uint8_t *)in);

        case DT_DATE_TIME:
        return a_xdr_en_datetime(out, in);

        case DT_DATE:
        return a_xdr_en_date(out, in);

        case DT_TIME:
        return a_xdr_time(out, in);

        case DT_NULL_DATA:
        *out = DT_NULL_DATA;
        return 1;

        default: return 0;
    }
}

/// @brief 使用A-XDR规则解码数据
/// @param in_out 输入输出缓冲区 
/// @param in_len 输入缓冲区长度 
/// @return       解码后数据长度
uint16_t a_xdr_de_data(uint8_t * in_out, uint16_t in_len)
{
    uint8_t * in  = in_out;
    uint8_t * out = in_out;

    if((DT_NULL_DATA != *in) && (in_len <= 1)) return 0;
    while(in_len--)
    {
        switch(*in++)
        {
            case DT_BOOLEAN:              // BOOLEAN
            case DT_ENUM:                 // Enum
            case DT_INTEGER:              // Integer8
            case DT_INTEGER_UNSIGNED:     // Unsigned8
            if(in_len < 1) return 0;
            *out = *in;
            out++, in++, in_len--;
            break;

            case DT_LONG:                 // Integer16
            case DT_LONG_UNSIGNED:        // Unsigned16
            if(in_len < 2) return 0;
            memcpy(out, in, 2);
            out += 2, in += 2, in_len -= 2;
            break;

            case DT_DOUBLE_LONG:           // Integer32
            case DT_DOUBLE_LONG_UNSIGNED:  // Unsigned32
            case DT_FLOAT32:
            case DT_TIME:
            if(in_len < 4) return 0;
            memcpy(out, in, 4);
            out += 4, in += 4, in_len -= 4;
            break;

            case DT_BIT_STRING:
            {
                uint16_t bit_len = 0;             // bit len
                uint8_t  tmp = a_xdr_de_asn1_len(&bit_len, in);
                bit_len = (bit_len + 7) / 8;    // bit len, 根据位个数计算字节个数
                in += tmp, in_len -= tmp;
                if(in_len < bit_len) return 0;
                for(uint8_t  i = 0; i < bit_len; i++)
                {
                    //*out++ = byte_reverse(*in++);
                    *out++ = *in++;             // 字节内不翻转
                }
                in_len -= bit_len;
                break;
            }

            case DT_OCTET_STRING:
            case DT_VISIBLE_STRING:
            {
                uint16_t str_len = 0;
                uint8_t  tmp = a_xdr_de_asn1_len(&str_len, in); // string len
                in += tmp, in_len -= tmp;
                if(in_len < str_len) return 0;
                memcpy(out, in, str_len);
                out += str_len, in += str_len, in_len -= str_len;
                break;
            }

            case DT_DATE_TIME:
            if(in_len < 12) return 0;
            memcpy(out, in, 12);
            out += 12, in += 12, in_len -= 12;
            break;

            case DT_DATE:
            if(in_len < 5) return 0;
            memcpy(out, in, 5);
            out += 5, in += 5, in_len -= 5;
            break;

            case DT_ARRAY:
            case DT_STRUCTURE:
            if(in_len < 2) return 0;
            in++, in_len--;
            break;

            case DT_NULL_DATA: break;

            case DT_LONG64:
            case DT_LONG64_UNSIGNED:
            case DT_FLOAT64:
            if(in_len < 8) return 0;
            memcpy(out, in, 8);
            out += 8, in += 8, in_len -= 8;
            break;

            case DT_UNDEF:
            default: return 0;  // don't support
        }
    }
    return (uint16_t)(out - in_out);
}

/// @brief 根据A-XDR规则 ，指针指向下一个数据
/// @param in 输入缓冲区 
/// @return   当前数据长度
uint16_t DataLenQuery(const uint8_t * in)
{
    const uint8_t * pin = in;

    switch(*in++)
    {
        case DT_BOOLEAN:              // BOOLEAN
        case DT_ENUM:                 // Enum
        case DT_INTEGER:              // Integer8
        case DT_INTEGER_UNSIGNED:     // Unsigned8
        in += 1;
        break;

        case DT_LONG:                 // Integer16
        case DT_LONG_UNSIGNED:        // Unsigned16
        in += 2;
        break;

        case DT_FLOAT32:              // Float32
        case DT_DOUBLE_LONG:          // Integer32
        case DT_DOUBLE_LONG_UNSIGNED: // Unsigned32
        in += 4;
        break;

        case DT_FLOAT64:              // Double64
        case DT_LONG64:
        case DT_LONG64_UNSIGNED:
        in += 8;
        break;

        case DT_BIT_STRING:
        in += (*in + 7) / 8 + 1;
        break;

        case DT_OCTET_STRING:
        case DT_VISIBLE_STRING:
        {
            uint16_t str_len = 0;
            in += a_xdr_de_asn1_len(&str_len, in); // string len
            in += str_len;
            break;
        }

        case DT_DATE_TIME:
        in += 12;
        break;

        case DT_DATE:
        in += 5;
        break;

        case DT_TIME:
        in += 4;
        break;

        case DT_ARRAY:
        case DT_STRUCTURE:
        {
            uint16_t num = 0;
            in += a_xdr_de_asn1_len(&num, in);
            while(num--)
            {
                in += DataLenQuery(in); // recursive transfer
            }
            break;
        }

        default: break;
    }
    return (uint16_t)(in - pin);
}





























