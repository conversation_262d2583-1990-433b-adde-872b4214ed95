/******************************************************************************
 * @file    dcu.h
 * <AUTHOR> @date    2024
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#ifndef __DCU_H__
#define __DCU_H__

#include "typedef.h"
#include ".\REMOTE_MODULE\remote_module.h"

#define HTTP_UPGRADE_ENABLE true    // 是否启用HTTP升级功能

/// 通讯模块类型定义
#define MOD_TYPE_NBIOT 0x03          // NB-IOT 物联网
#define MOD_TYPE_4G 0x04             // 4G 通讯
#define MOD_TYPE_WIRELESS_RF 0x05    // 无线RF 通讯

#define DCU_DATA_BUF_SIZE 512    // DCU数据缓冲区大小

/// @brief DCU状态
typedef union
{
    struct
    {
        uint8_t reset : 1;            // 复位通讯模块标志
        uint8_t http_download : 1;    // http 下载状态
        uint8_t mod_insert : 1;       // 模块插入标志
        uint8_t login_ok : 1;         // 登录状态
        uint8_t login_req : 1;        // 登录请求
        uint8_t heartbeat_req : 1;    // 心跳包发送需求
        uint8_t heartbeat_ok : 1;     // 心跳包正确响应
        uint8_t forward : 1;          // 透明转发标志
    };
    uint8_t value;
} dcu_state_s;

/// @brief 网络状态定义
typedef enum
{
    NET_NO_CARD = 0,     // 无卡
    NET_INIT,            // 初始化
    NET_REGSITERING,     // 注册中
    NET_CONNECTED,       // 已连接
    NET_FAULT,           // 故障
    NET_TXRX,            // 收发数据
    NET_IDLE,            // 空闲
    NET_DISCONNECT,      // 断开连接
    NET_UNKNOW = 255,    // 未知状态
} NET_STATUS_t;

/// @brief 通讯模块错误信息定义
typedef union
{
    struct
    {
        uint8_t com_error : 1;          // 串口错误
        uint8_t sim_error : 1;          // SIM卡错误
        uint8_t network_error : 1;      // 网络错误
        uint8_t connected_error : 1;    // 登录主站错误
        uint8_t net_time_ok : 1;        // 网络时间可用
    };
    uint8_t error_code;    // 错误码
} CM_ERROR_s;

/// @brief 通讯模块状态定义
typedef struct
{
    uint32_t     cid;             // cell_id-基站ID
    NET_STATUS_t status;          // 通讯模块状态
    CM_ERROR_s   error;           // 模块错误码
    uint8_t      signal_rsq;      // 信号强度
    uint8_t      signal_level;    // 信号等级
    uint8_t      m_type;          // 通讯模块类型
} CM_STATUS_s;

typedef struct transparent_forward_struct
{
    uint8_t *buf;        // 数据缓冲区
    uint16_t len;        // 数据长度
    uint16_t timeout;    // 等待超时时间
    uint8_t  com;        // 通讯端口
    uint8_t  format;     // 数据格式
    uint8_t  baud;       // 波特率
} transparent_forward_s;

/// @brief 模块参数定义
typedef enum
{
    CM_MEDIA_TYPE,          /// 获取模块媒介类型
    CM_SIGNAL_LEVEL,        /// 获取模块信号强度
    CM_NET_STATUS,          /// 获取通讯模块网络状态
    CM_EVENT_RCD_STATUS,    /// 获取通讯模块事件记录状态
    CM_EVENT_INS_STATUS,    /// 获取通讯模块事件瞬时状态
    CM_NET_TIME,            /// 获取通讯模块网络时间
    RESET_CM = 255,         /// 复位模块
} DCU_SYNC_TYPE_t;

/// @brief DCU事件定义
typedef uint16_t TYPE_DCU_EVT_OUT;
#define EVT_MOD_HW_RST ((TYPE_DCU_EVT_OUT)(1 << 1))
#define EVT_MOD_SW_RST ((TYPE_DCU_EVT_OUT)(1 << 2))
#define EVT_MOD_INIT_FAILURE ((TYPE_DCU_EVT_OUT)(1 << 3))
#define EVT_USER_INIT_FAILURE ((TYPE_DCU_EVT_OUT)(1 << 4))
#define EVT_SIM_CARD_FAILURE ((TYPE_DCU_EVT_OUT)(1 << 5))
#define EVT_SIM_CARD_OK ((TYPE_DCU_EVT_OUT)(1 << 6))
#define EVT_SIGNAL_LOW ((TYPE_DCU_EVT_OUT)(1 << 7))
#define EVT_MODULE_REMOVE ((TYPE_DCU_EVT_OUT)(1 << 8))
#define EVT_MODULE_INSERT ((TYPE_DCU_EVT_OUT)(1 << 9))

struct dcu_s
{
    const dcu_state_s *state;    // DCU状态
    void (*init)(void);
    char (*process)(void);
    void (*reset)(uint8_t type);
    bool (*state_query)(TYPE_DCU_EVT_OUT state);
    void (*state_clr)(void);
    void (*state_set)(TYPE_DCU_EVT_OUT state);
    uint16_t (*para_get)(uint8_t *buf);
    uint16_t (*para_set)(void);
    uint16_t (*para_act)(void);
    uint32_t (*sync)(DCU_SYNC_TYPE_t type, void *dat);
    void (*lastgasp)(uint8_t typ);
    /// @brief 链路测试确认，1表示登录，0表示心跳
    void (*link_ack)(uint8_t typ);

    /// @brief 透明转发设置
    /// @param buf 需要转发的数据缓冲区
    /// @param len 数据长度
    /// @param com 通讯端口
    /// @param format 端口格式
    /// @param baud 波特率
    /// @param timeout 等待超时时间
    void (*transparent_set)(uint8_t *buf, uint16_t len, uint8_t com, uint8_t format, uint8_t baud, uint16_t timeout);
};

extern const struct dcu_s dcu;
#endif    // __DCU_H__
