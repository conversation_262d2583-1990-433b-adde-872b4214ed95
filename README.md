### 1. 项目工程文件夹说明

项目工程文件夹命名： **`DTZY1980\MCU型号\   (项目名\MCU型号\)`**

### 2. 项目工程说明

推荐在 [Visual Studio Code](https://code.visualstudio.com/) 开发环境中进行源码编辑，还可以进行项目工程编译、调试等。

#### 2.1 目标程序空间分配

所有项目工程采用LOW优化等级，最终生成目标程序空间分配如下图所示：

INFO:为用于存储电表参数、掉电保存数据、升级信息的一部分程序空间。

```c

 ____________  0x00000000

|    BOOT    | 16K

|____________| 0x00004000

|    APP     | 220K

|____________| 0x00008000

|    REV     | 1K, 预留暂未使用

|____________| 0x00008000

|    INFO    | 16K

|____________| 0x0003FFFF

```

#### 2.2  BOOT工程

目标文件：**`\Boot\Exe\boot.bin`**

BOOT一般分配在程序空间的开始位置（0地址），当MCU复位或产生中断后，都是从复位或向量地址开始运行，该地址位于BOOT区。 因此当产生复位和中断时，BOOT区需要根据情况跳转到相应的应用程序以及中断服务应用处理中运行。如果有升级任务，将会执行升级包下载（YMODEM）或者把准备好的升级程序编程到MCU的CODE FLASH中。

BOOT建议设计在**16K** ROM空间以内，占用RAM资源取决于中断向量表的大小。

#### 2.3  APP工程

目标文件：**`Release\Exe\app.bin`**、**`Release\Exe\combin.hex`**

APP工程的复位向量始终定义为 **`<u>`0x10000 `</u>`** 位置。APP电表应用程序区。

此工程含两个子工程选项，分别为 `app-Debug`, `app-Release`. 主要区别在于目标链接时，`app-Release` 子工程会生成包含 `boot` 的完整烧录程序（HEX），没有仿真信息。进行程序仿真时，选择激活 `Debug`工程子项，按< **`<u>`F8 `</u>`** >选择 `Debug-All` 批编译命令。

`app-Release` 子工程在目标生成后（`<u>`post-build comand line `</u>`）, 会执行 [file_copy.bat]() 批处理命令，用来进行重命名目标程序并且生成升级文件。

### 3. 使用调试打印功能

#### 3.1 目前支持的调试打印输出

| 宏定义           | 打印输出方式                                                               |

| ---------------- | -------------------------------------------------------------------------- |

| `UART_PRINT`   | 电表串口硬件输出                                                           |

| `SEGGER_PRINT` | JLINK仿真硬件输出打印                                                      |

### 4. 程序文件输出

> **烧录文件目录**： output\电表型号\平台型号\\

> **升级文件目录**： output\电表型号\平台型号\

---

## 关于C编程规范

编程风格应在同一个模块内尽量保持统一，并且按照如下规范进行：

#### 至关重要的几点

1. 主循环运行一圈的时间要尽可能的短，最长时间最好限制在50ms以内，否则应用多线程的方式， 确保系统的实时性。
2. 中断服务函数中原则上禁用延时，因确保中断函数精简，运行时间短，特别是频繁中断服务。
3. 在中断函数中调用函数要考虑重入的问题，原则上只调用唯一的函数，且定义为内联函数。禁止在中断服务中操作EEPROM、FALSH等外设。

#### 缩进、TAB、空格、空行规范

1. 保证必要的缩进为 **4** 空格对齐。
2. 所有编辑环境中需设置 `TAB` 键 = **4** 空格。
3. 除了在行首可以使用 `TAB` 键以外，禁止在其它位置使用，除非编辑环境设置了自动把 `TAB` 替换成空格。
4. 在使用运算符时，前后应空一格。例如 `+、-、*、/、%、=、|、&、||、&&、==、!=、<、>、?` 等。
5. 代码中应适当添加空格和空行，尽量保证代码的整齐、干净、美观。

#### 注释格式

1. 无论是在行中或者行尾，推荐使用 `//` 或 `///` 方式。
2. 不建议在行尾使用 `/*` 方式。
3. 注释应占到代码总行数的30%左右。

#### 命名规范

命名规范不是绝对的，一般遵循以下规则：

1. 常数宏、枚举使用 **”全部大写+下划线“** 方式
2. 函数宏、变量、函数、结构成员等使用 **“全部小写+下划线”** 方式
3. 声明结构名，后缀添加 **“_s”** 助记符
4. 声明枚举名，后缀添加 **“_t”** 助记符
5. 布尔变量，前缀添加 **“b_”** 助记符
6. 全局变量，前缀添加 **“g_”** 助记符

#### 代码可读性

1.`#include` 必要的头文件，不相关的头文件尽量避免包含进来，尤其避免形成交叉包含。

2. 尽量避免全局变量的使用，模块内变量及函数尽量使用 `static` 修饰。
3. 函数入参为指针型参数时，如果指向的内容不会或不希望发生变化，应尽量用 `const` 修饰。
