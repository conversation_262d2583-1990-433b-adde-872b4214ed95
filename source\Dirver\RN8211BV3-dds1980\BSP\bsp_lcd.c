/*
 * @Author: li
 * @Date: 2025-08-06 16:37:28
 * @LastEditTime: 2025-08-08 09:02:20
 * @Description:
 */
/**
 ******************************************************************************
 * @file    bsp_lcd.c
 * <AUTHOR> @date    2024
 * @brief   隐藏LCD驱动。移植时函数内容不用修改。(注意时间日期格式)
 * @note    lcd.h 引入液晶屏定义；
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include <stdio.h>
#include "bsp_lcd.h"
#include "lcd.h"

///////这里选择日期显示格式定义长度，yyyy.mm.dd或yy.dd.mm
#define DATE_FORMAT_DATE_LEN 6

#if USE_LCD_EXT_DRIVER

#if LCD_DRV_TYPE == LCD_DRV_BL55080
#include ".\ext_lcd_driver\bl55080.c"    /// BL55080 LCD驱动
#else
#error "请选择外部液晶驱动型号 in bsp_cfg.h"
#endif
#define lcd_open() ext_lcd_open()
#define lcd_open_nopower() ext_lcd_open_nopower()
#define lcd_close() ext_lcd_close()
#define lcd_all_seg_clr() ext_lcd_all_seg_clr()
#define lcd_all_seg_set() ext_lcd_all_seg_set()
#define lcd_light(seg, on_off) ext_lcd_light(seg, on_off)
#define lcd_refresh() ext_lcd_refresh()

#elif LCD_DRV_TYPE == LCD_DRV_MCU
///< MCU内置LCD驱动

#define lcd_open() hal_lcd.open()
#define lcd_open_nopower() hal_lcd.open_nopower()
#define lcd_close() hal_lcd.close()
#define lcd_all_seg_clr() hal_lcd.all_seg_clr()
#define lcd_all_seg_set() hal_lcd.all_seg_set()
#define lcd_light(seg, on_off) hal_lcd.light(seg, on_off)
#define lcd_refresh() hal_lcd.refresh()
#else    ///< 无LCD驱动或不适用液晶屏
#define lcd_open()
#define lcd_open_nopower()
#define lcd_close()
#define lcd_all_seg_clr()
#define lcd_all_seg_set()
#define lcd_light(seg, on_off)
#define lcd_refresh()
#endif

void bsp_lcd_disp_char(uint8_t screen, uint8_t pos, uint16_t ch);

////移植时以下函数不用修改

/// @brief LCD控制函数
/// @param on_off
void bsp_lcd_ctrl(LCD_CTRL_MODE_t on_off)
{
#if USE_LCD
    switch(on_off)
    {
        case LCD_PWRON_OPEN:
            lcd_open();
            break;

        case LCD_PWROFF_OPEN:
            lcd_open_nopower();
            break;

        default:
            lcd_close();
            return;
    }

    lcd_all_seg_clr();
    lcd_refresh();
#endif
}

/// @brief 点亮/熄灭所有LCD段
/// @param on_off
void bsp_lcd_all_seg_light(char on_off)
{
#if USE_LCD
    (on_off == 1) ? lcd_all_seg_set() : lcd_all_seg_clr();
#endif
}

/// @brief 显示熄灭图标
/// @param icon
/// @param on_off 1-点亮，0-熄灭
void bsp_lcd_icon_light(ICON_TYPE_t icon, char on_off)
{
#if USE_LCD
    for(uint8_t i = 0; i < icon_map_list_num; i++)
    {
        if(icon == icon_map_list[i].app_icon)
        {
            const SEG_TYPE_t *ptr = (SEG_TYPE_t *)(icon_map_list[i].seg_tab);
            uint8_t           num = icon_map_list[i].seg_num;
            while(num != 0)
            {
                lcd_light(*ptr++, on_off);
                num--;
            }
            return;
        }
    }
#endif
}

/// @brief 在LCD主屏/副屏上指定位置开始显示一个字符
/// @param screen 屏幕选择 LCD_MS/LCD_LS
/// @param pos 显示开始位置
/// @param ch 待显示的字符
void bsp_lcd_disp_char(uint8_t screen, uint8_t pos, uint16_t ch)
{
#if USE_LCD
    uint8_t           i;
    const SEG_TYPE_t *ptr;

    if(screen >= screen_number) return;
    if(ch == SPACE || pos >= digit_screen[screen].digit_num) return;
    ptr = digit_screen[screen].seg_tab + pos * digit_screen[screen].seg_num;
    for(i = digit_screen[screen].seg_num; i != 0; i--)
    {
        lcd_light(*ptr++, ch);
        ch = ch >> 1;
    }
#endif
}

/// @brief 在LCD主屏/副屏上开始显示数字及小数点, 右对齐,主屏可显示负数(副屏不支持)
/// @param screen 屏幕选择 LCD_MS/LCD_LS
/// @param value 待显示数据
/// @param len 显示长度
/// @param dot 小数点位数, 0表示无小数点
/// @param lead 是否显示前导零，1表示显示，0表示不显示
/// @return 返回显示的数字个数
uint8_t bsp_lcd_disp_dight(uint8_t screen, int32_t value, uint8_t len, uint8_t dot, uint8_t lead)
{
#if USE_LCD
    const Screen_s *ptr = digit_screen + screen;
    int8_t          i   = ptr->digit_num - 1;
    uint32_t        dat = labs(value);

    if(screen >= screen_number) return 0;
    if(dot == 0)
    {
        if(screen == LCD_MS) i -= LCD_LS_DIGITS;
    }
    else if(dot < len)
    {
        const SEG_TYPE_t *pdot = &ptr->dot_tab[ptr->dot_num - dot];
        while(*pdot == SPACE) { pdot--, i--; }
        lcd_light(*pdot, 1);
    }

    for(; i >= 0 && len > 0; i--, len--)
    {
        uint8_t digit = (uint8_t)(dat % 10);
        dat           = dat / 10;
        bsp_lcd_disp_char(screen, i, segs_digit_tab[digit]);
        if(dat == 0 && dot == 0 && !lead) break;
        if(dot > 0) dot--;
    }

    if(value < 0)
    {
#if LCD_MS_MINUS_SIGN
        /// 主屏有单独负号
        if(screen == LCD_MS) { bsp_lcd_icon_light(TYPE_ICON_SIGN, 1); }
        else
        {    /// 占用主屏数码位置，显示负号
            if(lead) { bsp_lcd_disp_char(screen, 0, CHAR_); }
            else if(i > 0) { bsp_lcd_disp_char(screen, i - 1, CHAR_); }
        }
#else
        /// 占用主屏数码位置，显示负号
        if(lead) { bsp_lcd_disp_char(screen, 0, CHAR_); }
        else if(i > 0) { bsp_lcd_disp_char(screen, i - 1, CHAR_); }
#endif
    }
    return (uint8_t)i;
#else
    return 0;
#endif
}

/// @brief 在LCD主屏/副屏上开始显示一定数量的BCD码，右边对齐，不足左边补空
/// @param screen 屏幕选择 LCD_MS/LCD_LS
/// @param bcd 待显示bcd串
/// @param len 显示数量
void bsp_lcd_disp_bcd(uint8_t screen, const uint8_t *bcd, uint8_t len)
{
    uint8_t i;
    len <<= 1;
    for(i = 1; i <= len; i++)
    {
        uint8_t digit;
        if(i & 0x01)
        {
            digit = *bcd & 0x0f;    // %16; // bcd码左边半字节
        }
        else
        {
            digit = *bcd >> 4;    // /16; // bcd码右边半字节
            bcd++;
        }
        bsp_lcd_disp_char(screen, digit_screen[screen].digit_num - i, segs_digit_tab[digit]);
    }
}

/// @brief 在LCD主屏/副屏上开始显示可视'0'~'9','a'~'z'的字串 ASCII
/// @param screen 屏幕选择 LCD_MS/LCD_LS
/// @param pos 显示开始位置
/// @param ch 待显示字串
/// @param num 显示数量
void bsp_lcd_disp_string(uint8_t screen, uint8_t pos, const uint8_t *ch, uint8_t num)
{
#if USE_LCD
    uint8_t i;
    for(i = 0; i < num; i++, ch++)
    {
        if(*ch >= '0' && *ch <= '9') { bsp_lcd_disp_char(screen, pos, segs_digit_tab[*ch - '0']); }
        else if(*ch >= 'A' && *ch <= 'Z') { bsp_lcd_disp_char(screen, pos, segs_alp_tab[*ch - 'A']); }
        else if(*ch >= 'a' && *ch <= 'z') { bsp_lcd_disp_char(screen, pos, segs_alp_tab[*ch - 'a']); }
        else if(*ch == '-') { bsp_lcd_disp_char(screen, pos, CHAR_); }
        else if(*ch == '_') { bsp_lcd_disp_char(screen, pos, CHAR__); }
        else if(*ch == '.')
        {
            SEG_TYPE_t com_seg = digit_screen[screen].dot_tab[pos - 1];
            if(com_seg != SPACE) lcd_light(com_seg, 1);
            continue;
        }
        else { bsp_lcd_disp_char(screen, pos, SPACE); }
        pos++;
    }
#endif
}

/// @brief 在LCD主屏上开始显示日期,显示格式DD-MM-YY
/// @param year 年
/// @param month 月
/// @param day 日
void bsp_lcd_disp_date(uint8_t year, uint8_t month, uint8_t day)
{
    char buf[] = "------";
    if(year != 0xff && month != 0xff && day != 0xff) { sprintf(buf, "%.2d%.2d%.2d", year, month, day); }
    bsp_lcd_disp_string(LCD_MS, LCD_MS_DIGITS - DATE_FORMAT_DATE_LEN, (const uint8_t *)buf, LCD_MS_DIGITS);
    bsp_lcd_icon_light(TYPE_ICON_DATE, 1);
}

/// @brief 在LCD主屏上开始显示时间,显示格式hh:mm:ss
/// @param hour 时
/// @param min 分
/// @param sec 秒
void bsp_lcd_disp_time(uint8_t hour, uint8_t min, uint8_t sec)
{
    char buf[] = "------";
    if(hour != 0xff && min != 0xff && sec != 0xff) { sprintf(buf, "%.2d%.2d%.2d", hour, min, sec); }
    bsp_lcd_disp_string(LCD_MS, LCD_MS_DIGITS - 6, (const uint8_t *)buf, LCD_MS_DIGITS);
    bsp_lcd_icon_light(TYPE_ICON_TIME, 1);
}

/// @brief 刷新显示内容
/// @note 调用此函数将立即刷新LCD显示内容
void bsp_lcd_disp_refresh(void)
{
#if USE_LCD
    lcd_refresh();
#endif
}

/// @brief 声明lcd子模块对象
const struct lcd_s lcd = {
    .ctrl        = bsp_lcd_ctrl,
    .all_light   = bsp_lcd_all_seg_light,
    .icon_light  = bsp_lcd_icon_light,
    .disp_char   = bsp_lcd_disp_char,
    .disp_digit  = bsp_lcd_disp_dight,
    .disp_string = bsp_lcd_disp_string,
    .disp_bcd    = bsp_lcd_disp_bcd,
    .disp_date   = bsp_lcd_disp_date,
    .disp_time   = bsp_lcd_disp_time,
    .refresh     = bsp_lcd_disp_refresh,
};
