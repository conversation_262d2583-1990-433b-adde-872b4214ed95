
/**
  ******************************************************************************
  * @file    step_tariff.c
  * <AUTHOR> @date    2024  
  * @brief   阶梯电价管理，附带费率电价存储和切换
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "datastore.h"
#include "step_tariff.h"
#include "debug.h"
#include "energy.h"
#include "mic.h"
#include "tariff.h"
#include "crc.h"
#include "app.h"
#include "billing.h"

#define STEP_TF_CRC16               0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(STEP_TF_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(STEP_TF_CRC16, struct, len)

#define STEP_TARIFF_PARA_ADDR       nvm_addr(NVM_STEP_TARIFF_PARA)


extern const step_tariff_para_s  step_tariff_default_para; ///默认参数
static const step_tariff_para_s* step_tariff_running_para; ///运行参数，指向codeflash


static uint8_t step_tariff_current_idx;     // 当前阶梯号，从1开始， 1表示第一阶梯
static TYPE_STUS_STEP step_tariff_out_stus;
static uint32_t step_tariff_billing_energy; // 当前阶梯结算参考电能

/* Private function prototypes -----------------------------------------------*/
uint32_t step_tariff_inc_energy_get(void);
/* Private functions ---------------------------------------------------------*/

/// @brief 参数计算检验后存入NVM中
/// @param ofst
/// @param val
/// @param len
/// @return
static bool step_tariff_para_store(uint16_t ofst, const void* val, uint16_t len)
{
    step_tariff_para_s para;
    if(ofst != 0) memcpy(&para, step_tariff_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(step_tariff_para_s));
    step_tariff_running_para = (const step_tariff_para_s*)STEP_TARIFF_PARA_ADDR;
    return nvm.write((uint32_t)step_tariff_running_para, &para, sizeof(para));
}

/// @brief 参数指针初始化并检验参数
/// @param
static void step_tariff_para_load(void)
{
    step_tariff_running_para = (const step_tariff_para_s*)STEP_TARIFF_PARA_ADDR;
    if(CRC16_CHK(step_tariff_running_para, sizeof(step_tariff_para_s)) == FALSE)
    {
        step_tariff_running_para = &step_tariff_default_para;
    }
}

/// @brief 扫描当前阶梯电能，计算得到当前阶梯号
/// @param  
/// @return 
static uint8_t step_energy_scan(void)
{
    uint32_t inc_energy = step_tariff_inc_energy_get(); /* STEP_TARIFF_ENERGY_SCALER;*/
    uint8_t i = step_tariff_running_para->active_step.step_num;
    while(i != 0)
    {
        if(inc_energy >= step_tariff_running_para->active_step.value[--i]) return i + 1;   /// 阶梯号从1开始， 阶梯值需按顺序排列
    }
    return 1;
}

/* Public functions ----------------------------------------------------------*/
/// @brief 阶梯电价初始化
void step_tariff_init(void)
{
    step_tariff_current_idx     = 0;
    step_tariff_out_stus        = 0;
    step_tariff_billing_energy  = 0xFFFFFFFF; // 当前阶梯结算参考电能
    step_tariff_para_load();
    DBG_PRINTF(P_STEP, D, "\r\n ** step_tariff_init: step_tariff_para_s = %d",  sizeof(step_tariff_para_s));
    DBG_PRINTF(P_STEP, D, "\r\n ** step_tariff_init: STEP_TARIFF_PARA_ADDR = %x", STEP_TARIFF_PARA_ADDR);
}
/// @brief 阶梯电价每秒运行
/// @param  
void step_tariff_second_run(void)
{
    /* 阶梯费率参数检出 */
    step_tariff_para_load();

    //  备用套阶梯电价切换
    if(!mclock.datetime->stus.invalid_value && !mclock.datetime->stus.doubtful_value)
    {
        //  备用套阶梯电价切换，注意存储时秒补为0
        if(mclock.is_valid(&step_tariff_running_para->switch_time_step) &&
            (mclock.compare(&step_tariff_running_para->switch_time_step) >= 0))
        {
            step_tariff_para_s para;
            /* 激活备用阶梯表 */
            para = *step_tariff_running_para;
            memcpy(&para.active_step, &para.passive_step, sizeof(step_tab_s));

            /* 清备用阶梯表激活时间 */
            mclock.invalid_set(&para.switch_time_step); // 置激活时间无效
            step_tariff_para_store(0, &para, sizeof(step_tariff_para_s));

            step_tariff_out_stus |= STUS_PASSIVE_STEP_ACT;
            DBG_PRINTF(P_STEP, D, "\r\n ** step_tariff: STUS_PASSIVE_STEP_ACT ");
        }

        //  备用套分时费率切换，注意存储时秒补为0  
        if(mclock.is_valid(&step_tariff_running_para->switch_time_tf) &&
            (mclock.compare(&step_tariff_running_para->switch_time_tf) >= 0))
        {
            step_tariff_para_s para;
            /* 激活备用费率表 */
            para = *step_tariff_running_para;
            memcpy(&para.active_tf, &para.passive_tf, sizeof(tariff_tab_s));

            /* 清备用费率表激活时间 */
            mclock.invalid_set(&para.switch_time_tf); // 置激活时间无效
            step_tariff_para_store(0, &para, sizeof(step_tariff_para_s));

            step_tariff_out_stus |= STUS_PASSIVE_TF_ACT;
            DBG_PRINTF(P_STEP, D, "\r\n ** step_tariff: STUS_PASSIVE_TF_ACT ");
        }
    }
    /* 扫描当前阶梯 */
    step_tariff_current_idx = step_energy_scan();
#if P_STEP
    static uint8_t step_bak = 0;
    if(step_bak != step_tariff_current_idx)
    {   
        step_bak = step_tariff_current_idx;
        DBG_PRINTF(P_STEP, D, "\r\n ** step_tariff_current_idx:%d", step_tariff_current_idx);
    }
#endif   // P_STEP
}

/// @brief 获取当前阶梯号
/// @param   
/// @return 
uint8_t step_tariff_current_step_get(void)
{
    return step_tariff_current_idx;
}

/// @brief 获取当前阶梯结算周期实时电能值
/// @param  
/// @return 
uint32_t step_tariff_inc_energy_get(void)
{
    if(billing.is_happen() || (step_tariff_billing_energy == 0xFFFFFFFF)) // 只在上电时和结算时更新
    {
        step_tariff_billing_energy = billing.phs_cum_energy_get(bl_step, T_PHASE, 1, TYPE_BL_COM_ACT, 0); // 获取上次阶梯结算时的累计电能
    }

    return energy.phs_cum_value_get(T_PHASE, TYPE_ENERGY_ADD_ACT, 0) - step_tariff_billing_energy;  // 获取本次阶梯电能增量
}

/// @brief 获取阶梯的阶梯电能值
/// @param step 
/// @return 
uint32_t step_tariff_energy_get(uint8_t step)
{
    if(step == 0 || step > STEP_TARIFF_NUM + 1) return 0;
    return step_tariff_running_para->active_step.value[step - 1];
}

/// @brief 查询阶梯费率状态
/// @param state 
/// @return 
bool step_tariff_state_query(TYPE_STUS_STEP state)
{
    return boolof(step_tariff_out_stus & state);
}

/// @brief 清除阶梯费率状态
/// @param  
void step_tariff_state_clr(void)
{
    step_tariff_out_stus = 0;
}

/// @brief 获取当前激活的阶梯表
/// @param  
/// @return 
const step_tab_s* step_tariff_active_step_get(void)
{
    return &step_tariff_running_para->active_step;
}

/// @brief 获取备份的阶梯表
/// @param  
/// @return 
const step_tab_s* step_tariff_passive_step_get(void)
{
    return &step_tariff_running_para->passive_step;
}

/// @brief 设置备份的阶梯表
/// @param passive 
void step_tariff_passive_step_set(const step_tab_s* passive)
{
    step_tariff_para_store(member_offset(step_tariff_para_s, passive_step), passive, sizeof(step_tab_s));
}

/// @brief 获取当前激活的费率表
/// @param  
/// @return 
const tariff_tab_s* step_tariff_active_tf_get(void)
{
    return &step_tariff_running_para->active_tf;
}

/// @brief 获取备份的费率表
/// @param  
/// @return 
const tariff_tab_s* step_tariff_passive_tf_get(void)
{
    return &step_tariff_running_para->passive_tf;
}

/// @brief 设置备份的费率表
/// @param passive 
void step_tariff_passive_tf_set(const tariff_tab_s* passive)
{
    step_tariff_para_store(member_offset(step_tariff_para_s, passive_tf), passive, sizeof(tariff_tab_s));
}

/// @brief 获取备份的阶梯费率的激活时间
/// @param  
/// @return 
const clock_s* step_tariff_passive_step_active_time_get(void)
{
    return &step_tariff_running_para->switch_time_step;
}

/// @brief  设置备份的阶梯费率的激活时间
/// @param time 
bool step_tariff_passive_step_active_time_set(clock_s* time)
{
    time->stus.value = 0;
    time->second = 0;
    if(mclock.is_valid(time) == false) return false;
    return step_tariff_para_store(member_offset(step_tariff_para_s, switch_time_step), time, sizeof(clock_s));
}

/// @brief 获取备份的阶梯费率的激活时间
/// @param  
/// @return 
const clock_s* step_tariff_passive_tf_active_time_get(void)
{
    return &step_tariff_running_para->switch_time_tf;
}

/// @brief  设置备份的阶梯费率的激活时间
/// @param time 
bool step_tariff_passive_tf_active_time_set(clock_s* time)
{
    time->stus.value = 0;
    time->second = 0;
    if(mclock.is_valid(time) == false) return false;
    return step_tariff_para_store(member_offset(step_tariff_para_s, switch_time_tf), time, sizeof(clock_s));
}

/// @brief 获取当前阶梯电价
/// @param  
/// @return 
uint32_t step_tariff_current_step_price_get(void)
{
    if((step_tariff_running_para->active_step.step_num != 0) &&
       (step_tariff_running_para->active_step.step_num <= STEP_TARIFF_NUM) &&
       (step_tariff_current_idx <= step_tariff_running_para->active_step.step_num))
       {
           return step_tariff_running_para->active_step.price[step_tariff_current_idx - 1];
       }
    return step_tariff_running_para->active_step.price[1];
}

/// @brief 获取当前费率电价
/// @param price 
uint32_t step_tariff_current_tf_price_get(void)
{
    uint8_t cur_tariff = tariff.cur_rate_get();
    if((step_tariff_running_para->active_tf.tariff_num != 0) &&
       (step_tariff_running_para->active_tf.tariff_num <= TARIFF_RATE_NUM) &&
       (cur_tariff <= step_tariff_running_para->active_tf.tariff_num))
       {
           return step_tariff_running_para->active_tf.price[cur_tariff - 1];
       }
    return step_tariff_running_para->active_tf.price[1];
}
/// @brief 获取当前电价 = 阶梯电价+费率电价 ，混合电价
/// @param  
/// @return 
uint32_t step_tariff_current_price_get(void)
{
    return (step_tariff_current_step_price_get() + step_tariff_current_tf_price_get());  // 阶梯电价+费率电价 混合电价
}

/// @brief 阶梯费率模块复位
/// @param type 
void step_tariff_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        step_tariff_para_store(0, &step_tariff_default_para, sizeof(step_tariff_para_s));

        step_tariff_current_idx = 0;
        step_tariff_out_stus = 0;
        step_tariff_billing_energy = 0xFFFFFFFF;
    }
}

/// @brief 声明阶梯费率模块对象
const struct step_tariff_s step_tariff =
{
    .reset                          = step_tariff_reset,
    .state_query                    = step_tariff_state_query,
    .state_clr                      = step_tariff_state_clr,

    .current_step_get               = step_tariff_current_step_get,     // 获取当前阶梯号
    .energy_get                     = step_tariff_energy_get,           // 获取阶梯电能值
    .inc_energy_get                 = step_tariff_inc_energy_get,       // 获取阶梯电能增量

    .active_step_get                = step_tariff_active_step_get,      // 获取当前激活的阶梯表
    .passive_step_get               = step_tariff_passive_step_get,     // 获取备份的阶梯表
    .passive_step_set               = step_tariff_passive_step_set,     // 设置备份的阶梯表
    .passive_step_active_time_get   = step_tariff_passive_step_active_time_get, // 获取备份的阶梯费率的激活时间
    .passive_step_active_time_set   = step_tariff_passive_step_active_time_set, // 设置备份的阶梯费率的激活时间

    .active_tf_get                  = step_tariff_active_tf_get,        // 获取当前激活的费率表
    .passive_tf_get                 = step_tariff_passive_tf_get,       // 获取备份的费率表
    .passive_tf_set                 = step_tariff_passive_tf_set,       // 设置备份的费率表
    .passive_tf_active_time_get     = step_tariff_passive_tf_active_time_get,   // 获取备份的阶梯费率的激活时间
    .passive_tf_active_time_set     = step_tariff_passive_tf_active_time_set,   // 设置备份的阶梯费率的激活时间

    .step_price_get                 = step_tariff_current_step_price_get,// 获取当前阶梯电价
    .tf_price_get                   = step_tariff_current_tf_price_get,  // 获取当前费率电价
    .price_get                      = step_tariff_current_price_get,     // 获取当前电价 = 阶梯电价+费率电价 ，混合电价
};


/// @brief 声明阶梯费率模块任务接口
const struct app_task_t step_tariff_task =
{
    .init       = step_tariff_init,
    .second_run = step_tariff_second_run,
};

/// end of file

