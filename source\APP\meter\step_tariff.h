/**
  ******************************************************************************
  * @file    step_tariff.h
  * <AUTHOR> @date    2024
  * @brief    
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

#ifndef STEP_TARIFF_H_
#define STEP_TARIFF_H_
#include "typedef.h"
#include "timeapp.h"
#include "app_config.h"

typedef uint16_t TYPE_STUS_STEP;
#define STUS_PRG_TF_PRICE                   (1U << 1)   // 编程费率表
#define STUS_PRG_STEP_PRICE                 (1U << 2)   // 编程阶梯电价表
#define STUS_PASSIVE_STEP_ACT               (1U << 3)   // 备用阶梯表激活
#define STUS_PASSIVE_TF_ACT                 (1U << 4)   // 备用费率表激活

/// @brief 费率参数
typedef struct
{
    uint8_t  tariff_num;
    uint32_t price[TARIFF_RATE_NUM];   // 费率电价 0.0001元/kWh  
}tariff_tab_s;

/// @brief 阶梯参数
typedef struct 
{
    uint32_t value[STEP_TARIFF_NUM];    // 阶梯值   0.01kWh
    uint32_t price[STEP_TARIFF_NUM];    // 阶梯电价 0.0001元/kWh 
    uint8_t  bill_time[STEP_TARIFF_BILLING_NUM][3];  ///阶梯结算日，月日时 
    uint8_t  step_num;  ///阶梯数
} step_tab_s;

/// @brief 阶梯电价参数
typedef struct
{
    uint16_t crc;
    uint16_t chk;
    tariff_tab_s active_tf;
    tariff_tab_s passive_tf;
    step_tab_s   active_step;
    step_tab_s   passive_step;
    clock_s      switch_time_tf;    
    clock_s      switch_time_step;  
}step_tariff_para_s;

struct step_tariff_s
{
    void (*reset)(uint8_t type);
    bool (*state_query)(TYPE_STUS_STEP state);
    void (*state_clr)(void);

    uint8_t (*current_step_get)(void);
    uint32_t (*energy_get)(uint8_t step);
    uint32_t (*inc_energy_get)(void);

    const step_tab_s *(*active_step_get)(void);
    const step_tab_s *(*passive_step_get)(void);
    void (*passive_step_set)(const step_tab_s *passive);
    const clock_s *(*passive_step_active_time_get)(void);
    bool (*passive_step_active_time_set)(clock_s *time);

    const tariff_tab_s *(*active_tf_get)(void);
    const tariff_tab_s *(*passive_tf_get)(void);
    void (*passive_tf_set)(const tariff_tab_s *passive);
    const clock_s *(*passive_tf_active_time_get)(void);
    bool (*passive_tf_active_time_set)(clock_s *time);

    /// @brief 获取当前阶梯电价
    /// @param  
    /// @return 
    uint32_t (*step_price_get)(void);
    /// @brief 获取当前费率电价
    /// @param price 
    uint32_t (*tf_price_get)(void);
    /// @brief 获取当前电价 = 阶梯电价+费率电价 ，混合电价
    /// @param  
    /// @return 
    uint32_t (*price_get)(void);
};
extern const struct step_tariff_s step_tariff;
extern const struct app_task_t    step_tariff_task;

#endif /* STEP_TARIFF_H_ */

