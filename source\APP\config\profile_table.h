/********************************************************************************
* @file    event_table.h
* <AUTHOR> @date    2024
* @brief   本文件配置电表默认显示项目
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifndef __EVENT_TABLE_H__
#define __EVENT_TABLE_H__

#include "app_config.h"
#include "utils.h"
#include "typedef.h"
#include "status.h"
#include "event.h"
#include "datastore.h"
#include "..\interface\profile.h"
#include "..\interface\DLT645_2007_id.h"
#include "..\interface\dispApp.h"
#include "..\datastore\datastore.h"
#include "..\datastore\log.h"
#include "..\config\profile_capture_obj.h"
#include "..\config\profile_table.h"
#include "..\meter\demand.h"
#include "..\meter\timeapp.h"
#include "..\meter\tariff.h"
#include "..\meter\status.h"
#include "..\meter\RelayApp.h"
#include "..\meter\step_tariff.h"
#include "..\meter\sysinit.h"
#include "..\meter\status.h"
#include "..\meter\power_event.h"

typedef bool (*state_query_func)(uint16_t state);
typedef uint16_t (*capture_func)(uint8_t *buf, NVM_LOG_t idx, uint8_t typ);

typedef struct
{
    uint32_t  id;                       // 事件ID
    const capture_func*     capture;    // 事件数据捕获函数指针
    const state_query_func* state_query;// 事件状态查询函数指针,对应各模块的state_query接口
    uint16_t   s_state;                 // 事件发生状态位，在各模块的头文件中定义e
    uint16_t   e_state;                 // 事件结束状态位，在各模块的头文件中定义e
    NVM_LOG_t  idx;                     // 事件在NVM中的存储索引
    evt_type_t et;                      // 事件类型
} profile_tab_s;

#define EVT_ITEM(i,c,q,s,e,d,t) {.id=i, .capture=c, .state_query=q, .s_state=s, .e_state=e, .idx=d, .et=t},
static const profile_tab_s evt_tab[] = {
#if EVENT_LOSS_VOL_EN              
    EVT_ITEM(C73_A_LOSS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_LOS_V_A_S,  EVT_LOS_V_A_E,  NVM_LOG_LOSS_VOL_A, EVENT_TYPE_LOSS_VOL_A)/// 失压事件发生
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_LOSS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_LOS_V_B_S,  EVT_LOS_V_B_E,  NVM_LOG_LOSS_VOL_B, EVENT_TYPE_LOSS_VOL_B)/// 失压事件发生
    EVT_ITEM(C73_C_LOSS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_LOS_V_C_S,  EVT_LOS_V_C_E,  NVM_LOG_LOSS_VOL_C, EVENT_TYPE_LOSS_VOL_C)/// 失压事件发生
    #endif
#endif
#if EVENT_LOW_VOL_EN              
    EVT_ITEM(C73_A_LOW_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_LOW_V_A_S,  EVT_LOW_V_A_E,  NVM_LOG_LOW_VOL_A,  EVENT_TYPE_LOW_VOL_A)/// 欠压事件发生
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_LOW_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_LOW_V_B_S,  EVT_LOW_V_B_E,  NVM_LOG_LOW_VOL_B,  EVENT_TYPE_LOW_VOL_B)/// 欠压事件发生
    EVT_ITEM(C73_C_LOW_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_LOW_V_C_S,  EVT_LOW_V_C_E,  NVM_LOG_LOW_VOL_C,  EVENT_TYPE_LOW_VOL_C)/// 欠压事件发生
    #endif
#endif
#if EVENT_OVR_VOL_EN             
 /// 过压事件
    EVT_ITEM(C73_A_OVR_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_OVR_V_A_S,  EVT_OVR_V_A_E,  NVM_LOG_OVR_VOL_A,  EVENT_TYPE_OVR_VOL_A)/// 过压事件发生
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_OVR_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_OVR_V_B_S,  EVT_OVR_V_B_E,  NVM_LOG_OVR_VOL_B,  EVENT_TYPE_OVR_VOL_B)/// 过压事件发生
    EVT_ITEM(C73_C_OVR_VOL_RECORD,      &profile.evt1_capture,  &power_event.state_query,   EVT_OVR_V_C_S,  EVT_OVR_V_C_E,  NVM_LOG_OVR_VOL_C,  EVENT_TYPE_OVR_VOL_C)/// 过压事件发生
    #endif
#endif
#if EVENT_MISS_VOL_EN             
/// 断相事件
    EVT_ITEM(C73_A_MISS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_MIS_PH_A_S, EVT_MIS_PH_A_E, NVM_LOG_MISS_VOL_A, EVENT_TYPE_MISS_VOL_A)/// 断相事件发生
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_MISS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_MIS_PH_B_S, EVT_MIS_PH_B_E, NVM_LOG_MISS_VOL_B, EVENT_TYPE_MISS_VOL_B)/// 断相事件发生
    EVT_ITEM(C73_C_MISS_VOL_RECORD,     &profile.evt1_capture,  &power_event.state_query,   EVT_MIS_PH_C_S, EVT_MIS_PH_C_E, NVM_LOG_MISS_VOL_C, EVENT_TYPE_MISS_VOL_C)/// 断相事件发生
    #endif  
#endif
#if EVENT_ALL_LOSS_VOL_EN         
    EVT_ITEM(C73_C_MISS_VOL_RECORD,     &profile.evt7_capture,  &power_event.state_query,   EVT_ALL_LOS_V_S,EVT_ALL_LOS_V_E,NVM_LOG_ALL_LOSS_VOL, EVENT_TYPE_ALL_LOSS_VOL) /// 全失压事件
#endif
#if EVENT_BAK_PWR_LOS_EN          
/// 辅助电源失电事件
    EVT_ITEM(C73_ALL_LOSS_VOL_RECORD,      NULL,    NULL   0, NVM_LOG_BAK_PWR_LOS_A)
#endif
#if EVENT_V_REV_SQR_EN            
/// 电压逆向序事件
    EVT_ITEM(C73_V_REV_SQR_RECORD,      &profile.evt3_capture,  &power_event.state_query,   EVT_REV_V_SEQ_S,EVT_REV_V_SEQ_E,NVM_LOG_V_REV_SQR,  EVENT_TYPE_V_REV_SQR)
#endif
#if EVENT_I_REV_SQR_EN            
/// 电流逆向序事件
    EVT_ITEM(C73_I_REV_SQR_RECORD,      &profile.evt3_capture,  &power_event.state_query,   EVT_REV_I_SEQ_S,EVT_REV_I_SEQ_S,NVM_LOG_I_REV_SQR,  EVENT_TYPE_I_REV_SQR)
#endif
#if EVENT_V_UNB_EN                
/// 电压不平衡事件
    EVT_ITEM(C73_V_UNB_RECORD,          &profile.evt4_capture,  &power_event.state_query,   EVT_UNB_V_S,    EVT_UNB_V_E,    NVM_LOG_V_UNB,      EVENT_TYPE_V_UNB)
#endif
#if EVENT_I_UNB_EN            
/// 电流不平衡事件
    EVT_ITEM(C73_I_UNB_RECORD,          &profile.evt4_capture,  &power_event.state_query,   EVT_UNB_I_S,    EVT_UNB_I_E,    NVM_LOG_I_UNB,      EVENT_TYPE_I_UNB)
#endif
#if EVENT_LOS_CUR_EN              
/// 失流事件
    EVT_ITEM(C73_A_LOS_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_LOS_I_A_S,  EVT_LOS_I_A_E,  NVM_LOG_LOS_CUR_A,  EVENT_TYPE_LOS_CUR_A)
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_LOS_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_LOS_I_B_S,  EVT_LOS_I_B_E,  NVM_LOG_LOS_CUR_B,  EVENT_TYPE_LOS_CUR_B)
    EVT_ITEM(C73_C_LOS_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_LOS_I_C_S,  EVT_LOS_I_C_E,  NVM_LOG_LOS_CUR_C,  EVENT_TYPE_LOS_CUR_C)
    #endif
#endif
#if EVENT_OVR_CUR_EN              
/// 过流事件
    EVT_ITEM(C73_A_OVR_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_OVR_I_A_S,  EVT_OVR_I_A_E,  NVM_LOG_OVR_CUR_A,  EVENT_TYPE_OVR_CUR_A)
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_OVR_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_OVR_I_B_S,  EVT_OVR_I_B_E,  NVM_LOG_OVR_CUR_B,  EVENT_TYPE_OVR_CUR_B)
    EVT_ITEM(C73_C_OVR_CUR_RECORD,      &profile.evt2_capture,  &power_event.state_query,   EVT_OVR_I_C_S,  EVT_OVR_I_C_E,  NVM_LOG_OVR_CUR_C,  EVENT_TYPE_OVR_CUR_C)
    #endif
#endif
#if EVENT_MISS_CUR_EN             
/// 断流事件
    EVT_ITEM(C73_A_MISS_CUR_RECORD,     &profile.evt2_capture,  &power_event.state_query,   EVT_MIS_I_A_S,  EVT_MIS_I_A_E,  NVM_LOG_MISS_CUR_A, EVENT_TYPE_MISS_CUR_A)
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_MISS_CUR_RECORD,     &profile.evt2_capture,  &power_event.state_query,   EVT_MIS_I_B_S,  EVT_MIS_I_B_E,  NVM_LOG_MISS_CUR_B, EVENT_TYPE_MISS_CUR_B)
    EVT_ITEM(C73_C_MISS_CUR_RECORD,     &profile.evt2_capture,  &power_event.state_query,   EVT_MIS_I_C_S,  EVT_MIS_I_C_E,  NVM_LOG_MISS_CUR_C, EVENT_TYPE_MISS_CUR_C)
    #endif
#endif
#if EVENT_REV_EN                  
/// 潮流反向事件
    EVT_ITEM(C73_A_REV_RECORD,          &profile.evt3_capture,  &power_event.state_query,   EVT_REV_P_A_S,  EVT_REV_P_A_E,  NVM_LOG_REV_A,      EVENT_TYPE_REV_A)
    #if defined(POLYPHASE_METER)
    EVT_ITEM(C73_B_REV_RECORD,          &profile.evt3_capture,  &power_event.state_query,   EVT_REV_P_B_S,  EVT_REV_P_B_E,  NVM_LOG_REV_B,      EVENT_TYPE_REV_B)
    EVT_ITEM(C73_C_REV_RECORD,          &profile.evt3_capture,  &power_event.state_query,   EVT_REV_P_C_S,  EVT_REV_P_C_E,  NVM_LOG_REV_C,      EVENT_TYPE_REV_C)
    #endif
#endif
#if EVENT_OVR_LOAD_EN             
/// 过载事件
    EVT_ITEM(C73_A_OVR_LOAD_RECORD,     &profile.evt3_capture,  &power_event.state_query,   EVT_OVR_P_A_S,  EVT_OVR_P_A_E,  NVM_LOG_OVR_LOAD_A, EVENT_TYPE_OVR_LOAD_A)
    #if defined(POLYPHASE_METER)        
    EVT_ITEM(C73_B_OVR_LOAD_RECORD,     &profile.evt3_capture,  &power_event.state_query,   EVT_OVR_P_B_S,  EVT_OVR_P_B_E,  NVM_LOG_OVR_LOAD_B, EVENT_TYPE_OVR_LOAD_B)
    EVT_ITEM(C73_C_OVR_LOAD_RECORD,     &profile.evt3_capture,  &power_event.state_query,   EVT_OVR_P_C_S,  EVT_OVR_P_C_E,  NVM_LOG_OVR_LOAD_C, EVENT_TYPE_OVR_LOAD_C)
    #endif
#endif
#if EVENT_PWR_DOWN_EN             
/// 掉电事件
    EVT_ITEM(C73_PWR_DOWN_RECORD,       &profile.evt8_capture,  &power_event.state_query,   EVT_PWR_DOWN_S, EVT_PWR_DOWN_E, NVM_LOG_PWR_DOWN,   EVENT_TYPE_PWR_DOWN)
#endif
#if EVENT_OVR_DM_EN               
/// 超需量事件
    EVT_ITEM(EVENT_TYPE_OVR_DM_POS_kW,      NULL,   0, NVM_LOG_OVR_DM_POS_kW)
    // EVT_ITEM(EVENT_TYPE_OVR_DM_NEG_kW,   NULL,   0, NVM_LOG_OVR_DM_NEG_kW)
    // EVT_ITEM(EVENT_TYPE_OVR_DM_Q1_kvar,  NULL,   0, NVM_LOG_OVR_DM_Q1_kvar)
    // EVT_ITEM(EVENT_TYPE_OVR_DM_Q2_kvar,  NULL,   0, NVM_LOG_OVR_DM_Q2_kvar)
    // EVT_ITEM(EVENT_TYPE_OVR_DM_Q3_kvar,  NULL,   0, NVM_LOG_OVR_DM_Q3_kvar)
    // EVT_ITEM(EVENT_TYPE_OVR_DM_Q4_kvar,  NULL,   0, NVM_LOG_OVR_DM_Q4_kvar)
#endif
#if EVENT_PROGRAM_EN              
/// 编程事件
    EVT_ITEM(EVENT_TYPE_PROGRAM,            NULL,   0, NVM_LOG_PROGRAM)
#endif
#if EVENT_METER_CLEAN_EN          
/// 电表清零事件
    EVT_ITEM(C73_EVENT_CLEAN_RECORD,     NULL,   &sysinit.state_query,  INIT_DATA_CLEAR,        0, NVM_LOG_METER_CLEAN,     EVENT_TYPE_METER_CLEAN)
#endif
#if EVENT_DEMAND_CLEAN_EN         
/// 需量清零事件
    EVT_ITEM(C73_EVENT_CLEAN_RECORD,     NULL,   &sysinit.state_query,  INIT_MD_CLEAR,          0, NVM_LOG_DEMAND_CLEAN,    EVENT_TYPE_DEMAND_CLEAN)
#endif
#if EVENT_EVENT_CLEAN_EN          
/// 事件清零事件
    EVT_ITEM(C73_EVENT_CLEAN_RECORD,     NULL,   &event.state_query,    EVT_CLEAR_STATUS,        0, NVM_LOG_EVENT_CLEAN,    EVENT_TYPE_EVENT_CLEAN)
#endif
#if EVENT_SHITFT_TIME_EN          
    /// 校时事件
    EVT_ITEM(C73_SCHEDULE_RECORD,        NULL,   &mclock.state_query,   STUS_CLOCK_SHIFT_EVENT,  0, NVM_LOG_SHITFT_TIME,    EVENT_TYPE_SHITFT_TIME)
#endif
#if EVENT_BC_TIME_EN              
    /// 广播校时事件
    EVT_ITEM(C73_BC_TIME_RECORD,         NULL,   &mclock.state_query,   STUS_CLOCK_BC_EVENT,     0, NVM_LOG_BC_TIME,        EVENT_TYPE_BC_TIME)
#endif
#if EVENT_SCHEDULE_EN             /// 时段表事件
    EVT_ITEM(EVENT_TYPE_SCHEDULE,           NULL,   0, NVM_LOG_SCHEDULE)
#endif
#if EVENT_ZONE_TAB_EN             /// 时区表事件
    EVT_ITEM(EVENT_TYPE_ZONE_TAB,           NULL,   0, NVM_LOG_ZONE_TAB)
#endif
#if EVENT_WEEKENDS_PGM_EN         /// 周休日编程事件
    EVT_ITEM(EVENT_TYPE_WEEKENDS_PGM,       NULL,   0, NVM_LOG_WEEKENDS_PGM)
#endif
#if EVENT_HOLIDAY_PGM_EN          /// 节假日表编程事件
    EVT_ITEM(EVENT_TYPE_HOLIDAY_PGM,        NULL,   0, NVM_LOG_HOLIDAY_PGM)
#endif
#if EVENT_COMB_kWh_PGM_EN         /// 有功组合方式编程事件
    EVT_ITEM(EVENT_TYPE_COMB_kWh_PGM,       NULL,   0, NVM_LOG_COMB_kWh_PGM)
#endif
#if EVENT_COMB1_kvarh_PGM_EN      /// 无功组合方式1编程事件
    EVT_ITEM(EVENT_TYPE_COMB1_kvarh_PGM,    NULL,   0, NVM_LOG_COMB1_kvarh_PGM)
#endif
#if EVENT_COMB2_kvarh_PGM_EN      /// 无功组合方式2编程事件
    EVT_ITEM(EVENT_TYPE_COMB2_kvarh_PGM,    NULL,   0, NVM_LOG_COMB2_kvarh_PGM)
#endif
#if EVENT_BL_DAY_PGM_EN           /// 结算日编程事件
    EVT_ITEM(EVENT_TYPE_BL_DAY_PGM,         NULL,   0, NVM_LOG_BL_DAY_PGM)
#endif
#if EVENT_METER_COVER_EN          /// 开表盖事件
    EVT_ITEM(C73_METER_COVER_RECORD,        &profile.evt_cov_capture,   &mstatus.state_query,   STUS_TOP_OPN_S, STUS_TOP_OPN_E, NVM_LOG_METER_COVER,    EVENT_TYPE_METER_COVER)
#endif
#if EVENT_TEM_COVER_EN            /// 开端盖事件
    EVT_ITEM(C73_TEM_COVER_RECORD,          &profile.evt_cov_capture,   &mstatus.state_query,   STUS_BOT_OPN_S, STUS_BOT_OPN_E, NVM_LOG_TEM_COVER,      EVENT_TYPE_TEM_COVER)
#endif
};

static const uint16_t evt_obj_num = eleof(evt_tab);

#endif //__EVENT_TABLE_H__

