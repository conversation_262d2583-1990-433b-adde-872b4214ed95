# 智能电表测试项目

## 1. 基础功能测试

### TC-PF-01: 上下电测试 (热启动)

- **测试目的**: 验证设备在短时间频繁上下电后，核心数据（电量、余额、时间、事件记录）的完整性和准确性。
- **前置条件**: 电表运行正常，已记录初始电量、余额和当前时间。
- **测试步骤**:
  1. 记录当前总电量、剩余金额、时钟、费率等关键数据。
  2. 断开电表电源。
  3. 分别等待 100ms, 1s, 5s, 5min 后重新上电。
  4. 待电表启动后，读取步骤1中记录的关键数据。
- **预期结果**:
  - 电表能正常启动。
  - 读取的数据与断电前一致（除时钟可能有秒级误差外）。
  - 不应有异常的事件记录（如数据丢失、时钟超前/滞后等）。

### TC-PF-02: 低功耗测试

- **测试目的**: 验证设备在特定模式下的功耗是否符合设计要求。
- **前置条件**:
  - 将电表置于指定的低功耗模式。
  - 准备高精度电流表或功率分析仪。
- **测试步骤**:
  1. 将电流表串联到电表的供电回路中。
  2. 触发设备进入低功耗模式。
  3. 观察并记录稳定后的电流值。
- **预期结果**:
  - 电流值应在规格要求范围内（例如，接近或低于10uA）。

## 2. 计费与费控 (Payment & Tariff)

### TC-PAY-01: 本地费控扣费

- **测试目的**: 验证在有预付金额的情况下，电表能否根据实际用电量正确扣费。
- **前置条件**:
  - 电表为本地费控模式。
  - 已通过485或蓝牙等方式向电表充值一笔金额（例如：50元）。
  - 当前电价已知（例如：1元/度）。
- **测试步骤**:
  1. 记录充值后的剩余金额和总电量。
  2. 给电表接上一个稳定功率的负载（例如：1kW的加热器）。
  3. 让负载运行1小时，消耗1度电。
  4. 读取当前的剩余金额和总电量。
- **预期结果**:
  - 总电量增加约1度。
  - 剩余金额减少1元。

### TC-PAY-02: 零/负金额跳闸

- **测试目的**: 验证当剩余金额消耗至低于跳闸阈值（通常为0）时，电表能否自动断开继电器。
- **前置条件**:
  - 电表内剩余金额接近0（例如：0.5元）。
  - 电价为1元/度。
- **测试步骤**:
  1. 接上负载，持续用电。
  2. 监控剩余金额的变化。
  3. 观察当剩余金额变为0或负数时，电表的行为。
- **预期结果**:
  - 电表继电器跳闸，负载断电。
  - 生成“余额不足跳闸”相关事件记录。

### TC-PAY-03: 阶梯电价正确性

- **测试目的**: 验证电表能否根据设定的阶梯电价方案，在不同用量阶段使用正确的电价进行计费。
- **前置条件**:
  - 已设置阶梯电价参数（例如：第一档0-100度，电价0.5元；第二档101-200度，电价0.6元）。
  - 当前总用电量在第一档内（例如：99度）。
- **测试步骤**:
  1. 记录当前总用电量和剩余金额。
  2. 持续用电，使用电量超过第一档阈值（例如，再用3度电，使总用量达到102度）。
  3. 记录并计算这3度电的扣费情况。
- **预期结果**:
  - 1度电按0.5元扣费，2度电按0.6元扣费。
  - 扣费总额正确。

## 3. 通信与协议 (Communication)

**说明：本章节所有测试用例需分别通过RS485串口和蓝牙口执行，以确保所有通信渠道功能一致。**

### TC-COM-01: DLT645-2007 数据读取

- **测试目的**: 验证通过RS485串口和蓝牙口能够正确读取DLT645-2007协议定义的各项数据。
- **前置条件**:
  - 上位机或测试工具分别连接到电表的RS485串口和蓝牙口。
- **测试步骤**:
  1. 通过RS485串口发送读取“正向有功总电能”的命令。
  2. 通过蓝牙口发送读取“正向有功总电能”的命令。
  3. 重复步骤1-2，测试读取“A相电压”、“当前剩余金额”等不同数据。
- **预期结果**:
  - 电表能对各命令正确响应。
  - 两种通信方式读取的数据一致，且与电表内部数据一致。

### TC-COM-02: DLT645-2007 参数设置

- **测试目的**: 验证通过RS485串口和蓝牙口能够正确设置电表参数。
- **前置条件**:
  - 已通过密码验证或获得编程权限。
  - 上位机或测试工具分别连接到电表的RS485串口和蓝牙口。
- **测试步骤**:
  1. 通过RS485串口设置电表时间为一个新值。
  2. 读取并验证时间是否被修改。
  3. 通过蓝牙口设置电表时间为另一个新值。
  4. 读取并验证时间是否被修改。
- **预期结果**:
  - 两种通信方式均可成功修改参数。
  - 每次修改后都生成相应的“参数设置”或“校时”事件记录。

### TC-COM-03: DLT645-2007 远程控制

- **测试目的**: 验证通过RS485串口和蓝牙口能否远程控制电表的继电器。
- **前置条件**:
  - 已通过密码验证或获得控制权限。
  - 电表继电器处于闭合状态。
  - 上位机或测试工具分别连接到电表的RS485串口和蓝牙口。
- **测试步骤**:
  1. 通过RS485串口发送“远程拉闸”命令，验证继电器断开。
  2. 通过RS485串口发送“允许合闸”及“远程合闸”命令，验证继电器闭合。
  3. 通过蓝牙口发送“远程拉闸”命令，验证继电器断开。
  4. 通过蓝牙口发送“允许合闸”及“远程合闸”命令，验证继电器闭合。
- **预期结果**:
  - 两种通信方式均可成功控制继电器的拉合闸。
  - 每次操作都正确生成“远程拉闸”、“远程合闸”事件记录。

## 4. 事件记录 (Event Logging)

### TC-EVT-01: 掉电事件

- **测试目的**: 验证在发生掉电和上电时，电表能正确记录相应的事件。
- **前置条件**:
  - 清除所有历史事件。
- **测试步骤**:
  1. 电表正常运行。
  2. 断电并等待1分钟。
  3. 重新上电。
  4. 读取所有事件记录。
- **预期结果**:
  - 应记录到1次“掉电”事件和1次“上电”事件。
  - 事件发生的时间应与实际操作时间一致。

### TC-EVT-02: 编程事件

- **测试目的**: 验证在通过通信修改参数时，能正确记录编程事件。
- **前置条件**:
  - 清除所有历史事件。
- **测试步骤**:
  1. 通过通信接口设置一个新的表号。
  2. 读取事件记录。
- **预期结果**:
  - 应记录到1次“编程”事件。
  - 事件记录中应包含被修改项的标识。
