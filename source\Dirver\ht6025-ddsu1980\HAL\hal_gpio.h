/**
  ******************************************************************************
  * @file    hal_gpio.h
  * <AUTHOR> 
  * @version V1.0
  * @date    2024-08-05
  * @brief   This file contains all the functions prototypes for the GPIO
  * @note  
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
  *
  ******************************************************************************/
// #ifdef __cplusplus
//  extern "C" {
// #endif

#ifndef __HAL_GPIO_H
#define __HAL_GPIO_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"


/*
 * @brief 系统引脚功能定义:
   -o  输出
   -i  输入
   -ii 中断输入
   -io 双向IO
   -fa 摸拟输入
   -fi 外设功能输入
   -fo 外设功能输出
   -ff 外设功能双向输入/出
   -fw PWM输出
*/
#define PIN_A0                HT_GPIOA,0     // -悬空
#define PIN_A1                HT_GPIOA,1     // -悬空
#define PIN_A2                HT_GPIOA,2     // -悬空
#define PIN_EMU_CTL             HT_GPIOA,3     // -o 计量芯片复位
#define PIN_EEP_PWR             HT_GPIOA,4     // -o EEPROM 电源
#define PIN_MsrIC_Pulse         HT_GPIOA,5     // -ii 脉冲输入
#define PIN_JTAG                HT_GPIOA,6     // -io JTAG
#define PIN_A8                HT_GPIOA,8     // --
#define PIN_SEG13               HT_GPIOA,7     // -fo LCD SEG
#define PIN_SEG15               HT_GPIOA,8     // -fo LCD SEG
#define PIN_KEY2                HT_GPIOA,9     // -i 按键2
#define PIN_T2_T3               HT_GPIOA,10    // -i T2T3选型
#define PIN_T1_T4               HT_GPIOA,11    // -i T1T4选型
#define PIN_FLASH_MOSI          HT_GPIOA,12    // -o SPI1 MOSI
#define PIN_A13              HT_GPIOA,13    // -悬空
#define PIN_A14              HT_GPIOA,14    // -悬空
#define PIN_A15              HT_GPIOA,15    // -悬空
     

#define PIN_RLY_CHK             HT_GPIOB,0     // i - 继电器检测
#define PIN_SEG1                HT_GPIOB,1     // -fo LCD SEG
#define PIN_SEG2                HT_GPIOB,2     // -fo LCD SEG
#define PIN_SEG3                HT_GPIOB,3     // -fo LCD SEG
#define PIN_SEG4                HT_GPIOB,4     // -fo LCD SEG
#define PIN_SEG5                HT_GPIOB,5     // -fo LCD SEG
#define PIN_SEG6                HT_GPIOB,6     // -fo LCD SEG
#define PIN_SEG7                HT_GPIOB,7     // -fo LCD SEG
#define PIN_SEG8                HT_GPIOB,8     // -fo LCD SEG
#define PIN_SEG9                HT_GPIOB,9     // -fo LCD SEG
#define PIN_SEG10               HT_GPIOB,10    // -fo LCD SEG
#define PIN_SEG11               HT_GPIOB,11    // -fo LCD SEG
#define PIN_SEG12               HT_GPIOB,12    // -fo LCD SEG
#define PIN_SWIO                HT_GPIOB,13    // -io 仿真口
#define PIN_SEG14               HT_GPIOB,14    // -fo LCD SEG
#define PIN_SWCLK               HT_GPIOB,15    // -io 仿真口

#define PIN_UART1_TXD           HT_GPIOC,0     // -fo  模块串口输出
#define PIN_UART1_RXD           HT_GPIOC,1     // -fi  模块串口输入
#define PIN_UART0_RXD           HT_GPIOC,2     // -fo  计量芯片串口输出
#define PIN_UART0_TXD           HT_GPIOC,3     // -fi  计量芯片串口输入
#define PIN_UART5_RXD           HT_GPIOC,4     // -fi 485串口输入
#define PIN_UART5_TXD           HT_GPIOC,5     // -fo 485串口输出
#define PIN_C6                HT_GPIOC,6     // -i  悬空
#define PIN_C7                HT_GPIOC,7     // -o  悬空
#define PIN_IN_MODULE_RST       HT_GPIOC,8     // -o  模块复位
#define PIN_RELAY_ON            HT_GPIOC,9     // -o  继电器
#define PIN_RELAY_OFF           HT_GPIOC,10    // -o  继电器
#define PIN_UART2_TXD           HT_GPIOC,11    // -fo 红外发射
#define PIN_UART2_RXD           HT_GPIOC,12    // -fi 红外接收
#define PIN_EE_SCL              HT_GPIOC,13    // -o  EEPROM iic 时钟线 
#define PIN_EE_SDA              HT_GPIOC,14    // -io EEPROM iic 数据线
// #define P                HT_GPIOC,15    // -io LCD iic 时钟线

#define PIN_SEG16             HT_GPIOD,0     // -fo LCD SEG
#define PIN_SEG17             HT_GPIOD,1     // -fo LCD SEG
#define PIN_SEG18             HT_GPIOD,2     // -fo LCD SEG
#define PIN_SEG19             HT_GPIOD,3     // -fo LCD SEG
#define PIN_SEG20             HT_GPIOD,4     // -fo LCD SEG 
#define PIN_SEG21             HT_GPIOD,5     // -fo LCD SEG
#define PIN_SEG22             HT_GPIOD,6     // -fo LCD SEG
#define PIN_SEG23             HT_GPIOD,7     // -fo LCD SEG
#define PIN_COM0              HT_GPIOD,8     // -fo LCD COM
#define PIN_COM1              HT_GPIOD,9     // -fo LCD COM
#define PIN_COM2              HT_GPIOD,10    // -fo LCD COM
#define PIN_COM3              HT_GPIOD,11    // -fo LCD COM
#define PIN_RELAY_LED         HT_GPIOD,12    // -o  继电器LED
#define PIN_NET_STATUS        HT_GPIOD,13    // -i  
#define PIN_D14            HT_GPIOD,14    // - 悬空
#define PIN_MODULE_SLEEP        HT_GPIOD,15    // o 

#define PIN_BLE_MOD_RST         HT_GPIOE,0   // -o  蓝牙复位
#define PIN_UART4_TXd           HT_GPIOE,1   // -fo 蓝牙串口输出
#define PIN_UART4_RXd           HT_GPIOE,2   // -fi 蓝牙串口输入
#define PIN_FLASH_MISO          HT_GPIOE,3   // -i  SPI1 MISO
#define PIN_UART3_RXD           HT_GPIOE,4   // -fi 打印
#define PIN_UART3_TXD           HT_GPIOE,5   // -fo 打印
#define PIN_FLASH_SCLK          HT_GPIOE,6   // -o SPI1 SCLK
#define PIN_LVDIN0              HT_GPIOE,7   // -o  LVDIN0检测输入
#define PIN_FLASH_CS            HT_GPIOE,8   // -o  spi1片选
#define PIN_E10               HT_GPIOE,10    // -悬空
#define PIN_E11               HT_GPIOE,11    // -悬空
#define PIN_E12               HT_GPIOE,12    // -悬空
#define PIN_E13               HT_GPIOE,13    // -悬空
#define PIN_E14               HT_GPIOE,14    // -悬空
#define PIN_E15               HT_GPIOE,15    // -悬空

/* Exported macro ------------------------------------------------------------*/
///以下根据芯片手册寄存器描述进行配置。当没有相应功能时，应定义为空，不能直接注释掉
/* @brief 寄存器操作方式配置GPIO输入 */
#define HAL_GPIO_DIR_IN(port,pin)     ((port->PTDIR) &= ~(1 << (pin))) 
/* @brief 寄存器操作方式配置GPIO输出 */
#define HAL_GPIO_DIR_OUT(port,pin)    ((port->PTDIR) |= (1 << (pin)))

/* @brief 寄存器操作方式配置获取GPIO输入电平 */
#define HAL_GPIO_IN_GET(port,pin)     ((port->PTDAT) & (1 << pin))
/* @brief 寄存器操作方式配置获取GPIO输出电平 */
#define HAL_GPIO_OUT_GET(port,pin)    ((port->PTDAT) & (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出低电平 */
#define HAL_GPIO_OUT_RST(port,pin)    ((port->PTCLR) = (1 << pin))
/* @brief 寄存器操作方式配置GPIO输出高电平 */
#define HAL_GPIO_OUT_SET(port,pin)    ((port->PTSET) = (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出翻转电平 */
#define HAL_GPIO_OUT_REV(port,pin)    ((port->PTTOG) = (1 << pin))
/* @brief 寄存器操作方式配置GPIO上拉使能 */
#define HAL_GPIO_PTUP_EN(port,pin)    ((port->PTUP) &= ~(1 << pin))

/* @brief 寄存器操作方式配置GPIO上拉关闭 */
#define HAL_GPIO_PTUP_DIS(port,pin)   ((port->PTUP) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO开漏关闭 */
#define HAL_GPIO_PTOD_DIS(port,pin)   ((port->PTOD) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO开漏开启 */
#define HAL_GPIO_PTOD_EN(port,pin)   ((port->PTOD) &= ~(1 << pin))
/* @brief 获取GPIO寄存器名称及管脚编号 */
#define HAL_GPIO_PORT(port,pin)       (port)
#define HAL_GPIO_PIN(port,pin)        (pin)

///以下为宏参数分解函数，无须更改。供底层驱动调用。
/* @brief 获取管脚的端口及编号分配 */
#define gpio_port(x)        HAL_GPIO_PORT(x)
#define gpio_pin(x)         HAL_GPIO_PIN(x)
#define gpio_pin_mask(x)    (1 << HAL_GPIO_PIN(x))
/* @brief 设置管脚输入模式 */
#define gpio_set_input(x)   HAL_GPIO_DIR_IN(x)
/* @brief 设置管脚输出模式 */
#define gpio_set_output(x)  HAL_GPIO_DIR_OUT(x)
/* @brief 获取管脚输入电平 */
#define gpio_input_get(x)   boolof(HAL_GPIO_IN_GET(x))
/* @brief 获取管脚输出电平 */
#define gpio_output_get(x)  boolof(HAL_GPIO_OUT_GET(x))
/* @brief 管脚置高电平 */
#define gpio_out_H(x)       HAL_GPIO_OUT_SET(x)
/* @brief 管脚置低电平 */
#define gpio_out_L(x)       HAL_GPIO_OUT_RST(x)
/* @brief 管脚反转电平 */
#define gpio_out_rev(x)     HAL_GPIO_OUT_REV(x)
/* @brief 管脚打开上拉 */
#define gpio_up_en(x)       HAL_GPIO_PTUP_EN(x)
/* @brief 管脚关闭上拉 */
#define gpio_up_dis(x)      HAL_GPIO_PTUP_DIS(x)
/* @brief 管脚打开开漏 */
#define gpio_od_en(x)       HAL_GPIO_PTOD_EN(x)
/* @brief 管脚关闭开漏 */
#define gpio_od_dis(x)      HAL_GPIO_PTOD_DIS(x)

/* @brief 外部中断服务函数枚举 */
typedef enum
{
    TYPE_EXTI0 = 0,
    TYPE_EXTI1,
    TYPE_EXTI2,
    TYPE_EXTI3,
    TYPE_EXTI4,
    TYPE_EXTI5,
    TYPE_EXTI6,
    TYPE_EXTI7,
    TYPE_EXTI8,
    TYPE_EXTI9,
    TYPE_EXTI_NUM,
} GPIO_EXTI_TYPE;

typedef enum
{
    GPIO_OPEN,
    GPIO_CLOSE,
    GPIO_MONITOR,
}GPIO_INIT_TYPE_t;

/* Exported functions ------------------------------------------------------- */
struct hal_gpio_t
{
    /// @brief  串口GPIO配置
    void (*uart_init)(uint8_t com);

    /// @brief  GPIO配置
    void (*init)(void);

    /// @brief  GPIO低功耗下初始化
    void (*init_nopower)(void);

    /// @brief  输出脉冲模式设置
    void (*pulse_out_mode)(uint8_t mode);
    
    /// @brief IO口扫描，typ=0只扫描电源检测IO口，typ=1扫描所有IO口
    void (*monitor)(uint8_t typ);

    /// @brief 外部中断服务函数设置
    void (*exti_set)(uint8_t irq, void func(void));

    /// @brief lcd GPIO初始化
    void (*ext_lcd)(GPIO_INIT_TYPE_t typ);

    /// @brief FLASH GPIO初始化
    void (*data_flash)(GPIO_INIT_TYPE_t type);

    /// @brief eeprom GPIO初始化
    void (*ext_eeprom)(GPIO_INIT_TYPE_t type);

    /// @brief 计量芯片GPIO初始化
    void (*mic_init)(GPIO_INIT_TYPE_t type);

    /// @brief 外部通讯模组GPIO初始化
    void (*remote_module)(GPIO_INIT_TYPE_t type);
};
extern const struct hal_gpio_t hal_gpio;


#endif /* __HAL_GPIO_H */

/** @} */
/** @} */
// #ifdef __cplusplus
// }
// #endif

