/**
 ******************************************************************************
 * @file    api.h
 * <AUTHOR> @date    2024
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef API_H
#define API_H
#include "typedef.h"
#include "DLT_698_typedef.h"
#include "ver.h"

#define METER_SN_LEN 6            // 定义表号最大长度，实际以外部配置为 BCD编码
#define ASSET_CODE_LEN 32         // 定义资产号最大长度，实际以外部配置为准
#define BAR_CODE_LEN 22           // 定义生产条码长度
#define HW_VER_LEN 32             // 硬件版本长度
#define METER_DATE_LEN 10         // 定义生产日期最大长度，实际以外部配置为准
#define USER_CODE_LEN 6           // 用户码长度
#define PASSWORD_NUM 10           // 密码个数
#define PAYMENT_PASSWORD_LEN 4    // 支付密码长度
#define DES_KEY_LEN 8             // DES密钥长度
#define ACCOUNT_CODE_LEN 4        // 账户长度

#define DES_KEY_DEFAULT 0x829052EF8BE94987     // 默认DES密钥
#define PAYMENT_PASSWORD_DEFAULT 0x19800000    // 默认支付密码

#define MANUFACTURER "SheWei"           ///< 厂商名称
#define PROTOCOL_VER "DLT/T645-2007"    ///< 协议版本

#define PRODUCT_INFO(tag) member_offset(product_info_s, tag)

typedef enum protocol_type_e
{
    PROTOCOL_645 = 0,    ///< DLT645-2007协议
    PROTOCOL_698,        ///< DLT698协议
    PROTOCOL_10376       ///< QGWD10376协议
} protocol_type_t;

/// @brief 数据格式定义，用于曲线捕获，显示
typedef struct
{
    uint8_t unit;    // 单位
    uint8_t type;    // 数据类型
    uint8_t len;     // 数据长度
    uint8_t sign;    // 符号位
} data_format_s;

typedef struct
{
    data_format_s (*format)(uint32_t id);
    uint16_t (*data)(uint32_t id, void *data);
} dlt645_data_s;

/* 定义产品相关信息 */
typedef struct
{
    uint8_t lock_status;                               // 锁表状态00-未锁表 其它值-已锁表, 厂家设置
    uint8_t production_model;                          // 生产模式，0x5A - 用户模式，其他 - 生产模式
    uint8_t activity_model;                            // 运营模式，0xA5 - 运营模式，其他 - 非运营模式
    uint8_t meter_sn[1 + METER_SN_LEN];                // 表号, 厂家设置 len(1byte) + data
    uint8_t asset_code[1 + ASSET_CODE_LEN];            // 资产号, 厂家设置 len(1byte) + data
    uint8_t meter_bc[1 + BAR_CODE_LEN];                // 电表生产条码  len(1byte) + data
    uint8_t module_bc[1 + BAR_CODE_LEN];               // 模块生产条码  len(1byte) + data
    uint8_t manufacture_date[1 + METER_DATE_LEN];      // 生产年月日,例：20240930， 厂家设置
    uint8_t cal_date[1 + METER_DATE_LEN];              // 生产年月日,例：20240930， 厂家设置
    uint8_t hardware_ver[1 + HW_VER_LEN];              // 硬件版本
    uint8_t user_code[1 + 6];                          // 用户码，可选，厂家设置
    uint8_t last_firmware_ver[1 + VERSION_LEN_MAX];    // 上一版本号，len(1byte) + data
#if SHE_WEI_PAYMENT_PROTOCOL == 1
    union
    {
        uint64_t des_key;
        uint8_t des_key_bytes[8];
    };
    union
    {
        uint32_t payment_password;
        uint8_t payment_password_bytes[4];
    };
    union
    {
        uint32_t account_code;
        uint8_t account_code_bytes[4];
    };
#endif
} product_info_s;

/// @brief 密码等信息
typedef struct
{
    uint16_t crc;
    uint16_t chk;

    uint8_t password[PASSWORD_NUM][3];    // 645协议密码，0-9级密码，每个密码3字节

} private_info_s;

struct api_s
{
    bool (*data_get)(uint32_t id, void *data);
    bool (*data_set)(uint32_t id, void *data);
    /// @brief 读取电能
    uint16_t (*energy_get)(uint32_t id, void *data);
    /// @brief 读取产品信息
    /// @param tag 偏移地址
    /// @param out 输出缓冲区
    /// @return    返回数据长度+1，因为长度字节也算在内
    uint16_t (*product_info_get)(uint16_t tag, void *out);
    /// @brief 设置产品信息
    /// @param tag 偏移地址
    /// @param in  输入缓冲区
    /// @param len 输入数据长度
    /// @return    返回true表示成功，false表示失败
    bool (*product_info_set)(uint16_t tag, const void *in, uint16_t len);
    /// @brief 表号获取，也作为电表通讯地址。
    /// @param buf
    /// @return
    uint8_t (*meter_sn_get)(uint8_t *buf);
    /// @brief 协议帧长度解析
    uint16_t (*protocol_len_parse)(const uint8_t *buf, uint16_t len, protocol_type_t type);
    /// @brief 645密码比对
    /// @param buf  密码数据
    /// @return 密码等级 0-9级密码，0xFF表示密码错误
    uint8_t (*dlt645_password_query)(uint8_t *buf);
    /// @brief 设置645密码
    /// @param buf 密码数据
    /// @param tye 密码类型
    /// @return 成功返回true，失败返回false
    bool (*dlt645_password_set)(uint8_t *buf);

    bool (*is_factory_mode)(void);
};
extern const struct api_s api;

extern const dlt645_data_s c0_data;
extern const dlt645_data_s c1_data;
extern const dlt645_data_s c2_data;
extern const dlt645_data_s c3_data;
extern const dlt645_data_s c4_data;
extern const dlt645_data_s c5_data;
extern const dlt645_data_s c6_data;
extern const dlt645_data_s c7_data;

#endif /* API_H */
