/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   ram.c
 *
 * @brief  Source file for RAM functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ram.h"
/**
 *******************************************************************************
 ** \addtogroup ramGroup
 ******************************************************************************/
//@{

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 *****************************************************************************
 ** \brief Ram奇偶校验出错地址获取
 **
 ** 
 ** \retval ERROR ADDRESS                                    
 *****************************************************************************/
uint32_t Ram_ErrAddrGet(void)
{    
    return M0P_RAM->ERRADDR;
}

/**
 *****************************************************************************
 ** \brief Ram中断标志获取
 **
 ** 
 ** \retval TRUE or FALSE                                     
 *****************************************************************************/
boolean_t Ram_GetIntFlag(void)
{    
    if(M0P_RAM->IFR & 0x1)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 *****************************************************************************
 ** \brief Ram中断标志清除
 **
 **
 ** \retval Null                                      
 *****************************************************************************/
void Ram_ClearIntFlag(void)
{
    M0P_RAM->ICLR = 0u;    
}

/**
 *****************************************************************************
 ** \brief Ram中断使能
 ** 
 ** \retval Null                                     
 *****************************************************************************/
void Ram_EnableIrq (void)
{
    M0P_RAM->CR |= 0x2u;  
}

/**
 *****************************************************************************
 ** \brief ram中断禁止
 ** 
 ** \retval Null                                      
 *****************************************************************************/
void Ram_DisableIrq(void)
{
    M0P_RAM->CR &= 0x1;
}


//@} // RamGroup

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
