/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   debug.c
 *
 * @brief  Source file for DEBUG functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "hc32l19x_debug.h"
/**
 *******************************************************************************
 ** \addtogroup FlashGroup
 ******************************************************************************/
//@{

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 *****************************************************************************
 ** \brief 调试模式下模块功能计数使能
 **
 ** 
 ** \retval TRUE or FALSE                                      
 *****************************************************************************/
en_result_t Debug_ActiveEnable(en_debug_module_active_t enModule)
{
    M0P_DEBUG_ACTIVE->DEBUG_ACTIVE &= ~(uint32_t)enModule;

    return Ok;
}

/**
 *****************************************************************************
 ** \brief 调试模式下模块功能计数暂停
 **
 ** 
 ** \retval TRUE or FALSE                                      
 *****************************************************************************/
en_result_t Debug_ActiveDisable(en_debug_module_active_t enModule)
{
    M0P_DEBUG_ACTIVE->DEBUG_ACTIVE |= (uint32_t)enModule;
    
    return Ok;
}


//@} // BgrGroup

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
