import argparse

VERIFYTAG_POS = 24

hex_table = {
    0: '0', 1: '1',  2: '2',  3: '3',  4: '4',  5: '5',  6: '6',  7: '7',
    8: '8', 9: '9', 10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E', 15: 'F'
}

## Action: 计算hex文件一行的校验和
## Input:  line-hex文件的一行数据
## Output: 校验和
def calculate_checksum(hex_line):
    # 跳过第一个冒号以及最后两位校验和字符
    data = hex_line[1:-2]

    # 将每两个十六进制字符转换为整数并累加
    checksum = sum(int(data[i:i+2], 16) for i in range(0, len(data), 2))

    # 计算校验和（取模补码）
    checksum = (-checksum) & 0xFF

    # 将校验和转换回十六进制字符串形式，并确保结果为两位
    return format(checksum, '02X')

## Action: 把一个十六进制字节转换成两个可视字符
## Input:  hex-输入字节
## Output: 可视字符
def hex_2char(hex):
    char1 = hex_table.get((hex >> 4) & 0xf)
    char2 = hex_table.get(hex & 0xf)
    return char1 + char2

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description = '将二进制文件的头检验填充到hex文件对应位置')
    parser.add_argument('--bin_file', '-bf', type = str, required = True, help = "input a binnary file name")
    parser.add_argument('--hex_file', '-hf', type = str, required = True, help = "input a hex file name")
    args = parser.parse_args()

    # 1.从bin文件中获取APP0地址和APP0 header校验
    with open(args.bin_file, 'rb') as f:
        value = bytearray(f.read(4))
        value.reverse()
        addr = int(value.hex(),16)

        f.seek(VERIFYTAG_POS)
        bin_str = bytearray(f.read(4))

        crc32_val = ''
        for i in range(4):
            # 调用 hex_2char 函数，将 crc32_val[i] 转换为两位十六进制字符串
            crc32_val = crc32_val + hex_2char(bin_str[i])

    # 2.根据APP0地址，修改hex对应位置的数据
    with open(args.hex_file, 'r+') as f:
        while True:
            line = f.readline().strip()
            data_length = int(line[1:3],16)
            data_addr   = int(line[3:7],16)
            if data_addr == addr:
                if(data_length < VERIFYTAG_POS):
                    file_addr = f.tell()    # 记录读取到的文件指针位置
                    line = f.readline().strip()
                    line = line[0:25] + crc32_val + line[33:]
                    line = line[:-2] + calculate_checksum(line)
                    f.seek(file_addr)
                    f.write(line)
                    break