/// @file ringbuf.h
/// @brief Ring buffer utility for handling circular buffers in C

#ifndef __RINGBUF_H__
#define __RINGBUF_H__

#include <stdint.h>
#include <stdbool.h>


typedef struct ringbuffer_struct
{
    uint8_t *buf;
    uint16_t size;    // 缓冲区空间
    uint16_t in;
    uint16_t out;
} ringbuffer_s;


extern void ringbuf_init(ringbuffer_s *rb_p, uint8_t *buf, int16_t size);
extern void ringbuf_reset(ringbuffer_s *rb_p);
extern uint16_t ringbuf_data_len(ringbuffer_s *rb_p);
extern uint16_t ringbuf_space_left(ringbuffer_s *rb_p);
extern uint16_t ringbuf_write(ringbuffer_s *rb_p, const uint8_t *ptr, uint16_t length);
extern uint16_t ringbuf_read(ringbuffer_s *rb_p, uint8_t *ptr, uint16_t length);
extern uint16_t ringbuf_read_from(ringbuffer_s *rb_p, uint16_t start_offset, uint16_t length, uint8_t *outbuf);
extern uint16_t ringbuf_read_from_noclr(ringbuffer_s *rb_p, uint16_t start_offset, uint16_t length, uint8_t *outbuf);
extern uint16_t ringbuf_get_until(ringbuffer_s *rb_p, const char *pattern, uint16_t start_offset, uint8_t *outbuf);
extern int16_t ringbuf_search(const ringbuffer_s *rb_p, const char *pattern, uint16_t start_offset);

#endif
