/********************************************************************************
  * @file    profile.c
  * <AUTHOR> @date    2024
  * @brief   事件，曲线，冻结捕获
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "profile.h"
#include "..\meter\energy.h"
#include "..\config\profile_table.h"
#include "..\config\profile_capture_obj.h"
#include "utils.h"
#include "loadcurve.h"
#include "debug.h"

#define EVT_MEMBER(T,member)  {.offset = member_offset(T, member), .size = member_size(T, member)},
#define eng_get(ph,type,tariff) energy.phs_cum_value_get(ph, type, tariff)

const evt_member_s evt1_members[] = 
{
    EVT_MEMBER(evt1_s, s_time   )
    EVT_MEMBER(evt1_s, s_0pkWh  )
    EVT_MEMBER(evt1_s, s_0nkWh  )
    EVT_MEMBER(evt1_s, s_0pkvarh)
    EVT_MEMBER(evt1_s, s_0nkvarh)
    EVT_MEMBER(evt1_s, s_1pkWh  )
    EVT_MEMBER(evt1_s, s_1nkWh  )
    EVT_MEMBER(evt1_s, s_1pkvarh)
    EVT_MEMBER(evt1_s, s_1nkvarh)
    EVT_MEMBER(evt1_s, s_1v     )
    EVT_MEMBER(evt1_s, s_1i     )
    EVT_MEMBER(evt1_s, s_1kW4   )
    EVT_MEMBER(evt1_s, s_1kvar  )
    EVT_MEMBER(evt1_s, s_1pf    )
    EVT_MEMBER(evt1_s, s_2pkWh  )
    EVT_MEMBER(evt1_s, s_2nkWh  )
    EVT_MEMBER(evt1_s, s_2pkvarh)
    EVT_MEMBER(evt1_s, s_2nkvarh)
    EVT_MEMBER(evt1_s, s_2v     )
    EVT_MEMBER(evt1_s, s_2i     )
    EVT_MEMBER(evt1_s, s_2kW    )
    EVT_MEMBER(evt1_s, s_2kvar  )
    EVT_MEMBER(evt1_s, s_2pf    )
    EVT_MEMBER(evt1_s, s_3pkWh  )
    EVT_MEMBER(evt1_s, s_3nkWh  )
    EVT_MEMBER(evt1_s, s_3pkvarh)
    EVT_MEMBER(evt1_s, s_3nkvarh)
    EVT_MEMBER(evt1_s, s_3v     )
    EVT_MEMBER(evt1_s, s_3i     )
    EVT_MEMBER(evt1_s, s_3kW    )
    EVT_MEMBER(evt1_s, s_3kvar  )
    EVT_MEMBER(evt1_s, s_3pf    )
    EVT_MEMBER(evt1_s, e_0ah    )
    EVT_MEMBER(evt1_s, e_1ah    )
    EVT_MEMBER(evt1_s, e_2ah    )
    EVT_MEMBER(evt1_s, e_3ah    )
    EVT_MEMBER(evt1_s, e_time   )
    EVT_MEMBER(evt1_s, e_0pkWh  )
    EVT_MEMBER(evt1_s, e_0nkWh  )
    EVT_MEMBER(evt1_s, e_0pkvarh)
    EVT_MEMBER(evt1_s, e_0nkvarh)
    EVT_MEMBER(evt1_s, e_1pkWh  )
    EVT_MEMBER(evt1_s, e_1nkWh  )
    EVT_MEMBER(evt1_s, e_1pkvarh)
    EVT_MEMBER(evt1_s, e_1nkvarh)
    EVT_MEMBER(evt1_s, e_2pkWh  )
    EVT_MEMBER(evt1_s, e_2nkWh  )
    EVT_MEMBER(evt1_s, e_2pkvarh)
    EVT_MEMBER(evt1_s, e_2nkvarh)
    EVT_MEMBER(evt1_s, e_3pkWh  )
    EVT_MEMBER(evt1_s, e_3nkWh  )
    EVT_MEMBER(evt1_s, e_3pkvarh)
    EVT_MEMBER(evt1_s, e_3nkvarh)
};

const evt_member_s evt2_members[] = 
{
    EVT_MEMBER(evt2_s, s_time   )
    EVT_MEMBER(evt2_s, s_0pkWh  )
    EVT_MEMBER(evt2_s, s_0nkWh  )
    EVT_MEMBER(evt2_s, s_0pkvarh)
    EVT_MEMBER(evt2_s, s_0nkvarh)
    EVT_MEMBER(evt2_s, s_1pkWh  )
    EVT_MEMBER(evt2_s, s_1nkWh  )
    EVT_MEMBER(evt2_s, s_1pkvarh)
    EVT_MEMBER(evt2_s, s_1nkvarh)
    EVT_MEMBER(evt2_s, s_1v     )
    EVT_MEMBER(evt2_s, s_1i     )
    EVT_MEMBER(evt2_s, s_1kW    )
    EVT_MEMBER(evt2_s, s_1kvar  )
    EVT_MEMBER(evt2_s, s_1pf    )
    EVT_MEMBER(evt2_s, s_2pkWh  )
    EVT_MEMBER(evt2_s, s_2nkWh  )
    EVT_MEMBER(evt2_s, s_2pkvarh)
    EVT_MEMBER(evt2_s, s_2nkvarh)
    EVT_MEMBER(evt2_s, s_2v     )
    EVT_MEMBER(evt2_s, s_2i     )
    EVT_MEMBER(evt2_s, s_2kW    )
    EVT_MEMBER(evt2_s, s_2kvar  )
    EVT_MEMBER(evt2_s, s_2pf    )
    EVT_MEMBER(evt2_s, s_3pkWh  )
    EVT_MEMBER(evt2_s, s_3nkWh  )
    EVT_MEMBER(evt2_s, s_3pkvarh)
    EVT_MEMBER(evt2_s, s_3nkvarh)
    EVT_MEMBER(evt2_s, s_3v     )
    EVT_MEMBER(evt2_s, s_3i     )
    EVT_MEMBER(evt2_s, s_3kW    )
    EVT_MEMBER(evt2_s, s_3kvar  )
    EVT_MEMBER(evt2_s, s_3pf    )
    EVT_MEMBER(evt2_s, e_time   )
    EVT_MEMBER(evt2_s, e_0pkWh  )
    EVT_MEMBER(evt2_s, e_0nkWh  )
    EVT_MEMBER(evt2_s, e_0pkvarh)
    EVT_MEMBER(evt2_s, e_0nkvarh)
    EVT_MEMBER(evt2_s, e_1pkWh  )
    EVT_MEMBER(evt2_s, e_1nkWh  )
    EVT_MEMBER(evt2_s, e_1pkvarh)
    EVT_MEMBER(evt2_s, e_1nkvarh)
    EVT_MEMBER(evt2_s, e_2pkWh  )
    EVT_MEMBER(evt2_s, e_2nkWh  )
    EVT_MEMBER(evt2_s, e_2pkvarh)
    EVT_MEMBER(evt2_s, e_2nkvarh)
    EVT_MEMBER(evt2_s, e_3pkWh  )
    EVT_MEMBER(evt2_s, e_3nkWh  )
    EVT_MEMBER(evt2_s, e_3pkvarh)
    EVT_MEMBER(evt2_s, e_3nkvarh)
};

const evt_member_s evt3_members[] = 
{
    EVT_MEMBER(evt3_s, s_time   )
    EVT_MEMBER(evt3_s, s_0pkWh  )
    EVT_MEMBER(evt3_s, s_0nkWh  )
    EVT_MEMBER(evt3_s, s_0pkvarh)
    EVT_MEMBER(evt3_s, s_0nkvarh)
    EVT_MEMBER(evt3_s, s_1pkWh  )
    EVT_MEMBER(evt3_s, s_1nkWh  )
    EVT_MEMBER(evt3_s, s_1pkvarh)
    EVT_MEMBER(evt3_s, s_1nkvarh)
    EVT_MEMBER(evt3_s, s_2pkWh  )
    EVT_MEMBER(evt3_s, s_2nkWh  )
    EVT_MEMBER(evt3_s, s_2pkvarh)
    EVT_MEMBER(evt3_s, s_2nkvarh)
    EVT_MEMBER(evt3_s, s_3pkWh  )
    EVT_MEMBER(evt3_s, s_3nkWh  )
    EVT_MEMBER(evt3_s, s_3pkvarh)
    EVT_MEMBER(evt3_s, s_3nkvarh)
    EVT_MEMBER(evt3_s, e_time   )
    EVT_MEMBER(evt3_s, e_0pkWh  )
    EVT_MEMBER(evt3_s, e_0nkWh  )
    EVT_MEMBER(evt3_s, e_0pkvarh)
    EVT_MEMBER(evt3_s, e_0nkvarh)
    EVT_MEMBER(evt3_s, e_1pkWh  )
    EVT_MEMBER(evt3_s, e_1nkWh  )
    EVT_MEMBER(evt3_s, e_1pkvarh)
    EVT_MEMBER(evt3_s, e_1nkvarh)
    EVT_MEMBER(evt3_s, e_2pkWh  )
    EVT_MEMBER(evt3_s, e_2nkWh  )
    EVT_MEMBER(evt3_s, e_2pkvarh)
    EVT_MEMBER(evt3_s, e_2nkvarh)
    EVT_MEMBER(evt3_s, e_3pkWh  )
    EVT_MEMBER(evt3_s, e_3nkWh  )
    EVT_MEMBER(evt3_s, e_3pkvarh)
    EVT_MEMBER(evt3_s, e_3nkvarh) 
};

const evt_member_s evt4_members[] = 
{
    EVT_MEMBER(evt4_s, s_time   )  
    EVT_MEMBER(evt4_s, s_0pkWh  )  
    EVT_MEMBER(evt4_s, s_0nkWh  )  
    EVT_MEMBER(evt4_s, s_0pkvarh)  
    EVT_MEMBER(evt4_s, s_0nkvarh)  
    EVT_MEMBER(evt4_s, s_1pkWh  )  
    EVT_MEMBER(evt4_s, s_1nkWh  )  
    EVT_MEMBER(evt4_s, s_1pkvarh)  
    EVT_MEMBER(evt4_s, s_1nkvarh)  
    EVT_MEMBER(evt4_s, s_2pkWh  )  
    EVT_MEMBER(evt4_s, s_2nkWh  )  
    EVT_MEMBER(evt4_s, s_2pkvarh)  
    EVT_MEMBER(evt4_s, s_2nkvarh)  
    EVT_MEMBER(evt4_s, s_3pkWh  )  
    EVT_MEMBER(evt4_s, s_3nkWh  )  
    EVT_MEMBER(evt4_s, s_3pkvarh)  
    EVT_MEMBER(evt4_s, s_3nkvarh)  
    EVT_MEMBER(evt4_s, e_max_per)  
    EVT_MEMBER(evt4_s, e_time   )  
    EVT_MEMBER(evt4_s, e_0pkWh  )  
    EVT_MEMBER(evt4_s, e_0nkWh  )  
    EVT_MEMBER(evt4_s, e_0pkvarh)  
    EVT_MEMBER(evt4_s, e_0nkvarh)  
    EVT_MEMBER(evt4_s, e_1pkWh  )  
    EVT_MEMBER(evt4_s, e_1nkWh  )  
    EVT_MEMBER(evt4_s, e_1pkvarh)  
    EVT_MEMBER(evt4_s, e_1nkvarh)  
    EVT_MEMBER(evt4_s, e_2pkWh  )  
    EVT_MEMBER(evt4_s, e_2nkWh  )  
    EVT_MEMBER(evt4_s, e_2pkvarh)  
    EVT_MEMBER(evt4_s, e_2nkvarh)  
    EVT_MEMBER(evt4_s, e_3pkWh  )  
    EVT_MEMBER(evt4_s, e_3nkWh  )  
    EVT_MEMBER(evt4_s, e_3pkvarh)  
    EVT_MEMBER(evt4_s, e_3nkvarh)  
};

const evt_member_s lc1_members[] = 
{
    EVT_MEMBER(lc1_blk_s, timestamp  )
    EVT_MEMBER(lc1_blk_s, time_status)
    EVT_MEMBER(lc1_blk_s, status     )
    EVT_MEMBER(lc1_blk_s, vrms[0])
    EVT_MEMBER(lc1_blk_s, vrms[1])
    EVT_MEMBER(lc1_blk_s, vrms[2])
    EVT_MEMBER(lc1_blk_s, irms[0])
    EVT_MEMBER(lc1_blk_s, irms[1])
    EVT_MEMBER(lc1_blk_s, irms[2])
    EVT_MEMBER(lc1_blk_s, freq)
    EVT_MEMBER(lc1_blk_s, pwr_p[0])
    EVT_MEMBER(lc1_blk_s, pwr_p[1])
    EVT_MEMBER(lc1_blk_s, pwr_p[2])
    EVT_MEMBER(lc1_blk_s, pwr_p[3])
    EVT_MEMBER(lc1_blk_s, pwr_q[0])
    EVT_MEMBER(lc1_blk_s, pwr_q[1])
    EVT_MEMBER(lc1_blk_s, pwr_q[2])
    EVT_MEMBER(lc1_blk_s, pwr_q[3])
    EVT_MEMBER(lc1_blk_s, pf[0])
    EVT_MEMBER(lc1_blk_s, pf[1])
    EVT_MEMBER(lc1_blk_s, pf[2])
    EVT_MEMBER(lc1_blk_s, pf[3])
    EVT_MEMBER(lc1_blk_s, tpkWh)
    EVT_MEMBER(lc1_blk_s, tnkWh)
    EVT_MEMBER(lc1_blk_s, comb1_kvarh)
    EVT_MEMBER(lc1_blk_s, comb2_kvarh)
    EVT_MEMBER(lc1_blk_s, qx_kvarh[0])
    EVT_MEMBER(lc1_blk_s, qx_kvarh[1])
    EVT_MEMBER(lc1_blk_s, qx_kvarh[2])
    EVT_MEMBER(lc1_blk_s, qx_kvarh[3])
    EVT_MEMBER(lc1_blk_s, md_kW)
    EVT_MEMBER(lc1_blk_s, md_kvar)
};

#if (EVENT_LOSS_VOL_EN||EVENT_LOW_VOL_EN||EVENT_OVR_VOL_EN||EVENT_MISS_VOL_EN)
/// @brief 事件1：发生时刻捕获：失压，低压，过压，缺相
/// @param buf  - 事件数据缓存
/// @param idx  - 存储地址索引
/// @param typ  - 1发生时刻，0结束时刻
/// @return       返回数据长度
uint16_t profile_evt1_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t ah[4];    /// ABC 总
    uint32_t time_s;
    uint32_t time_e;

    switch (idx)
    {
    #if EVENT_LOSS_VOL_EN
        case NVM_LOG_LOSS_VOL_A:      /// 失压事件地址索引
        case NVM_LOG_LOSS_VOL_B:
        case NVM_LOG_LOSS_VOL_C:
            memcpy(ah, power_event.pwrdn_data_get()->vol_loss_ah[idx - NVM_LOG_LOSS_VOL_A], sizeof(ah));
            time_s = power_event.pwrdn_data_get()->vol_loss_time_s;
            time_e = power_event.pwrdn_data_get()->vol_loss_time_e;
            break;
    #endif
    #if EVENT_LOW_VOL_EN
        case NVM_LOG_LOW_VOL_A:       /// 欠压事件地址索引
        case NVM_LOG_LOW_VOL_B:
        case NVM_LOG_LOW_VOL_C:
            memcpy(ah, power_event.pwrdn_data_get()->vol_low_ah[idx - NVM_LOG_LOW_VOL_A], sizeof(ah));
            time_s = power_event.pwrdn_data_get()->vol_low_time_s;
            time_e = power_event.pwrdn_data_get()->vol_low_time_e;
            break;
    #endif
    #if EVENT_OVR_VOL_EN
        case NVM_LOG_OVR_VOL_A:       /// 过压事件地址索引
        case NVM_LOG_OVR_VOL_B:
        case NVM_LOG_OVR_VOL_C:
            memcpy(ah, power_event.pwrdn_data_get()->vol_ovr_ah[idx - NVM_LOG_OVR_VOL_A], sizeof(ah));
            time_s = power_event.pwrdn_data_get()->vol_ovr_time_s;
            time_e = power_event.pwrdn_data_get()->vol_ovr_time_e;
            break;
    #endif
    #if EVENT_MISS_VOL_EN
        case NVM_LOG_MISS_VOL_A:      /// 断相事件地址索引
        case NVM_LOG_MISS_VOL_B:
        case NVM_LOG_MISS_VOL_C:  
            memcpy(ah, power_event.pwrdn_data_get()->ph_miss_ah[idx - NVM_LOG_MISS_VOL_A], sizeof(ah));
            time_s = power_event.pwrdn_data_get()->ph_miss_time_s;
            time_e = power_event.pwrdn_data_get()->ph_miss_time_e;
            break;
    #endif
        default: return 0;
    }
    ah[3] = ah[2] + ah[1] + ah[0];

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt1_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
        p = buf + member_offset(evt1_s, s_0pkWh);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt1_s), 1);

        p = buf + member_offset(evt1_s, e_0ah);  // 结束时刻数据位置
        uint32_to_lsbbcd(p, ah[3], 4), p += 4;
        uint32_to_lsbbcd(p, ah[0], 4), p += 4;
        uint32_to_lsbbcd(p, ah[1], 4), p += 4;
        uint32_to_lsbbcd(p, ah[2], 4), p += 4;
        p = buf + member_offset(evt1_s, e_time);  // 结束时刻数据位置
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt1_s, e_0pkWh);
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[A_PHASE - 1] * 10),   2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[A_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[A_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[A_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [A_PHASE]  * 1000),  2), p += 2;
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[B_PHASE - 1] * 10),   2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[B_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[B_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[B_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [B_PHASE]  * 1000),  2), p += 2;        
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[C_PHASE - 1] * 10), 2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[C_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[C_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[C_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [C_PHASE]  * 1000),  2), p += 2;  
    }
    return sizeof(evt1_s);
}
#endif
#if (EVENT_LOS_CUR_EN||EVENT_OVR_CUR_EN||EVENT_MISS_CUR_EN)
/// @brief 事件2：发生时刻捕获：失流,过流,断流
/// @param buf  - 事件数据缓存
/// @param idx  - 存储地址索引
/// @param typ  - 1发生时刻，0结束时刻
/// @return       返回数据长度
uint16_t profile_evt2_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p   = buf;
    uint32_t time_s;
    uint32_t time_e;

    switch (idx)
    {
   // #if EVENT_LOS_CUR_EN
        case NVM_LOG_LOS_CUR_A:      /// 失压事件地址索引
        case NVM_LOG_LOS_CUR_B:
        case NVM_LOG_LOS_CUR_C:
    #if EVENT_LOS_CUR_EN
            time_s = power_event.pwrdn_data_get()->i_loss_time_s;
            time_e = power_event.pwrdn_data_get()->i_loss_time_e;
    #endif
            break;
    //#endif
    #if EVENT_OVR_CUR_EN
        case NVM_LOG_OVR_CUR_A:       /// 过压事件地址索引
        case NVM_LOG_OVR_CUR_B:
        case NVM_LOG_OVR_CUR_C:
            time_s = power_event.pwrdn_data_get()->i_ovr_time_s;
            time_e = power_event.pwrdn_data_get()->i_ovr_time_e;
            break;
    #endif
    #if EVENT_MISS_CUR_EN
        case NVM_LOG_MISS_CUR_A:      /// 断相事件地址索引
        case NVM_LOG_MISS_CUR_B:
        case NVM_LOG_MISS_CUR_C:  
            time_s = power_event.pwrdn_data_get()->i_miss_time_s;
            time_e = power_event.pwrdn_data_get()->i_miss_time_e;
            break;
    #endif
    }

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt2_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
        p = buf + member_offset(evt2_s, s_0pkWh);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt2_s), 1);

        p = buf + member_offset(evt2_s, e_time);  // 结束时刻数据位置
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt2_s, e_0pkWh);
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[A_PHASE - 1] * 10), 2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[A_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[A_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[A_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [A_PHASE]  * 1000),  2), p += 2;
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[B_PHASE - 1] * 10), 2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[B_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[B_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[B_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [B_PHASE]  * 1000),  2), p += 2;        
    }
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    if(typ)
    {
        uint32_to_lsbbcd(p, (uint32_t)(mic.ins->vrms[C_PHASE - 1] * 10), 2), p += 2;
        int32_to_lsbbcd(p,  (uint32_t)(mic.ins->irms[C_PHASE - 1] * 1000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_p[C_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pwr_q[C_PHASE]  * 10000), 3), p += 3;
        int32_to_lsbbcd(p,  (int32_t)(mic.ins->pf   [C_PHASE]  * 1000),  2), p += 2;  
    }
    return sizeof(evt2_s);
}
#endif
#if (EVENT_V_REV_SQR_EN||EVENT_I_REV_SQR_EN||EVENT_OVR_LOAD_EN||EVENT_REV_EN)
/// @brief profile_evt3_capture 事件3数据采集,电压 电流逆相序 潮流反向，过载
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt3_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t time_e;

    switch (idx)
    {
    #if EVENT_V_REV_SQR_EN
        case NVM_LOG_V_REV_SQR:
            time_s = power_event.pwrdn_data_get()->vol_rev_sqr_time_s;
            time_e = power_event.pwrdn_data_get()->vol_rev_sqr_time_e;
            break;
    #endif
    #if EVENT_I_REV_SQR_EN
        case NVM_LOG_I_REV_SQR:
            time_s = power_event.pwrdn_data_get()->i_rev_sqr_time_s;
            time_e = power_event.pwrdn_data_get()->i_rev_sqr_time_e;
            break;
    #endif
    }

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt3_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
        p = buf + member_offset(evt3_s, s_0pkWh);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt3_s), 1);

        p = buf + member_offset(evt3_s, e_time);  // 结束时刻数据位置
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt3_s, e_0pkWh);
    }

    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    return sizeof(evt3_s);
}
#endif
#if (EVENT_V_UNB_EN || EVENT_I_UNB_EN)
/// @brief profile_evt4_capture 事件4数据采集, 电压 电流不平衡
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt4_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t time_e;
    uint32_t max_per;

    switch (idx)
    {
    #if EVENT_V_UNB_EN
        case NVM_LOG_V_UNB:
            time_s = power_event.pwrdn_data_get()->vol_unb_time_s;
            time_e = power_event.pwrdn_data_get()->vol_unb_time_e;
            max_per= power_event.pwrdn_data_get()->vol_unb_max_per;
            break;
    #endif
    #if EVENT_I_UNB_EN
        case NVM_LOG_I_UNB:
            time_s = power_event.pwrdn_data_get()->i_unb_time_s;
            time_e = power_event.pwrdn_data_get()->i_unb_time_e;
            max_per= power_event.pwrdn_data_get()->i_unb_max_per;
            break;
    #endif
    }

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt4_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
        p = buf + member_offset(evt4_s, s_0pkWh);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt4_s), 1);
        p = buf + member_offset(evt4_s, e_max_per);  // 最大不平衡率位置
        uint32_to_lsbbcd(p, max_per, 4);
        p = buf + member_offset(evt4_s, e_time);  // 结束时刻数据位置
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt4_s, e_0pkWh);
    }

    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(A_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(B_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(C_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    return sizeof(evt4_s);
}
#endif
#if EVENT_LOW_PF_EN
/// @brief profile_evt5_capture 事件5数据采集, 低功率因素
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt5_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t time_e;

    time_s = power_event.pwrdn_data_get()->vol_unb_time_s;
    time_e = power_event.pwrdn_data_get()->vol_unb_time_e;

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt5_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
        p = buf + member_offset(evt4_s, s_0pkWh);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt5_s), 1);

        p = buf + member_offset(evt5_s, e_time);  // 结束时刻数据位置
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt5_s, e_0pkWh);
    }

    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_REA, 0), 4), p += 4;

    return sizeof(evt5_s);
}
#endif
/// @brief profile_evt6_capture 事件4数据采集 拉合闸事件
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt6_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t opt;

    switch (idx)
    {
        case NVM_LOG_DISCONNECT:
            opt    = 0; //合闸闸操作者代码
            time_s = 0; //合闸闸时间
        break;
    
        case NVM_LOG_RECONNECT:
            opt    = 0; //拉闸操作者代码
            time_s = 0; //拉闸时间
        break;
    }
    mclock.gseconds_to645(p, time_s, CLOCK_YMDhms), p += 6;
    uint32_to_lsbbcd(p, opt, 4), p += 4;

    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q1_REA,  0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q2_REA,  0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q3_REA,  0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q4_REA,  0), 4), p += 4;

    return sizeof(evt6_s);
}
#if EVENT_ALL_LOSS_VOL_EN
/// @brief profile_evt7_capture 事件7数据采集, 全失压
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt7_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t time_e;
    uint32_t current;

    time_s = power_event.pwrdn_data_get()->all_miss_time_s;
    time_e = power_event.pwrdn_data_get()->all_miss_time_e;
    current= power_event.pwrdn_data_get()->all_miss_i;

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt7_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt7_s), 1);
        p = buf + member_offset(evt7_s, s_i);
        uint32_to_lsbbcd(p, current, member_size(evt7_s, s_i)), p + member_size(evt7_s, s_i);
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
    }
    return sizeof(evt7_s);
}
#endif
/// @brief profile_evt8_capture 事件8数据采集, 掉电
/// @param buf 
/// @param idx 
/// @param typ 
/// @return 
uint16_t profile_evt8_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    uint32_t time_s;
    uint32_t time_e;

    time_s = power_event.pwrdn_data_get()->pwdn_clock.u32datetime;
    time_e = power_event.pwrdn_data_get()->pwon_clock.u32datetime;

    if(typ)
    {
        memset(p, 0xFF, sizeof(evt8_s)); /// 把数据缓存清零(读取时遇FF 转为0), 结束追加结束时刻数据时可直接写入,免去擦除时间.
        mclock.gseconds_to645(p, time_s, CLOCK_YMDhms);
    }
    else
    {
        //事件结束时，先读取最近的一条记录，再插入结束时刻数据
        clock_s t;
        mlog.fetch(log_addr(idx), buf, sizeof(evt8_s), 1);
        p = buf + member_offset(evt8_s, e_time);
        mclock.gseconds_to645(p, time_e, CLOCK_YMDhms);
    }
    return sizeof(evt8_s);
}

/// @brief 开盖事件开始时刻捕获
/// @param buf- 数据缓存 
/// @param idx- 事件存储地址索引
/// @param typ- 1 开始，0 结束
/// @return 数据长度
uint16_t profile_evt_cov_capture(uint8_t *buf, NVM_LOG_t idx, uint8_t typ)
{
    uint8_t *p = buf;
    status_rcd_t rcd_type;

    if(idx == NVM_LOG_METER_COVER)
    {
        rcd_type = TOP_COV_OPN;
    }
    else
    {
        rcd_type = BOT_COV_OPN;
    }

    status_rcd_data_s *rcd_data = mstatus.rcd_data_get(rcd_type);

    if(typ) ///事件开始时刻
    {
        memset(p, 0, sizeof(evt_cov_s));
        mclock.gseconds_to645(p, rcd_data->time_s, CLOCK_YMDhms);
        p += member_offset(evt_cov_s, s_0pkWh); 
    }
    else
    {
        mlog.fetch(log_addr(idx), buf, sizeof(evt_cov_s), 1);
        p = buf + member_offset(evt_cov_s, e_time); 
        mclock.gseconds_to645(p, rcd_data->time_e, CLOCK_YMDhms);
        p = buf + member_offset(evt_cov_s, e_0pkWh); 
    }    
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_POS_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_NEG_ACT, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q1_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q2_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q3_REA, 0), 4), p += 4;
    uint32_to_lsbbcd(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q4_REA, 0), 4), p += 4;

    return sizeof(evt_cov_s);
}



/// @brief 获取每条记录中成员data的大小,member = 0xFF 时获取整个记录大小
/// @param nvm_idx 事件记录地址索引
/// @param member  成员
/// @return 成员偏移和大小
evt_member_s profile_rcd_size_get(uint8_t nvm_idx, uint8_t member)
{
    evt_member_s evt_m = {0,0};

    switch (nvm_idx)
    {
        case NVM_LOG_MON_FROZEN:      /// 月结曲线地址索引
            break;
        case NVM_LOG_DAY_FROZEN:      /// 日结曲线地址索引
            break;
        case NVM_LOG_STEP_FROZEN:     /// 阶梯结算地址索引
            break;

        case NVM_LOG_LOSS_VOL_A:      /// 失压事件地址索引
        case NVM_LOG_LOSS_VOL_B:
        case NVM_LOG_LOSS_VOL_C:
        case NVM_LOG_LOW_VOL_A:       /// 欠压事件地址索引
        case NVM_LOG_LOW_VOL_B:
        case NVM_LOG_LOW_VOL_C:
        case NVM_LOG_OVR_VOL_A:       /// 过压事件地址索引
        case NVM_LOG_OVR_VOL_B:
        case NVM_LOG_OVR_VOL_C:
        case NVM_LOG_MISS_VOL_A:      /// 断相事件地址索引
        case NVM_LOG_MISS_VOL_B:
        case NVM_LOG_MISS_VOL_C:
            if(member == 0xFF){ evt_m.offset = 0, evt_m.size = sizeof(evt1_s);}
            else evt_m = evt1_members[member - 1];
            break;

        case NVM_LOG_ALL_LOSS_VOL:    /// 全失压事件地址索引
            {evt_m.offset = 0, evt_m.size = sizeof(evt7_s);}
            break;

        case NVM_LOG_BAK_PWR_LOS:     /// 辅助电源失电事件地址索引
            break;

        case NVM_LOG_V_REV_SQR:       /// 电压逆向序事件地址索引
        case NVM_LOG_I_REV_SQR:       /// 电流逆向序事件地址索引
        case NVM_LOG_REV_A:           /// 潮流反向事件地址索引
        case NVM_LOG_REV_B:
        case NVM_LOG_REV_C:
        case NVM_LOG_OVR_LOAD_A:      /// 过载事件地址索引
        case NVM_LOG_OVR_LOAD_B:
        case NVM_LOG_OVR_LOAD_C:
            if(member == 0xFF){ evt_m.offset = 0, evt_m.size = sizeof(evt3_s);}
            else evt_m = evt3_members[member - 1];
            break;

        case NVM_LOG_V_UNB:           /// 电压不平衡事件地址索引
        case NVM_LOG_I_UNB:           /// 电流不平衡事件地址索引
            if(member == 0xFF){ evt_m.offset = 0, evt_m.size = sizeof(evt4_s);}
            else evt_m = evt4_members[member - 1];
            break;

        case NVM_LOG_LOS_CUR_A:       /// 失流事件地址索引
        case NVM_LOG_LOS_CUR_B:
        case NVM_LOG_LOS_CUR_C:
        case NVM_LOG_OVR_CUR_A:       /// 过流事件地址索引
        case NVM_LOG_OVR_CUR_B:
        case NVM_LOG_OVR_CUR_C:
        case NVM_LOG_MISS_CUR_A:      /// 断流事件地址索引
        case NVM_LOG_MISS_CUR_B:
        case NVM_LOG_MISS_CUR_C:
            if(member == 0xFF){ evt_m.offset = 0, evt_m.size = sizeof(evt2_s);}
            else evt_m = evt2_members[member - 1];
            break;

        case NVM_LOG_LOW_PF:          /// 低功率因素事件地址索引
            {evt_m.offset = 0, evt_m.size = sizeof(evt5_s);}
            break;

        case NVM_LOG_DISCONNECT:      /// 拉闸记录
        case NVM_LOG_RECONNECT:       /// 合闸记录
            {evt_m.offset = 0, evt_m.size = sizeof(evt6_s);}
            break;

        case NVM_LOG_PWR_DOWN:        /// 掉电事件地址索引
            {evt_m.offset = 0, evt_m.size = sizeof(evt8_s);}
            break;

        case NVM_LOG_OVR_DM_POS_kW:   /// 超需量事件地址索引
        case NVM_LOG_OVR_DM_NEG_kW:
        case NVM_LOG_OVR_DM_Q1_kvar:
        case NVM_LOG_OVR_DM_Q2_kvar:
        case NVM_LOG_OVR_DM_Q3_kvar:
        case NVM_LOG_OVR_DM_Q4_kvar:
            break;

        case NVM_LOG_PROGRAM:         /// 编程事件地址索引
        case NVM_LOG_METER_CLEAN:     /// 电表清零事件地址索引
        case NVM_LOG_DEMAND_CLEAN:    /// 需量清零事件地址索引
        case NVM_LOG_EVENT_CLEAN:     /// 事件清零事件地址索引
        case NVM_LOG_SHITFT_TIME:     /// 校时事件地址索引
        case NVM_LOG_BC_TIME:         /// 广播校时事件地址索引
        case NVM_LOG_SCHEDULE:        /// 时段表事件地址索引
        case NVM_LOG_ZONE_TAB:        /// 时区表事件地址索引
        case NVM_LOG_WEEKENDS_PGM:    /// 周休日编程事件地址索引
        case NVM_LOG_HOLIDAY_PGM:     /// 节假日表编程事件地址索引
        case NVM_LOG_COMB_kWh_PGM:    /// 有功组合方式编程事件地址索引
        case NVM_LOG_COMB1_kvarh_PGM: /// 无功组合方式1编程事件地址索引
        case NVM_LOG_COMB2_kvarh_PGM: /// 无功组合方式2编程事件地址索引
        case NVM_LOG_BL_DAY_PGM:      /// 结算日编程事件地址索引
            break;

        case NVM_LOG_METER_COVER:     /// 开表盖事件地址索引
        case NVM_LOG_TEM_COVER:       /// 开端盖事件地址索引
            {evt_m.offset = 0, evt_m.size = sizeof(evt_cov_s);}
            break;
        case NVM_LC1_PROFILE:
            if(member == 0xFF) {evt_m.offset = 0, evt_m.size = sizeof(lc1_blk_s);}
            else { evt_m = lc1_members[member]; }
            break;
        case NVM_LC2_PROFILE:
           
            break;
        default:
            break;
    }
    return evt_m;
}

uint16_t profile_rcd_data_out(uint8_t nvm_idx, uint16_t fixptr, uint16_t point, uint8_t* buf)
{
    log_addr_s addr = log_addr((NVM_LOG_t)nvm_idx);
    return mlog.FIFO(addr, fixptr, point, buf, addr.size); // 取出对应偏移的已编码数据
}

/// @brief 获取事件记录数据，可以是某个成员的数据，也可以是整个事件记录数据
/// @param buf     - 数据缓存
/// @param point   - 记录指针，0最早记录，1-N最近记录
/// @param evt_type- 事件类型
/// @param member  - 事件记录数据成员序号
/// @return        - 数据长度
uint16_t profile_evt_get(uint8_t *buf, uint16_t point, evt_type_t evt_type, uint8_t member)
{
    for(uint16_t i = 0; i < eleof(evt_tab); i++)
    {
        const profile_tab_s* ptr = &evt_tab[i];
        if(ptr->et == evt_type)
        {
            log_addr_s addr = log_addr(ptr->idx);
            evt_member_s evt_member = profile_rcd_size_get(ptr->idx, member);
 
            addr.ofst += evt_member.offset;
            if(mlog.fetch(addr, buf, evt_member.size, point) == 0xFFFF) { memset(buf, 0x00, evt_member.size); }
            return evt_member.size;
        }
    }
    return 0;
}

/// @brief 获取指定事件ID的总记录数
/// @param id 事件ID
/// @return 总记录数
uint32_t profile_evt_cnt_get(evt_type_t evt_type)
{
    for(uint16_t i = 0; i < eleof(evt_tab); i++)
    {
        const profile_tab_s* ptr = &evt_tab[i];
        if(ptr->et == evt_type)
        {
            return mlog.entries_cnt_get(log_addr(ptr->idx));
        }
    }
    return 0;
}

/// @brief 负荷曲线1数据捕获
/// @param buf  - 数据缓存 
/// @param time - 时间戳
/// @param time_status - 时间状态
/// @param staus - 负荷曲线状态
/// @return 数据长度
uint16_t profile_lc1_capture(uint8_t *buf, uint32_t time, uint8_t time_status, uint8_t status)
{
    uint8_t *p = buf;

#if P_LOAD
    calendar_s calendar;
    mclock.seconds_to_calendar(&calendar, time);
    DBG_PRINTF(P_LOAD, D, "lc1 s_time: 20%d-%d-%d,  %d:%d:%d \r\n", calendar.date.year, calendar.date.month, calendar.date.day, calendar.time.hour, calendar.time.minute, calendar.time.second); 
#endif 
    
    set_lsbdata32(p, time), p += 4;
    *p = time_status, p += 1;
    *p = status, p += 1;

#if defined(POLYPHASE_METER)
    for(uint8_t i = 0; i < 3; i++) { set_lsbdata16(p, (uint16_t)(mic.ins->vrms[i] * 10)), p += 2;}
    for(uint8_t i = 0; i < 3; i++) { set_lsbdata32(p, (uint32_t)(mic.ins->irms[i] * 1000)), p += 4;}
    set_lsbdata16(p, (uint16_t)(mic.ins->freq* 100)), p += 2;

    for(uint8_t i = 0; i < 4; i++) { set_lsbdata32(p, (uint32_t)(mic.ins->pwr_p[i] * 10000)), p += 4; }
    for(uint8_t i = 0; i < 4; i++) { set_lsbdata32(p, (uint32_t)(mic.ins->pwr_q[i] * 10000)), p += 4; }

    for(uint8_t i = 0; i < 4; i++) { set_lsbdata16(p, (uint16_t)(mic.ins->pf[i] * 1000)), p += 2;}
    
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_POS_ACT, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_NEG_ACT, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_CM1_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_CM2_REA, 0)), p += 4;

    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q1_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q2_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q3_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q4_REA, 0)), p += 4;

    set_lsbdata32(p, (uint32_t)demand.blk_value_get(TYPE_DEMAND_ADD_ACT)->lst_avg_val[0]), p += 4; //demand.blk_value_get(TYPE_ENERGY_ADD_ACT)->lst_avg_val[0];
    set_lsbdata32(p, 0), p += 4; //demand.blk_value_get(TYPE_DEMAND_POS_REA)->lst_avg_val[0];
#else
    memset(p, 0, 6);   set_lsbdata16(p, (uint16_t)(mic.ins->vrms[0] * 10)),   p += 6;
    memset(p, 0, 12);  set_lsbdata32(p, (uint16_t)(mic.ins->irms[0] * 1000)), p += 12;
    set_lsbdata16(p, (uint16_t)(mic.ins->freq* 100)), p += 2;

    for(uint8_t i = 0; i < 3; i++) { set_lsbdata32(p, (uint32_t)(mic.ins->pwr_p[i] * 10000)), p += 4; }
    set_lsbdata32(p, 0), p += 4;
    for(uint8_t i = 0; i < 3; i++) { set_lsbdata32(p, (uint32_t)(mic.ins->pwr_q[i] * 10000)), p += 4; }
    set_lsbdata32(p, 0), p += 4;

    for(uint8_t i = 0; i < 4; i++) { set_lsbdata16(p, (uint16_t)(mic.ins->pf[i] * 1000)), p += 2;}
    set_lsbdata16(p, 1000), p += 2;
    
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_POS_ACT, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_NEG_ACT, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_CM1_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, LC_ENG_CM2_REA, 0)), p += 4;

    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q1_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q2_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q3_REA, 0)), p += 4;
    set_lsbdata32(p, (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q4_REA, 0)), p += 4;

    set_lsbdata32(p, 0), p += 4; //demand.blk_value_get(TYPE_ENERGY_ADD_ACT)->lst_avg_val[0];
    set_lsbdata32(p, 0), p += 4; //demand.blk_value_get(TYPE_DEMAND_POS_REA)->lst_avg_val[0];
#endif

    return (p - buf);
}

/// @brief 负荷曲线1数据捕获
/// @param buf  - 数据缓存 
/// @param time - 时间戳
/// @param time_status - 时间状态
/// @param staus - 负荷曲线状态
/// @return 数据长度
uint16_t profile_lc2_capture(uint8_t *buf, uint32_t time, uint8_t time_status, uint8_t status)
{
    // lc1_blk_s *blk = (lc1_blk_s*)buf;
    // uint16_t  len  = 0;

    // blk->timestamp   = time, len += member_size(lc1_blk_s, timestamp);
    // blk->time_status = time_status, len += member_size(lc1_blk_s, time_status);
    // blk->status.value= status, len += member_size(lc1_blk_s, status);

    // for(uint8_t i = 0; i < 3; i++) { blk->vrms[i] = (uint16_t)(mic.ins->vrms[i] * 10), len += member_size(lc1_blk_s, vrms[0]); }
    // for(uint8_t i = 0; i < 3; i++) { blk->irms[i] = (uint16_t)(mic.ins->irms[i] * 10), len += member_size(lc1_blk_s, irms[0]); }
    // blk->freq = (uint16_t)(mic.ins->freq * 100), len += member_size(lc1_blk_s, freq);
    // for(uint8_t i = 0; i < 4; i++) { blk->pwr_p[i] = (uint32_t)(mic.ins->pwr_p[i] * 10000), len += member_size(lc1_blk_s, pwr_p[0]); }
    // for(uint8_t i = 0; i < 4; i++) { blk->pwr_q[i] = (uint32_t)(mic.ins->pwr_q[i] * 10000), len += member_size(lc1_blk_s, pwr_q[0]); }
    // for(uint8_t i = 0; i < 4; i++) { blk->pf[i]    = (uint16_t)(mic.ins->pf[i] * 1000),     len += member_size(lc1_blk_s, pf[0]); }
    
    // blk->tpkWh       = (uint32_t)eng_get(T_PHASE, LC_ENG_POS_ACT, 0), len += member_size(lc1_blk_s, tpkWh);
    // blk->tnkWh       = (uint32_t)eng_get(T_PHASE, LC_ENG_NEG_ACT, 0), len += member_size(lc1_blk_s, tnkWh);
    // blk->comb1_kvarh = (uint32_t)eng_get(T_PHASE, LC_ENG_CM1_REA, 0), len += member_size(lc1_blk_s, comb1_kvarh);
    // blk->comb2_kvarh = (uint32_t)eng_get(T_PHASE, LC_ENG_CM2_REA, 0), len += member_size(lc1_blk_s, comb2_kvarh);
    // blk->qx_kvarh[0] = (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q1_REA, 0), len += member_size(lc1_blk_s, qx_kvarh[0]);
    // blk->qx_kvarh[1] = (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q2_REA, 0), len += member_size(lc1_blk_s, qx_kvarh[1]);
    // blk->qx_kvarh[2] = (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q3_REA, 0), len += member_size(lc1_blk_s, qx_kvarh[2]);
    // blk->qx_kvarh[3] = (uint32_t)eng_get(T_PHASE, TYPE_ENERGY_Q4_REA, 0), len += member_size(lc1_blk_s, qx_kvarh[3]);

    // blk->md_kW   = 0, len += member_size(lc1_blk_s, md_kW);   //demand.blk_value_get(TYPE_ENERGY_ADD_ACT)->lst_avg_val[0];
    // blk->md_kvar = 0, len += member_size(lc1_blk_s, md_kvar); //demand.blk_value_get(TYPE_DEMAND_POS_REA)->lst_avg_val[0];

    // return len;
    return 0;
}

//// @brief 负荷曲线点位获取
//// @param idx     - 曲线在NVM中的索引
//// @param para[0] - 条件参数0-最早的记录，1-按时间读取，2-读取最近的记录
//// @param fixptr  - 最早记录指针
//// @param api_idx - 符合要求的索引
void entry_idx_get(NVM_LOG_t idx, const uint8_t* para, uint16_t fixptr, uint16_t* api_idx)
{
    if(para[0] == 1)
    {
        uint32_t   stamp;
        uint32_t   start_time = get_lsbdata32(&para[1]);
        log_addr_s addr = log_addr(idx);
        uint16_t entry_in_use = mlog.entry_inuse_get(addr);

        do
        {
            (*api_idx)++;
            stamp = 0;
            mlog.FIFO(addr, fixptr, *api_idx, &stamp, 4); // 取出时间
            if(stamp < MAX_SECONDS && stamp >= start_time)
            {
                break; // 符合时间范围
            }
        } while(*api_idx <= entry_in_use);
    }
    else
    {
        (*api_idx)++;
    }
}
/// @brief 扫描符合读取要求的曲线
/// @param idx            - 曲线在NVM中的索引
/// @param para           - 条件参数0-最早的记录，1-按时间读取，2-读取最近的记录
/// @param fixed_pointer  - 最早记录指针/起始指针
/// @param valid_num      - 符合要求的记录数
void profile_entry_scan(NVM_LOG_t idx, const lc_get_s* para, uint16_t* fixed_pointer, uint16_t* valid_num)
{
    uint32_t current_stamp;
    log_addr_s addr       = log_addr(idx);
    uint16_t entry_in_use = (addr.num == 0) ? 1 : mlog.entry_inuse_get(addr);
    if(entry_in_use == 0) return;
    
    if(para->type == 0)
    {
        *valid_num = (entry_in_use > para->get_num) ? para->get_num : entry_in_use;  //读取最早的记录数
        *fixed_pointer = (addr.num == 0) ? 0 : mlog.first_pointer_get(addr); // 得到最早的记录指针
    }
    else if(para->type == 1)
    {
        uint32_t stamp;
        uint32_t target_stamp = para->time;
        uint8_t  get_num      = para->get_num;
        uint16_t pointer;
        uint16_t index = 0xFFFF;

        if(addr.num == 0) return; // 空记录
        pointer = mlog.first_pointer_get(addr); // 得到最早的记录指针
#if P_LOAD
{
        calendar_s calendar;
        mclock.seconds_to_calendar(&calendar, target_stamp);
        DBG_PRINTF(P_LOAD, D, "target_stamp: 20%d-%d-%d,  %d:%d:%d \r\n", calendar.date.year, calendar.date.month, calendar.date.day, calendar.time.hour, calendar.time.minute, calendar.time.second); 
}        
#endif 
        if(entry_in_use > 20) // (大于20条)记录数过大则采用1/2查找法, 否则采用循环查找法
        {
            uint16_t start = 1;
            uint16_t end   = entry_in_use;
            uint16_t mid;
            
            while ((start < end) && ((end - start) > 20)) //大于20条用二分法查找
            {
                mid = start + (end - start) / 2;
                current_stamp = 0;
                mlog.FIFO(addr, pointer, mid, &current_stamp, 4); ///从最早记录开始查找 
#if P_LOAD
{
        calendar_s calendar;
        mclock.seconds_to_calendar(&calendar, current_stamp);
        DBG_PRINTF(P_LOAD, D, "current_stamp: 20%d-%d-%d,  %d:%d:%d \r\n", calendar.date.year, calendar.date.month, calendar.date.day, calendar.time.hour, calendar.time.minute, calendar.time.second);  
}        
#endif
                if (current_stamp == target_stamp) { index = mid; break; }// 找到了
                else if (current_stamp > target_stamp || current_stamp > MAX_SECONDS)  
                {
                    end = mid - 1;   // 查找左半部分
                }
                else 
                {
                    start = mid + 1; // 查找右半部分
                }
            }
            if(index == 0xFFFF) // 未找到，则采用循环查找法
            {
                while(start <= end) // 循环查找
                {
                    current_stamp = 0;
                    mlog.FIFO(addr, pointer, start, &current_stamp, 4);
                    if(current_stamp >= target_stamp) break;
                    start++;
                }
                if(current_stamp >= target_stamp) 
                {
                    index = start; // 找到了
                }
                else
                {
                    *valid_num = 0; // 时间范围内无有效记录
                    return;
                }
            }
#if P_LOAD
{
        calendar_s calendar;
        mclock.seconds_to_calendar(&calendar, current_stamp);
        DBG_PRINTF(P_LOAD, D, "current_stamp start: 20%d-%d-%d,  %d:%d:%d \r\n", calendar.date.year, calendar.date.month, calendar.date.day, calendar.time.hour, calendar.time.minute, calendar.time.second);  
}
#endif
            // *fixed_pointer = index - 1; // 得到最早的记录指针
            *fixed_pointer = (pointer + index - 1) % addr.max;// 得到最早的记录指针
            *valid_num = 0;
            for(uint8_t j = 0; j < get_num; j++)
            {
                current_stamp = 0;
                if(((pointer + index + j) / entry_in_use) >= 2) break; // 超出记录数范围
                mlog.FIFO(addr, pointer, index + j, &current_stamp, 4);
                if(current_stamp >= target_stamp && current_stamp < MAX_SECONDS) { (*valid_num)++; }// 累计在时间范围内的有效点数
            }
        }
        else
        {
            for(uint16_t i = 1; (i <= entry_in_use) && (i != 0); i++)
            {
                mlog.FIFO(addr, pointer, i, &current_stamp, 4);
                // if((pointer + i) > entry_in_use) break; // 超出记录数范围
                if(current_stamp >= target_stamp && current_stamp < MAX_SECONDS)
                {
                    (*valid_num)++; // 累计在时间范围内的有效点数
                    if(index == 0xFFFF)
                    {
                        index = i; // 找到了最早的记录
                        // *fixed_pointer = i - 1; // 得到最早的记录指针
                        *fixed_pointer = (pointer + i - 1) % addr.max;// 得到最早的记录指针
                    }
                    if((*valid_num) >= get_num) break; // 累计到要求的记录数
                }
            }
        }
    }
    else
    {
        /// 读取最近的一条记录
        *valid_num = 0;
        if(addr.num > 0)
        {
            *valid_num = 1;
            *fixed_pointer = mlog.fetch(addr, &current_stamp, 4, 1); // 得到最新的记录指针
        }
    }
    DBG_PRINTF(P_LOAD, D, "entry_in_use:%d \r\n",  entry_in_use); 
    DBG_PRINTF(P_LOAD, D, "*fixed_pointer: %d\r\n", *fixed_pointer);  
}

/// @brief 负荷曲线数据格式化,转换为645协议数据
/// @param src     - 负荷曲线数据
/// @param com_buf - 645协议数据缓存
/// @return        - 645协议数据长度
uint16_t profile_lc_data_format(const uint8_t* src, uint8_t* com_buf)
{
    lc1_blk_s blk_data;

    uint8_t *data = (uint8_t*)src;
    uint8_t *buf  = (uint8_t*)com_buf;
    uint8_t *len  = (uint8_t*)buf;
    uint32_t temp32;
    // uint8_t  time_status;
    uint8_t  status;

#if P_LOAD
{
    memcpy(&blk_data, data, sizeof(lc1_blk_s));
    calendar_s calendar;
    mclock.seconds_to_calendar(&calendar, get_lsbdata32(data));
    DBG_PRINTF(P_LOAD, D, "read out lc1 time: 20%d-%d-%d,  %d:%d:%d \r\n", calendar.date.year, calendar.date.month, calendar.date.day, calendar.time.hour, calendar.time.minute, calendar.time.second); 
}
#endif

    temp32      = get_lsbdata32(data), data += 4; //时间戳
    data++; //time_status = *data, data++;  //时间状态
    status      = *data, data += sizeof(ls_status_s);  //状态
    
    if(status == PRF_DATA_INVALID) memset(buf, 0xE0, 2); //负荷记录起始码
    else memset(buf, 0xA0, 2);
    buf += 2;

    len = buf, buf++; //负荷记录字节数 
    mclock.gseconds_to645(buf, temp32, CLOCK_YMDhm), buf += 5;  //负荷记录存储时间

    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //电压A
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //电压B
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //电压C
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //电流A
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //电流B
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //电流C
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //频率
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节

    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //功率总
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //功率A
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //功率B
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //功率C
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //无功功率总
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //无功功率A
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //无功功率B
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4; //无功功率C
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节

    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //功率因素总
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //功率因素A
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //功率因素B
    uint32_to_lsbbcd(buf, get_lsbdata16(data), 2), buf += 2, data += 2; //功率因素C
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节

    uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //正向有功总电能（4字节，单位：0.01kWh）
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //反向有功总电能（4字节，单位：0.01kWh）
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //组合无功1总电能（4字节，单位：0.01kvarh）
    uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //组合无功2总电能（4字节，单位：0.01kvarh）
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节

    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //第一象限无功总电能（4字节，单位：0.01kvarh）
    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //第二象限无功总电能（4字节，单位：0.01kvarh）
    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //第三象限无功总电能（4字节，单位：0.01kvarh）
    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 4), buf += 4, data += 4; //第四象限无功总电能（4字节，单位：0.01kvarh）
    data += 16; // 跳过16字节，不解析
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节

    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4;  //当前有功需量（3字节，单位：0.0001kW）
    // uint32_to_lsbbcd(buf, get_lsbdata32(data), 3), buf += 3, data += 4;  //当前无功需量（3字节，单位：0.0001kvar）
    *buf = 0xAA, buf++; // 块分隔码：AAH，1字节
    *len = (buf - len - 1); // 负荷记录字节数
    *buf = cal_checksum8(com_buf, buf - com_buf), buf++;// 负荷记录校验码
    *buf = 0xE5, buf++; // 负荷记录结束码：55H，1字节
   
    return (buf - com_buf); // 返回负荷记录字节数
}

/// @brief 负荷曲线读取
/// @param idx  - 曲线在NVM中的索引
/// @param res  - 条件参数0-最早的记录，1-按时间读取，2-读取最近的记录
/// @param buf  - 读取到的曲线数据
/// @return     - 还有未读取的记录true，否则false
bool profile_lc_fetch(NVM_LOG_t idx, lc_get_s* res, uint8_t* buf, uint16_t buf_size)
{
    uint8_t *p_data = buf;
    log_addr_s addr = log_addr(idx);
    if(res->frame_num == 0) //第一次读取
    {   
        uint16_t fixed_pointer = 0;
        uint16_t valid_num     = 0;
        profile_entry_scan(idx, res, &fixed_pointer, &valid_num);  // 扫描符合条件的记录
        res->pointer   = fixed_pointer; // 得到最早的记录指针
        res->valid_num = valid_num;     // 得到符合条件的记录数
        res->index     = 1;             // 读取记录索引
    }
    res->size = 0;
    if(res->valid_num > 0)
    {
        
        uint16_t index;
        uint16_t size = 0;
        uint8_t  get_num;
        uint8_t  num;
        uint8_t  buffer[MAX_CAPTURE_DATA_LEN];
        evt_member_s info = profile_rcd_size_get(idx, 0); // 记录大小

        num     = buf_size / info.size; // 一次请求最大记录数
        index   = res->frame_num * num + 1;
        get_num = (res->frame_num < (res->valid_num / num)) ? num : (res->valid_num % num); //每次读取3条记录
        
        for(uint8_t i = 0; i < get_num; i++)
        {
            mlog.FIFO(addr, res->pointer, index + i, buffer, info.size);
            size = profile_lc_data_format(buffer, p_data); // 格式化数据 转换为645协议数据
            p_data += size;
        }
        if(res->frame_num != 0) { *p_data = res->frame_num, p_data++;}//uint32_to_lsbbcd(p_data, res->frame_num, 1), p_data++; }
        res->size = p_data - buf;
        index = (res->frame_num + 1) * num + 1;
        if(index > res->valid_num) {res->valid_num = 0; return false; }//读取完毕

        return true; //还有记录未读取完
    }
    return false;
}

/// @brief 负荷曲线读取，服务于376协议
/// @param res  - 条件参数0-最早的记录，1-按时间读取，2-读取最近的记录
/// @param buf  - 读取到的曲线数据
/// @param member - 负荷曲线成员
/// @param typ  - 0-获取指针，1-获取数据
/// @return     - 0-数据有效，0x01-时间无效，0xFF-无数据
uint8_t profile_lc1_fetch_for_376(lc_get_s* res, uint8_t* buf, lc1_member_t member, uint8_t typ)
{
    uint8_t *p_data = buf;
    log_addr_s addr = log_addr(NVM_LC1_PROFILE);

    if(typ == 0) // 获取指针
    {   
        uint16_t fixed_pointer = 0;
        uint16_t valid_num     = 0;
        profile_entry_scan(NVM_LC1_PROFILE, res, &fixed_pointer, &valid_num);  // 扫描符合条件的记录
        res->pointer   = fixed_pointer; // 得到最早的记录指针
        res->valid_num = valid_num;     // 得到符合条件的记录数
        res->index     = 1;             // 读取记录索引
    }
    res->size = 0;
    if(res->valid_num > 0)
    {
        evt_member_s info = profile_rcd_size_get(NVM_LC1_PROFILE, member); // 记录大小
        uint8_t status;

        if((res->frame_num + 1) > res->valid_num){ res->valid_num = 0; return 0xFF; } //读取完毕
        
        addr.ofst += info.offset; // 设置偏移地址
        mlog.FIFO(addr, res->pointer, res->frame_num + 1, p_data, info.size);
        p_data += info.size;
        
        info = profile_rcd_size_get(NVM_LC1_PROFILE, LC1_TIME_STATUS); // 获取状态
        addr.ofst += info.offset; // 设置偏移地址
        mlog.FIFO(addr, res->pointer, res->frame_num + 1, &status, 1);
        if(status == PRF_DATA_INVALID)  return 0x01; // 时间无效，可能是补冻等，数据不可靠。

        res->size = p_data - buf;
        return 0x00; // 返回1 表示数据有效
    }
    return 0xFF;
}


const struct profile_s profile =
{
#if (EVENT_LOSS_VOL_EN||EVENT_LOW_VOL_EN||EVENT_OVR_VOL_EN||EVENT_MISS_VOL_EN)
    .evt1_capture       = profile_evt1_capture,
#endif
#if (EVENT_LOS_CUR_EN||EVENT_OVR_CUR_EN||EVENT_MISS_CUR_EN)
    .evt2_capture       = profile_evt2_capture, 
#endif
#if (EVENT_V_REV_SQR_EN||EVENT_I_REV_SQR_EN||EVENT_OVR_LOAD_EN||EVENT_REV_EN)
    .evt3_capture       = profile_evt3_capture,
#endif
#if (EVENT_V_UNB_EN || EVENT_I_UNB_EN)
    .evt4_capture       = profile_evt4_capture,
#endif
#if EVENT_LOW_PF_EN
    .evt5_capture       = profile_evt5_capture,
#endif
    .evt6_capture       = profile_evt6_capture,
#if EVENT_ALL_LOSS_VOL_EN
    .evt7_capture       = profile_evt7_capture,
#endif
#if LC1_ENABLE
    .lc1_capture        = profile_lc1_capture,
#endif
#if LC2_ENABLE
    .lc2_capture        = profile_lc2_capture,
#endif
#if LC1_ENABLE || LC2_ENABLE
    .lc_fetch           = profile_lc_fetch,
    .lc1_fetch_for_376  = profile_lc1_fetch_for_376,
#endif
    .evt8_capture       = profile_evt8_capture,
    .evt_cov_capture    = profile_evt_cov_capture,
    .rcd_size_get       = profile_rcd_size_get,
    .evt_get            = profile_evt_get,
    .evt_cnt_get        = profile_evt_cnt_get,
};
