/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      local_port.h
*    Describe:      
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#ifndef __LOCAL_PORT_H__
#define __LOCAL_PORT_H__

#include "typedef.h"


// 本地串口参数结构体
typedef struct
{
    uint16_t chk;
    uint16_t crc;
    uint16_t inactivity_timeout; /// 连接超时时间
    uint16_t hangup_cnt_thd;     /// 挂起计数阈值,如果每天收到的数据帧数据验证码校验失败、密文校验失败总累计达到 200 次，
                                 /// 则终端远程设置参数、远程控制、清零功能挂起；在每日的零点，清除挂起状态及累计次数.
                                 /// 收到的抄读命令中数据验证码校验失败、密文校验失败不累计失败次数，挂起后能正常抄读。
    uint16_t auth_timeout;       /// 认证超时时间
    uint8_t  baud_rate;          /// 默认通讯速率 ，0：300，1：600，2：1200，3：2400，4：4800，5：9600，6：19200，
                                 /// 7：38400，8：57600，9：115200
    uint8_t  check_bits;         /// 校验位 0：8E1，1：8O1；2：8N1
    uint8_t  modbus_addr;        /// modbus地址 1-247 (0 表示modbus不启用)
    uint16_t modbus_delay;       /// modbus发送延时
}LocalPortPara_s;


typedef struct
{
    uint16_t chk;
    uint16_t crc;

    uint32_t hang_up_date_time;   // 上次挂起时间
    uint32_t lock_date_time;      // 上次锁定时间
    uint16_t hangup_cnt;          // 认证失败计数/挂起计数
    uint16_t lock_cnt;            // 明文合闸密码错/未授权计数（锁定计数） 
    union{
        uint8_t status;
        struct{
            uint8_t hang_up     :1;     // 挂起标志
            uint8_t lock        :1;     // 锁定标志
        };
    };
}LocalPortData_s;



struct local_port_s
{
    /// @brief 串口数据初始化
    void (*init)(uint8_t chn);
    /// @brief 串口数据重置
    void (*reset)(uint8_t typ);
    /// @brief 链接超时时间获取
    uint16_t (*inactivity_timeout_get)(uint8_t chn);
    /// @brief 链接超时时间设置
    bool (*inactivity_timeout_set)(uint8_t chn, uint16_t timeout);
    /// @brief 挂起计数阈值获取
    uint16_t (*hangup_cnt_thd_get)(uint8_t chn);
    /// @brief 挂起计数阈值设置
    bool (*hangup_cnt_thd_set)(uint8_t chn, uint16_t thd);
    /// @brief 认证超时时间获取
    uint16_t (*auth_timeout_get)(uint8_t chn);
    /// @brief 认证超时时间设置
    bool (*auth_timeout_set)(uint8_t chn, uint16_t timeout);
    /// @brief 通讯速率获取
    uint8_t (*baud_rate_get)(uint8_t chn);
    /// @brief 通讯速率设置
    bool (*baud_rate_set)(uint8_t chn, uint8_t rate);
    /// @brief 校验位获取
    uint8_t (*check_bits_get)(uint8_t chn);
    /// @brief 校验位设置
    bool (*check_bits_set)(uint8_t chn, uint8_t check);
    
    /// @brief modbus地址获取
    uint8_t (*modbus_addr_get)(uint8_t chn);
    /// @brief modbus地址设置
    bool (*modbus_addr_set)(uint8_t chn, uint8_t addr);
    /// @brief modbus发送延时获取
    uint16_t (*modbus_delay_get)(uint8_t chn);
    /// @brief modbus发送延时设置
    bool (*modbus_delay_set)(uint8_t chn, uint16_t delay);
    
    /// @brief 用于645协议的通讯速率设置和读取
    uint8_t (*baud_rate_get_645)(uint8_t chn);
    bool (*baud_rate_set_645)(uint8_t chn, uint8_t val);

   /// @brief 认证失败
   void (*auth_failed)(uint8_t chn);
   /// @brief 认证成功
   void (*auth_success)(uint8_t chn);
   /// @brief 认证剩余时间
   uint16_t (*auth_remain_time)(uint8_t chn);
   /// @brief 挂起
};

extern const struct local_port_s local_port;

#endif /* __LOCAL_PORT_H__ */


