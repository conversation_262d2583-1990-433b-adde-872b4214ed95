/**
 ******************************************************************************
 * @file    ymodem.h
 * <AUTHOR> @date    2025
 * @brief  
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#ifndef __YMODEM_H_
#define __YMODEM_H_

/// Ymodem对象定义 
#define FILENAME_MAX_LEN        32  // 支持文件名最大长度

/// @brief   Ymodem数据包结构体
typedef struct modem_struct
{
    unsigned long filelen;          // Ymodem文件长度
    char filename[FILENAME_MAX_LEN + 2]; // Ymodem文件名称
    unsigned char rec_err;          // 数据块接收状态
    unsigned int  nxt_num;          // 下一数据块序号
    unsigned int  len;              // 数据包指示长度
    unsigned char buf[1024];        // 数据包缓存
}modem_struct_t;


typedef void (*modem_send_byte_t)(unsigned char ch);                         // Ymodem串口字节发送函数
typedef char (*modem_recv_byte_t)(unsigned char* ch, unsigned int time_out); // Ymodem串口字节接收函数


/// @Ymodem触发文件传输请求, 调用前先外部申请生成一个Ymodem对象. return 0: 已选择文件  -1: 未选择文件
int ymodem_init(modem_struct_t* mblock, modem_send_byte_t modem_send, modem_recv_byte_t modem_recv);
/// @Ymodem文件数据包接收
int ymodem_recv(modem_struct_t* mblock);
/// @Ymodem取消文件传输
void ymodem_cancle(void);
/// @Ymodem字符串打印
void ymodem_print(const unsigned char* s);

#endif

