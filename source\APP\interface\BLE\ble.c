/**
 ******************************************************************************
* @file    ble.c
* <AUTHOR> @date    2024
* @brief   蓝牙驱动
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#include "ble.h"

#if (BLE_M_TYPE == BLE_M_AC6368A)
#include "ble_AC6368A.c"
#elif (BLE_M_TYPE == BLE_M_WS8201)
#include "ble_WS8201.c"
#elif (BLE_M_TYPE == BLE_M_PHY6252)
#include "ble_PHY6252.c"
#else
#error "Unsupported BLE module type"
#endif

/// @brief BLE模块任务清单
const struct ble_s ble =
{
    .init             = ble_init,
    .recv             = ble_recv,
    .send             = ble_send,
    .send_over_query  = ble_send_over_query,
};


