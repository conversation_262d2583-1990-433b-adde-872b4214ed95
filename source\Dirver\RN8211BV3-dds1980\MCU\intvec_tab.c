/**
 ******************************************************************************
* @file    intvec_tab.c
* <AUTHOR> @date    2024
* @brief   中断重定向表
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*       /// HC32L196系列中断向量表
        ; External Interrupts
            DCD     PORTA_IRQHandler
            DCD     PORTB_IRQHandler
            DCD     PORTC_E_IRQHandler
            DCD     PORTD_F_IRQHandler
            DCD     DMAC_IRQHandler
            DCD     TIM3_IRQHandler
            DCD     UART0_2_IRQHandler
            DCD     UART1_3_IRQHandler
            DCD     LPUART0_IRQHandler
            DCD     LPUART1_IRQHandler
            DCD     SPI0_IRQHandler
            DCD     SPI1_IRQHandler
            DCD     I2C0_IRQHandler
            DCD     I2C1_IRQHandler
            DCD     TIM0_IRQHandler
            DCD     TIM1_IRQHandler
            DCD     TIM2_IRQHandler
            DCD     LPTIM0_1_IRQHandler
            DCD     TIM4_IRQHandler
            DCD     TIM5_IRQHandler
            DCD     TIM6_IRQHandler
            DCD     PCA_IRQHandler
            DCD     WDT_IRQHandler
            DCD     RTC_IRQHandler
            DCD     ADC_DAC_IRQHandler
            DCD     PCNT_IRQHandler
            DCD     VC0_IRQHandler
            DCD     VC1_2_IRQHandler
            DCD     LVD_IRQHandler
            DCD     LCD_IRQHandler
            DCD     FLASH_RAM_IRQHandler
            DCD     CLKTRIM_IRQHandler

******************************************************************************/

#pragma section = ".intvec"
/* Includes ------------------------------------------------------------------*/
#include "intvec_tab.h"

extern void systick_handler(void);

#ifndef NULL
#define NULL (void *)0
#endif

#pragma location = ".boot_ram"
static void (*(int_vector_tab[INT_NUM]))();    // 中断请求向量

/**
 * @brief  This function handles NMI exception.
 * @param  None
 * @retval None
 */
__weak void NMI_Handler(void)
{
    while(1) {}
}

/**
 * @brief  This function handles Hard Fault exception.
 * @param  None
 * @retval None
 */
__weak void HardFault_Handler(void)
{
    while(1) {}
}

/**
 * @brief  This function handles SVCall exception.
 * @param  None
 * @retval None
 */
__weak void SVC_Handler(void)
{
    while(1) {}
}

/**
 * @brief  This function handles PendSVC exception.
 * @param  None
 * @retval None
 */
__weak void PendSV_Handler(void)
{
    while(1) {}
}

// /**
//   * @brief  This function handles SysTick Handler.
//   * @param  None
//   * @retval None
//   */
// __weak void SysTick_Handler(void)
// {
//     systick_handler();
// }

// 宏定义用于定义中断处理函数的声明
#define _PRAGMA(x) _Pragma(#x)
#define IRQ_FUN_DECLARATION(v, f)                          \
    void f(void)                                           \
    {                                                      \
        if(int_vector_tab[v] != NULL) int_vector_tab[v](); \
    }
// 宏定义用于定义中断处理函数的定义
IRQ_FUN_DECLARATION(INT_SYSCLKCAL, SYSCLKCAL_HANDLER)
IRQ_FUN_DECLARATION(INT_CMP, CMP_HANDLER)
IRQ_FUN_DECLARATION(INT_VCH, VCH_HANDLER)
IRQ_FUN_DECLARATION(INT_RTC, RTC_HANDLER)
IRQ_FUN_DECLARATION(INT_EMU, EMU_HANDLER)
IRQ_FUN_DECLARATION(INT_MADC, MADC_HANDLER)
IRQ_FUN_DECLARATION(INT_UART0, UART0_HANDLER)
IRQ_FUN_DECLARATION(INT_UART1, UART1_HANDLER)
IRQ_FUN_DECLARATION(INT_UART2, UART2_HANDLER)
IRQ_FUN_DECLARATION(INT_UART3, UART3_HANDLER)
IRQ_FUN_DECLARATION(INT_SPI0, SPI0_HANDLER)
IRQ_FUN_DECLARATION(INT_I2C, I2C_HANDLER)
IRQ_FUN_DECLARATION(INT_ISO78160, ISO78160_HANDLER)
IRQ_FUN_DECLARATION(INT_ISO78161, ISO78161_HANDLER)
IRQ_FUN_DECLARATION(INT_TC0, TC0_HANDLER)
IRQ_FUN_DECLARATION(INT_TC1, TC1_HANDLER)
IRQ_FUN_DECLARATION(INT_UART4, UART4_HANDLER)
IRQ_FUN_DECLARATION(INT_UART5, UART5_HANDLER)
IRQ_FUN_DECLARATION(INT_WDT, WDT_HANDLER)
IRQ_FUN_DECLARATION(INT_KBI, KBI_HANDLER)
IRQ_FUN_DECLARATION(INT_LCD, LCD_HANDLER)
IRQ_FUN_DECLARATION(INT_CP, CP_HANDLER)
IRQ_FUN_DECLARATION(INT_DMA, DMA_HANDLER)
IRQ_FUN_DECLARATION(INT_NVM, NVM_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT0, EXT0_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT1, EXT1_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT2, EXT2_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT3, EXT3_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT4, EXT4_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT5, EXT5_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT6, EXT6_HANDLER)
IRQ_FUN_DECLARATION(INT_EXT7, EXT7_HANDLER)

/// 配置中断向量调用的处理函数.
/// @param  irq-中断类型
/// @param  vec-用户处理函数(或者清中断标志函数)指针.
/// @note   本函数允许设置空函数指针NULL用于取消中断调用处理函数.但对于必须清零
///         中断标志的中断向量，必须设置包含清中断标志的函数指针。
void int_vector_set(int irq, void vec(void))
{
    if(int_vector_tab[irq] != vec) int_vector_tab[irq] = vec;
}

/** @} */
/** @} */
/** @} */
