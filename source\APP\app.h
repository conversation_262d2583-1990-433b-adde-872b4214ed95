#ifndef __APP_H
#define __APP_H

#include <string.h>
#include <stdint.h>
#include "bsp_cfg.h"
#include "bsp.h"
#if USE_LCD
#include "lcd.h"
#endif
#include "adc.h" 
#include "beep.h"
#include "key.h"
#include "eeprom.h"
#include "ext_flash.h"
#include "led.h"
#include "app_config.h"
#include "datastore.h"
#include "crc.h"

#define APP_VERSION_MAJOR 1
#define APP_VERSION_MINOR 0
#define APP_VERSION_PATCH 0

extern const struct image_header_t app_header;
extern void app_power_down_save(void);

/* 任务调度结构体定义 */
struct app_task_t
{
    void (*init)(void);
    void (*second_run)(void);
    void (*idle_run)(void);
    void (*power_down_save)(void);
    void (*power_off_init)(void);
    void (*power_off_run)(void);
};

#endif /* __APP_H */