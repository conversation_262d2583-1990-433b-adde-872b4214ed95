/**
  ******************************************************************************
  * @file    emu_virtual.c
  * <AUTHOR> @date    2024
  * @brief   虚拟计量芯片，用于测试
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include <math.h>
#include "bsp_cfg.h"
#include "mic.h"
#include "utils.h"
#include "debug.h"
#include "eeprom.h"
#include "hal_gpio.h"
#include "hal_timer.h"
#include "hal_flash.h"
#include "mic.h"
#include "math.h"

typedef union
{
    struct
    {
        uint16_t chk;       // 
        uint16_t cs;        // 校表参数校验和
        uint16_t U_gain;    // 电压增益
        uint16_t I_gain;    // 电流增益
        uint16_t P_gain;    // 功率增益
        uint16_t Phs_ofst;  // 相位校准0
        uint16_t Phs_ofst1; // 相位校准1
        uint16_t Phs_ofst2; // 相位校准2
        uint16_t P_ofst;    // 功率偏移校准
        uint16_t I_ofst;    // 电流偏移校准
        uint16_t Q_ofst;    // 无功偏置校准
        uint16_t P_ofstl;   // 功率偏移校准低字节
        uint16_t Q_ofstl;   // 无功偏置校准低字节
        uint16_t HFconst;   // 高频脉冲常数
        uint16_t I_RatioN;  // 电流调整系数
        uint16_t I_zero_reg;
	};
	uint8_t reserve[32];
}CalPara_s;

typedef struct
{
    CalPara_s     cal_para[CHANNEL_NUM];    
    MeasurePara_s mic_para;
}EepromReserveSpace_s;

typedef struct
{
    SMALLOC(EepromReserveSpace_s, space, 512);  /// 如果这里编译报错，需在datastore.h中修改 CAL_DATA_SIZE 的大小(修改为128的整数倍)直到编译通过，默认是 256.
} MicStore_s;


#define CAL_CS16(ptr,len)       crc16(0,(uint8_t*)(ptr) + 4, len - 4)

#define ee_cal_read(x,y,z)      eeprom.read(x,y,z)
#define ee_cal_write(x,y,z)     eeprom.write(x,y,z)

#define mcu_cal_read(x,y,z)     hal_flash.read((MCU_CAL_DATA_BASE + x), y, z)
#define mcu_cal_write(x,y,z)    hal_flash.write((MCU_CAL_DATA_BASE + x), y, z)
#define MEASURE_PARA_ADDR       (member_offset(EepromReserveSpace_s, mic_para))  // 计量参数地址

static uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph);

static uint8_t       pulse_cnt[2];          // 合相有功, 无功
static CalPara_s     cal_para[CHANNEL_NUM]; // att7022e-校正参数
static MeasurePara_s measure_para;          // 计量参数
static InstantVal_s  instant_value;         // 瞬时计量数据块,如电流电压频率功率等等
static bool          measure_status;        // 计量状态

static uint8_t energy_integral_cnt[3][4];   //P Q S 三相合相电能脉冲计数
static int32_t energy_integral[3][4];       //P Q S 三相合相电能积分值

static virtrual_meter_data_t virtrual_meter_data;
const virtrual_meter_data_t virtrual_meter_data_default = 
{
    .urms = 
    {
        220.0,  //A
        220.0,  //B
        220.0   //C
    },
    .irms = 
    {
        0.0,
        0.0,
        0.0,
        0.0,
        0.0
    },
    .vi_angle =
    {
        360.0,
        360.0,
        360.0
    },
    .pt_ratio = 1.0,
    .ct_ratio = 1.0
};

/// @brief 根据功率符号计算相位
static PWR_QUADRANT emu_quadrant_in(EMU_PHASE_TYPE ph)
{
    if(instant_value.pwr_p[ph] >= 0)
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_1 : QUADRANT_4;
    }
    else
    {
        return (instant_value.pwr_q[ph] >= 0) ? QUADRANT_2 : QUADRANT_3;
    }
}

static float emu_pf_val(EMU_PHASE_TYPE ph)
{
    float pf;

    if(equ(instant_value.pwr_s[ph], 0)) return 1.0;
    pf = fabs(instant_value.pwr_p[ph] / instant_value.pwr_s[ph]);
    if(pf > 1.0) return  1.0;
    return pf;
}

static void virtual_data_calculate(void)
{
    instant_value.pt_ratio = virtrual_meter_data.pt_ratio; // PT变比
    instant_value.ct_ratio = virtrual_meter_data.ct_ratio; // CT变比
    instant_value.pwr_s[0] = 0;
    instant_value.pwr_p[0] = 0;
    instant_value.pwr_q[0] = 0;
    for(uint8_t i = 0; i < 3; i++)
    {
        instant_value.vrms[i]         = virtrual_meter_data.urms[i];
        instant_value.irms[i]         = virtrual_meter_data.irms[i];
        instant_value.vi_angle[i]     = virtrual_meter_data.vi_angle[i];
        instant_value.vv_angle[i]     = virtrual_meter_data.vv_angle[i];
        instant_value.pwr_s[i + 1]    = instant_value.vrms[i] * instant_value.irms[i] / 1000.0;
        instant_value.pwr_p[i + 1]    = instant_value.pwr_s[i + 1] * cos(instant_value.vi_angle[i] * 3.1415926 / 180.0);
        instant_value.pwr_q[i + 1]    = instant_value.pwr_s[i + 1] * sin(instant_value.vi_angle[i] * 3.1415926 / 180.0);

        instant_value.quadrant[i + 1] = emu_quadrant_in((EMU_PHASE_TYPE)(i + 1));
        instant_value.pf[i + 1]       = emu_pf_val((EMU_PHASE_TYPE)(i + 1));

        instant_value.pwr_s[0] += instant_value.pwr_s[i + 1];
        instant_value.pwr_p[0] += instant_value.pwr_p[i + 1];
        instant_value.pwr_q[0] += instant_value.pwr_q[i + 1];

        instant_value.pwr_abs[P_POWER]  += fabs(instant_value.pwr_p[i + 1]);
        instant_value.pwr_abs[Q_POWER]  += fabs(instant_value.pwr_q[i + 1]);
        instant_value.pwr_abs[S_POWER]  += fabs(instant_value.pwr_s[i + 1]);
    }
    instant_value.pf[0]       = emu_pf_val(T_PHASE);
    
    instant_value.freq        = 50.0;
    instant_value.n_irms      = virtrual_meter_data.irms[3];
    instant_value.v_irms      = virtrual_meter_data.irms[4];
    instant_value.stus.pt_rev = boolof(less(instant_value.pwr_p[T_PHASE], 0));
    instant_value.stus.pa_rev = boolof(less(instant_value.pwr_p[A_PHASE], 0));
    instant_value.stus.pb_rev = boolof(less(instant_value.pwr_p[B_PHASE], 0));
    instant_value.stus.pc_rev = boolof(less(instant_value.pwr_p[C_PHASE], 0));

    instant_value.stus.qt_rev = boolof(less(instant_value.pwr_q[T_PHASE], 0));
    instant_value.stus.qa_rev = boolof(less(instant_value.pwr_q[A_PHASE], 0));
    instant_value.stus.qb_rev = boolof(less(instant_value.pwr_q[B_PHASE], 0));
    instant_value.stus.qc_rev = boolof(less(instant_value.pwr_q[C_PHASE], 0));

    instant_value.stus.pa_start = boolof(fabs(instant_value.pwr_p[A_PHASE]) >= START_PWR);
    instant_value.stus.pb_start = boolof(fabs(instant_value.pwr_p[B_PHASE]) >= START_PWR);
    instant_value.stus.pc_start = boolof(fabs(instant_value.pwr_p[C_PHASE]) >= START_PWR);

    instant_value.quadrant[0] = emu_quadrant_in(T_PHASE);
}

/* Public functions ----------------------------------------------------------*/
/// @brief 刷新实时数据
static void instant_refresh(void)
{
    static virtrual_meter_data_t last_data = {0};
    if(measure_status == false) measure_status = true;

    if(memcmp(&last_data, &virtrual_meter_data, sizeof(virtrual_meter_data_t)) != 0)
    {
        last_data = virtrual_meter_data;
        virtual_data_calculate();
    }
    DBG_PRINTF(P_EMU, D, "\r\n measure instant value: \r\n");
    DBG_PRINTF(P_EMU, D, "VOL:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vrms[0], instant_value.vrms[1], instant_value.vrms[2]);
    DBG_PRINTF(P_EMU, D, "CUR:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.irms[0], instant_value.irms[1], instant_value.irms[2]);
    DBG_PRINTF(P_EMU, D, "V-V:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vv_angle[0], instant_value.vv_angle[1], instant_value.vv_angle[2]);
    DBG_PRINTF(P_EMU, D, "V-I:  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.vi_angle[0], instant_value.vi_angle[1], instant_value.vi_angle[2]);
    DBG_PRINTF(P_EMU, D, "ACT:  %-10.4f\t%-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.pwr_p[1], instant_value.pwr_p[2], instant_value.pwr_p[3], instant_value.pwr_p[0]);
    DBG_PRINTF(P_EMU, D, "PF :  %-10.4f\t%-10.4f\t%-10.4f\r\n", instant_value.pf[0], instant_value.pf[1], instant_value.pf[2]);
    DBG_PRINTF(P_EMU, D, "FREQ: %-10.4f\r\n", instant_value.freq);
    DBG_PRINTF(P_EMU, D, "\r\n");
}

/// 电能获取
static uint8_t measure_pulse_get(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph)
{
    uint8_t pulse_num;

    HAL_CRITICAL_STATEMENT(
        pulse_num = energy_integral_cnt[type][ph];
        energy_integral_cnt[type][ph] = 0;
    );

    return (pulse_num < MAX_PULSE_PER_SECOND) ? pulse_num : 0;
}

/// 快速脉冲计数器获取，掉电时使用
static uint8_t measure_fast_cnt_add(EMU_PHASE_TYPE ph, uint32_t* fast_cnt, uint8_t isclac)
{
    uint8_t cnt;
    if(!isclac)
    {
        int32_t val = energy_integral[P_POWER][ph];
        if(val >= 0x800000) val -= (1ul << 24); // 快速脉冲计数寄存器为负值处理
        (*fast_cnt) += labs(val);
    }
    else
    {
        cnt = (*fast_cnt) / ENERGY_PULSE_THRESHOLD_S * 100;
        (*fast_cnt) %= ENERGY_PULSE_THRESHOLD_S * 100;
    }
    return cnt;
}

/// @brief 电能积分,放秒中断
static void emu_energy_integral(void)
{
    for(uint8_t i = 0; i < 4; i++)
    {
        energy_integral[P_POWER][i] += (int32_t)(instant_value.pwr_p[i] * 100000);// 0.01 W
        energy_integral[Q_POWER][i] += (int32_t)(instant_value.pwr_q[i] * 100000);// 0.01 var
        energy_integral[S_POWER][i] += (int32_t)(instant_value.pwr_s[i] * 100000);// 0.01 VA
        while(energy_integral[P_POWER][i] > ENERGY_PULSE_THRESHOLD_S * 100)  //功率为0.01W，所以脉冲阈值要乘以100
        {
            energy_integral[P_POWER][i] -= ENERGY_PULSE_THRESHOLD_S * 100;
            energy_integral_cnt[P_POWER][i]++;
        }
        while(energy_integral[Q_POWER][i] > ENERGY_PULSE_THRESHOLD_S * 100)
        {
            energy_integral[Q_POWER][i] -= ENERGY_PULSE_THRESHOLD_S * 100;
            energy_integral_cnt[Q_POWER][i]++;
        }
        while(energy_integral[S_POWER][i] > ENERGY_PULSE_THRESHOLD_S * 100)
        {
            energy_integral[S_POWER][i] -= ENERGY_PULSE_THRESHOLD_S * 100;
            energy_integral_cnt[S_POWER][i]++;
        }
    }
}

/// @brief 计量芯片初始化
/// @param pwr_down 1-掉电时初始化，0-上电时初始化
static void measure_ic_init(uint8_t pwr_down)
{
    if(pwr_down) hal_rtc.energy_integral_set(NULL);
    else hal_rtc.energy_integral_set(emu_energy_integral);
    virtrual_meter_data = virtrual_meter_data_default;
    virtual_data_calculate();
    memset(energy_integral_cnt, 0, sizeof(energy_integral_cnt));
    memset(energy_integral,     0, sizeof(energy_integral));
}

/// @brief 计量芯片关闭
/// @param  
static void measure_ic_off(void)
{
    hal_rtc.energy_integral_set(NULL);
    virtrual_meter_data.irms[0] = 0;
    virtrual_meter_data.irms[1] = 0;
    virtrual_meter_data.irms[2] = 0;
    virtual_data_calculate();
}

//#pragma optimize=none
/// @brief 校表处理
/// @param chn 通道
/// @param step 校表步骤
/// @param pdat_std 标准数据缓冲
/// @param pdat_samp 采样数据缓冲
/// @return 
static uint32_t calibrate_proc(uint8_t chn, uint8_t step, void* pdat_std, void* pdat_samp)
{
    return 0;
}

static void virtual_set_virtual_mic_data(uint8_t *buf)
{
    memcpy(&virtrual_meter_data, buf, sizeof(virtrual_meter_data_t));
    virtual_data_calculate();
}

/// @brief 计量芯片接口
const struct mic_s mic = {
    .ins                  = &instant_value,
    .init                 = measure_ic_init,
    .off                  = measure_ic_off,
    .ins_val_refresh      = instant_refresh,
    .poweroff_run         = NULL,
    .pulse                = measure_pulse_get,
    .cali                 = calibrate_proc,
    .measure_fast_cnt_add = measure_fast_cnt_add,
#if (EMU_TYPE == EMU_VIRTUAL) 
    .set_virtual_mic_data = virtual_set_virtual_mic_data,
#endif
};

///

