
/**
 ******************************************************************************
 * @file    hal_mcu.c
 * <AUTHOR> @date    2024
 * @brief   本模块主要包括MCU的内核、时钟总线、复位源、电源功耗管理等芯片级驱动。
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "rn821x_rn721x_soc_madc.h"
#include "hal_mcu.h"
#include "hal_gpio.h"
#include "hal_timer.h"
#include "bsp_cfg.h"
// #include <stdint.h>

/* Private typedef -----------------------------------------------------------*/
/* @brief 供电检测模式定义 */
typedef enum
{
    PWR_ON,     // 上电电压检测
    PWR_OFF,    // 掉电电压检测
} PVD_MODE;

/* Private define ------------------------------------------------------------*/
#define TEST_POR_TIMES 10    // 检测系统上电电压次数
#define TEST_PDR_TIMES 100    // 检测系统下电电压次数

#define MCU_VOL_CMP
#define BAT_VOL_CMP

/* Private macro -------------------------------------------------------------*/
#define HRC_ADJ ((*(uint32_t *)0x00040140) & 0x3F)    /// HRC载入校准值

static McuCoreStus_s mcu_stus;
/* Private variables ---------------------------------------------------------*/
__weak uint32_t SystemCoreClock = WORK_RC_HZ;    // 系统复位后默认时钟

#if USE_SAG_TYPE != SAG_NULL
static bool (*is_sag)(void);
#endif
static uint8_t InstructionsPerUs = WORK_RC_HZ / 2000000;
static uint8_t lvd_intrpt        = FALSE;

/* Private functions ---------------------------------------------------------*/
void hal_mcu_wait_us(uint16_t nUs);

// PMU中断处理函数
void irq_handler_lvd(void)
{
    if((MADC->LVD_STAT & MADC_LVD_STAT_CMP2IIF) && ((MADC->LVD_STAT & MADC_LVD_STAT_CMP2IF) != MADC_LVD_STAT_CMP2IF))
    {
        lvd_intrpt = boolof(MADC->LVD_STAT & MADC_LVD_STAT_CMP2IF);    // 检测比较输出电平（可用于判断触发类型）
    }
    if((MADC->LVD_STAT & MADC_LVD_STAT_CMP1IIF) && ((MADC->LVD_STAT & MADC_LVD_STAT_CMP1IF) != MADC_LVD_STAT_CMP1IF))
    {
        /// @attention 电池电压低，需要向上进通知
    }
    if((MADC->LVD_STAT & MADC_LVD_STAT_LVDIIF) && ((MADC->LVD_STAT & MADC_LVD_STAT_LVDIIF) != MADC_LVD_STAT_LVDIIF))
    {
        /// @attention mcu掉电,按道理不会来这里,除非外部没电,电池也没电
    }

    MADC->LVD_STAT |= (MADC_LVD_STAT_CMP2IIF | MADC_LVD_STAT_CMP1IIF | MADC_LVD_STAT_LVDIIF);
}

/**
 * @description:
 * @param {uint8_t} pin 掉电引脚1和掉电引脚2，3是一起
 * @param {eLVDCMPSAR_IE_TypeDef} LVDCMPSAR_IE 中断
 * @return {*}
 */
static void hal_cmp_init(uint8_t pin, uint8_t ie)
{
    // CMP2_PD,CMP1_PD设置0比较器上电
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
    LL_SYSC_ApbClkCtrl(LL_SYSC_SAR_ID, ERN_ENABLE);

    GPIO_InitTypeDef GPIO_Init;

    GPIO_Init.Mode        = _AIN;         /*!< 复用模式 */
    GPIO_Init.OutputLevel = High_Level;   /*"输出电平选择"*/
    GPIO_Init.Pull        = Pull_OFF;     /*!< 上拉选择 */
    GPIO_Init.Dir         = GPIO_MODE_IN; /*!< 输入输出选择 */
    GPIO_Init.InputMode   = TTL_MODE;     /*!< 输入模式*/
    GPIO_Init.OutputMode  = PushPll_MODE; /*!< 输出模式*/

    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_CMP1, ERN_DISABLE);
    LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_CMP2, ERN_DISABLE);

    if(pin & PIN_3V0_EP)
    {
        LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_CMP1, ERN_ENABLE);
        GPIO_Init.Pin = PIN_LVD_3V0;
        LL_GPIO_Init(&GPIO_Init);
    }
    if(pin & PIN_12V_EP)
    {
        LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_CMP2, ERN_ENABLE);
        GPIO_Init.Pin = PIN_LVD_12V;
        LL_GPIO_Init(&GPIO_Init);
    }

    LL_ANA_CMPInit(CMP2_CHANNEL, CMP_R600K_DISABLE, CMP_HYSEN_DISABLE, (eLVDCMPSAR_IE_TypeDef)ie);
    LL_ANA_CMPInit(CMP1_CHANNEL, CMP_R600K_DISABLE, CMP_HYSEN_DISABLE, (eLVDCMPSAR_IE_TypeDef)ie);

    hal_mcu_wait_us(100);    // LVD开启后到输出稳定建立大约需要100us时间
}

/**
 * @brief  配置供电检测模式
 * @retval mode=PWR_ON 表示检测上电
 */
static void mcu_lvd_mode_set(PVD_MODE mode)
{}

/**
 * @brief  获取LVD检测状态
 * @retval 0-供电正常
 * @retval 1-供电异常
 */
static bool mcu_lvd_state_get(PVD_MODE mode)
{
    return (MADC->LVD_STAT & MADC_LVD_STAT_CMP2IF) ? 0 : 1;
}

/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */

/**
 * @brief  MCU指令等待若干uS时间.
 * @param  [in]  nUs-等待时长, 单位:微秒
 * @note   该等待采用的是指令摸拟, 并不精确, 适用于硬件驱动不需要严格的时序等待
 */
void hal_mcu_wait_us(uint16_t nUs)
{
    SystemDelayUs(nUs);
}

/**
 * @brief  上电确认检测
 * @param  none
 * @retval TRUE-power on  FALSE-not power on
 */
bool hal_mcu_pwron_query(void)
{
    /// @attention 库函数带微秒步长时间检查掉电
    // return LL_ANA_LvdCmp_STATPowerUp(CMP2_CH,TEST_POR_TIMES);

    for(int i = TEST_POR_TIMES; i > 0; i--)
    {
        /**为了防止电源抖动，如果检测到上电，先进行1ms间隔的连续检测 */
        if(mcu_lvd_state_get(PWR_ON)) return false;
        HAL_WDG_RESET();
        hal_mcu_wait_us(1000);
    }
    return true;
}

/**
 * @brief  掉电确认检测
 * @param  none
 * @retval TRUE-power down  FALSE-not power down
 */
bool hal_mcu_pwrdn_query(void)
{
    if(mcu_stus.rly_act) return false;

    for(int i = TEST_PDR_TIMES; i > 0; i--)
    {
#if USE_SAG_TYPE == SAG_EMU
        if(is_sag != NULL && (is_sag)()) return true;
#elif USE_SAG_TYPE == SAG_PIN
        if(is_sag != NULL && (is_sag)()) continue;
#endif
        if(!mcu_lvd_state_get(PWR_OFF))
        {
            if(lvd_intrpt) { HAL_CRITICAL_STATEMENT(lvd_intrpt = FALSE;); }
            return false;
        }
    }
    return true;
}

bool hal_lvd_intrpt_get(void)
{
    return boolof(lvd_intrpt);
}

void hal_dwt_enable(void)
{
    sWDT_Cfg_TypeDef Cfg;
    memset(&Cfg, 0, sizeof(sWDT_Cfg_TypeDef));

    Cfg.Window = WDT_Window_100;
    Cfg.wdcs   = WDT_WDCS_4s;
    Cfg.Int    = WDT_Int_Disable;
    Cfg.Halt   = WDT_HALT_Disable;

    /// @attention 掉电时需要打开然后定期进行喂狗
    Cfg.Stby = WDT_STBY_Enable;

    LL_WDT_Cfg(&Cfg);
}

void hal_dwt_reset(void)
{
    WDT->EN = 0xbb;
}

void hal_wdt_close(void)
{
    // M0P_SYSCTRL->PERI_CLKEN0_f.WDT = FALSE;    // 关闭DWT时钟
}

/// @brief 系统时钟切换
/// @param mode
/// @return 0-外部晶振异常
uint8_t hal_sysclk_set(MCU_CLOCK_MODE mode)
{
    eSysclkRet_TypeDef ret = SYSCLK_FAIL;

    ret = LL_SYSCLK_SysModeChg((eSysclkMode_TypeDef)mode, Clock_Div_1);
    SystemCoreClockUpdate();

    return ret;
}

/**
 * @brief  MCU复位后运行模式前的MCU初始化.
 * @param  none
 * @retval MCU core 初始化状态, 参考tMcuCoreStus定义
 */
McuCoreStus_s hal_mcu_core_init(void)
{
    /* 初始化RCMF */
    hal_dwt_enable();

    /// @attention 初始化gpio要在检查掉电之前，检查掉电设置了gpio
    hal_gpio.init();

    /// @attention 只在第一次上电的时候动上电检测的引脚，进入低功耗的时候注意不要
    hal_mcu.cmp_init(PIN_3V0_EP | PIN_12V_EP, LVDCMPSAR_IE_DISABLE);

    mcu_stus.power_on_rst = hal_mcu_pwron_query();
    if(mcu_stus.power_on_rst)
    {
        {
            /*             /// 获取复位源信息
                        mcu_stus.por_bor_rst = boolof(M0P_RESET->RESET_FLAG_f.POR5V || M0P_RESET->RESET_FLAG_f.POR15V || M0P_RESET->RESET_FLAG_f.LVD);    /// 判断是否冷启动

                        M0P_RESET->RESET_FLAG_f.POR5V  = 0;
                        M0P_RESET->RESET_FLAG_f.POR15V = 0;
                        M0P_RESET->RESET_FLAG_f.LVD    = 0;

                        mcu_stus.abnormal_rst       = boolof(M0P_RESET->RESET_FLAG_f.WDT && (!mcu_stus.por_bor_rst));    /// 查询是否看门狗复位
                        M0P_RESET->RESET_FLAG_f.WDT = 0;
             */

            /// @bug BOR2p8,怎么处理比较好
            mcu_stus.por_bor_rst = boolof(LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_BORV2P8));

            mcu_stus.abnormal_rst = boolof(LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_WDT) && (!mcu_stus.por_bor_rst));

            LL_SYSC_SysRstFlagClr(LL_SYSC_RSTFLAG_BORV2P8);
            LL_SYSC_SysRstFlagClr(LL_SYSC_RSTFLAG_WDT);
        }
        hal_dwt_reset();

        if(hal_sysclk_set(WORK_RUN_HZ)) { mcu_stus.clk_src_extosc = 0; }
        /* Configurate system clock */
        else { mcu_stus.clk_src_extosc = 1; }

        /*外设时钟*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_TC1_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_UART0_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_UART5_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_I2C_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_LCD_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_RTC_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_UART4_ID, true);
        LL_SYSC_ApbClkCtrl(LL_SYSC_WDT_ID, true);

        irq_vector_set(INT_CMP, irq_handler_lvd);    // 设置LVD中断处理函数
        EnableNvic(CMP_IRQn, 3, TRUE);               // 使能LVD中断
    }
    else
    {
        /* 低功耗运行模式前的MCU初始化 */
        SYSCTL->SYS_PS  = 0x82;
        SYSCTL->MOD0_EN = 0x0;
        SYSCTL->MOD1_EN = SYSCTL_MOD1_EN_SAR_EN | SYSCTL_MOD1_EN_RTC_EN | SYSCTL_MOD1_EN_WDT_EN | SYSCTL_MOD1_EN_GPIO_EN;
        SYSCTL->KBI_EN  = 0x0;
        SYSCTL->INTC_EN = 0x0;
        // 确保关闭BGR
        SYSCTL->SYS_PD |= 0xff;
        SYSCTL->SYS_PD &= ~(uint32_t)(1 << 17);
        SYSCTL->SYS_PS = 0x0;
        LL_SYSCLK_SysModeChg(Clock_Losc, Clock_Div_1);
        DisableNvic(CMP_IRQn);    // 使能LVD中断
    }

    return mcu_stus;
}

/**
 * @brief  MCU睡眠模式设置
 * @param  none
 * @retval none
 */
void hal_mcu_sleep(void)
{
    HAL_ENABLE_INTERRUPTS();    /// 睡眠前必须始终打开中断!!!

    /// 进入deepsleep模式，关闭基准电压源
    /// MCU唤醒后会自动切换到HRC，频率默认为8M
    // HAL_SAFETY_WR(HT_PMU->PMUCON = (PMU_PMUCON_LVD0DETEN);    // 使能LVD检测，检测电压为1.21V,关闭BOR检测，关闭大功耗LDO
    // );
    SCB->SCR &= ~SCB_SCR_SLEEPDEEP_Msk;
    SCB->SCR &= ~SCB_SCR_SLEEPONEXIT_Msk;
    __WFI();

    /// 开启BOR
    // HAL_SAFETY_WR(HT_PMU->PMUCON = (PMU_PMUCON_LVD0DETEN | PMU_PMUCON_BORDETEN | PMU_PMUCON_BORRST);    // 使能LVD检测，检测电压为1.21V,使能BOR检测且开启BOR复位，保持LDO状态
    // );

    HAL_WDG_RESET();    /// 唤醒后清看门狗

    /* 唤醒后检查上电运行情况 */
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();
}

void hal_is_sag_callback(bool func(void))
{
#if USE_SAG_TYPE != SAG_NULL
    is_sag = func;
#endif
}

static void hal_mcu_wdt_rst_msu(void)
{
    sWDT_Cfg_TypeDef Cfg;
    memset(&Cfg, 0, sizeof(sWDT_Cfg_TypeDef));

    Cfg.wdcs = WDT_WDCS_16ms;
    Cfg.Halt = WDT_HALT_Enable;

    LL_WDT_Cfg(&Cfg);

    while(1) {}
}

/// @brief 声明hal_uart子模块对象
const struct hal_mcu_t hal_mcu = {
    .stus            = &mcu_stus,
    .init            = hal_mcu_core_init,
    .sleep           = hal_mcu_sleep,
    .wait_us         = hal_mcu_wait_us,
    .pwrdn_query     = hal_mcu_pwrdn_query,
    .pwron_query     = hal_mcu_pwron_query,
    .sysclk_set      = hal_sysclk_set,
    .wdg_clr         = hal_dwt_reset,
    .is_sag_callback = hal_is_sag_callback,
    .lvd_intrpt_get  = hal_lvd_intrpt_get,
    .wdt_rst_msu     = hal_mcu_wdt_rst_msu,
    .cmp_init        = hal_cmp_init,
};

/** @} */
/** @} */
/** @} */
