/********************************************************************************
  * @file    log.h
  * <AUTHOR> @date    2024
  * @brief   事件，曲线，冻结存储  
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifndef __LOG_H__
#define __LOG_H__

#include "typedef.h"
#include "datastore.h"


struct log_s
{
    bool     (*roll_add)(log_addr_s addr, const void* content, uint16_t len);
    bool     (*append)(log_addr_s addr, const void* content, uint32_t offset, uint16_t len);
    bool     (*erase)(log_addr_s addr, uint16_t num);
    bool     (*pre_earse)(log_addr_s addr, uint16_t num);
    void     (*empty)(log_addr_s addr);
    uint16_t (*fetch)(log_addr_s addr, void* content, uint16_t len, uint16_t point);
    uint16_t (*FIFO)(log_addr_s addr, uint16_t fixptr, uint16_t idx, void* content, uint16_t len);
    uint16_t (*first_pointer_get)(log_addr_s addr);
    uint16_t (*entry_inuse_get)(log_addr_s addr);
    uint32_t (*entries_cnt_get)(log_addr_s addr);
    bool     (*entry_inuse_del)(log_addr_s addr, uint16_t num);
};

extern const struct log_s mlog;

#endif /* __LOG_H__ */

