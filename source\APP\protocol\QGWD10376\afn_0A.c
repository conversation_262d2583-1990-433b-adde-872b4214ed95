/**
 ******************************************************************************
 * @file    afn_0A.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 10 查询参数
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "dcu.h"
#include "debug.h"
#include "module_para.h"
#include "api.h"
#include "tariff.h"
#include "utils.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

rsp_err_t anf0A_get(req_obj_s *req, rsp_obj_s *rsp)
{
    uint8_t  *rsp_apdu = rsp->apdu;
    rsp_err_t ret;

    if(req->pn)
    {
        rsp->err = ACK_ERR;
        return ACK_ERR;
    }    // pn必须为0
    switch(req->fn)
    {
        case 3:    /// @note F3 主站IP地址和端口
        {
            char     ipstr[MODULE_IP_LEN + 1];
            uint8_t  ip[4];
            uint16_t port;

            module_para.para_get(ipstr, MODULE_TCP_IP);                              // 获取模块IP地址
            port = module_para.para_get(&port, MODULE_TCP_PORT);                     // 获取模块端口号
            sscanf(ipstr, "%hhu.%hhu.%hhu.%hhu", &ip[0], &ip[1], &ip[2], &ip[3]);    // 解析IP地址

            memcpy(rsp_apdu, ip, 4), rsp_apdu += 4;    // 将IP地址各段存入响应缓冲区
            *rsp_apdu++ = port & 0xFF;                 // 将端口号低字节存入响应缓冲区
            *rsp_apdu++ = (port >> 8) & 0xFF;          // 将端口号高字节存入响应缓冲区

            module_para.para_get(ipstr, MODULE_TCP_IP_BAK);                          // 获取模块IP地址
            port = module_para.para_get(&port, MODULE_TCP_BAK_PORT);                 // 获取模块端口号
            sscanf(ipstr, "%hhu.%hhu.%hhu.%hhu", &ip[0], &ip[1], &ip[2], &ip[3]);    // 解析IP地址

            memcpy(rsp_apdu, ip, 4), rsp_apdu += 4;
            *rsp_apdu++ = port & 0xFF;
            *rsp_apdu++ = (port >> 8) & 0xFF;

            module_para.para_get(rsp_apdu, MODULE_APN), rsp_apdu += 16;    // 376协议中APN固定16字节

            rsp->err     = ACK_RIGHT;
            ret          = ACK_RIGHT;
            rsp->rsp_len = rsp_apdu - rsp->apdu;    // 计算响应数据长度
            break;
        }
        case 10:    /// @note F10 终端电能表/交流采样装置配置参数
        {
            uint16_t num;

            num = get_lsbdata16(req->req_apdu), req->req_apdu += 2;    // 获取配置表数量

            req->apdu_len = req->apdu_len - 2 - 2 * num;    // 减去配置数量和装置序号
            if(num != 1) { return ACK_ERR; }                // 电能表端只能配置一个电表

            // 本次电能表/交流采样装置配置数量 n BIN 2
            set_lsbdata16(rsp_apdu, 1), rsp_apdu += 2;

            // 单个电表 27 字节
            // 电能表/交流采样装置序号 BIN 2 本次配置第 1 块
            set_lsbdata16(rsp_apdu, 1), rsp_apdu += 2;
            // 电能表/交流采样装置所属测量点号 BIN 2
            set_lsbdata16(rsp_apdu, 1), rsp_apdu += 2;
            // 通信速率及端口号 BIN 1
            *rsp_apdu++ = 0x62;
            // 通信协议类型 BIN 1
            *rsp_apdu++ = 0x1E;    // DLT645 2007 协议
            // 通信地址 见附录 A.12 6
            api.meter_sn_get(rsp_apdu), rsp_apdu += 6;
            // 通信密码 BIN 6
            memset(rsp_apdu, 0x00, 6), rsp_apdu += 6;
            // 电能费率个数 BS8 1
            *rsp_apdu++ = tariff.day_tf_num_get();
            // 有功电能示值整数位及小数位个数 BS8 1
            *rsp_apdu++ = 9;    // 6 + 2
            // 所属采集器通信地址 见附录 A.12 6
            memset(rsp_apdu, 0x00, 6), rsp_apdu += 6;
            // 用户大类号及用户小类号 BS8 1 … … … …
            *rsp_apdu++ = 0x32;

            rsp->rsp_len = 29;
            break;
        }
    }

    return ret;
}

const gdw376_table_s afn0A_table = {
    .afn    = AFN_QUERY_PARAM,    ///< 功能码
    .reset  = NULL,               ///< 复位函数
    .verify = NULL,               ///< 验证函数
    .get    = anf0A_get,          ///< 获取函数
    .set    = NULL,               ///< 设置函数
};