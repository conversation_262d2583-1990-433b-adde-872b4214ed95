/********************************************************************************
 * @file    hal_uart.c
 * <AUTHOR> @date    2024
 * @brief   uart driver source file. mcu 前缀表示与mcu类型相关，不同平台下须做相应移植更改，
 *          hal前缀表示与硬件相关，与mcu无关，与具体平台无关。
 *          APP 数据接收发送使用中断方式，接收结束采用超时机制，协议解析后直接发送数据，无需等待。接收缓冲区满时自动丢弃数据，防止内存泄漏。
 *          BOOT 数据接收采用轮询方式。
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_uart.h"
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* @brief 硬件串口类型定义 */
typedef struct
{
    HT_UART_TypeDef *uart;
    uint8_t          gpio;
    IRQn_Type        irqn;
    uint32_t         clk;
    int              int_num;
    void (*func)(void);
} mcuUSART_s;

/* Private define ------------------------------------------------------------*/

/* @brief 串口带收发控制的定义 */
typedef enum
{
    HAL_UART_RXOFF,
    HAL_UART_RXON,
} HAL_UART_RXCTRL_TYPE;

/* @brief 串口通讯模型定义 */
typedef struct
{
    HAL_UART_BAUDE_TYPE baude;         // 波特率
    HAL_UART_CTRL       ctrl;          // 控制字
    bool                fullStus;      // 半双工状态
    uint8_t             filter_ms;     // 半双工过滤时间
    uint16_t            timeoutCnt;    // 超时计数器
    uint16_t            rxbufSize;     // 接收缓冲大小
    uint16_t            rxptr;         // 接收指针-代表常度接收缓冲区的读写位置
    uint16_t            txlen;         // 发送数据长度
    uint8_t            *txbuf;         // 发送缓冲
    uint8_t            *rxbuf;         // 接收缓冲
} halUartProfile_t;

/* Private macro -------------------------------------------------------------*/
/* @brief RS485收发控制 */
#ifdef PIN_4851_CTRL
#define RS485_RXD_ENABLE() gpio_out_H(PIN_4851_CTRL)
#define RS485_RXD_DISABLE() gpio_out_L(PIN_4851_CTRL)
#else
#define RS485_RXD_ENABLE()
#define RS485_RXD_DISABLE()
#endif

#ifdef PIN_4852_CTRL
#define RS4852_RXD_ENABLE() gpio_out_H(PIN_4852_CTRL)
#define RS4852_RXD_DISABLE() gpio_out_L(PIN_4852_CTRL)
#else
#define RS4852_RXD_ENABLE()
#define RS4852_RXD_DISABLE()
#endif

#ifdef PIN_IR_CTRL
#define IR_POWER_ON() gpio_out_L(PIN_IR_CTRL)
#define IR_POWER_OFF() gpio_out_H(PIN_IR_CTRL)
#else
#define IR_POWER_ON()
#define IR_POWER_OFF()
#endif

/* Private constants ---------------------------------------------------------*/
void                    irq_handle_uart0(void);
void                    irq_handle_uart1(void);
void                    irq_handle_uart2(void);
void                    irq_handle_uart3(void);
void                    irq_handle_uart4(void);
void                    irq_handle_uart5(void);
static void             hal_uart_recv_irq(HAL_UART_TYPE com);
static void             hal_uart_send_irq(HAL_UART_TYPE com);
static const mcuUSART_s usart[] = {
#if HAL_UART0_ENABLE
    {
        .uart    = HT_UART0,
        .gpio    = 0,
        .irqn    = UART0_IRQn,
        .clk     = CMU_CLKCTRL1_UART0EN,
        .int_num = INT_UART0,
        .func    = irq_handle_uart0,
    },
#endif
#if HAL_UART1_ENABLE
    {
        .uart    = HT_UART1,
        .gpio    = 1,
        .irqn    = UART1_IRQn,
        .clk     = CMU_CLKCTRL1_UART1EN,
        .int_num = INT_UART1,
        .func    = irq_handle_uart1,
    },
#endif
#if HAL_UART2_ENABLE
    {
        .uart    = HT_UART2,
        .gpio    = 2,
        .irqn    = UART2_IRQn,
        .clk     = CMU_CLKCTRL1_UART2EN,
        .int_num = INT_UART2,
        .func    = irq_handle_uart2,
    },
#endif
#if HAL_UART3_ENABLE
    {
        .uart    = HT_UART3,
        .gpio    = 3,
        .irqn    = UART3_IRQn,
        .clk     = CMU_CLKCTRL1_UART3_7816_1EN,
        .int_num = INT_UART3,
        .func    = irq_handle_uart3,
    },
#endif
#if HAL_UART4_ENABLE
    {
        .uart    = HT_UART4,
        .gpio    = 4,
        .irqn    = UART4_IRQn,
        .clk     = CMU_CLKCTRL1_UART4_7816_0EN,
        .int_num = INT_UART4,
        .func    = irq_handle_uart4,
    },
#endif
#if HAL_UART5_ENABLE
    {
        .uart    = HT_UART5,
        .gpio    = 5,
        .irqn    = UART5_IRQn,
        .clk     = CMU_CLKCTRL1_UART5EN,
        .int_num = INT_UART5,
        .func    = irq_handle_uart5,
    },
#endif
};
#define uart(x) (usart[x].uart)

/* Private variables ---------------------------------------------------------*/
halUartProfile_t      halUartProfile[HAL_UART_NUM];
static const uint32_t cBaudeValue[] = {
    300, 600, 1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200,
};

/* Private functions ---------------------------------------------------------*/
/* @brief 清除串口接收中断标志 */
__INLINE void mcu_uart_rxirq_clear(HAL_UART_TYPE com)
{
    uart(com)->UARTSTA &= ~(UART_UARTSTA_RXIF | UART_UARTSTA_PARITY | UART_UARTSTA_PRDIF);
}

/* @brief 获取串口接收中断标志 */
__INLINE bool mcu_uart_rxirq_get(HAL_UART_TYPE com)
{
    return boolof(uart(com)->UARTSTA & (UART_UARTSTA_RXIF | UART_UARTSTA_PARITY | UART_UARTSTA_PRDIF));
}

/* @brief 清除串口发送完成标志 */
__INLINE void mcu_uart_txirq_clear(HAL_UART_TYPE com)
{
    uart(com)->UARTSTA &= ~UART_UARTSTA_TXIF;
}

/* @brief 获取串口发送完成标志 */
__INLINE bool mcu_uart_txirq_get(HAL_UART_TYPE com)
{
    return boolof(uart(com)->UARTSTA & UART_UARTSTA_TXIF);
}

/* @brief 从串口移位寄存器中读一个字符 */
__INLINE void mcu_uart_char_get(HAL_UART_TYPE com, uint8_t *ch)
{
    mcu_uart_rxirq_clear(com);
    *ch = (uint8_t)uart(com)->SBUF;
}

/* @brief 往串口移位寄存器中写一个字符 */
__INLINE void mcu_uart_char_send(HAL_UART_TYPE com, uint8_t ch)
{
    mcu_uart_txirq_clear(com);
    uart(com)->SBUF = ch;
}

/* @brief 串口发送硬件中断打开 */
__INLINE void mcu_uart_send_start(HAL_UART_TYPE com)
{
    hal_uart_send_irq(com);
    uart(com)->UARTCON |= UART_UARTCON_TXIE;
}

/* @brief 串口发送硬件中断停止 */
__INLINE void mcu_uart_send_stop(HAL_UART_TYPE com)
{
    uart(com)->UARTCON &= ~UART_UARTCON_TXIE;
}

/* @brief 串口硬件使能 */
__INLINE void mcu_uart_enable(HAL_UART_TYPE com)
{
    uart(com)->UARTCON |= (UART_UARTCON_RXEN | UART_UARTCON_TXEN);
}

/* @brief 串口硬件关闭 */
__INLINE void mcu_uart_disable(HAL_UART_TYPE com)
{
    uart(com)->UARTCON &= ~(UART_UARTCON_RXEN | UART_UARTCON_TXEN);
}

/* @brief 串口硬件初始化，包括格式、波特率、中断处理句柄 */
__INLINE void mcu_uart_config(HAL_UART_TYPE com, HAL_UART_TRANS_MODE mode, HAL_UART_CHAR_TYPE format, uint32_t baude)
{
    const mcuUSART_s *ptr = &usart[com];
    UART_InitTypeDef  UART_InitStructure;

    /* USARTx - Configuration */
    HT_CMU_ClkCtrl1Config(usart[com].clk, ENABLE);    /// 使能UART模块 clk
    switch(format)
    {
        case CHAR_7E1:                                                // 芯片暂不支持 TODO
            UART_InitStructure.UART_WordLength = WordLength_7Bits;    // 7位数据位
            UART_InitStructure.UART_Parity     = UartParity_EVEN;     // 偶校验
            UART_InitStructure.UART_StopBits   = OneStopBits;         // 1位停止位
            break;
        case CHAR_8E1:
            UART_InitStructure.UART_WordLength = WordLength_8Bits;    // 8位数据位
            UART_InitStructure.UART_Parity     = UartParity_EVEN;     // 偶校验
            UART_InitStructure.UART_StopBits   = OneStopBits;         // 1位停止位
            break;
        case CHAR_8D1:
            UART_InitStructure.UART_WordLength = WordLength_8Bits;    // 8位数据位
            UART_InitStructure.UART_Parity     = UartParity_ODD;      // 奇校验
            UART_InitStructure.UART_StopBits   = OneStopBits;         // 1位停止位
            break;
        default:                                                        // 默认只使用CHAR_8N1传输格式
            UART_InitStructure.UART_WordLength = WordLength_8Bits;      // 8位数据位
            UART_InitStructure.UART_Parity     = UartParity_Disable;    // 无校验
            UART_InitStructure.UART_StopBits   = OneStopBits;           // 1位停止位
            break;
    }
    NVIC_DisableIRQ(ptr->irqn);
    UART_InitStructure.UART_Logic    = UartLogicPositive;    /// UART逻辑为正
    UART_InitStructure.UART_BaudRate = baude;                /// 波特率
    UART_InitStructure.ReceiveEN     = ENABLE;               /*!< 接收使能   注：非中断使能  */
    UART_InitStructure.SendEN        = ENABLE;               /*!< 发送使能   注：非中断使能  */

    HT_UART_Init(uart(com), &UART_InitStructure);    ///< 初始化串口

    hal_gpio.uart_init(ptr->gpio);    ///< 初始化串口GPIO

    /* Enable/Disable Interruption */
    if(mode == RxTx_INT)
    {
        /* Enable UARTx rx interrupt */
        irq_vector_set(ptr->int_num, ptr->func);
        NVIC_ClearPendingIRQ(ptr->irqn);
        NVIC_EnableIRQ(ptr->irqn);
        HT_UART_ITConfig(uart(com), UART_UARTCON_RXIE, ENABLE);
    }
    else
    {
        /* Disable UARTx rx interrupt */
        HT_UART_ITConfig(uart(com), UART_UARTCON_RXIE, DISABLE);
        NVIC_DisableIRQ(ptr->irqn);
        NVIC_ClearPendingIRQ(ptr->irqn);
    }
}

__INLINE void mcu_ir38k_init(HAL_UART_TYPE com)
{
#if HAL_IR38K_ENABLE
    uart(com)->IRCON  = (UART_IRCON_IR38KSOURCE_PLL | UART_IRCON_IRLVL_POSITIVE);    ///
    uart(com)->IRDUTY = UART_IRDUTY_IRDUTY_50;                                       /// 50%占空比
#endif
}

__INLINE void mcu_ir38k_enable(HAL_UART_TYPE com)
{
#if HAL_IR38K_ENABLE
    if(com == HAL_IR_CHANNL_NUM) { uart(com)->IRCON |= UART_IRCON_IRTX; }
#endif
}

__INLINE void mcu_ir38k_close(HAL_UART_TYPE com)
{
#if HAL_IR38K_ENABLE
    if(com == HAL_IR_CHANNL_NUM) { uart(com)->IRCON &= ~UART_IRCON_IRTX; }
#endif
}

/* @brief 串口数据接收、发送控制函数 */
static void mcu_uart_rxd_ctrl(HAL_UART_CTRL ctrl, HAL_UART_RXCTRL_TYPE txrx)
{
    switch(ctrl)
    {
#ifdef PIN_4851_CTRL
        case UC_RS485_DE: {
            if(txrx == HAL_UART_RXON) { RS485_RXD_ENABLE(); }
            else { RS485_RXD_DISABLE(); }
        }
        break;
#endif
#ifdef PIN_4852_CTRL
        case UC_RS4852_DE: {
            if(txrx == HAL_UART_RXON) { RS4852_RXD_ENABLE(); }
            else { RS4852_RXD_DISABLE(); }
        }
        break;
#endif
#if HAL_IR38K_ENABLE
        case UC_IR_38K: {
            if(txrx == HAL_UART_RXON)
            {
                mcu_ir38k_close(HAL_IR_CHANNL_NUM);    /// 关闭 TX 输出的红外调制功能
            }
            else
            {
                mcu_ir38k_enable(HAL_IR_CHANNL_NUM);    /// 使能 TX 输出的红外调制功能
            }
            IR_POWER_ON();
        }
        break;
#endif
        default:
            break;
    }
}

#if HAL_UART0_ENABLE
/* @brief 串口0中断处理函数 */
void irq_handle_uart0()
{
    if(mcu_uart_txirq_get(HAL_UART0)) { hal_uart_send_irq(HAL_UART0); }
    if(mcu_uart_rxirq_get(HAL_UART0)) { hal_uart_recv_irq(HAL_UART0); }
}
#endif

#if HAL_UART1_ENABLE
/* @brief 串口1中断处理函数 */
void irq_handle_uart1()
{
    if(mcu_uart_txirq_get(HAL_UART1)) { hal_uart_send_irq(HAL_UART1); }
    if(mcu_uart_rxirq_get(HAL_UART1)) { hal_uart_recv_irq(HAL_UART1); }
}
#endif

#if HAL_UART2_ENABLE
/* @brief 串口2中断处理函数 */
void irq_handle_uart2()
{
    if(mcu_uart_txirq_get(HAL_UART2)) { hal_uart_send_irq(HAL_UART2); }
    if(mcu_uart_rxirq_get(HAL_UART2)) { hal_uart_recv_irq(HAL_UART2); }
}
#endif

#if HAL_UART3_ENABLE
/* @brief 串口3中断处理函数 */
void irq_handle_uart3()
{
    if(mcu_uart_txirq_get(HAL_UART3)) { hal_uart_send_irq(HAL_UART3); }
    if(mcu_uart_rxirq_get(HAL_UART3)) { hal_uart_recv_irq(HAL_UART3); }
}
#endif

#if HAL_UART4_ENABLE
/* @brief 串口4中断处理函数 */
void irq_handle_uart4()
{
    if(mcu_uart_txirq_get(HAL_UART4)) { hal_uart_send_irq(HAL_UART4); }
    if(mcu_uart_rxirq_get(HAL_UART4)) { hal_uart_recv_irq(HAL_UART4); }
}
#endif

#if HAL_UART5_ENABLE
/* @brief 串口5中断处理函数 */
void irq_handle_uart5()
{
    if(mcu_uart_txirq_get(HAL_UART5)) { hal_uart_send_irq(HAL_UART5); }
    if(mcu_uart_rxirq_get(HAL_UART5)) { hal_uart_recv_irq(HAL_UART5); }
}
#endif

/* ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
 +                                                                  +
 +  !<以下代码与具体的MCU类型无关，移值无须修改                           +
 +                                                                  +
 ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ */

/* @brief 串口中断模式接收，由系统串口中断句柄调用 */
static void hal_uart_recv_irq(HAL_UART_TYPE com)
{
    halUartProfile_t *ptr = halUartProfile + com;
    uint8_t           ch;
    mcu_uart_char_get(com, &ch);
    if((ptr->ctrl == UC_NONE) || !ptr->fullStus)
    {
        if(ptr->rxptr >= ptr->rxbufSize) ptr->rxptr = 0;
        *(ptr->rxbuf + ptr->rxptr) = ch;
        ptr->rxptr++;
        ptr->fullStus = false;

        if(ptr->baude > BAUDE_2400BPS) { ptr->timeoutCnt = 20; }
        else if(ptr->baude >= BAUDE_1200BPS) { ptr->timeoutCnt = 40; }
        else { ptr->timeoutCnt = 100; }
    }
}

/* @brief 串口中断模式发送，由系统串口中断句柄调用 */
static void hal_uart_send_irq(HAL_UART_TYPE com)
{
    halUartProfile_t *ptr = halUartProfile + com;
    if(ptr->txlen > 0)
    {
        mcu_uart_char_send(com, *(ptr->txbuf));
        ptr->txbuf++;
        ptr->txlen--;
        ptr->timeoutCnt = 100;
    }
    else
    {
        mcu_uart_send_stop(com);
        if(ptr->ctrl != UC_NONE)
        {
            ptr->timeoutCnt = ptr->filter_ms;    // 用于滤除反射
            mcu_uart_rxd_ctrl(ptr->ctrl, HAL_UART_RXON);
        }
        else
        {
            ptr->txbuf      = NULL;
            ptr->timeoutCnt = 0;
        }
    }
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  串口中断收发模式下的软件定时器运行处理, 放在1ms定时器中断中调用
 * @note   串口中断模式下，接收到数据后，会自动进入接收状态，等待软件定时器超时后，解析协议，发送数据，发送完毕后，自动进入接收状态
 * @param  none
 * @param  none
 * @retval none
 */
void hal_uart_timer_run(void)
{
    for(uint16_t com = 0; com < HAL_UART_NUM; com++)
    {
        halUartProfile_t *ptr = halUartProfile + com;
        if(ptr->timeoutCnt > 0)
        {
            if(--ptr->timeoutCnt == 0)
            {
                if(!ptr->fullStus)    // 接收帧完成
                {
                    ptr->fullStus   = TRUE;
                    ptr->timeoutCnt = HAL_UART_APP_TIMEOUT;
                }
                else    // 应答超时或者滤除反射
                {
                    ptr->txbuf    = NULL;
                    ptr->fullStus = FALSE;
                }
            }
        }
    }
}

/**
 * @brief  串口打印输出一个字符(Polling方式)
 * @param  [in]  com-指串口号
 * @param  [in]  ch-待打印字符
 * @retval none
 */
void hal_uart_print(HAL_UART_TYPE com, char ch)
{
    mcu_uart_rxd_ctrl(halUartProfile[com].ctrl, HAL_UART_RXOFF);
    mcu_uart_char_send(com, (uint8_t)ch);
    while(!mcu_uart_txirq_get(com)) {}
    HAL_WDG_RESET();

    if(halUartProfile[com].ctrl != UC_NONE)
    {
        mcu_uart_rxirq_clear(com);    // 滤除反射
    }
}

/**
 * @brief  串口扫描输入一个字符(Polling方式)
 * @param  [in]  com-指串口号
 * @param  [in]  msWait-指超时计数单位ms， =0表示不启动超时直到接收到字符为止
 * @param  [out] ch-接收字符指针
 * @retval 0-超时未收到字符
 * @retval 1-收到字符
 */
bool hal_uart_scan(HAL_UART_TYPE com, uint8_t *ch, uint16_t msWait)
{
    uint32_t cnt10us = (uint32_t)msWait * 100;
    mcu_uart_rxd_ctrl(halUartProfile[com].ctrl, HAL_UART_RXON);

    while(!mcu_uart_rxirq_get(com))
    {
        HAL_WDG_RESET();
        if(cnt10us > 0)
        {
            if(--cnt10us == 0) return FALSE;
            hal_mcu.wait_us(8);
        }
    }
    mcu_uart_char_get(com, ch);

    return TRUE;
}

/**
 * @brief  查询是否有串口数据串接收到(中断方式)
 * @param  [in]  com-指串口号
 * @retval =0-未收到数据
 * @retval >0-有收到数据的数据长度
 */
uint16_t hal_uart_receive(HAL_UART_TYPE com)
{
    halUartProfile_t *ptr  = halUartProfile + com;
    uint16_t          size = 0;
    if(ptr->fullStus || ptr->ctrl == UC_NONE)
    {
        HAL_CRITICAL_STATEMENT(size = ptr->rxptr; ptr->rxptr = 0; if(ptr->ctrl == UC_NONE) ptr->fullStus = FALSE;);
        return size;
    }
    return 0;
}

/**
 * @brief  查询是否有串口数据串接收到(中断方式)
 * @param  [in]  com-指串口号
 * @retval =0-未收到数据
 * @retval >0-有收到数据的数据长度
 */
uint16_t hal_uart_receive_dont_clear(HAL_UART_TYPE com)
{
    halUartProfile_t *ptr = halUartProfile + com;

    return (ptr->fullStus) ? (ptr->rxptr) : (0);
}

/**
 * @brief  帧解析查询是否有串口数据串接收到(中断方式)
 * @param  [in]  com-指串口号
 * @retval =0-未收到数据
 * @retval >0-有收到数据的数据长度
 */
uint16_t hal_uart_recv_by_frame(HAL_UART_TYPE com, uint16_t func(void *, const uint8_t *, uint16_t), void *para)
{
    halUartProfile_t *ptr  = halUartProfile + com;
    uint16_t          size = 0;
    if(ptr->fullStus) { HAL_CRITICAL_STATEMENT(size = ptr->rxptr; ptr->rxptr = 0; if(ptr->ctrl == UC_NONE) ptr->fullStus = FALSE;); }

    if(size != 0) { size = func(para, ptr->rxbuf, size); }
    return size;
}

/**
 * @brief  串口全双工接收数据(中断方式)
 * @param  [in]  com-指串口号
 * @param  [out] buf-接收数据缓冲
 * @retval >0-有收到数据的数据长度
 */
uint16_t hal_uart_full_duplex_recv(HAL_UART_TYPE com, void *buf)
{
    halUartProfile_t *ptr  = halUartProfile + com;
    uint16_t          size = 0;
    if(ptr->ctrl == UC_NONE || ptr->fullStus)
    {
        HAL_CRITICAL_STATEMENT(size = ptr->rxptr; if(size) { memcpy(buf, ptr->rxbuf, size); } ptr->rxptr = 0; if(ptr->ctrl == UC_NONE) ptr->fullStus = FALSE;);
    }
    return size;
}

/**
 * @brief  启动串口数据串发送(中断方式)
 * @param  [in]  com-指串口号
 * @param  [in]  str-待发送数据缓冲
 * @param  [in]  size-发送数据长度
 * @retval none
 */
void hal_uart_send(HAL_UART_TYPE com, const void *str, uint16_t size)
{
    halUartProfile_t *ptr = halUartProfile + com;
    if(size > 0)
    {
        /* 查询上次串口发送数据是否完成 */
        //	while(!hal_uart_send_over_query(com)){}
        if(ptr->ctrl != UC_NONE)
        {
            mcu_uart_rxd_ctrl(ptr->ctrl, HAL_UART_RXOFF);
            ptr->fullStus = TRUE;    // 发送时关闭接收
        }
        ptr->txbuf = (uint8_t *)str;
        ptr->txlen = size;
        mcu_uart_send_start(com);
    }
    else if(ptr->ctrl != UC_NONE)
    {
        ptr->fullStus   = FALSE;
        ptr->timeoutCnt = 0;
    }
}

/**
 * @brief  查询串口数据串是否发送完成(中断方式)
 * @param  [in]  com-指串口号
 * @retval TRUE -发送完成
 * @retval FALSE-发送中
 */
bool hal_uart_send_over_query(HAL_UART_TYPE com)
{
    return boolof(halUartProfile[com].txbuf == NULL);
}

/**
 * @brief  打开UART串口
 * @param  [in]  com-指串口号
 * @param  [in]  com-串口控制类型
 * @param  [in]  format-指通讯数据格式, 可以为以下参数之一:
 *               UART_7E1:  7 bits data, 1 stop bit, Even parity
 *               UART_8N1:  8 bits data, 1 stop bit, no parity
 *               UART_8E1:  8 bits data, 1 stop bit, Even parity
 * @param  [in]  baude-指波特率值
 * @param  [in]  rxbuf-中断方式接收缓冲. 如果为NULL，则配置串口为polling方式
 * @param  [in]  size-接收缓冲大小
 * @retval none
 */
void hal_uart_open(HAL_UART_TYPE com, HAL_UART_CTRL ctrl, HAL_UART_CHAR_TYPE format, HAL_UART_BAUDE_TYPE baude, uint8_t *rxbuf, uint16_t size)
{
    HAL_UART_TRANS_MODE mode;

    if(com == HAL_IR_CHANNL_NUM) IR_POWER_ON();    // 开启红外电源

    HAL_CRITICAL_STATEMENT(mode = (rxbuf == NULL) ? RxTx_POLLING : RxTx_INT; halUartProfile[com].rxbuf = rxbuf; halUartProfile[com].rxbufSize = size; halUartProfile[com].rxptr = 0;
                           halUartProfile[com].txlen = 0; halUartProfile[com].timeoutCnt = 0; halUartProfile[com].fullStus = FALSE; halUartProfile[com].baude = baude;
                           halUartProfile[com].filter_ms = 1 + 10000 / cBaudeValue[baude - BAUDE_300BPS]; mcu_uart_config(com, mode, format, cBaudeValue[baude - BAUDE_300BPS]);

                           halUartProfile[com].ctrl = ctrl; mcu_uart_rxd_ctrl(ctrl, HAL_UART_RXON);

                           if(ctrl == UC_IR_38K) mcu_ir38k_init(com);    // 打开IR38K模块
    );
}

/**
 * @brief  关闭UART串口
 * @param  [in]  com-指串口号
 * @retval none
 */
void hal_uart_close(HAL_UART_TYPE com)
{
    mcu_uart_disable(com);
    mcu_ir38k_close(com);
    if(com == HAL_IR_CHANNL_NUM) IR_POWER_OFF();    // 关闭红外电源
}

/**
 * @brief  重新打开UART串口
 * @param  [in]  com-指串口号
 * @retval none
 */
void hal_uart_re_open(HAL_UART_TYPE com)
{
    if(com == HAL_IR_CHANNL_NUM) IR_POWER_ON();    // 开启红外电源
    mcu_uart_enable(com);
}

/// @brief 声明hal_uart子模块对象
const struct hal_uart_t hal_uart = {
    .open             = hal_uart_open,
    .close            = hal_uart_close,
    .reopen           = hal_uart_re_open,
    .print            = hal_uart_print,
    .scan             = hal_uart_scan,
    .recv             = hal_uart_receive,
    .recv_dont_clear  = hal_uart_receive_dont_clear,
    .send             = hal_uart_send,
    .send_over_query  = hal_uart_send_over_query,
    .timer            = hal_uart_timer_run,
    .frame_recv       = hal_uart_recv_by_frame,
    .full_duplex_recv = hal_uart_full_duplex_recv,
};

/** @} */
/** @} */
/** @} */
