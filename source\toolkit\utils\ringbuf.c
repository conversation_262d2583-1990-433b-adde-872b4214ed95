/**
 ******************************************************************************
 * @file    ringbuf.c
 * <AUTHOR> @date    2025
 * @brief   环形缓冲区实现
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include "ringbuf.h"
#include "typedef.h"
#include <string.h>

/// @brief 定义在buf满时，任然搜索不到有效字段是否清除缓冲区
#define RINGBUF_CLEAR_ON_FULL 0    // 设置为0时须在应用层处理，否则有可能导致无法通讯。

/// @brief 初始化环形缓冲区
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param buf 指向缓冲区的指针
/// @param size 缓冲区大小
void ringbuf_init(ringbuffer_s *rb_p, uint8_t *buf, int16_t size)
{
    rb_p->buf  = buf;
    rb_p->size = size;
    rb_p->out  = 0;
    rb_p->in   = 0;
}
/// @brief 重置环形缓冲区
/// @param rb_p 指向环形缓冲区结构体的指针
void ringbuf_reset(ringbuffer_s *rb_p)
{
    rb_p->in  = 0;
    rb_p->out = 0;
    if(rb_p->buf) { memset(rb_p->buf, 0, rb_p->size); }
}

/// @brief 获取环形缓冲区中的数据长度
/// @param rb_p 指向环形缓冲区结构体的指针
uint16_t ringbuf_data_len(ringbuffer_s *rb_p)
{
    if(rb_p->size == 0 || rb_p->buf == NULL) { return 0; }
    return (rb_p->size + rb_p->in - rb_p->out) % rb_p->size;
}

/// @brief 获取环形缓冲区的剩余空间
/// @param rb_p 指向环形缓冲区结构体的指针
/// @return 剩余空间的字节数
uint16_t ringbuf_space_left(ringbuffer_s *rb_p)
{
    if(rb_p->size == 0 || rb_p->buf == NULL) { return 0; }
    return rb_p->size - ringbuf_data_len(rb_p) - 1;    // 保留一个字节用于区分满和空
}

/// @brief 写入数据到环形缓冲区
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param ptr 指向要写入的数据的指针
/// @param length 要写入的数据长度
/// @return 实际写入的长度
/// @note 如果缓冲区已满，则不会写入数据，返回0
uint16_t ringbuf_write(ringbuffer_s *rb_p, const uint8_t *ptr, uint16_t length)
{
    uint16_t space_left = ringbuf_space_left(rb_p);

    if(length == 0) return 0;    // 如果读取长度为0，直接返回0
    if(rb_p == NULL || rb_p->in >= rb_p->size || rb_p->size == 0)
    {
        ringbuf_reset(rb_p);
        return 0;    // 无效参数或缓冲区未初始化
    }
    if(length > space_left)
    {
        length = space_left;    // 可用空间不足，截断写入长度
    }
    if(length == 0) return 0;

    uint16_t part1_len = rb_p->size - rb_p->in;
    if(length <= part1_len)
    {
        // 数据不会环绕，一次拷贝即可
        memcpy(&rb_p->buf[rb_p->in], ptr, length);
    }
    else
    {
        // 数据会环绕，需要分两次拷贝
        uint16_t part2_len = length - part1_len;
        memcpy(&rb_p->buf[rb_p->in], ptr, part1_len);
        memcpy(&rb_p->buf[0], ptr + part1_len, part2_len);
    }

    rb_p->in = (rb_p->in + length) % rb_p->size;
    return length;
}
/// @brief 从环形缓冲区读取数据
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param ptr 指向存储读取数据的缓冲区的指针
/// @param length 要读取的数据长度
uint16_t ringbuf_read(ringbuffer_s *rb_p, uint8_t *ptr, uint16_t length)
{
    uint16_t data_len = ringbuf_data_len(rb_p);

    if(length == 0) return 0;    // 如果读取长度为0，直接返回0
    if(rb_p == NULL || rb_p->out >= rb_p->size || rb_p->size == 0)
    {
        ringbuf_reset(rb_p);
        return 0;    // 无效参数或缓冲区未初始化
    }
    if(length > data_len) { length = data_len; }    // 缓冲区数据不足，调整读取长度
    if(length == 0) return 0;

    uint16_t part1_len = rb_p->size - rb_p->out;
    if(length <= part1_len)
    {
        // 数据不环绕，一次拷贝
        memcpy(ptr, &rb_p->buf[rb_p->out], length);
        memset(&rb_p->buf[rb_p->out], 0, length);    // 清除已读取的数据
    }
    else
    {
        // 数据环绕，分两次拷贝
        uint16_t part2_len = length - part1_len;
        memcpy(ptr, &rb_p->buf[rb_p->out], part1_len);
        memcpy(ptr + part1_len, &rb_p->buf[0], part2_len);
        memset(&rb_p->buf[rb_p->out], 0, part1_len);    // 清除已读取的数据
        memset(&rb_p->buf[0], 0, part2_len);            // 清除已读取的数据
    }

    rb_p->out = (rb_p->out + length) % rb_p->size;
    return length;
}

/// @brief 从指定位置获取指定长度的数据，并清除已读取的数据
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param start_offset 从读指针开始的偏移量
/// @param length 要读取的数据长度
/// @param outbuf 存放读取数据的目标缓冲区
/// @return 实际读取的数据长度，若无数据或参数无效则返回0
uint16_t ringbuf_read_from(ringbuffer_s *rb_p, uint16_t start_offset, uint16_t length, uint8_t *outbuf)
{
    if(rb_p == NULL || rb_p->buf == NULL || outbuf == NULL || rb_p->size == 0)
    {
        return 0;    // 参数无效
    }

    uint16_t data_len = ringbuf_data_len(rb_p);

    // 检查起始偏移是否超出数据长度
    if(start_offset >= data_len)
    {
        return 0;    // 起始偏移无效
    }

    // 确保读取长度不超过可用数据长度
    if(length > data_len - start_offset) { length = data_len - start_offset; }

    uint16_t start_idx = (rb_p->out + start_offset) % rb_p->size;
    uint16_t part1_len = rb_p->size - start_idx;

    if(length <= part1_len)
    {
        // 数据不环绕，一次拷贝
        memcpy(outbuf, &rb_p->buf[start_idx], length);
        memset(&rb_p->buf[start_idx], 0, length);    // 清除已读取的数据
    }
    else
    {
        // 数据环绕，分两次拷贝
        uint16_t part2_len = length - part1_len;
        memcpy(outbuf, &rb_p->buf[start_idx], part1_len);
        memcpy(outbuf + part1_len, &rb_p->buf[0], part2_len);
        memset(&rb_p->buf[start_idx], 0, part1_len);    // 清除已读取的数据
        memset(&rb_p->buf[0], 0, part2_len);            // 清除已读取的数据
    }

    // 更新读指针
    rb_p->out = (rb_p->out + start_offset + length) % rb_p->size;

    return length;
}

/// @brief 从指定位置获取指定长度的数据，并不清除已读取的数据
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param start_offset 从读指针开始的偏移量
/// @param length 要读取的数据长度
/// @param outbuf 存放读取数据的目标缓冲区
/// @return 实际读取的数据长度，若无数据或参数无效则返回0
uint16_t ringbuf_read_from_noclr(ringbuffer_s *rb_p, uint16_t start_offset, uint16_t length, uint8_t *outbuf)
{
    if(rb_p == NULL || rb_p->buf == NULL || outbuf == NULL || rb_p->size == 0)
    {
        return 0;    // 参数无效
    }

    uint16_t data_len = ringbuf_data_len(rb_p);

    // 检查起始偏移是否超出数据长度
    if(start_offset >= data_len)
    {
        return 0;    // 起始偏移无效
    }

    // 确保读取长度不超过可用数据长度
    if(length > data_len - start_offset) { length = data_len - start_offset; }

    uint16_t start_idx = (rb_p->out + start_offset) % rb_p->size;
    uint16_t part1_len = rb_p->size - start_idx;

    if(length <= part1_len)
    {
        // 数据不环绕，一次拷贝
        memcpy(outbuf, &rb_p->buf[start_idx], length);
    }
    else
    {
        // 数据环绕，分两次拷贝
        uint16_t part2_len = length - part1_len;
        memcpy(outbuf, &rb_p->buf[start_idx], part1_len);
        memcpy(outbuf + part1_len, &rb_p->buf[0], part2_len);
    }

    return length;
}

/// @brief 在环形缓冲区中从指定位置开始搜索字段，
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param pattern 要搜索的字符串
/// @param start_offset 从读指针开始的偏移量
/// @return 字段首次出现的偏移（相对读指针），未找到返回-1
int16_t ringbuf_search(const ringbuffer_s *rb_p, const char *pattern, uint16_t start_offset)
{
    if(rb_p == NULL || rb_p->buf == NULL || pattern == NULL) return -1;

    uint16_t plen     = strlen(pattern);
    uint16_t data_len = ringbuf_data_len((ringbuffer_s *)rb_p);

    // 检查起始偏移是否超出数据长度
    if(start_offset >= data_len || plen == 0 || plen > data_len - start_offset) return -1;

    for(uint16_t i = start_offset; i <= data_len - plen; i++)
    {
        uint16_t idx   = (rb_p->out + i) % rb_p->size;
        bool     match = true;
        for(uint16_t j = 0; j < plen; j++)
        {
            if(rb_p->buf[(idx + j) % rb_p->size] != (uint8_t)pattern[j])
            {
                match = false;
                break;
            }
        }
        if(match) return (int16_t)i;    // 返回相对读指针的偏移量
    }
    // 如果没有找到匹配的模式，返回-1
#if RINGBUF_CLEAR_ON_FULL
    if(data_len >= rb_p->size - 1) { ringbuf_reset((ringbuffer_s *)rb_p); }    // 数据超缓冲区大小，考虑数据无效，全部清除
#endif
    return -1;
}

/// @brief 从指定位置开始搜索字段，匹配后取出从读指针到指定字段（含）之间的数据，并清除这一段数据
/// @param rb_p 指向环形缓冲区结构体的指针
/// @param pattern 要搜索的字符串
/// @param start_offset 从读指针开始的偏移量
/// @param outbuf 存放取出数据的目标缓冲区
/// @return 实际取出的数据长度，未找到返回0
uint16_t ringbuf_get_until(ringbuffer_s *rb_p, const char *pattern, uint16_t start_offset, uint8_t *outbuf)
{
    int pos = ringbuf_search(rb_p, pattern, start_offset);
    if(pos < 0) return 0;    // 未找到

    uint16_t total_len = pos + strlen(pattern);
    uint16_t data_len  = ringbuf_data_len(rb_p);

    total_len = (total_len > data_len) ? data_len : total_len;    // 确保不超过数据长度
    return ringbuf_read(rb_p, outbuf, total_len);
}


