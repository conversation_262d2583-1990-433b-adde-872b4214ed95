/**
  ******************************************************************************
  * @file    energy.c
  * <AUTHOR> @date    2024
  * @brief   电能处理模块  
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "app_config.h"
#include "utils.h"
#include "bsp.h"
#include "datastore.h"
#include "energy.h"
#include "tariff.h"

#define RAM_CS_VALUE            0x5AA5      ///电能内存防错乱标志
#define PWRDWN_UPDATE_EN_FLAG   0xA55A      ///电能掉电保存防错乱标志

#define ENERGY_CRC16            0           ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)   STRUCT_CRC16_CHK(ENERGY_CRC16, struct, len)
#define CRC16_CAL(struct,len)   STRUCT_CRC16_GET(ENERGY_CRC16, struct, len)
/// 存储地址
#define ENERGY_PD_SAVE_ADDR     nvm_addr(NVM_PD_ENG_DATA) 
#define ENERGY_SAVE_ADDR        nvm_addr(NVM_ENG_DATA)
#define ENERGY_SAVE_BAK_ADDR    nvm_addr(NVM_ENG_BAK_DATA)
#define ENERGY_PARA_ADDR        nvm_addr(NVM_ENG_PARA)

extern const energy_para_s  energy_default_para;
static const energy_para_s *energy_para;

__no_init static EnergyValue_s energy_blk;   ///总及分费率电能数据块


#if defined(POLYPHASE_METER)
static uint16_t second_pulse[4][ENERGY_TYPE_NUM];   ///每秒电能T/A/B/C脉冲数
#else
static uint16_t second_pulse[1][ENERGY_TYPE_NUM];   ///每秒电能脉冲数
#endif
static uint16_t     inc_value;          ///电能增量(以脉冲计数)，用于电能存储的判断,每秒累计电能，存储后清理
static funcPointer  ext_data_store;     ///电能保存时调用的函数指针


/// @brief 确认电能是否需要存储
static bool eng_save_query(void)
{
	static uint16_t timer = 0;
	if(++timer > 60 * 15)  // 15分钟保存一次 
	{
		timer = 0;
		if(inc_value > 0)
        {
            inc_value = 0;
            timer = 0;
            return true;
        }
	}
	return FALSE;
}


/// @brief 参数计算校验后存储
/// @param ofst  偏移地址
/// @param val   值
/// @param len   长度
/// @return      
static bool energy_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    energy_para_s para;
    if(ofst != 0) memcpy(&para, energy_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(energy_para_s));
    energy_para = (const energy_para_s*)ENERGY_PARA_ADDR;
    return nvm.write((uint32_t)energy_para, &para, sizeof(para));
}

/// @brief 电能参数重载
static void energy_para_load(void)
{
    energy_para = (const energy_para_s*)ENERGY_PARA_ADDR;
    if(CRC16_CHK(energy_para, sizeof(energy_para_s)) == FALSE)
    {
        energy_para = &energy_default_para;
    }
}

/// @brief 电能数据块检出（依次是：主、备、掉电存储区）， 只在上电初始化后，正常运行中校验电能数据调用, 初始化时禁止使用
/// @param
static void energy_check_recovery(void)
{
    /* 校验当前总及分费率总电能 */
    if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
    {
        /* 读取EEPROM主存储的总及分费率总电能 */
        nvm.read(ENERGY_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
        //if(energy_blk.cs != CAL_CS16(&energy_blk, sizeof(EnergyValue_s)))
        if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
        {
            /* 读取EEPROM备份存储的总及分费率总电能 */
            nvm.read(ENERGY_SAVE_BAK_ADDR, &energy_blk, sizeof(EnergyValue_s));
            if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
            {
                /* 读取FLASH存储的总及分费率总电能 */
                nvm.read(ENERGY_PD_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
                if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
                {
                    inc_value = 0;
                    memset(&energy_blk, 0, sizeof(EnergyValue_s));
                    energy_blk.flag = RAM_CS_VALUE;
                    //energy_blk.cs = CAL_CS16(&energy_blk, sizeof(EnergyValue_s));
                    CRC16_CAL(&energy_blk, sizeof(energy_blk));
                }
            }
        }
    } 
}

/// @brief 电能数据块保存（主、备存储区）
/// @param
static void energy_check_store(void)
{
	/// 放置跌落试验误写数据
	if(energy_blk.flag != RAM_CS_VALUE) return;

	/// EEPROM保存两份电能
	nvm.write(ENERGY_SAVE_ADDR,     &energy_blk, sizeof(EnergyValue_s));
    nvm.write(ENERGY_SAVE_BAK_ADDR, &energy_blk, sizeof(EnergyValue_s));

	/// 预付费模块的数据存储
	if(ext_data_store != NULL) ext_data_store();
}

/// @brief 单相表或三相表代数和脉冲模式
/// @param pwr_down
static void energy_cumulate_faraday(bool pwr_down)
{
    uint16_t act, rea, app, total;
    memset(second_pulse, 0, sizeof(second_pulse));

    for(EMU_PHASE_TYPE i = A_PHASE; i <= EN_CHN_NUM; i++)
    {
        EMU_PHASE_TYPE ph       = (EMU_PHASE_TYPE)(i % EN_CHN_NUM);    /// 先读分相电能，最后读合相电能
        uint8_t        quadrant = mic.ins->quadrant[ph];

        /* 从计量驱动模块中获取电能脉冲数增量 */
        act = mic.pulse(P_POWER, ph);
        rea = mic.pulse(Q_POWER, ph);
        app = mic.pulse(S_POWER, ph);

#if defined(POLYPHASE_METER) && ENERGY_PHASE_ENABLE
        /* 如果处于掉电处理下, 累加分相快速脉冲计数器值 */
        if(pwr_down && ph != T_PHASE)
        {
            uint8_t num = mic.measure_fast_cnt_add(ph, &energy_blk.fast_cnt[ph], 1);
            act += num, app += num;
        }
#endif
        total = act + rea + app;
        if(total == 0) continue;
        if(inc_value < 65535) inc_value += total;

        /* 根据象限值, 对各类型电能进行累计 */
        switch(quadrant)
        {
            case 1:    // 一象限
                second_pulse[ph][TYPE_ENERGY_POS_ACT] = act;
                second_pulse[ph][TYPE_ENERGY_Q1_REA]  = rea;
#if ENERGY_APP_ENABLE
                second_pulse[ph][TYPE_ENERGY_POS_APP] = app;
#endif
                break;

            case 2:    // 二象限
                second_pulse[ph][TYPE_ENERGY_NEG_ACT] = act;
                second_pulse[ph][TYPE_ENERGY_Q2_REA]  = rea;
#if ENERGY_APP_ENABLE
                second_pulse[ph][TYPE_ENERGY_NEG_APP] = app;
#endif
                break;

            case 3:    // 三象限
                second_pulse[ph][TYPE_ENERGY_NEG_ACT] = act;
                second_pulse[ph][TYPE_ENERGY_Q3_REA]  = rea;
#if ENERGY_APP_ENABLE
                second_pulse[ph][TYPE_ENERGY_NEG_APP] = app;
#endif
                break;

            case 4:    // 四象限
                second_pulse[ph][TYPE_ENERGY_POS_ACT] = act;
                second_pulse[ph][TYPE_ENERGY_Q4_REA]  = rea;
#if ENERGY_APP_ENABLE
                second_pulse[ph][TYPE_ENERGY_POS_APP] = app;
#endif
                break;
        }
    }
}

/// @brief 电能脉冲累加操作
/// @param pwr_down 是否掉电情形
static void energy_cumulate(bool pwr_down)
{
#if defined(POLYPHASE_METER)
    if(mic.ins->stus.abs)
    {
        energy_cumulate_faraday(pwr_down);    // 如果三相表采用绝对值出脉冲（暂时只支持代数和方式）
    }
    else
#endif
    {
        energy_cumulate_faraday(pwr_down);    // 如果三相表采用代数和出脉冲
    }

    /* 累计分费率电能脉冲 */
    for(uint8_t i = 0, j = tariff.cur_rate_get(); i < ENERGY_TYPE_NUM; i++)
    {
        if(second_pulse[0][i] == 0) continue;

        energy_blk.value[0][i] += second_pulse[0][i];
        energy_blk.value[j][i] += second_pulse[0][i];
#if ENERGY_PHASE_ENABLE
        energy_blk.a_value[0][i] += second_pulse[1][i];
        energy_blk.b_value[0][i] += second_pulse[2][i];
        energy_blk.c_value[0][i] += second_pulse[3][i];
#if ENERGY_PHASE_TARIFF_EN
        energy_blk.a_value[j][i] += second_pulse[1][i];
        energy_blk.b_value[j][i] += second_pulse[2][i];
        energy_blk.c_value[j][i] += second_pulse[3][i];
#endif
#endif
    }

    if(energy_blk.flag != RAM_CS_VALUE)
    {
        energy_check_recovery();
        energy_blk.flag = RAM_CS_VALUE;
    }
    else { CRC16_CAL(&energy_blk, sizeof(energy_blk)); }
}

/// @brief 电能模块初始化
/// @param
static void energy_init(void)
{
    energy_para_load();
    if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
    {
    	/* 读取FLASH存储的总及分费率总电能 */
		nvm.read(ENERGY_PD_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
		if(energy_blk.flag != PWRDWN_UPDATE_EN_FLAG || (CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE))
		{ // 说明该FLASH中的电能数据未正确掉电保存过
			/* 读取EEPROM主存储的总及分费率总电能 */
			nvm.read(ENERGY_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
			if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
			{
				/* 读取EEPROM备份存储的总及分费率总电能 */
				nvm.read(ENERGY_SAVE_BAK_ADDR, &energy_blk, sizeof(EnergyValue_s));
				if(CRC16_CHK(&energy_blk, sizeof(energy_blk)) == FALSE)
				{
					memset(&energy_blk, 0, sizeof(EnergyValue_s));
					CRC16_CAL(&energy_blk, sizeof(energy_blk));
				}
			}
			energy_blk.flag = RAM_CS_VALUE;
		}
    }

    if(energy_blk.flag == PWRDWN_UPDATE_EN_FLAG && bsp.state_query(STUS_BSP_PWR_ON))
    { // 说明该FLASH中的电能数据有正确掉电保存过
    	uint16_t cs = 0;
    	energy_blk.flag = RAM_CS_VALUE;
    	nvm.read(ENERGY_SAVE_ADDR + member_offset(EnergyValue_s, crc), &cs, 2);
    	if(cs != energy_blk.crc) nvm.write(ENERGY_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s)); // 刷新EEPROM中电能
    	nvm.write(ENERGY_PD_SAVE_ADDR + member_offset(EnergyValue_s, flag), &energy_blk.flag, 2); // 清除掉电存储标志
    }
}

/// @brief 电能数据块更新（秒任务）
/// @param
static void energy_update(void)
{
	mic.ins_val_refresh();
    energy_para_load();
	energy_check_recovery();
	energy_cumulate(0);
	if(eng_save_query()) energy_check_store();
}

/// @brief 电能数据块掉电保存
/// @param
static void energy_power_down_save(void)
{
	mic.off(); // 关闭计量
	energy_check_recovery();
	energy_cumulate(1);
	/* 以安全和速度最快考虑, 只保存一份电能数据块到MCU的FLASH中 */
    if(inc_value == 0) return;
	energy_blk.flag = PWRDWN_UPDATE_EN_FLAG; // 掉电保存标志
	nvm.write(ENERGY_PD_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
}

/// @brief 电能模块复位操作
/// @param type
static void energy_reset(uint8_t type)
{
    if(type & SYS_DATA_RESET)
    {
    	memset(&energy_blk, 0, sizeof(EnergyValue_s));
    	energy_blk.flag = RAM_CS_VALUE;
        CRC16_CAL(&energy_blk, sizeof(energy_blk));
    	if(type != SYS_GLOBAL_RESET)
    	{
        	nvm.write(ENERGY_SAVE_ADDR, &energy_blk, sizeof(EnergyValue_s));
        	nvm.write(ENERGY_SAVE_BAK_ADDR, &energy_blk, sizeof(EnergyValue_s));
    	}

        memset(second_pulse, 0, sizeof(second_pulse));
        inc_value = 0;
    }
}

/// @brief 设置与电能同步checkin的外部函数
/// @param func  外部函数
/// @return
static void energy_checkin_callback(void func(void))
{
	ext_data_store = func;
}

//energy 模块电能数据访问接口*********************************************

/// @brief 获取秒发生电能(脉冲数)，主要给需量模块使用
/// @param ph    合相及分相
/// @param type  电能类型
/// @return 脉冲数
static int16 energy_phs_sec_pulse_get(uint8_t ph, ENERGY_TYPE type)
{
	uint16_t* psec = second_pulse[ph];
	switch(type)
	{
	#if ENERGY_REA_LAG_LEAD_EN
		case TYPE_ENERGY_POS_REA: return psec[TYPE_ENERGY_Q1_REA ] + psec[TYPE_ENERGY_Q3_REA ];
		case TYPE_ENERGY_NEG_REA: return psec[TYPE_ENERGY_Q2_REA ] + psec[TYPE_ENERGY_Q4_REA ];
	#else
		case TYPE_ENERGY_POS_REA: return psec[TYPE_ENERGY_Q1_REA ] + psec[TYPE_ENERGY_Q2_REA ]
        #if ENERGY_NEG_TO_POS_EN
		                               + psec[TYPE_ENERGY_Q3_REA ] + psec[TYPE_ENERGY_Q4_REA ]
		#endif
		                               ;
		case TYPE_ENERGY_NEG_REA: return psec[TYPE_ENERGY_Q3_REA ] + psec[TYPE_ENERGY_Q4_REA ];
	#endif
    #if ENERGY_NEG_TO_POS_EN
        case TYPE_ENERGY_POS_ACT: return psec[TYPE_ENERGY_POS_ACT] + psec[TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_POS_APP: return psec[TYPE_ENERGY_POS_APP] + psec[TYPE_ENERGY_NEG_APP];
    #endif
		case TYPE_ENERGY_ADD_ACT: return psec[TYPE_ENERGY_POS_ACT] + psec[TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_SUB_ACT: return psec[TYPE_ENERGY_POS_ACT] - psec[TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_ADD_REA: return psec[TYPE_ENERGY_Q1_REA ] + psec[TYPE_ENERGY_Q2_REA ]
		                               + psec[TYPE_ENERGY_Q3_REA ] + psec[TYPE_ENERGY_Q4_REA ];
    #if ENERGY_APP_ENABLE
		case TYPE_ENERGY_ADD_APP: return psec[TYPE_ENERGY_POS_APP] + psec[TYPE_ENERGY_NEG_APP];
    #else
        case TYPE_ENERGY_ADD_APP: return 0;   
    #endif
        default: return psec[type];
	}
}

/// @brief 获取累计电能（脉冲数）
/// @param ph    合相及分相
/// @param type  电能类型
/// @param rate  费率类型
/// @return 获取累计的脉冲数
static int64 energy_phs_cum_pulse_get(uint8_t ph, ENERGY_TYPE type, uint8_t rate)
{
    int64 (*pcum)[ENERGY_TYPE_NUM];

    switch(ph)
    {
    	case T_PHASE: pcum = energy_blk.value;   break;
#if ENERGY_PHASE_ENABLE
    	case A_PHASE: pcum = energy_blk.a_value; break;
     	case B_PHASE: pcum = energy_blk.b_value; break;
    	case C_PHASE: pcum = energy_blk.c_value; break;
#endif
    	default: return 0;
    }

#if defined(POLYPHASE_METER) && !ENERGY_PHASE_TARIFF_EN
    if(ph != T_PHASE && rate > 0) return 0; // 分相分费率电能返回0
#endif

	switch(type)
	{
	#if ENERGY_REA_LAG_LEAD_EN
		case TYPE_ENERGY_POS_REA: return pcum[rate][TYPE_ENERGY_Q1_REA ] + pcum[rate][TYPE_ENERGY_Q3_REA ];
		case TYPE_ENERGY_NEG_REA: return pcum[rate][TYPE_ENERGY_Q2_REA ] + pcum[rate][TYPE_ENERGY_Q4_REA ];
	#else
		case TYPE_ENERGY_POS_REA: return pcum[rate][TYPE_ENERGY_Q1_REA ] + pcum[rate][TYPE_ENERGY_Q2_REA ]
        #if ENERGY_NEG_TO_POS_EN
		                               + pcum[rate][TYPE_ENERGY_Q3_REA ] + pcum[rate][TYPE_ENERGY_Q4_REA ]
        #endif
		                               ;
		case TYPE_ENERGY_NEG_REA: return pcum[rate][TYPE_ENERGY_Q3_REA ] + pcum[rate][TYPE_ENERGY_Q4_REA ];
	#endif

    #if ENERGY_NEG_TO_POS_EN
		case TYPE_ENERGY_POS_ACT: return pcum[rate][TYPE_ENERGY_POS_ACT] + pcum[rate][TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_POS_APP: return pcum[rate][TYPE_ENERGY_POS_APP] + pcum[rate][TYPE_ENERGY_NEG_APP];
    #endif
		case TYPE_ENERGY_ADD_ACT: return pcum[rate][TYPE_ENERGY_POS_ACT] + pcum[rate][TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_SUB_ACT: return pcum[rate][TYPE_ENERGY_POS_ACT] - pcum[rate][TYPE_ENERGY_NEG_ACT];
		case TYPE_ENERGY_ADD_REA: return pcum[rate][TYPE_ENERGY_Q1_REA ] + pcum[rate][TYPE_ENERGY_Q2_REA ]
		                               + pcum[rate][TYPE_ENERGY_Q3_REA ] + pcum[rate][TYPE_ENERGY_Q4_REA ];
#if ENERGY_APP_ENABLE
		case TYPE_ENERGY_ADD_APP: return pcum[rate][TYPE_ENERGY_POS_APP] + pcum[rate][TYPE_ENERGY_NEG_APP];
#endif
        default: return pcum[rate][type]; 
	}
}

/// @brief 获取累计电能 单位kWh（需根据 ENERGY_DEF_SCALER 计算）
/// @param ph    合相及分相
/// @param type  电能类型
/// @param rate  费率类型
/// @return
static ENERGY_DEF_FORMAT energy_phs_cum_value_get(uint8_t ph, ENERGY_TYPE type, uint8_t rate)
{
#if ENERGY_FLOAT_EN
    fp64 val = energy_phs_cum_pulse_get(ph, type, rate) / (METER_CONST * ENERGY_DEF_SCALER);
    return (ENERGY_DEF_FORMAT)(val);
#else
    int64 val = (int64)(energy_phs_cum_pulse_get(ph, type, rate) / (METER_CONST * ENERGY_DEF_SCALER));
    return (ENERGY_DEF_FORMAT)(val);
#endif
}

/// @brief 参数设置
/// @param ofst 
/// @param val 
/// @param len 
bool energy_para_set(uint16_t ofst, const void* val, uint16_t len)
{
    return energy_para_save(ofst, val, len);
}

/// @brief 获取参数
const energy_para_s* energy_para_get(void)
{
    return energy_para;
}

/// @brief 声明电能子模块对象
const struct energy_s energy =
{
    .init                       = energy_init,
    .refresh                    = energy_update,
    .pwr_down_save              = energy_power_down_save,

    .reset                      = energy_reset,
    .checkin_callback           = energy_checkin_callback,
    .phs_sec_pulse_get          = energy_phs_sec_pulse_get,
    // .phs_cum_pulse_get          = energy_phs_cum_pulse_get,
    .phs_cum_value_get          = energy_phs_cum_value_get,

    .para_set                   = energy_para_set,
    .para_get                   = energy_para_get,
};


