# *注意！！！*
  此部分为底层驱动，HAL、BSP各文件的接口函数不要随意更改，如需增加或修改接口函数，请先与平平台维护人员沟通。

## 1，说明

底层驱动命名： **`\MCU名称-电表型号`**

`\MCU       `    一般存放的是MCU厂商提供的相关驱动库等内容。
`\HAL       `    使用MCU的驱动进一步实现电表硬件抽象层，隐藏MCU细节内容。
`\BSP       `    使用HAL层提供的接口实现PCB设计相关的板层硬件驱动。
`\boot_cfg.c`    主要在BOOT区实现应用程序升级需要的驱动相关函数接口，不同硬件平台兼容，移植无需修改。
`\boot_cfg.h`    配置应用程序升级相关参数，移植注意修改MCU flash 参数。
`\hal_cfg.h `    配置HAL硬件抽象层包含哪些模块单元。
`\bsp_cfg.h `    配置PCB板层硬件驱动相关功能，在此配置项目的硬件参数。

注意： 在 `boot_cfg.c`中实现相关函数时，应直接调用HAL和BSP层的接口

