/**
 ******************************************************************************
 * @file    display_table.h
 * <AUTHOR> @date    2024
 * @brief   电表显示总表，包含所有可显示的电表数据
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "DLT645_2007_id.h"
#include "display_table.h"


/// 普通数据显示对象
#if LCD_MULTI_LINE_DISPLAY
#define DISP_NORMAL_ID(idn,inf,dfn) {.id=idn,.info=inf,{.df=dfn,.num=0,},},
#define DISP_HR_ID(m,id,inf,dfn) {id(m), .info=inf,{.df=dfn,.num=0,},},
#else
#define DISP_NORMAL_ID(idn,inf,dfn) {.id=idn,{.df=dfn,.num=0,},},
#define DISP_HR_ID(m,id,inf,dfn) {id(m), {.df=dfn,.num=0,},},
#endif


#define DISP_MUL_HR_ID(m,...)    M1REPEAT(m, DISP_HR_ID, __VA_ARGS__)       ///   谐波数据项定义

const disp_obj_s disp_total_table[] = 
{
    DISP_NORMAL_ID(ID_LCD_ALL,          NULL,             DF_CHR_STRING)     /// LCD test, 全显示
    DISP_NORMAL_ID(C0_CMB_kWh(0,0),     NULL,             DF_ENERGY    )     /// 当前累计消耗组合有功电能总
    DISP_NORMAL_ID(C0_POS_kWh(0,0),     NULL,             DF_ENERGY    )     /// 当前累计消耗正向有功电能总
    DISP_NORMAL_ID(C0_NEG_kWh(0,0),     NULL,             DF_ENERGY    )     /// 当前累计消耗反向有功电能总
    DISP_NORMAL_ID(C0_CMB1_kvarh(0, 0), NULL,             DF_ENERGY    )     /// 当前累计消耗组合无功1电能总
    DISP_NORMAL_ID(C0_CMB2_kvarh(0, 0), NULL,             DF_ENERGY    )     /// 当前累计消耗组合无功2电能总
         
    DISP_NORMAL_ID(C2_A_VOL,            NULL,             DF_VOLTAGE   )     /// A相电压
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_B_VOL,            NULL,             DF_VOLTAGE   )     /// B相电压
    DISP_NORMAL_ID(C2_C_VOL,            NULL,             DF_VOLTAGE   )     /// C相电压
#endif           
    DISP_NORMAL_ID(C2_A_CUR,            NULL,             DF_CURRENT   )     /// A相   电流
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_B_CUR,            NULL,             DF_CURRENT   )     /// B相   电流
    DISP_NORMAL_ID(C2_C_CUR,            NULL,             DF_CURRENT   )     /// C相   电流
#endif           
    DISP_NORMAL_ID(C2_T_INS_P,          NULL,             DF_POWER     )     /// 总    有功功率
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_A_INS_P,          NULL,             DF_POWER     )     /// A相   有功功率
    DISP_NORMAL_ID(C2_B_INS_P,          NULL,             DF_POWER     )     /// B相   有功功率
    DISP_NORMAL_ID(C2_C_INS_P,          NULL,             DF_POWER     )     /// C相   有功功率
#endif           
    DISP_NORMAL_ID(C2_T_INS_Q,          NULL,             DF_POWER     )     /// 总    无功功率
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_A_INS_Q,          NULL,             DF_POWER     )     /// A相   无功功率
    DISP_NORMAL_ID(C2_B_INS_Q,          NULL,             DF_POWER     )     /// B相   无功功率
    DISP_NORMAL_ID(C2_C_INS_Q,          NULL,             DF_POWER     )     /// C相   无功功率
#endif           
    DISP_NORMAL_ID(C2_T_INS_S,          NULL,             DF_POWER     )     /// 总    视在功率
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_A_INS_S,          NULL,             DF_POWER     )     /// A相   视在功率
    DISP_NORMAL_ID(C2_B_INS_S,          NULL,             DF_POWER     )     /// B相   视在功率
    DISP_NORMAL_ID(C2_C_INS_S,          NULL,             DF_POWER     )     /// C相   视在功率
#endif           
    DISP_NORMAL_ID(C2_T_PF,             NULL,             DF_PF        )     /// 总    功率因素
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_A_PF,             NULL,             DF_PF        )     /// A相   功率因素
    DISP_NORMAL_ID(C2_B_PF,             NULL,             DF_PF        )     /// B相   功率因素
    DISP_NORMAL_ID(C2_C_PF,             NULL,             DF_PF        )     /// C相   功率因素
#endif           
    DISP_NORMAL_ID(C2_A_ANGEL,          NULL,             DF_DECIMAL_1 )     /// A相   相角
#if defined(POLYPHASE_METER)             
    DISP_NORMAL_ID(C2_B_ANGEL,          NULL,             DF_DECIMAL_1 )     /// B相   相角
    DISP_NORMAL_ID(C2_C_ANGEL,          NULL,             DF_DECIMAL_1 )     /// C相   相角
#endif           
    DISP_NORMAL_ID(C2_A_VOL_THD,        NULL,             DF_DECIMAL_2 )     /// A相电压波形失真度
#if defined(POLYPHASE_METER) 
    DISP_NORMAL_ID(C2_B_VOL_THD,        NULL,             DF_DECIMAL_2 )     /// B相电压波形失真度
    DISP_NORMAL_ID(C2_C_VOL_THD,        NULL,             DF_DECIMAL_2 )     /// C相电压波形失真度
#endif   
    DISP_NORMAL_ID(C2_A_CUR_THD,        NULL,             DF_DECIMAL_2 )     /// A相电流波形失真度
#if defined(POLYPHASE_METER) 
    DISP_NORMAL_ID(C2_B_CUR_THD,        NULL,             DF_DECIMAL_2 )     /// B相电流波形失真度
    DISP_NORMAL_ID(C2_C_CUR_THD,        NULL,             DF_DECIMAL_2 )     /// C相电流波形失真度
#endif   
#if HARMONIC_WAVE
    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_A_VOL_HARMONIC, NULL,DF_DECIMAL_2)     /// A相电压谐波含量 1-21
#if defined(POLYPHASE_METER) 
    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_B_VOL_HARMONIC, NULL,DF_DECIMAL_2)     /// B相电压谐波含量 1-21
    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_C_VOL_HARMONIC, NULL,DF_DECIMAL_2)     /// C相电压谐波含量 1-21s
#endif  

    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_A_CUR_HARMONIC, NULL,DF_DECIMAL_2)     /// A相电流谐波含量 1-21
#if defined(POLYPHASE_METER) 
    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_B_CUR_HARMONIC, NULL,DF_DECIMAL_2)     /// B相电流谐波含量 1-21
    DISP_MUL_HR_ID(HARMONIC_WAVE, C2_C_CUR_HARMONIC, NULL,DF_DECIMAL_2)     /// C相电流谐波含量 1-21
#endif  
#endif
    DISP_NORMAL_ID(C2_N_CUR,            NULL,             DF_CURRENT   )     /// 零线电流
    DISP_NORMAL_ID(C2_FREQUENCY,        NULL,             DF_DECIMAL_2 )     /// 电网频率
    DISP_NORMAL_ID(C2_AVR_POWER_1M,     NULL,             DF_POWER     )     /// 一分钟有功平均功率
    DISP_NORMAL_ID(C2_CUR_DM_kW,        NULL,             DF_POWER     )     /// 当前有功需量
    DISP_NORMAL_ID(C2_CUR_DM_kva,       NULL,             DF_POWER     )     /// 当前无功需量
    DISP_NORMAL_ID(C2_CUR_DM_kV,        NULL,             DF_POWER     )     /// 当前视在需量
    DISP_NORMAL_ID(C2_TEMPARETURE,      NULL,             DF_DECIMAL_1 )     /// 温度
    DISP_NORMAL_ID(C2_INTBAT_VOL,       NULL,             DF_DECIMAL_1 )     /// 内部时钟电池电压
    DISP_NORMAL_ID(C2_EXTBAT_VOL,       NULL,             DF_DECIMAL_1 )     /// 外部抄表电池电压
    DISP_NORMAL_ID(C2_INTBAT_RUN_TIME,  NULL,             DF_DECIMAL_0 )     /// 内部电池工作时间
    DISP_NORMAL_ID(C2_CUR_STEP_PRICE,   NULL,             DF_DECIMAL_0 )     /// 当前阶梯电价

    DISP_NORMAL_ID(C2_LINE_VOL_AB,      NULL,             DF_VOLTAGE   )     /// 当前AB线电压
    DISP_NORMAL_ID(C2_LINE_VOL_BC,      NULL,             DF_VOLTAGE   )     /// 当前AB线电压
    DISP_NORMAL_ID(C2_LINE_VOL_AC,      NULL,             DF_VOLTAGE   )     /// 当前AB线电压
         
    DISP_NORMAL_ID(C4_DATE_WEEK,        NULL,             DF_DATE      )     /// 当前日期
    DISP_NORMAL_ID(C4_TIME,             NULL,             DF_TIME      )     /// 当前时间
    DISP_NORMAL_ID(C4_COMM_ADDR,        NULL,             DF_HEX_STRING)     /// 通信地址
    DISP_NORMAL_ID(C4_METER_NO,         NULL,             DF_HEX_STRING)     /// 电表编号

    DISP_NORMAL_ID(C4_DEVICE_SOFT,      NULL,             DF_CHR_STRING)     /// 设备软件版本号
#if LCD_MULTI_LINE_DISPLAY
    DISP_NORMAL_ID(ID_LCD_NONE,         "          ",     DF_CHR_STRING)     /// 电表类型
    DISP_NORMAL_ID(ID_LCD_MENU(0),      "PTCT",           DF_CHR_STRING)     /// PT/CT变比显示
    DISP_NORMAL_ID(ID_LCD_MENU(1),      "TEMP",           DF_CHR_STRING)     /// 温度显示
    DISP_NORMAL_ID(ID_LCD_MENU(2),      "FREQ",           DF_CHR_STRING)     /// 频率
    DISP_NORMAL_ID(ID_LCD_MENU(3),      "SET",            DF_CHR_STRING)     /// SET
    DISP_NORMAL_ID(ID_LCD_MENU(4),      "CONN",           DF_CHR_STRING)     /// CONN
    DISP_NORMAL_ID(ID_LCD_MENU(5),      "SYS",            DF_CHR_STRING)     /// SYS
    DISP_NORMAL_ID(ID_LCD_MENU(6),      "CLR",            DF_CHR_STRING)     /// CLR
    DISP_NORMAL_ID(ID_LCD_MENU(7),      "DISP",           DF_CHR_STRING)     /// DISP

    DISP_NORMAL_ID(ID_LCD_MENU(8),      "BPS1",           DF_CHR_STRING)     /// BPS1
    DISP_NORMAL_ID(ID_LCD_MENU(9),      "BPS2",           DF_CHR_STRING)     /// BPS2
    DISP_NORMAL_ID(ID_LCD_MENU(10),     "PRY1",           DF_CHR_STRING)     /// PRY1
    DISP_NORMAL_ID(ID_LCD_MENU(11),     "PRY2",           DF_CHR_STRING)     /// PRY2
    DISP_NORMAL_ID(ID_LCD_MENU(12),     "MBUS",           DF_CHR_STRING)     /// MBUS
    DISP_NORMAL_ID(ID_LCD_MENU(13),     "DLRY",           DF_CHR_STRING)     /// DLAY
    DISP_NORMAL_ID(ID_LCD_MENU(14),     "645H",           DF_CHR_STRING)     /// 645H
    DISP_NORMAL_ID(ID_LCD_MENU(15),     "645L",           DF_CHR_STRING)     /// 645L
    DISP_NORMAL_ID(ID_LCD_MENU(16),     "PT-N",           DF_CHR_STRING)     /// PT-N
    DISP_NORMAL_ID(ID_LCD_MENU(17),     "PT-D",           DF_CHR_STRING)     /// PT-D
    DISP_NORMAL_ID(ID_LCD_MENU(18),     "CT-N",           DF_CHR_STRING)     /// CT-N
    DISP_NORMAL_ID(ID_LCD_MENU(19),     "CT-D",           DF_CHR_STRING)     /// CT-D
    DISP_NORMAL_ID(ID_LCD_MENU(20),     "TYPE",           DF_CHR_STRING)     /// TYPE
    DISP_NORMAL_ID(ID_LCD_MENU(21),     "PASS",           DF_CHR_STRING)     /// PASS
    DISP_NORMAL_ID(ID_LCD_MENU(22),     "ETRG",           DF_CHR_STRING)     /// ETRG
    DISP_NORMAL_ID(ID_LCD_MENU(23),     "ALL",            DF_CHR_STRING)     /// ALL
    DISP_NORMAL_ID(ID_LCD_MENU(24),     "DMD",            DF_CHR_STRING)     /// DMD
    DISP_NORMAL_ID(ID_LCD_MENU(25),     "KEY",            DF_CHR_STRING)     /// KEY
    DISP_NORMAL_ID(ID_LCD_MENU(26),     "LIGH",           DF_CHR_STRING)     /// LIGH
    DISP_NORMAL_ID(ID_LCD_MENU(27),     "CYCL",           DF_CHR_STRING)     /// CYCL

    DISP_NORMAL_ID(ID_LCD_MENU(28),     "SOFT",           DF_CHR_STRING)     /// SOFT
    DISP_NORMAL_ID(ID_LCD_MENU(29),     "METER",          DF_CHR_STRING)     /// METER

    DISP_NORMAL_ID(ID_LCD_MENU(49),     "FAIL",           DF_CHR_STRING)     /// FAIL
    DISP_NORMAL_ID(ID_LCD_MENU(50),     "SURE",           DF_CHR_STRING)     /// SURE
#endif
};


const uint16_t disp_total_table_num = eleof(disp_total_table);  //显示总表数量

#if LCD_MULTI_LINE_DISPLAY
/// 设置模式 通讯设置 波特率映射表
const uint8_t disp_conn_bps[][LCD_MS_DIGITS] = 
{
    "300     ",
    "600     ",
    "1200    ",
    "2400    ",
    "4800    ",
    "9600    ",
    "19200   ",
    "38400   ",
    "57600   ",
    "115200  "
};

/// 设置模式 通信模式 校验位映射表
const uint8_t disp_conn_pry[][LCD_MS_DIGITS] =
{
    "8E1     ",
    "8O1     ",
    "8N1     "
};

/// 设置模式 系统设置 接线模式映射表
const uint8_t disp_sys_type[][LCD_MS_DIGITS] =
{
    "3P4L    ",
    "3P3L    "
};
#endif
// end of file
