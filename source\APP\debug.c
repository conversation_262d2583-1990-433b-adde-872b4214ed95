/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 * Filename: ../BSP/debug.c
 * Describe: 本文件主要包含对Debug的调用
 *
 * Device  :
 * Compiler:
 *
 * Created on   :
 * Modify record:
 *
 *******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include <string.h>
#include <stdarg.h>
#include <time.h>
#include <stdio.h>
#include "debug.h"
#include "hal_rtc.h"

#if defined(UART_PRINT)
#define DBG_BUF_LEN 1024
#define PRINT_CTRL UC_NONE//UC_NONE                  // UC_RS485_DE       // 串口打印控制
#define PRINT_UART_NUM HAL_UART0//COM_BLE//HAL_UART0                // COM_RS4851
#define PRINT_UART_BAUD_RATE BAUDE_115200BPS    // BAUDE_19200BPS  BAUDE_57600BPS
#define PRINT_FORMAT CHAR_8N1
char dbg_buf[DBG_BUF_LEN];
uint8_t dbg_recv[DBG_BUF_LEN];
#endif

#if defined(DEBUG_PRINT)
void debug_printf(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    vprintf(fmt, args);
}
#endif

#if defined(UART_PRINT)

void uart_print_open(void)
{
    hal_uart.open(PRINT_UART_NUM, PRINT_CTRL, PRINT_FORMAT, PRINT_UART_BAUD_RATE, dbg_buf, DBG_BUF_LEN);
}

void uart_print_send(const char *fmt, ...)
{
    uint16_t len;

    va_list args;
    va_start(args, fmt);
    len = vsnprintf((char *)dbg_buf, sizeof(dbg_buf), fmt, args);
    va_end(args);

    if(len > DBG_BUF_LEN) len = DBG_BUF_LEN;
    for(uint16_t i = 0; i < len; i++) hal_uart.print(PRINT_UART_NUM, dbg_buf[i]);
}
#endif

#if defined(DEBUG_PRINT) || defined(UART_PRINT) || defined(SEGGER_PRINT)

/// @brief 数据打印，主要用于通讯模块调试，
/// @param msg 数据指针
/// @param len 数据长度
void mprintf(const uint8_t *msg, uint16_t len)
{
    uint16_t i, j;

#ifdef UART_PRINT
    if(len > DBG_BUF_LEN) len = DBG_BUF_LEN;
#endif
    for(i = len / 32; i != 0; i--)
    {
        dprintf("\r\n\t");
        for(j = 32; j != 0; j--) { dprintf("%.2X ", *msg++); }
    }
    j = len % 32;
    if(j > 0) dprintf("\r\n\t");
    for(; j != 0; j--) { dprintf("%.2X ", *msg++); }
    dprintf("\r\n");
}

/// @brief 打印ms计数器
/// @param
void tprintf(void)
{
    struct rtc_t rtc;
    uint32_t     tmr;

    tmr = (uint32_t)hal_timer.systick_cnt();
    hal_rtc.time_get(&rtc);    // 获取RTC时间，避免RTC时间未更新
    dprintf("\r\n%02d-%02d-%02d %02d:%02d:%02d ms:%03d > ", rtc.YY, rtc.MM, rtc.DD, rtc.hh, rtc.mm, rtc.ss, tmr % 1000);
}

#endif
