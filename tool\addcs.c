#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>

#define APP_SIZE_POS        4
#define CHECKSUM_POS        20
#define VERIFYTAG_POS       24
#define CRC32_START_VALUE   12345678

uint32_t crc32_update_revbit(uint32_t crc, const void* cp, uint32_t size)
{
    uint32_t i;
    crc ^= 0xFFFFFFFF;
    for(i = 0; i < size; i++)
    {
        uint8_t j;
        uint8_t tmp = ((uint8_t*)cp)[i];
        for(j = 0; j < 8; j++)
        {
            if(((uint8_t)crc ^ tmp) & 1)
            {
                crc >>= 1;
                crc ^= 0xEDB88320;
            }
            else
            {
                crc >>= 1;
            }
            tmp >>= 1;
        }
    }
    return ~crc;
}

int main(int argc, char *argv[])
{
    FILE *fd = 0;
    uint32_t file_size;
    char* buffer = 0;
    uint32_t crc32_val = 0;

    fd = fopen(argv[1], "rb");
    if(fd == 0) return -1;
    fseek(fd, 0, SEEK_END);
    file_size = ftell(fd);
    fseek(fd, 0, SEEK_SET);
	buffer = malloc(file_size);
    if (!buffer)
    {
        fclose(fd);
        return -1;
    }
    fread(buffer, 1, file_size, fd);
    fclose(fd);
    if(strstr(argv[1], "app1.bin") != NULL)
    {
        ///修改文件实际长度
        buffer[APP_SIZE_POS + 0] = (char)file_size;
        buffer[APP_SIZE_POS + 1] = (char)(file_size >> 8);
        buffer[APP_SIZE_POS + 2] = (char)(file_size >> 16);
        buffer[APP_SIZE_POS + 3] = (char)(file_size >> 24);

        ///计算输入的文件校验值
        memset(&buffer[CHECKSUM_POS + 0], 0, 8);
        crc32_val = crc32_update_revbit(crc32_val, buffer, file_size);
        buffer[CHECKSUM_POS + 0] = (char)crc32_val;
        buffer[CHECKSUM_POS + 1] = (char)(crc32_val >> 8);
        buffer[CHECKSUM_POS + 2] = (char)(crc32_val >> 16);
        buffer[CHECKSUM_POS + 3] = (char)(crc32_val >> 24);
        printf("the binary file:%s; file_size:%d bytes; file_checksum:0x%.2x;\n", argv[1], file_size, crc32_val);
    }

	// 计算APP头校验
    crc32_val = crc32_update_revbit(CRC32_START_VALUE, buffer, VERIFYTAG_POS);
    buffer[VERIFYTAG_POS + 0] = (char)crc32_val;
    buffer[VERIFYTAG_POS + 1] = (char)(crc32_val >> 8);
    buffer[VERIFYTAG_POS + 2] = (char)(crc32_val >> 16);
    buffer[VERIFYTAG_POS + 3] = (char)(crc32_val >> 24);
    printf("the binary file:%s; file_size:%d bytes; APP_header_checksum:0x%.2x;\n", argv[1], file_size, crc32_val);

    /// 保存文件
    fd = fopen(argv[1], "wb");
    fwrite(buffer, file_size, 1, fd);

    free(buffer);
    fclose(fd);
    return 0;
}