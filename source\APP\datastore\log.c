/********************************************************************************
  * @file    log.c
  * <AUTHOR> @date    2024
  * @brief   事件，曲线，冻结存储  
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

#include "log.h"
#include "string.h"
#include "crc.h"

#define MLOG_CRC16            0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(MLOG_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(MLOG_CRC16, struct, len)

/// @brief 从EEPROM中读取记录头信息, 并校验CRC16
/// @param addr 
/// @param hdr 
static void log_head_load(log_addr_s addr, log_head_s* hdr)
{
    nvm.read(addr.hdr, hdr, sizeof(log_head_s));
    if(CRC16_CHK(hdr, sizeof(log_head_s)) == false)
    {
        memset(hdr, 0, sizeof(log_head_s));
    }
}

/// @brief 写入记录头信息, 并计算CRC16
/// @param addr 
/// @param hdr 
static void log_head_save(log_addr_s addr, log_head_s* hdr)
{
    CRC16_CAL(hdr, sizeof(log_head_s));
    nvm.write(addr.hdr, hdr, sizeof(log_head_s));
}

/// @brief 滚动添加记录函数, 累加记录计数器, 当前记录指针指向最近一条
/// @param addr    - 记录信息头地址
/// @param content - 记录内容
/// @param len     - 内容长度
/// @return 0-失败  1-成功
bool log_roll_add(log_addr_s addr, const void* content, uint16_t len)
{
    log_head_s hdr;
    uint8_t    cmpData[32];
    uint16_t   cmplen = (len > 32) ? 32 : len;

    if(addr.size == 0 || addr.num == 0 || addr.max == 0 || len == 0) return false;
    //nvm.read(addr.hdr, &hdr, sizeof(log_head_s));       // 读出记录头信息
    log_head_load(addr, &hdr);
    if(hdr.num > addr.num) {hdr.num = 0; hdr.ptr = 0;}
    if(hdr.ptr >= addr.max) hdr.ptr = 0;

    if(nvm.write(addr.ofst + (uint32_t)hdr.ptr * addr.size, content, len) == false) return false;  // 写入记录内容
    if(nvm.read(addr.ofst  + (uint32_t)hdr.ptr * addr.size, cmpData, cmplen) == false) return false;
    if(memcmp(content, cmpData, cmplen)) return false;   // 防止数据写错问题

    hdr.ptr++; // 滚动记录指针
    if(hdr.num < addr.num) hdr.num++; // 增加有效点
    hdr.cnt++; // 事件计数器累加
    // nvm.write(addr.hdr, &hdr, sizeof(log_head_s)); // 写入记录指针信息
    log_head_save(addr, &hdr); // 保存记录头信息
    return true;
}

/// @brief 在最近一条记录中添加内容, 如之前有过开始记录, 现在添加结束记录
///        注意：如果如果事件存储在FLASH中，先读取整条记录再插入数据后写入！！！！！
/// @param addr    - 记录信息头地址
/// @param content - 记录内容
/// @param offset  - 追加内容在记录中的偏移位置
/// @param len     - 追加内容长度
/// @return 
bool log_append(log_addr_s addr, const void* content, uint32_t offset, uint16_t len)
{
    log_head_s hdr;
    uint8_t   tmp_buf[32];
    uint16_t  cmplen = (len > 32) ? 32 : len;

    if(addr.size == 0 || addr.num == 0 || addr.max == 0 || len == 0) return false;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录信息
    log_head_load(addr, &hdr);
    if(hdr.num == 0) return false;

    hdr.ptr = (hdr.ptr + addr.max - 1) % addr.max; // 最近一条记录指针
    if(nvm.write(addr.ofst + (uint32_t)hdr.ptr * addr.size + offset, content, len) == false) return false;// 写入记录内容    
    if(nvm.read(addr.ofst  + (uint32_t)hdr.ptr * addr.size + offset, tmp_buf, cmplen) == false) return false;
    if(memcmp(content, tmp_buf, cmplen)) return false;   // 防止数据写错问题

    return true;
}

/// @brief 以当前记录指针位置往前擦除若干条记录内容
/// @param addr - 记录信息头地址
/// @param num  - 擦除条数
/// @return 
bool log_earse(log_addr_s addr, uint16_t num)
{
    log_head_s hdr;

    if(addr.size == 0 || addr.num == 0 || addr.max == 0) return false;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录信息
    log_head_load(addr, &hdr);
    if(hdr.num > num)
    {
        if(hdr.ptr >= num)
        {
            hdr.ptr = hdr.ptr - num;
        }
        else
        {
            hdr.ptr = hdr.ptr + addr.max - num;
        }
        hdr.num -= num;
    }
    else
    {
        hdr.ptr = 0;
        hdr.num = 0;
    }
    // nvm.write(addr.hdr, &hdr, sizeof(log_head_s)); // 写入记录指针信息
    log_head_save(addr, &hdr);
    return true;
}

/// @brief 预擦除点, 用于提前删除有效点的个数, 以便在补点时读取曲线前能先删除即将的补点中会覆盖的点
/// @param addr - 记录信息头地址
/// @param num  - 预擦除点的个数
/// @return 
bool log_pre_erase(log_addr_s addr, uint16_t num)
{
    log_head_s hdr;

    if(addr.size == 0 || addr.num == 0 || addr.max == 0) return false;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录指针信息
    log_head_load(addr, &hdr);
    if(num > addr.num - hdr.num)
    {
        hdr.num -= num - (addr.num - hdr.num);
    }
    else return false;

    // nvm.write(addr.hdr, &hdr, sizeof(log_head_s)); // 写入记录指针信息
    log_head_save(addr, &hdr);
    return true;
}

/// @brief 快速清空记录内容, 仅清空记录头信息, 不清空记录内容
/// @param addr - 记录信息头地址
void log_empty(log_addr_s addr)
{
    log_head_s hdr;
    hdr.cnt  = 0;
    hdr.ptr  = 0;
    hdr.num  = 0;
    hdr.time = 0;
    // nvm.write(addr.hdr, &hdr, sizeof(log_head_s));
    log_head_save(addr, &hdr);
}

/// @brief 根据输入的指定点(指针有效模式)，从记录块中提取单条的数据
/// @param addr    - 记录信息头地址
/// @param content - 待存放记录内容的地址
/// @param len     - 提取内容长度
/// @param point   - 提取最近第N点数据, 0表示读取最早一条, 1表示读取最近一条. 2表示读取最近第二条，以此类推
/// @return    返回记录指针位置, 0xFFFF表示未曾有记录
uint16_t log_fetch(log_addr_s addr, void* content, uint16_t len, uint16_t point)
{
    log_head_s hdr;
    if(addr.size == 0 || addr.num == 0 || addr.max == 0) return 0xFFFF;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录头信息
    log_head_load(addr, &hdr);
    if(hdr.num == 0 || point > hdr.num || hdr.num > addr.num) return 0xFFFF;    // 记录为空 || 超出范围
    if(point == 0)
    {
        hdr.ptr = (hdr.ptr + addr.max - hdr.num) % addr.max; // 当point=0时，将读取最早一条
    }
    else
    {
        hdr.ptr = (hdr.ptr + addr.max - point) % addr.max;  // 当point>0时，将读取最近的第N条
    }
     nvm.read(addr.ofst + (uint32_t)hdr.ptr * addr.size, content, len); // 读取记录内容
    return hdr.ptr;
}

/// @brief 根据输入的读取索引、冻结起始指针及有效点模式下，以FIFO从记录块中提取单条的数据
/// @param addr    - 记录信息头地址
/// @param fixptr  - 起始指针
/// @param idx     - 记录索引,从起始位置开始的偏移量
/// @param content - 待存放记录内容的地址 
/// @param len     - 提取内容长度
/// @return    返回记录长度
uint16_t log_fifo(log_addr_s addr, uint16_t fixptr, uint16_t idx, void* content, uint16_t len)
{
    if(addr.size == 0 || addr.num == 0 || addr.max == 0) return 0;
    
    idx = (fixptr + idx - 1) % addr.max;
    
    nvm.read(addr.ofst + (uint32_t)idx * addr.size, content, len); // 读取记录内容
    return len;
}

/// @brief 获取最早的记录指针位置
/// @param addr - 记录信息头地址
/// @return 最早记录指针位置
uint16_t log_first_pointer_get(log_addr_s addr)
{
    log_head_s hdr;
    if(addr.num == 0 || addr.max == 0) return 0;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录信息头
    log_head_load(addr, &hdr);
    hdr.ptr = (hdr.ptr + addr.max - hdr.num) % addr.max;
    return hdr.ptr;
}

/// @brief 获取记录栈内有效记录条数, 可以用于判定记录块已满或末满
/// @param addr - 记录信息头地址
/// @return   有效记录条数
uint16_t log_entry_inuse_get(log_addr_s addr)
{
    uint16_t num = 0;
    nvm.read(addr.hdr + member_offset(log_head_s, num), &num, 2); // 读出缓冲有效点
    if(num > addr.num) { log_empty(addr);num = 0;}
    return num;
}

/// @brief      删除记录栈内有效记录条数, 不改变存储的指针位置,相当于删除最早的记录
/// @param addr - 记录信息头地址
/// @param num  - 待删除的有效点个数
/// @return 
bool log_entry_inuse_del(log_addr_s addr, uint16_t num)
{
    log_head_s hdr;

    if(addr.size == 0 || addr.num == 0 || addr.max == 0) return false;
    // nvm.read(addr.hdr, &hdr, sizeof(log_head_s)); // 读出记录指针信息
    log_head_load(addr, &hdr);
    if(num > hdr.num) { hdr.num = 0; }
    else hdr.num -= num;

    // nvm.write(addr.hdr, &hdr, sizeof(log_head_s)); // 写入记录指针信息
    log_head_save(addr, &hdr);
    return true;
}

/// @brief 获取记录的总次数
/// @param addr - 记录信息头地址
/// @return 记录总数
uint32_t log_entries_cnt_get(log_addr_s addr)
{
    uint32_t cnt = 0;
    addr.hdr += member_offset(log_head_s, cnt);
    nvm.read(addr.hdr, &cnt, 4); // 读出记录总数
    return cnt;
}

/// @brief 接口结构体
const struct log_s mlog =
{
    .roll_add           = log_roll_add,
    .append             = log_append,
    .erase              = log_earse,
    .pre_earse          = log_pre_erase,
    .empty              = log_empty,
    .fetch              = log_fetch,
    .FIFO               = log_fifo,
    .first_pointer_get  = log_first_pointer_get,
    .entry_inuse_get    = log_entry_inuse_get,
    .entries_cnt_get    = log_entries_cnt_get,
    .entry_inuse_del    = log_entry_inuse_del,
};

// end of file
