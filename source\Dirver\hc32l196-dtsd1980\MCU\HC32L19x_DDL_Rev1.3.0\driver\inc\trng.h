/******************************************************************************
 * Copyright (C) 2021, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************/

/******************************************************************************
 * @file   trng.h
 *
 * @brief  Header file for TRNG functions
 *
 * <AUTHOR> Team 
 *
 ******************************************************************************/

#ifndef __TRNG_H__
#define __TRNG_H__

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"


#ifdef __cplusplus
extern "C"
{
#endif

/**
 ******************************************************************************
 ** \defgroup TrngGroup (TRNG)
  **
 ******************************************************************************/
//@{

/**
 *******************************************************************************
 ** function prototypes.
 ******************************************************************************/

/*******************************************************************************
 * Global definitions
 ******************************************************************************/

/******************************************************************************
 * Global variable declarations ('extern', definition in C source)
 ******************************************************************************/

/******************************************************************************
 * Global function prototypes (definition in C source)
 ******************************************************************************/
///< 随机数初始化(上电第一次生成随机数）
en_result_t Trng_Init(void);
///< 生成随机数（非上电第一次生成随机数）
en_result_t Trng_Generate(void);
///< 获取64bits随机数（执行随机数初始化或生成随机数函数后可使用该函数获取随机数值）
uint32_t Trng_GetData0(void);
uint32_t Trng_GetData1(void);

//@} // Trng Group

#ifdef __cplusplus
#endif

#endif /* __TRNG_H__ */
/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/


