/********************************************************************************
  * @file    demand.h
  * <AUTHOR> @date    2024
  * @brief    
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/

#ifndef _DEMAND_H_
#define _DEMAND_H_
#include "typedef.h"
#include "app_config.h"
#include "timeapp.h"

typedef struct
{
    uint16_t crc;
    uint16_t chk;
    uint16_t period;            // 需量周期, 分钟
    uint16_t slip_period;       // 滑差周期, 分钟
} demand_para_s;

/* 计算需量所需电能保存结构体 */
typedef struct
{
    int32_t value[DEMAND_MAX_SLIP_NUM]; // 滑差数据缓冲
} demand_period_s;

typedef struct
{
    uint8_t dd_start     : 1;  // 允许计算需量标记位
    uint8_t dd_all_ready : 1;  // 所有滑差块电能值准备好了
    uint8_t dd_next_slip : 1;  // 滑差移动
    uint8_t dd_capture   : 1;  // 需量捕获标记位
    uint8_t dd_reset     : 1;  // 清除需量
    uint8_t md_capture_f : 1;  // 开始捕获最大需量
    uint8_t md_capture   : 1;  // 捕获最大需量
    uint8_t md_reset     : 1;  // 清除最大需量
} demand_flag_s;

/* 需量寄存器 */
typedef struct
{
    int32_t cur_avg_val[1 + DEMAND_TARIFF_RATE_NUM];    // 当前平均值 Unit: W
    int32_t lst_avg_val[1 + DEMAND_TARIFF_RATE_NUM];    // 上个周期的平均值 Unit: W
    clock_s capture_time;                               // 上个周期结束
    clock_s start_time;                                 // 当前周期开始时间
    uint8_t slip_idx;                                   // 滑差索引
    demand_flag_s   stus;                               // 状态
    demand_period_s period[1 + DEMAND_TARIFF_RATE_NUM]; // 滑差周期数据缓冲
} demand_block_s;

/* 最大需量结构体 */
typedef struct
{
    int32_t value;         // 最大需量值 Unit: W
    clock_s capture_time;  // 最大需量发生时间
} MD_reg_s;

/* 最大需量寄存器 */
typedef struct
{
    uint16_t crc;
    uint16_t chk;
    MD_reg_s reg[1 + DEMAND_TARIFF_RATE_NUM];
} MD_s;

typedef struct
{
    MD_s md[DEMAND_TYPE_NUM];
} MD_block_s;


/* Exported defines ----------------------------------------------------------*/
typedef uint16_t MD_STUS;
#define STUS_MD_RESET             ((MD_STUS)1 << 1 )  // 最大需量复位
#define STUS_MD_PRG_PERIOD        ((MD_STUS)1 << 2 )  // 更改需量周期
#define STUS_MD_PRG_PERIOD_NUM    ((MD_STUS)1 << 3 )  // 更改需量周期数

///< @brief 需量计算模块接口
struct demand_s
{
    void (*reset)(uint8_t type);
    bool (*state_query)(MD_STUS state);
    void (*state_clr)(void);
    const demand_para_s* (*para_get)(void);
    bool (*para_set)(uint16_t ofst, const void* val);
    demand_block_s* (*blk_value_get)(demand_type_t type);
    void (*blk_value_clr)(demand_type_t type);
    MD_reg_s (*max_value_get)(demand_type_t type, uint8_t rate);
    void (*max_value_clr)(void);
};
extern const struct demand_s demand;


#endif /* _DEMAND_H_ */
