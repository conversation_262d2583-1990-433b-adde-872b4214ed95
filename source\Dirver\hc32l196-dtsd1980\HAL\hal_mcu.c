/**
 ******************************************************************************
 * @file    hal_mcu.c
 * <AUTHOR> @date    2024
 * @brief   本模块主要包括MCU的内核、时钟总线、复位源、电源功耗管理等芯片级驱动。
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_mcu.h"
#include "hal_gpio.h"
#include "hal_def.h"
#include "hal_timer.h"
#include "debug.h"

/* Private typedef -----------------------------------------------------------*/
/* @brief 供电检测模式定义 */
typedef enum
{
    PWR_ON,     // 上电电压检测
    PWR_OFF,    // 掉电电压检测
} PVD_MODE;

/* Private define ------------------------------------------------------------*/
#define TEST_POR_TIMES 180    // 检测系统上电电压次数
#define TEST_PDR_TIMES 100    // 检测系统下电电压次数

/* Private macro -------------------------------------------------------------*/
#define HRC_ADJ ((*(uint32_t *)0x00040140) & 0x3F)    /// HRC载入校准值

/* Private variables ---------------------------------------------------------*/
static McuCoreStus_s mcu_stus;
__weak uint32_t      SystemCoreClock = WORK_RC_HZ;    // 系统复位后默认时钟
#if USE_SAG_TYPE != SAG_NULL
static bool (*is_sag)(void);
#endif
static uint8_t InstructionsPerUs = WORK_RC_HZ / 2000000;
static uint8_t lvd_intrpt        = FALSE;

/* Private functions ---------------------------------------------------------*/
void hal_mcu_wait_us(uint16_t nUs);

// PMU中断处理函数
// void PMU_IRQHandler(void)
void irq_handler_lvd(void)
{
    if(M0P_LVD->IFR_f.INTF)
    {
        lvd_intrpt = boolof(M0P_LVD->IFR_f.FILTER);    // 检测比较输出电平（可用于判断触发类型）
    }
    M0P_LVD->IFR_f.INTF = 0u;    // 清除所有中断标志位
}

/**
 * @brief  配置供电检测模式
 * @retval mode=PWR_ON 表示检测上电
 */
static void mcu_lvd_mode_set(PVD_MODE mode)
{
    hal_gpio.monitor(0);
    if(!(M0P_SYSCTRL->PERI_CLKEN0_f.VC)) { M0P_SYSCTRL->PERI_CLKEN0_f.VC = TRUE; }    // 使能VC,LVD时钟
    if(mode == PWR_ON)
    {
        // M0P_LVD->CR = 0;
        M0P_LVD->CR         = (uint32_t)LvdActMskInt | LvdInputSrcMskPC13 | LvdMskTH2_2V | LvdFilterMskEnable | LvdFilterMsk1_8ms | LvdIrqMskRiseFall;    // 上电检测2.0V

        M0P_LVD->CR_f.LVDEN = 1;    
        hal_mcu_wait_us(2000);//LVD开启后到输出稳定建立大约需要100us时间                                                                                                                     // 使能LVD检测
    }
    else
    {
        if(M0P_LVD->CR != ((uint32_t)LvdActMskInt | LvdInputSrcMskPC13 | LvdMskTH2_0V | LvdFilterMskEnable | LvdFilterMsk1_8ms | LvdIrqMskRiseFall | 0x01))    // 掉电检测1.8V
        {
            M0P_LVD->CR = (uint32_t)LvdActMskInt | LvdInputSrcMskPC13 | LvdMskTH2_0V | LvdFilterMskEnable | LvdFilterMsk1_8ms | LvdIrqMskRiseFall;
            M0P_LVD->CR_f.LVDEN = 1; 

            hal_mcu_wait_us(2000);//LVD开启后到输出稳定建立大约需要100us时间
        }
    }
     
}

/**
 * @brief  获取LVD检测状态
 * @retval 0-供电正常
 * @retval 1-供电异常
 */
static bool mcu_lvd_state_get(PVD_MODE mode)
{
    // return false;    // 未启用LVD检测，直接返回false
    return boolof(M0P_LVD->IFR_f.FILTER);
}

/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */

/**
 * @brief  MCU指令等待若干uS时间.
 * @param  [in]  nUs-等待时长, 单位:微秒
 * @note   该等待采用的是指令摸拟, 并不精确, 适用于硬件驱动不需要严格的时序等待
 */
void hal_mcu_wait_us(uint16_t nUs)
{
    int32_t clkcnt = nUs * InstructionsPerUs - 8;
    while(1)
    {
        clkcnt -= 2;    // hc32l196 24Mh 时 2 比较准，设为3大概0.7us
        if(clkcnt < 0) break;
    }
}

/**
 * @brief  上电确认检测
 * @param  none
 * @retval TRUE-power on  FALSE-not power on
 */
bool hal_mcu_pwron_query(void)
{
    mcu_lvd_mode_set(PWR_ON);
    for(int i = TEST_POR_TIMES; i > 0; i--)
    {
        /**为了防止电源抖动，如果检测到上电，先进行1ms间隔的连续检测 */
        if(mcu_lvd_state_get(PWR_ON)) return false;
        HAL_WDG_RESET();
        hal_mcu_wait_us(1000);
    }
    return true;
}

/**
 * @brief  掉电确认检测
 * @param  none
 * @retval TRUE-power down  FALSE-not power down
 */
bool hal_mcu_pwrdn_query(void)
{
    if(mcu_stus.rly_act) return false;

    mcu_lvd_mode_set(PWR_OFF);
    for(int i = TEST_PDR_TIMES; i > 0; i--)
    {
#if USE_SAG_TYPE == SAG_EMU
        if(is_sag != NULL && (is_sag)()) return true;
#elif USE_SAG_TYPE == SAG_PIN
        if(is_sag != NULL && (is_sag)()) continue;
#endif
        if(!mcu_lvd_state_get(PWR_OFF))
        {
            if(lvd_intrpt) { HAL_CRITICAL_STATEMENT(lvd_intrpt = FALSE;); }
            return false;
        }
    }
    return true;
}

bool hal_lvd_intrpt_get(void)
{
    return boolof(lvd_intrpt);
}

void hal_dwt_enable(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.WDT = TRUE;        // 使能DWT时钟
    M0P_WDT->CON_f.WOV             = WdtT6s55;    // 设置看门狗溢出时间周期为6s55ms
    M0P_WDT->CON_f.WINT_EN         = 0;           // 看门狗复位模式

    M0P_WDT->RST = 0x1E;    // 启动看门狗
    M0P_WDT->RST = 0xE1;
}

void hal_dwt_reset(void)
{
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

void hal_wdt_close(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.WDT = FALSE;    // 关闭DWT时钟
}

/// @brief 系统时钟切换
/// @param mode
/// @return 0-外部晶振异常
uint8_t hal_sysclk_set(MCU_CLOCK_MODE mode)
{
    // 使能外部32768Hz晶振
    Sysctrl_XTLDriverCfg(SysctrlXtlAmp2, SysctrlXtalDriver2);
    Sysctrl_SetXTLStableTime(SysctrlXtlStableCycle16384);
    Sysctrl_ClkSourceEnable(SysctrlClkXTL, TRUE);
    Sysctrl_ClkSourceEnable(SysctrlClkXTH, false);    // 禁止外部高速晶振
    Sysctrl_ClkSourceEnable(SysctrlClkPLL, false);    // 禁止PLL时钟
    switch(mode)
    {
        case WORK_LC_32768:
            /**********  配置CMU 模块  **********/
            ///< 切换时钟前（根据外部低速晶振）设置XTL晶振参数，使能目标时钟，SYSTEM_XTL = 32768Hz
            // Sysctrl_XTLDriverCfg(SysctrlXtlAmp2, SysctrlXtalDriver2);
            // Sysctrl_SetXTLStableTime(SysctrlXtlStableCycle16384);
            // Sysctrl_ClkSourceEnable(SysctrlClkXTL, TRUE);
            Sysctrl_SysClkSwitch(SysctrlClkXTL);

            Flash_WaitCycle(FlashWaitCycle0);

            SystemCoreClock   = WORK_LC_32768;
            InstructionsPerUs = 0;
            break;

        case WORK_RC_HZ:
            ///< RCH时钟不同频率的切换，需要先将时钟切换到RCL
            Sysctrl_SetRCLTrim(SysctrlRclFreq32768);
            Sysctrl_SetRCLStableTime(SysctrlRclStableCycle64);
            Sysctrl_ClkSourceEnable(SysctrlClkRCL, TRUE);
            Sysctrl_SysClkSwitch(SysctrlClkRCL);

            ///< 加载目标频率的RCH的TRIM值
            Sysctrl_SetRCHTrim(SysctrlRchFreq8MHz);    // SysctrlRchFreq8MHz
            ///< 使能RCH
            Sysctrl_ClkSourceEnable(SysctrlClkRCH, TRUE);
            ///< 时钟切换到RCH
            Sysctrl_SysClkSwitch(SysctrlClkRCH);

            ///< HCLK不超过24M：此处设置FLASH读等待周期为0 cycle
            Flash_WaitCycle(FlashWaitCycle0);

            hal_mcu_wait_us(10000);                           // 等待RCH稳定
            Sysctrl_ClkSourceEnable(SysctrlClkRCL, false);    // 低功耗关闭RCL时钟

            SystemCoreClock   = WORK_RC_HZ;
            InstructionsPerUs = WORK_RC_HZ / 2000000;
            break;

        case WORK_RUN_HZ:
            ///< RCH时钟不同频率的切换，需要先将时钟切换到RCL
            Sysctrl_SetRCLTrim(SysctrlRclFreq32768);
            Sysctrl_SetRCLStableTime(SysctrlRclStableCycle64);
            Sysctrl_ClkSourceEnable(SysctrlClkRCL, TRUE);
            Sysctrl_SysClkSwitch(SysctrlClkRCL);

            ///< 加载目标频率的RCH的TRIM值
            Sysctrl_SetRCHTrim(SysctrlRchFreq24MHz);
            ///< 使能RCH
            Sysctrl_ClkSourceEnable(SysctrlClkRCH, TRUE);
            ///< 时钟切换到RCH
            Sysctrl_SysClkSwitch(SysctrlClkRCH);

            ///< HCLK不超过24M：此处设置FLASH读等待周期为0 cycle
            Flash_WaitCycle(FlashWaitCycle0);

            SystemCoreClock   = WORK_RUN_HZ;
            InstructionsPerUs = WORK_RUN_HZ / 2000000;
            break;
    }
    return 1;
}

/**
 * @brief  MCU复位后运行模式前的MCU初始化.
 * @param  none
 * @retval MCU core 初始化状态, 参考tMcuCoreStus定义
 */
McuCoreStus_s hal_mcu_core_init(void)
{
    /* 初始化RCMF */
    hal_dwt_enable();

    mcu_stus.power_on_rst = hal_mcu_pwron_query();
    if(mcu_stus.power_on_rst)
    {
        {
            /// 获取复位源信息
            mcu_stus.por_bor_rst = boolof(M0P_RESET->RESET_FLAG_f.POR5V || M0P_RESET->RESET_FLAG_f.POR15V || M0P_RESET->RESET_FLAG_f.LVD);    /// 判断是否冷启动

            M0P_RESET->RESET_FLAG_f.POR5V  = 0;
            M0P_RESET->RESET_FLAG_f.POR15V = 0;
            M0P_RESET->RESET_FLAG_f.LVD    = 0;

            mcu_stus.abnormal_rst       = boolof(M0P_RESET->RESET_FLAG_f.WDT && (!mcu_stus.por_bor_rst));    /// 查询是否看门狗复位
            M0P_RESET->RESET_FLAG_f.WDT = 0;
        }
        hal_dwt_reset();

        /* Configurate system clock */
        if(hal_sysclk_set(WORK_RUN_HZ)) { mcu_stus.clk_src_extosc = 0; }
        else { mcu_stus.clk_src_extosc = 1; }

        M0P_SYSCTRL->PERI_CLKEN0_f.FLASH   = true;     // FLASH时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.DMA     = false;    // DMA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.GPIO    = true;     // GPIO时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.AES     = false;    // CRC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.CRC     = false;    // CRC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SWD     = true;     // SWD时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TICK    = true;     // TICK时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LCD     = false;    // LCD时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TRIM    = true;     // TRIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.RTC     = true;     // RTC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.PCNT    = false;    // PCNT时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.RNG     = false;    // RNG时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.VC      = true;     // VC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.ADC     = false;    // ADC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.WDT     = true;     // WDT时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.PCA     = false;    // PCA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.OPA     = false;    // OPA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TIM3    = false;    // TIM3时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.ADVTIM  = false;    // ADVTIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPTIM0  = false;    // LPTIM0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.BASETIM = false;    // BASETIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SPI1    = true;     // SPI1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SPI0    = true;     // SPI0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.I2C1    = false;    // I2C1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.I2C0    = false;    // I2C0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPUART1 = false;    // LPUART1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPUART0 = false;    // LPUART0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.UART1   = true;     // UART1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.UART0   = true;     // UART0时钟

        M0P_SYSCTRL->PERI_CLKEN1_f.UART3  = true;     // UART3时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.UART2  = true;     // UART2时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.LPTIM1 = false;    // LPTIM1时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.DAC    = false;    // DAC时钟
    }
    else
    {
        /* 低功耗运行模式前的MCU初始化 */
        hal_sysclk_set(WORK_RC_HZ);    // 默认使用RC时钟

        M0P_SYSCTRL->PERI_CLKEN0_f.FLASH   = true;     // FLASH时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.DMA     = false;    // DMA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.GPIO    = true;     // GPIO时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.AES     = false;    // CRC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.CRC     = false;    // CRC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SWD     = true;     // SWD时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TICK    = false;    // TICK时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LCD     = false;    // LCD时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TRIM    = false;    // TRIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.RTC     = true;     // RTC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.PCNT    = false;    // PCNT时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.RNG     = false;    // RNG时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.VC      = true;     // VC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.ADC     = false;    // ADC时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.WDT     = true;     // WDT时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.PCA     = false;    // PCA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.OPA     = false;    // OPA时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.TIM3    = false;    // TIM3时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.ADVTIM  = false;    // ADVTIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPTIM0  = false;    // LPTIM0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.BASETIM = false;    // BASETIM时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SPI1    = false;    // SPI1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.SPI0    = false;    // SPI0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.I2C1    = false;    // I2C1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.I2C0    = false;    // I2C0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPUART1 = false;    // LPUART1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.LPUART0 = false;    // LPUART0时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.UART1   = false;    // UART1时钟
        M0P_SYSCTRL->PERI_CLKEN0_f.UART0   = false;    // UART0时钟

        M0P_SYSCTRL->PERI_CLKEN1_f.UART3  = false;    // UART3时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.UART2  = false;    // UART2时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.LPTIM1 = false;    // LPTIM1时钟
        M0P_SYSCTRL->PERI_CLKEN1_f.DAC    = false;    // DAC时钟

        // 打开
    }

    /// 配置LVD检测
    M0P_LVD->CR = 0;
    M0P_LVD->CR = (uint32_t)LvdActMskInt | LvdInputSrcMskPC13 | LvdMskTH1_8V | LvdFilterMskEnable | LvdFilterMsk7_2ms | LvdIrqMskRiseFall;
    /// 使能PMU中断  LVD0
    M0P_LVD->CR_f.IE    = TRUE;
    M0P_LVD->IFR_f.INTF = 0;    // 清除中断标志位

    irq_vector_set(INT_LVD, irq_handler_lvd);    // 设置LVD中断处理函数
    EnableNvic(LVD_IRQn, 3, TRUE);               // 使能LVD中断
    M0P_LVD->CR_f.LVDEN = 1;                     // 使能LVD检测

    /* Configurate watchdog */
    hal_dwt_enable();

    return mcu_stus;
}

/**
 * @brief  MCU睡眠模式设置
 * @param  none
 * @retval none
 */
void hal_mcu_sleep(void)
{
    HAL_ENABLE_INTERRUPTS();    /// 睡眠前必须始终打开中断!!!

    /// 进入deepsleep模式，关闭基准电压源
    /// MCU唤醒后会自动切换到HRC，频率默认为8M
    // HAL_SAFETY_WR(HT_PMU->PMUCON = (PMU_PMUCON_LVD0DETEN);    // 使能LVD检测，检测电压为1.21V,关闭BOR检测，关闭大功耗LDO
    // );
    SCB->SCR &= ~SCB_SCR_SLEEPDEEP_Msk;
    SCB->SCR &= ~SCB_SCR_SLEEPONEXIT_Msk;
    __WFI();

    /// 开启BOR
    // HAL_SAFETY_WR(HT_PMU->PMUCON = (PMU_PMUCON_LVD0DETEN | PMU_PMUCON_BORDETEN | PMU_PMUCON_BORRST);    // 使能LVD检测，检测电压为1.21V,使能BOR检测且开启BOR复位，保持LDO状态
    // );

    HAL_WDG_RESET();    /// 唤醒后清看门狗

    /* 唤醒后检查上电运行情况 */
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();
}

void hal_is_sag_callback(bool func(void))
{
#if USE_SAG_TYPE != SAG_NULL
    is_sag = func;
#endif
}

/// @brief 声明hal_uart子模块对象
const struct hal_mcu_t hal_mcu = {
    .stus            = &mcu_stus,
    .init            = hal_mcu_core_init,
    .sleep           = hal_mcu_sleep,
    .wait_us         = hal_mcu_wait_us,
    .pwrdn_query     = hal_mcu_pwrdn_query,
    .pwron_query     = hal_mcu_pwron_query,
    .sysclk_set      = hal_sysclk_set,
    .wdg_clr         = hal_dwt_reset,
    .is_sag_callback = hal_is_sag_callback,
    .lvd_intrpt_get  = hal_lvd_intrpt_get,
};

/** @} */
/** @} */
/** @} */
