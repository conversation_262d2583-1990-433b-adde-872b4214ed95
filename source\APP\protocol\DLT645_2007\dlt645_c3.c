/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:  dlt645_c3.c
*    Describe:  DLT645-2007协议，03类数据部分     
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "mic.h"
#include "status.h"
#include "DLT645_2007_id.h"
#include "profile_capture_obj.h"
#include "profile.h"
#include "power_event.h"

/// @brief  读取数据处理
/// @param  p_info 
/// @return 
static uint16_t dlt_645_read_3(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t   *p_data= p_info->snd_dat;
    uint32_t   count;
    uint16_t   item  = ITEM(p_info->id);
    uint16_t   len;
    evt_type_t et    = EVENT_TYPE_NUM;
    uint8_t    point = (uint8_t)p_info->id;
    uint8_t    ph    = (uint8_t)(p_info->id>>8);
    memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    switch(item)
    {
        case ITEM(C3_LOSS_VOL_NUM             ): // @0x03010000                    /// 失压总次数
            break;
        case ITEM(C3_MISS_VOL_NUM             ): // @0x03040000                    /// 断相总次数
            break;
        case ITEM(C3_LOW_VOL_NUM              ): // @0x03020000                    /// 欠压总次数
            break;
        case ITEM(C3_OVR_VOL_NUM              ): // @0x03030000                    /// 过压总次数
            break;
    #if EVENT_LOSS_VOL_EN
        case ITEM(C3_A_LOSS_VOL_RECORD(1)     ): // @(0x03010100 | LAST_INDEX(N))  /// 上N次A相失压记录N 1-10
        case ITEM(C3_B_LOSS_VOL_RECORD(1)     ): // @(0x03010200 | LAST_INDEX(N))  /// 上N次B相失压记录N 1-10
        case ITEM(C3_C_LOSS_VOL_RECORD(1)     ): // @(0x03010300 | LAST_INDEX(N))  /// 上N次C相失压记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_LOSS_VOL_A + ph -1);
            break;
    #endif 
    #if EVENT_LOW_VOL_EN 
        case ITEM(C3_A_LOW_VOL_RECORD(1)      ): // @(0x03020100 | LAST_INDEX(N))  /// 上N次A相欠压记录N 1-10
        case ITEM(C3_B_LOW_VOL_RECORD(1)      ): // @(0x03020200 | LAST_INDEX(N))  /// 上N次B相欠压记录N 1-10
        case ITEM(C3_C_LOW_VOL_RECORD(1)      ): // @(0x03020300 | LAST_INDEX(N))  /// 上N次C相欠压记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_LOW_VOL_A + ph -1);
            break;
    #endif
    #if EVENT_OVR_VOL_EN 
        case ITEM(C3_A_OVR_VOL_RECORD(1)      ): // @(0x03030100 | LAST_INDEX(N))  /// 上N次A相过压记录N 1-10
        case ITEM(C3_B_OVR_VOL_RECORD(1)      ): // @(0x03030200 | LAST_INDEX(N))  /// 上N次B相过压记录N 1-10
        case ITEM(C3_C_OVR_VOL_RECORD(1)      ): // @(0x03030300 | LAST_INDEX(N))  /// 上N次C相过压记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_OVR_VOL_A + ph -1);
            break;
    #endif
    #if EVENT_MISS_VOL_EN
        case ITEM(C3_A_MISS_VOL_RECORD(1)     ): // @(0x03040100 | LAST_INDEX(N))  /// 上N次A相断相记录N 1-10
        case ITEM(C3_B_MISS_VOL_RECORD(1)     ): // @(0x03040200 | LAST_INDEX(N))  /// 上N次B相断相记录N 1-10
        case ITEM(C3_C_MISS_VOL_RECORD(1)     ): // @(0x03040300 | LAST_INDEX(N))  /// 上N次C相断相记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_MISS_VOL_A + ph -1);
            break;
    #endif
    #if EVENT_ALL_LOSS_VOL_EN 
        // case ITEM(C3_ALL_LOSS_VOL_NUM         ): // @0x03050000                    /// 全失压总次数
        case ITEM(C3_ALL_LOSS_VOL_RECORD(1)   ): // @(0x03050000 | LAST_INDEX(N))  /// 上N次全失压记录N 1-10
            if(point == 0)
            {
                
            }
            else if(point <= EVT7_RCD_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_ALL_LOSS_VOL);
            }
            break;
    #endif

        // case ITEM(C3_BAK_PWR_LOS_NUM          ): // @0x03060000                    ///      辅助电源失电总次数
        case ITEM(C3_BAK_PWR_LOS_RECORD(1)    ): // @(0x03060000 | LAST_INDEX(N))  /// 上N次辅助电源失电记录N 1-10
            break;
    #if EVENT_V_REV_SQR_EN
        // case ITEM(C3_V_REV_SQR_NUM            ): // @0x03070000                    ///      电压逆向序总次数
        case ITEM(C3_V_REV_SQR_RECORD(1)      ): // @(0x03070000 | LAST_INDEX(N))  /// 上N次电压逆向序记录N 1-10
            if(point == 0)
            {
                
            }
            else if(point <= EVT3_RCD_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_V_REV_SQR);
            }
            break;
    #endif
    #if EVENT_I_REV_SQR_EN
        // case ITEM(C3_I_REV_SQR_NUM            ): // @0x03080000                    ///      电流逆向序总次数
        case ITEM(C3_I_REV_SQR_RECORD(1)      ): // @(0x03080000 | LAST_INDEX(N))  /// 上N次电流逆向序记录N 1-10
            if(point == 0)
            {
                
            }
            else if(point <= EVT3_RCD_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_I_REV_SQR);
            }
            break;
    #endif
    #if EVENT_V_UNB_EN
        // case ITEM(C3_V_UNB_NUM                ): // @0x03090000                    ///      电压不平衡总次数
        case ITEM(C3_V_UNB_RECORD(1)          ): // @(0x03090000 | LAST_INDEX(N))  /// 上N次电压不平衡记录N 1-10
            if(point == 0)
            {
                
            }
            else if(point <= EVT3_RCD_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_V_UNB);
            }
            break;
    #endif
    #if EVENT_I_UNB_EN
        // case ITEM(C3_I_UNB_SQR_NUM            ): // @0x030A0000                    ///      电流不平衡总次数
        case ITEM(C3_I_UNB_SQR_RECORD(1)      ): // @(0x030A0000 | LAST_INDEX(N))  /// 上N次电流不平衡记录N 1-10
            if(point == 0)
            {
                
            }
            else if(point <= EVT3_RCD_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_I_UNB);
            }
            break;
    #endif
        case ITEM(C3_LOS_CUR_NUM              ): // @0x030B0000                    ///       失流总次数
            break;
        case ITEM(C3_OVR_CUR_NUM              ): // @0x030C0000                    ///       过流总次数
            break;
        case ITEM(C3_MISS_CUR_NUM             ): // @0x030D0000                    ///       断流总次数
            break;
#if EVENT_LOS_CUR_EN 
        case ITEM(C3_A_LOS_CUR_RECORD(1)      ): // @(0x030B0100 | LAST_INDEX(N))  /// 上N次A失流相记录N 1-10
        case ITEM(C3_B_LOS_CUR_RECORD(1)      ): // @(0x030B0200 | LAST_INDEX(N))  /// 上N次B失流相记录N 1-10
        case ITEM(C3_C_LOS_CUR_RECORD(1)      ): // @(0x030B0300 | LAST_INDEX(N))  /// 上N次C失流相记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_LOS_CUR_A + ph -1);
            break;
#endif
#if EVENT_OVR_CUR_EN 
        case ITEM(C3_A_OVR_CUR_RECORD(1)      ): // @(0x030C0100 | LAST_INDEX(N))  /// 上N次A过流相记录N 1-10
        case ITEM(C3_B_OVR_CUR_RECORD(1)      ): // @(0x030C0200 | LAST_INDEX(N))  /// 上N次B过流相记录N 1-10
        case ITEM(C3_C_OVR_CUR_RECORD(1)      ): // @(0x030C0300 | LAST_INDEX(N))  /// 上N次C过流相记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_OVR_CUR_A + ph -1);
            break;
#endif
#if EVENT_MISS_CUR_EN 
        case ITEM(C3_A_MISS_CUR_RECORD(1)     ): // @(0x030D0100 | LAST_INDEX(N))  /// 上N次A相断流相记录N 1-10
        case ITEM(C3_B_MISS_CUR_RECORD(1)     ): // @(0x030D0200 | LAST_INDEX(N))  /// 上N次B相断流相记录N 1-10
        case ITEM(C3_C_MISS_CUR_RECORD(1)     ): // @(0x030D0300 | LAST_INDEX(N))  /// 上N次C相断流相记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_MISS_CUR_A + ph -1);
            break;
#endif
        case ITEM(C3_REV_NUM                  ): // @0x030E0000                    ///         潮流反向总次数
            break;
        case ITEM(C3_OVR_LOAD_NUM             ): // @0x030F0000                    ///         过载总次数
            break;
#if EVENT_REV_EN 
        case ITEM(C3_A_REV_RECORD(1)          ): // @(0x030E0100 | LAST_INDEX(N))  /// 上N次A相潮流反向记录N 1-10
        case ITEM(C3_B_REV_RECORD(1)          ): // @(0x030E0200 | LAST_INDEX(N))  /// 上N次B相潮流反向记录N 1-10
        case ITEM(C3_C_REV_RECORD(1)          ): // @(0x030E0300 | LAST_INDEX(N))  /// 上N次C相潮流反向记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_REV_A + ph -1);
            break;
#endif
#if EVENT_OVR_LOAD_EN
        case ITEM(C3_A_OVR_LOAD_RECORD(1)     ): // @(0x030F0100 | LAST_INDEX(N))  /// 上N次A相过载记录N 1-10
        case ITEM(C3_B_OVR_LOAD_RECORD(1)     ): // @(0x030F0200 | LAST_INDEX(N))  /// 上N次B相过载记录N 1-10
        case ITEM(C3_C_OVR_LOAD_RECORD(1)     ): // @(0x030F0300 | LAST_INDEX(N))  /// 上N次C相过载记录N 1-10
            et = (evt_type_t)(EVENT_TYPE_OVR_LOAD_A + ph -1);
            break;
#endif
#if EVENT_PWR_DOWN_EN
        case ITEM(C3_PWR_DOWN_CUM_TIME        ):      /// 掉电累计时间
            count = power_event.pwrdn_data_get()->cum_pwdn_time / 60;
            uint32_to_lsbbcd(p_data, count, 4), p_data += 4;
            break;
        // case ITEM(C3_PWR_DOWN_NUM             ): // @0x03110000                    ///      掉电总次数
        case ITEM(C3_PWR_DOWN_RECORD(1)       ): // @(0x03110000 | LAST_INDEX(N))  /// 上N次掉电记录N 1-10
            if(point == 0)
            {
                count = power_event.pwrdn_data_get()->cum_pwdn_cnt;
                uint32_to_lsbbcd(p_data, count, 3), p_data += 3;
            }
            else if(point <= EVT_RCD_PWRDN_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_PWR_DOWN);
            }
            break;
#endif
        case ITEM(C3_OVR_DM_NUM               ): // @0x03120000                    ///                需量总次数
            break;
        case ITEM(C3_OVR_DM_POS_kW_RECORD(1)  ): // @(0x03120100 | LAST_INDEX(N))  /// 上N次正向   有功需量超限记录N 1-10
            break;
        case ITEM(C3_OVR_DM_NEG_kW_RECORD(1)  ): // @(0x03120200 | LAST_INDEX(N))  /// 上N次反向   有功需量超限记录N 1-10
            break;
        case ITEM(C3_OVR_DM_Q1_kvar_RECORD(1) ): // @(0x03120300 | LAST_INDEX(N))  /// 上N次第1象限无功需量超限记录N 1-10
            break;
        case ITEM(C3_OVR_DM_Q2_kvar_RECORD(1) ): // @(0x03120400 | LAST_INDEX(N))  /// 上N次第2象限无功需量超限记录N 1-10
            break;
        case ITEM(C3_OVR_DM_Q3_kvar_RECORD(1) ): // @(0x03120500 | LAST_INDEX(N))  /// 上N次第3象限无功需量超限记录N 1-10
            break;
        case ITEM(C3_OVR_DM_Q4_kvar_RECORD(1) ): // @(0x03120600 | LAST_INDEX(N))  /// 上N次第4象限无功需量超限记录N 1-10
            break;

        // case ITEM(C3_PROGRAM_NUM              ): // @0x03300000                    ///      编程总次数
        case ITEM(C3_PROGRAM_RECORD(1)        ): // @(0x03300000 | LAST_INDEX(N))  /// 上N次编程记录N 1-10
            break;
        // case ITEM(C3_METER_CLEAN_NUM          ): // @0x03300100                    ///      电表清零总次数
        case ITEM(C3_METER_CLEAN_RECORD(1)    ): // @(0x03300100 | LAST_INDEX(N))  /// 上N次电表清零记录N 1-10
            break;
        // case ITEM(C3_DEMAND_CLEAN_NUM         ): // @0x03300200                    ///      需量清零总次数
        case ITEM(C3_DEMAND_CLEAN_RECORD(1)   ): // @(0x03300200 | LAST_INDEX(N))  /// 上N次需量清零记录N 1-10
            break;
        // case ITEM(C3_EVENT_CLEAN_NUM          ): // @0x03300300                    ///      事件清零总次数
        case ITEM(C3_EVENT_CLEAN_RECORD(1)    ): // @(0x03300300 | LAST_INDEX(N))  /// 上N次事件清零记录N 1-10
            break;
        // case ITEM(C3_SHITFT_TIME_NUM          ): // @0x03300400                    ///      校时总次数
        case ITEM(C3_SHITFT_TIME_RECORD(1)    ): // @(0x03300400 | LAST_INDEX(N))  /// 上N次校时记录N 1-10
            break;
        // case ITEM(C3_SCHEDULE_NUM             ): // @0x03300500                    ///      时段表总次数
        case ITEM(C3_SCHEDULE_RECORD(1)       ): // @(0x03300500 | LAST_INDEX(N))  /// 上N次时段表记录N 1-10
            break;
        // case ITEM(C3_ZONE_TAB_NUM             ): // @0x03300600                    ///      时区表总次数
        case ITEM(C3_ZONE_TAB_RECORD(1)       ): // @(0x03300600 | LAST_INDEX(N))  /// 上N次时区表记录N 1-10
            break;
        // case ITEM(C3_WEEKENDS_PGM_NUM         ): // @0x03300700                    ///      周休日编程总次数
        case ITEM(C3_WEEKENDS_PGM_RECORD(1)   ): // @(0x03300700 | LAST_INDEX(N))  /// 上N次周休日编程记录N 1-10
            break;
        // case ITEM(C3_HOLIDAY_PGM_NUM          ): // @0x03300800                    ///      节假日表编程总次数
            break;
        case ITEM(C3_HOLIDAY_PGM_RECORD(1)    ): // @(0x03300800 | LAST_INDEX(N))  /// 上N次节假日表编程记录N 1-10
            break;
        // case ITEM(C3_COMB_kWh_PGM_NUM         ): // @0x03300900                    ///      有功组合方式编程总次数
        case ITEM(C3_COMB_kWh_PGM_RECORD(1)   ): // @(0x03300900 | LAST_INDEX(N))  /// 上N次有功组合方式编程记录N 1-10
            break;
        // case ITEM(C3_COMB1_kvarh_PGM_NUM      ): // @0x03300A00                    ///      无功组合方式1编程总次数
        case ITEM(C3_COMB1_kvarh_PGM_RECORD(1)): // @(0x03300A00 | LAST_INDEX(N))  /// 上N次无功组合方式1编程记录N 1-10
            break;
        // case ITEM(C3_COMB2_kvarh_PGM_NUM      ): // @0x03300B00                    ///      无功组合方式2编程总次数
        case ITEM(C3_COMB2_kvarh_PGM_RECORD(1)): // @(0x03300B00 | LAST_INDEX(N))  /// 上N次无功组合方式2编程记录N 1-10
            break;
        // case ITEM(C3_BL_DAY_PGM_NUM           ): // @0x03300C00                    ///      结算日编程总次数
        case ITEM(C3_BL_DAY_PGM_RECORD(1)     ): // @(0x03300C00 | LAST_INDEX(N))  /// 上N次结算日编程记录N 1-10
            break;
    #if EVENT_METER_COVER_EN 
        // case ITEM(C3_METER_COVER_NUM          ): // @0x03300B00                    ///      开表盖总次数
        case ITEM(C3_METER_COVER_RECORD(1)    ): // @(0x03300B00 | LAST_INDEX(N))  /// 上N次开表盖记录N 1-10
            if(point == 0)
            {
                count = profile.evt_cnt_get(EVENT_TYPE_METER_COVER);
                uint32_to_lsbbcd(p_data, count, 3), p_data += 3;
            }
            else if(point <= EVT_RCD_MTRCV_NUM)
            {
                et = (evt_type_t)(EVENT_TYPE_METER_COVER);
            }
            break;
        #endif
    #if EVENT_TEM_COVER_EN
        // case ITEM(C3_TEM_COVER_NUM            ): // @0x03300D00                    ///      开端盖总次数
        case ITEM(C3_TEM_COVER_RECORD(1)      ): // @(0x03300D00 | LAST_INDEX(N))  /// 上N次开端盖记录N 1-10
            if(point == 0)
            {
                count = profile.evt_cnt_get(EVENT_TYPE_TEM_COVER);
                uint32_to_lsbbcd(p_data, count, 3), p_data += 3;
            }
            else if(point <= EVT_RCD_TEMCV_NUM)
            {
                et = EVENT_TYPE_TEM_COVER;
            }
            break;
    #endif
        case ITEM(C3_PURCHASE_DATE(1)         ): // @(0x03320100 | LAST_INDEX(N))  ///  上N次购电日期           N 1-10
            break;
        case ITEM(C3_PURCHASE_NUM(1)          ): // @(0x03320200 | LAST_INDEX(N))  ///  上N次购电后总购电次数   N 1-10
            break;
        case ITEM(C3_PURCHASE_kWh(1)          ): // @(0x03320300 | LAST_INDEX(N))  ///  上N次购电电量           N 1-10
            break;
        case ITEM(C3_REMAIN_kWh_BEFORE(1)     ): // @(0x03320400 | LAST_INDEX(N))  ///  上N次购电前剩余电量     N 1-10
            break;
        case ITEM(C3_REMAIN_kWh_AFTER(1)      ): // @(0x03320500 | LAST_INDEX(N))  ///  上N次购电购电后剩余电量 N 1-10
            break;
        case ITEM(C3_CUM_PURCHASE_kWh(1)      ): // @(0x03320600 | LAST_INDEX(N))  ///  上N次购电累计购电量     N 1-10
            break;
        case ITEM(C3_PURCHASE_MONEY_DATE(1)   ): // @(0x03320100 | LAST_INDEX(N))  ///  上N次购电日期 金额           N 1-10
            break;
        case ITEM(C3_PURCHASE_MONEY_NUM(1)    ): // @(0x03320200 | LAST_INDEX(N))  ///  上N次购电后总购电次数 金额   N 1-10
            break;
        case ITEM(C3_PURCHASE_MONEY(1)        ): // @(0x03320300 | LAST_INDEX(N))  ///  上N次购电金额           N 1-10
            break;
        case ITEM(C3_REMAIN_MONEY_BEFORE(1)   ): // @(0x03320400 | LAST_INDEX(N))  ///  上N次购电前剩余金额     N 1-10
            break;
        case ITEM(C3_REMAIN_MONEY_AFTER(1)    ): // @(0x03320500 | LAST_INDEX(N))  ///  上N次购电购电后剩余金额 N 1-10
            break;
        case ITEM(C3_CUM_PURCHASE_MONEY(1)    ): // @(0x03320600 | LAST_INDEX(N))  ///  上N次购电累计购金额     N 1-10
            break;
            default:
                break;
    }

    if(et != EVENT_TYPE_NUM)
    {
       len = profile.evt_get(p_data, point, et, 0xFF);
       p_data += len; 
    }

    if((p_data - p_info->snd_dat) == 4) 
    {
        *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
        return 1;
    } // 无数据
    return (uint16_t)(p_data - p_info->snd_dat);
}


/// end of file
