/**
 ******************************************************************************
* @file    relay.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __BSP_RELAY_H
#define __BSP_RELAY_H


/* Includes -----------------------------------------------------------------*/
#include "..\bsp_cfg.h"

/* Export typedef -----------------------------------------------------------*/
#define RLY_INSIDE_ENABLE  USE_RLY_CTRL1  ///内置继电器
#define RLY_OUTSIDE_ENABLE USE_RLY_CTRL2  ///外置继电器
#define RLY_ALARM_ENABLE   USE_RLY_CTRL3  ///报警继电器
typedef enum{
    #if RLY_INSIDE_ENABLE
    TYPE_RLY_1,
    #endif
    #if RLY_OUTSIDE_ENABLE
    TYPE_RLY_2,
    #endif

    RLY_NUM,
} RLY_TYPE_t;

// 继电器 控制 枚举
typedef enum
{
	MODE_RLY_ON = 0,              // 闭合继电器
	MODE_RLY_OFF = !MODE_RLY_ON,  // 断开继电器
} RLY_MODE_t;

// 继电器 状态 枚举
typedef enum
{
	STA_RLY_UNASSURED = 0,        // 继电器状态正检测中尚不确定
    STA_RLY_ON,                   // 继电器闭合状态
    STA_RLY_OFF,                  // 继电器断开状态
    STA_EXT_CONTACT_ON,           // 外部空开闭合状态有负载
    STA_EXT_CONTACT_OFF,          // 外部空开断开状态无负载
    
} RLY_STATE_t;


/* Export define ------------------------------------------------------------*/
/* Export macro -------------------------------------------------------------*/
/* Exported functions -------------------------------------------------------*/
struct relay_t
{
    void (*init)(void);
    RLY_STATE_t (*status)(RLY_TYPE_t type);
    void (*action)(RLY_TYPE_t type, RLY_MODE_t mode);
#if RLY_ALARM_ENABLE
    void (*alarm)(RLY_MODE_t mode);
#endif
};
extern const struct relay_t relay;


#endif /* __BSP_RELAY_H */

