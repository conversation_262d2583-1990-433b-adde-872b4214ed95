/**
 ******************************************************************************
 * @file    ymodem.c
 * <AUTHOR> @date    2025
 * @brief   ymodem协议实现
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "ymodem.h"

/* Private define ------------------------------------------------------------*/
#define MODEM_MAX_RETRIES         200     // 接收重试次数
#define MODEM_NAK_TIMEOUT         1000    // 单位MS

#define MODEM_SOH  0x01                   // 数据块起始字符
#define MODEM_STX  0x02
#define MODEM_EOT  0x04
#define MODEM_ACK  0x06
#define MODEM_NAK  0x15
#define MODEM_CAN  0x18
#define MODEM_C    0x43
#define MODEM_ESC  0x1B

/* Private variables ---------------------------------------------------------*/
static modem_send_byte_t send;
static modem_recv_byte_t recv;

/* Private functions ---------------------------------------------------------*/

/// @brief ymodem起始帧文件长度解析
/// @param ptr 
/// @return 
static unsigned int ymodem_file_len_decode(unsigned char *ptr)
{
    int datatype=10, result=0;

    if (ptr[0] == '0' && (ptr[1] == 'x' || ptr[1] == 'X'))
    {
        datatype = 16;
        ptr += 2;
    }

    for ( ; *ptr != '\0'; ptr++)
    {
        if (*ptr >= '0' && *ptr <= '9')
        {
            result = result * datatype + *ptr - '0';
        }
        else
        {
            if (datatype == 10)
            {
                return result;
            }
            else
            {
                if (*ptr >= 'A' && *ptr <= 'F')
                {
                    result = result * 16 + *ptr - 55;             //55 = 'A'-10
                }
                else if (*ptr >= 'a' && *ptr <= 'f')
                {
                    result = result * 16 + *ptr - 87;             //87 = 'a'-10
                }
                else
                {
                    return result;
                }
            }
        }
    }
    return result;
}

/// @brief 数据块接收
/// @param mblock 
/// @return 0:成功 1:文件传输结束 2:取消传输 -1:超时 -2:CRC校验错误 -3:块序号错误
static int receive_data(modem_struct_t* mblock)
{
    unsigned char hd_found = 0;
    unsigned char ch, blk, cblk, crch, crcl;
    int cksum = 0;

    while(!hd_found) // 头字节
    {
        if(!(recv)(&ch, MODEM_NAK_TIMEOUT)) return -1;
        switch(ch)
        {
            case MODEM_SOH:  /// 起始帧，固定128字节
            hd_found = 1;
            mblock->len = 128;
            break;

            case MODEM_STX:  /// 数据帧，固定1024字节
            hd_found = 1;
            mblock->len = 1024;
            break;

            case MODEM_CAN:  /// 取消传输
            case MODEM_ESC: return 2;

            case MODEM_EOT:  /// 文件传输结束
            (send)(MODEM_ACK);
            (send)(MODEM_C); /// 单个文件 C ACK C后会自动停止传输
            (send)(MODEM_ACK);
            (send)(MODEM_C);
            return 1;
        }
    }

    if(!(recv)(&blk, MODEM_NAK_TIMEOUT))  return -1; // 数据块序号
    if(!(recv)(&cblk, MODEM_NAK_TIMEOUT)) return -1; // 数据块序号补码

    for(int i = 0; i < mblock->len ; i++)
    {
        if(!(recv)(&mblock->buf[i], MODEM_NAK_TIMEOUT)) return -1; // 数据内容
    }

    if(!(recv)(&crch, MODEM_NAK_TIMEOUT)) return -1; // CRC16 High byte
    if(!(recv)(&crcl, MODEM_NAK_TIMEOUT)) return -1; // CRC16 Low byte

    /// @note 块序号检查
    if((blk ^ cblk) != 0xff)
    {
        mblock->rec_err = 1;   // 块序号错误, 判断为数据帧错误，重发
        return -2;
    }
    if(blk != (unsigned char)mblock->nxt_num)
    {
        return -3;  // 块序号错误, 这里需要停止传输
    }

    /// @note CRC16校验
    for(int i = 0; i < mblock->len; i++)
    {
        cksum = cksum ^ (int)mblock->buf[i] << 8;
        for(unsigned char j = 8; j != 0; j--)
        {
            if (cksum & 0x8000)
                cksum = (cksum << 1) ^ 0x1021;
            else
                cksum = cksum << 1;
        }
    }
    if((cksum & 0xffff) != (((int)crch << 8) | crcl))
    {
        mblock->rec_err = 1;   // CRC校验错误, 判断为数据帧错误， 重发
        return -2;
    }

    mblock->nxt_num++;
    mblock->rec_err = 0;  // 接收成功
    return 0;
}

/* Public functions ----------------------------------------------------------*/
/// @brief 打印字符串
/// @param s 字符串
void ymodem_print(const unsigned char* s)
{
    while(*s != '\0')
    {
        (send)(*s);
        s++;
    }
}
/// @brief 取消传输
/// @param  
void ymodem_cancle(void)
{
    (send)(MODEM_ESC);
    (send)(MODEM_CAN);
    (send)(MODEM_CAN);
    (send)(MODEM_CAN);
    (send)(MODEM_CAN);
}
/// @brief 初始化YMODEM
/// @param mblock     数据块指针
/// @param modem_send 发送字节函数
/// @param modem_recv 接受字节函数
/// @return           0:成功 -1:失败
int ymodem_init(modem_struct_t* mblock, modem_send_byte_t modem_send, modem_recv_byte_t modem_recv)
{
    for(int i = 0; i < sizeof(modem_struct_t); i++)
    {
        ((unsigned char*)mblock)[i] = 0;
    }
    send = modem_send;
    recv = modem_recv;

    ymodem_print("\r\nPlease prepare update file...\r\n");
    for(int i = 0; i < MODEM_MAX_RETRIES; i++)
    {
        (send)(MODEM_C);  // 发C字符启动传输
        if(receive_data(mblock) == 0)  // 等待接收起始帧
        {
            volatile int j = 0;        // 此变量不能优化，否则可能导致YMODEM升级异常
            unsigned char* bufptr = mblock->buf;
            char* namptr = mblock->filename;  // 文件名指针
            do
            {// 获取文件名
                if(j < FILENAME_MAX_LEN)
                {
                    *namptr++ = *bufptr;
                    *namptr   = '~';  // 如果文件名太长, 剩余字符将会用'~'取代
                    j++;
                }
            } while (*bufptr++ != '\0');
            namptr[1] = '\0';

            while (*bufptr == ' ')
            {
                bufptr++;
            }

            mblock->filelen = ymodem_file_len_decode(bufptr); // 获取程序文件长度
            for(j = 10000; j != 0; j--){} // 延时发送ACK确认
            (send)(MODEM_ACK);  // 确认传输
            return 0;
        }
    }

    ymodem_print("\r\nNo file selected! \r\n");
    return -1;  // 启动传输失败
}
/// @brief ymodem接收
/// @param mblock 
/// @return 
int ymodem_recv(modem_struct_t* mblock)
{
    for(int i = 0; i < MODEM_MAX_RETRIES; i++)
    {
        if(mblock->nxt_num == 1)
        {
            (send)(MODEM_C);
        }
        else
        {
            (send)(!mblock->rec_err ? MODEM_ACK : MODEM_NAK);
        }
        switch(receive_data(mblock))
        {
            case -1: // 接收超时
            break;

            case 0:  // 数据包OK
            return 0;

            case 1:  // 文件结束
            ymodem_print("\r\nFile transfer ok! \r\n");
            return 1;

            case 2:  // 取消发送
            ymodem_print("\r\nFile transfer aborted! \r\n");
            return -1;

            default: // 传输错误
            ymodem_print("\r\nFile trans error! \r\n");
            return -1;
        }
    }
    return -1;
}

// End of file
