/********************************************************************************
* @file    NoVoltageM.h
* <AUTHOR> Application Team
* @version V1.0.1
* @date    2020.05.17
* @brief   全失压测试电流模块
******************************************************************************
* @attention       
* 自动DCOS校正
* 需求分析
* 初始化全失压模块，跟自动DCOS校正的位置（需要使用全失压测量时进行全失压初始化；需要进行全失压自动DCOS校正的时候进行全失压自动DCOS校正）
* 需求拆分
* 1、全失压模块初始化
* 开启计量模块
* 开启全失压模块
* 配置全失压模块（开模块、关闭高通、LS_DCOS）
* 2、自动DCOS校正
* 2.1、自动DCOS校正前是否需要对DCOS寄存器内的值清零？
* 2.2、自动DCOS校正完成后，用EMU->DCOS寄存器内值还是NVM->LS_DCOS_IA值，是直接用还是要处理后使用比如乘0.512还是乘2；
* 2.3、需要考虑增益校正寄存器
* 
* 3、全失压测量结果的获取
* 自动DCOS要求NVM模块开启，其高通关闭
* 若NVM的高通开启，则全失压自动校正标志会清零，但是DCOS寄存器中不会有值
* 自动DCOS校正时，计量的高通开启和关闭不影响自动校正的结果；但自动DCOS校正有时候会失效，如果校正后结果不合适，建议多次进行
* 另外，自动DCOS校正寄存器要求乘2后写到NVM模块的直流校正寄存器内
*******************************************************************************/	
/* Define to prevent recursive inclusion -------------------------------------*/ 
#ifndef _NoVoltageM_H
#define _NoVoltageM_H

/* Includes ------------------------------------------------------------------*/

/* Exported define ------------------------------------------------------------*/


/* Exported enum ------------------------------------------------------------*/

/* Exported types ------------------------------------------------------------*/

/* Exported variables ----------------------------------------------------------*/

/* Exported functions ------------------------------------------------------- */ 
extern void LL_NVM_Init(int16_t *ls_dcos,uint32_t nvm_ie);
extern void LL_NVM_DeInit(void);
extern ErrorStatus LL_NVM_AutoDC(int16_t *ls_dcos);
extern void LL_NVM_StartMeasure(void);
extern void LL_NVM_GetRMS(uint32_t *rml_lx);
#endif

/****************************  (C) COPYRIGHT Renergy  ****************************/
/****************************          END OF FILE          ****************************/

