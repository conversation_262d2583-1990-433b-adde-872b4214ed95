/**
  ******************************************************************************
  * @file    beep.h
  * <AUTHOR> @date    2024
  * @brief   蜂鸣器驱动头文件，包含初始化、打开、关闭等接口函数。移植不需要修改此文件。
  *          
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __BUZZ_H
#define __BUZZ_H

/* Includes ------------------------------------------------------------------*/

/* Exported types ------------------------------------------------------------*/
/* Exported defines ----------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions -------------------------------------------------------*/
struct beep_s
{ 
    /// @brief  Init the beep.
    void (*init)(void);

    /// @brief  Open the beep.
    void (*start)(uint16_t cycleon, uint16_t cycleoff, uint32_t durationon, uint32_t durationoff, uint16_t freq);

    /// @brief  Close the beep.
    void (*stop)(void);
};
extern const struct beep_s beep;


#endif /* __BUZZ_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

