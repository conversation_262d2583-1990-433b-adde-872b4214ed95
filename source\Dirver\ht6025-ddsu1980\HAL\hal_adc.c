/**
 ******************************************************************************
* @file    hal_adc.c
* <AUTHOR> @version V1.0.0
* @date    2024
* @brief   本模块完成对MCU的AD采样驱动(单次单通道).
* @note    单次采样时间: 采样时间 + 转换时间;
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_adc.h"
#include "hal_mcu.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/


/* Private macro -------------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
/* Public functions ----------------------------------------------------------*/
/** @defgroup API_functions
 * @{
 */
/**
 * @brief  多通道采样初始化
 * @param  None
 * @retval None
 */
void hal_adc_open(void)
{
    HT_TBS->TBSPRD  = (TBS_TBSPRD_ADC0PRD_1S | TBS_TBSPRD_ADC1PRD_1S | TBS_TBSPRD_TMPPRD_OSC1S_MEM4S); // 设置ADC0,ADC1和TMP的采样周期
    HT_TBS->TBSTEST = 0x0200; // FAQ文件需求上电后将寄存器 TBSTEST 赋一次默认值
    HT_TBSConfig(TBS_TBSCON_ADC0En | TBS_TBSCON_ADC1En | TBS_TBSCON_TMPEn, ENABLE); 
}

/**
 * @brief  Init and Open the Adc(No Power), 单通道单次采样
 * @param  None
 * @retval None
 */
void hal_adc_open_nopower(void)
{
    HT_TBS->TBSPRD = (TBS_TBSPRD_TMPPRD_OSC64S_MEM8mS); // 设置TMP的采样周期64S
    HT_TBSConfig(TBS_TBSCON_TMPEn, ENABLE);  
}

/**
 * @brief  Close the Adc
 * @param  None
 * @retval None
 */
void hal_adc_close(void)
{
    HT_TBSConfig(TBS_TBSCON_ADC0En | TBS_TBSCON_ADC1En | TBS_TBSCON_TMPEn, DISABLE); 
}

/**
 * @brief  Close the Adc(No Power)
 * @param  None
 * @retval None
 */
void hal_adc_close_nopower(void)
{
    HT_TBSConfig(TBS_TBSCON_TMPEn, DISABLE); 
}

void hal_adc_start_conversion(HAL_ADC_CHN_TYPE chn)
{

}
/// @brief Get the ADC sample value
/// @param chn-ADC channel
/// @return
int32_t hal_adc_result_get(HAL_ADC_CHN_TYPE chn)
{
    int16_t value;
    switch(chn)
    {
    case HAL_ADC_CHN0:
        value = HT_TBS->ADC0DAT;
        break;
    case HAL_ADC_CHN1:
        value = HT_TBS->ADC1DAT;
        break;
    case HAL_ADC_CHN3:
        value = HT_TBS->ADC3DAT;
        break;
    case HAL_ADC_CHN4:
        value = HT_TBS->ADC4DAT;
        break;
    case HAL_ADC_CHN5:
        value = HT_TBS->ADC5DAT;
        break;
    case HAL_ADC_VBAT:
        value = HT_TBS->VBATDAT;
        break;
    case HAL_ADC_TEMP:
        value = HT_TBS->TMPDAT;
        break;
    default:
        break;
    }
    return (int32_t)value;
}


/// @brief Get the current channel voltage value
/// @param chn-ADC channel
/// @return 电压值单位V
float hal_adc_voltage_get(HAL_ADC_CHN_TYPE chn)
{
    volatile float vol;
	vol = (float)hal_adc_result_get(chn);
    vol = vol * 0.0258 + 4.7559;
    return (vol / 1000);
}

/// @brief Get the current temperature value
/// @param
/// @return
float hal_adc_temperature_get(void)
{
    float temp;
    temp = 12.9852 - (hal_adc_result_get(HAL_ADC_TEMP)) * 0.002828;
    return temp;
}

/// @brief 声明hal_adc子模块对象
const struct hal_adc_t hal_adc =
{
    .open             = hal_adc_open,
    .open_nopower     = hal_adc_open_nopower,
    .close            = hal_adc_close,
    .close_nopower    = hal_adc_close_nopower,
    .start            = hal_adc_start_conversion,
    .result           = hal_adc_result_get,
    .voltage          = hal_adc_voltage_get,
    .temperature      = hal_adc_temperature_get,
};

/** @} */
