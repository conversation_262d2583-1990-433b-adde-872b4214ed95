/**
  ******************************************************************************
  * @file    flash.h
  * <AUTHOR> @date    2024
  * @brief   注意MCU的flash地址从0x80000000开始，则需定义为MCUEE = 1 !!!!!!!
  *          定义了NVM存储空间的起始地址，以及相关的索引定义    
  *          定义了日志存储空间的起始地址，以及相关的索引定义    
  *          
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifndef EEPROM_APP_H
#define EEPROM_APP_H

#include "typedef.h"

///地址类型定义，如果MCU的flash地址从0x80000000开始，则需定义为MCUEE = 1 !!!!!!!
typedef enum { MCUEE = 0, EXTEE = 2, EXTDF = 3} NVM_TYPE;

typedef uint8_t SYS_RESET_TYPE;

#define SYS_DATA_RESET          (1 << 0)
#define SYS_PARA_RESET          (1 << 1)
#define SYS_GLOBAL_RESET        (SYS_DATA_RESET | SYS_PARA_RESET)


#define CAL_DATA_SIZE           512 ///校表数据预留空间

/// 预留的空间定义, 必须为页的整数倍
#define NVM_MCUEE_RESVER_BTYES   0
#define NVM_EXTEE_RESVER_BTYES   (alignof(CAL_DATA_SIZE, EE_PAGE_SIZE))
#define NVM_EXTDF_RESVER_BTYES   0


/* NVM 首地址定义 */
#define MCUEE_BASE_ADDR          (((uint32_t)MCUEE << 30) + MCU_FLASH_DATA_BASE + NVM_MCUEE_RESVER_BTYES)   ///MCU存储空间起始地址
#define EXTEE_BASE_ADDR          (((uint32_t)EXTEE << 30) + NVM_EXTEE_RESVER_BTYES)                         ///EEPROM存储空间起始地址          
#define EXTDF_BASE_ADDR          (((uint32_t)EXTDF << 30) + NVM_EXTDF_RESVER_BTYES)                         ///ext flash存储空间起始地址


typedef struct
{
    uint16_t chk;   // 校验码
    uint16_t crc;   // crc16
    uint16_t ptr;   // 记录指针
    uint16_t num;   // 记录有效数
    uint32_t cnt;   // 记录计数器
    uint32_t time;  // 记录时间
} log_head_s;

typedef struct
{
    uint32_t hdr;   // 记录头地址，指向LogHead_s结构体，存储在EEPROM中
    uint32_t ofst;  // 记录内容地址起始地址
    uint16_t size;  // 每条记录内容的长度
    uint16_t num;   // 最大有效记录条数，
    uint16_t max;   // 最大记录条数，超过此值后，旧记录会被覆盖
} log_addr_s;

/// @brief 数据，参数在NVM中存储的索引定义
typedef enum
{
    NVM_EE_REV,             // 定义EXT EEPROM版本的地址索引
    NVM_EE_CAL_DATA,        // 定义EXT EEPROM校表数据地址索引

    NVM_ENG_DATA,           // 定义电能数据地址索引
    NVM_ENG_BAK_DATA,       // 定义电能备份数据地址索引
    NVM_CAL_DATA_EE,        // 定义eeprom中校表数据地址索引
    NVM_STATUS_PARA,        // 定义系统状态参数地址索引
    NVM_STATUS_DATA,        // 定义系统状态数据地址索引
    NVM_STATUS_PDR,         // 定义系统状态掉电存储地址索引
    
    NVM_CLOCK_PARA,         // 定义系统时钟参数地址索引
    NVM_CLOCK_DATA,         // 定义系统时钟数据地址索引
    NVM_CLOCK_PD,           // 定义系统时钟掉电存储地址索引

    NVM_PRIVATE_INFO,       // 定义私有信息地址索引

    NVM_ENG_PARA,           // 定义系统电能参数地址索引

    NVM_LOCAL_PORT_PARA,    // 定义本地串口参数地址索引
    NVM_LOCAL_PORT_DATA,    // 定义本地串口数据地址索引

    NVM_PAYMENT_PARA,       // 定义预付费参数地址索引
    NVM_PAYMENT_DATA,       // 定义预付费数据地址索引
    NVM_PAYMENT_DATA_BAK,   // 定义预付费备份数据地址索引
    NVM_PAYMENT_PD,         // 定义预付费掉电存储地址索引

    NVM_PWR_EVT_PARA,       // 定义电源事件参数地址索引
    NVM_PWR_EVT_DATA,       // 定义电源事件数据地址索引
    NVM_PWR_EVT_PD,         // 定义电源事件掉电存储地址索引

    NVM_DEMAND_PARA,        // 定义需量参数地址索引
    NVM_DEMAND_DATA,        // 定义需量数据地址索引

    NVM_PRODUCT_INFO_PARA,   // 定义产品信息参数地址索引
    NVM_ACTIVATE_TARIFF_PARA,// 定义当前套参数地址索引
    NVM_PASSIVE_TARIFF_PARA, // 定义备用套参数地址索引
    NVM_TARIFF_DATA,         // 定义费率数据地址索引

    NVM_DISPLAY_PARA,       // 定义显示参数地址索引
    NVM_STEP_TARIFF_PARA,   // 定义阶梯费率参数地址索引

    NVM_BILLING_PARA,       // 定义结算模块参数地址索引
    NVM_LOAD_CURVE_PARA,    // 定义负荷曲线参数地址索引
    
    NVM_MON_FZ_HEADER,      // 定义月结曲线头地址索引
    NVM_DAY_FZ_HEADER,      // 定义日结曲线头地址索引

    NVM_PD_ENG_DATA,        // 定义掉电能存储地址索引

    NVM_MODULE_PARA,        // 定义模块参数地址索引

    NVM_CTRL_PARA,          // 定义控制参数地址索引
    NVM_CTRL_DATA,          // 定义控制数据地址索引

    NVM_FIRMWARE_DOWNLOAD,  // 定义程序下载存储地址索引
    NVM_FMW_UPGRADE_DATA,   // 定义程序升级数据存储地址索引
    NVM_STORAGE_NUM,
} NVM_IDX_t;

typedef enum
{
    NVM_LOG_MON_FROZEN,      /// 月结曲线地址索引
    NVM_LOG_DAY_FROZEN,      /// 日结曲线地址索引
    NVM_LOG_STEP_FROZEN,     /// 阶梯结算地址索引

    NVM_LC1_PROFILE,         /// 负荷曲线1地址索引
    NVM_LC2_PROFILE,         /// 负荷曲线2地址索引

    NVM_LOG_LOSS_VOL_A,      /// 失压事件地址索引
    NVM_LOG_LOSS_VOL_B,
    NVM_LOG_LOSS_VOL_C,
    NVM_LOG_LOW_VOL_A,       /// 欠压事件地址索引
    NVM_LOG_LOW_VOL_B,
    NVM_LOG_LOW_VOL_C,
    NVM_LOG_OVR_VOL_A,       /// 过压事件地址索引
    NVM_LOG_OVR_VOL_B,
    NVM_LOG_OVR_VOL_C,
    NVM_LOG_MISS_VOL_A,      /// 断相事件地址索引
    NVM_LOG_MISS_VOL_B,
    NVM_LOG_MISS_VOL_C,
    NVM_LOG_ALL_LOSS_VOL,    /// 全失压事件地址索引
    NVM_LOG_BAK_PWR_LOS,     /// 辅助电源失电事件地址索引
    NVM_LOG_V_REV_SQR,       /// 电压逆向序事件地址索引
    NVM_LOG_I_REV_SQR,       /// 电流逆向序事件地址索引
    NVM_LOG_V_UNB,           /// 电压不平衡事件地址索引
    NVM_LOG_I_UNB,           /// 电流不平衡事件地址索引
    NVM_LOG_LOS_CUR_A,       /// 失流事件地址索引
    NVM_LOG_LOS_CUR_B,
    NVM_LOG_LOS_CUR_C,
    NVM_LOG_OVR_CUR_A,       /// 过流事件地址索引
    NVM_LOG_OVR_CUR_B,
    NVM_LOG_OVR_CUR_C,
    NVM_LOG_MISS_CUR_A,      /// 断流事件地址索引
    NVM_LOG_MISS_CUR_B,
    NVM_LOG_MISS_CUR_C,
    NVM_LOG_REV_A,           /// 潮流反向事件地址索引
    NVM_LOG_REV_B,
    NVM_LOG_REV_C,
    NVM_LOG_OVR_LOAD_A,      /// 过载事件地址索引
    NVM_LOG_OVR_LOAD_B,
    NVM_LOG_OVR_LOAD_C,
    NVM_LOG_LOW_PF,          /// 低功率因素事件地址索引
    NVM_LOG_DISCONNECT,      /// 拉闸记录
    NVM_LOG_RECONNECT,       /// 合闸记录
    NVM_LOG_PWR_DOWN,        /// 掉电事件地址索引
    NVM_LOG_OVR_DM_POS_kW,   /// 超需量事件地址索引
    NVM_LOG_OVR_DM_NEG_kW,
    NVM_LOG_OVR_DM_Q1_kvar,
    NVM_LOG_OVR_DM_Q2_kvar,
    NVM_LOG_OVR_DM_Q3_kvar,
    NVM_LOG_OVR_DM_Q4_kvar,
    NVM_LOG_PROGRAM,         /// 编程事件地址索引
    NVM_LOG_METER_CLEAN,     /// 电表清零事件地址索引
    NVM_LOG_DEMAND_CLEAN,    /// 需量清零事件地址索引
    NVM_LOG_EVENT_CLEAN,     /// 事件清零事件地址索引
    NVM_LOG_SHITFT_TIME,     /// 校时事件地址索引
    NVM_LOG_BC_TIME,         /// 广播校时事件地址索引
    NVM_LOG_SCHEDULE,        /// 时段表事件地址索引
    NVM_LOG_ZONE_TAB,        /// 时区表事件地址索引
    NVM_LOG_WEEKENDS_PGM,    /// 周休日编程事件地址索引
    NVM_LOG_HOLIDAY_PGM,     /// 节假日表编程事件地址索引
    NVM_LOG_COMB_kWh_PGM,    /// 有功组合方式编程事件地址索引
    NVM_LOG_COMB1_kvarh_PGM, /// 无功组合方式1编程事件地址索引
    NVM_LOG_COMB2_kvarh_PGM, /// 无功组合方式2编程事件地址索引
    NVM_LOG_BL_DAY_PGM,      /// 结算日编程事件地址索引
    NVM_LOG_METER_COVER,     /// 开表盖事件地址索引
    NVM_LOG_TEM_COVER,       /// 开端盖事件地址索引

    NVM_LOG_NUM,
    NVM_LOG_UNDEF = 255
} NVM_LOG_t;

struct nvm_s
{
    /// 读取存储空间里的若干字节
    /// addr表示存储类型及存储空间
    /// 地址范围:       0 ~ 3FFFFFFF(1024M bytes)
    /// buf:    读取缓冲地址
    /// len:    读取字节数
    /// 返回 0-失败  1-成功
    bool (*read)(uint32_t addr, void* buf, uint32_t len);


    /// 往存储空间里写若干字节
    /// addr表示存储类型及存储空间
    /// 地址范围:       0 ~ 3FFFFFFF(1024M bytes)
    /// buf:    写缓冲地址
    /// len:    写字节数
    /// 返回 0-失败  1-成功
    bool (*write)(uint32_t addr, const void* buf, uint32_t len);

    uint8_t (*check)(uint32_t addr);

    bool (*clean)(NVM_TYPE type);
};
extern const struct nvm_s nvm;

extern uint32_t   nvm_addr(NVM_IDX_t idx);
extern log_addr_s log_addr(NVM_LOG_t idx);
#endif
