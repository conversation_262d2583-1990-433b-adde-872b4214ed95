#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>

#define VERIFYTAG_POS       24

// 用于存储HEX记录的数据结构
typedef struct {
    unsigned char length;     // 数据长度
    unsigned int address;    // 十六进制地址
    unsigned char type;      // 记录类型
    unsigned char data[256]; // 数据区域，这里单条记录最大长度为256字节
    unsigned char checksum;   // 校验和
} HexRecord;

/*
*   Action: 计算hex文件一行的校验和
*   Input:  line-hex文件的一行数据
*   Output: 校验和
*/
int calculate_checksum(const char* line) {
    int sum = 0;
    for (size_t i = 1; i < strlen(line) - 3; i += 2)
    { // 跳过第一个':'和最后两个'校验和'字符
        char byteStr[3];
        strncpy(byteStr, &line[i], 2);
        byteStr[2] = '\0'; // 确保字符串结束
        int byteValue = strtol(byteStr, NULL, 16); // 将十六进制字符转换为整型数值
        sum += byteValue;
    }
    return ((-sum) & 0xFF); // 取模并返回补码
}

/*
*   Action: 把一个十六进制字节转换成两个可视字符
*   Input:  str-目的字符地址  hex-输入字节
*   Output: no
*/
static const char hexTable[] = "0123456789ABCDEF";
static void hex_2char(char* str, uint8_t hex)
{
    str[0] = hexTable[hex >> 4];
    str[1] = hexTable[hex & 0xf];
}

int main(int argc, char *argv[])
{
    if(argc != 3)
    {
        printf("usage: addcs_hex.exe bin_file hex_file");
        return -1;
    }

    FILE *fd = NULL;
    uint32_t addr = 0;
    uint8_t crc32_val[4] = {0};
    char buffer[300] = {0};
    long current_pos = 0;
    int checksum = 0;
    HexRecord record;

    ///1.从bin文件中获取APP0地址和APP0 header校验
    fd = fopen(argv[1], "rb");
    if(fd == 0) return -1;

    ///读出APP0地址
    fread(&addr, 4, 1, fd);

    ///读出头校验
    fseek(fd, VERIFYTAG_POS, SEEK_SET);
    fread(crc32_val, 1, 4, fd);
    fclose(fd);

    ///2.根据APP0地址，修改hex对应位置的数据
    fd = fopen(argv[2], "r+");
    if(fd == NULL) return -1;

    ///将每行数据读出来，获取地址信息
    while(fgets(buffer, sizeof(buffer), fd))
    {
        sscanf(buffer + 1, "%2hhx%4x%2hhx", &record.length, &record.address, &record.type);
        if(record.address == addr)
        {
            /// IAR生成的HEX文件一行数据是16字节
            if(record.length < VERIFYTAG_POS)
            {
                current_pos = ftell(fd); // 获取当前文件指针位置
                ///读取下一行数据(APP0 HEADER校验位置偏移为24)
                fgets(buffer, sizeof(buffer), fd);
                hex_2char(&buffer[25 + 0], crc32_val[0]);
                hex_2char(&buffer[25 + 2], crc32_val[1]);
                hex_2char(&buffer[25 + 4], crc32_val[2]);
                hex_2char(&buffer[25 + 6], crc32_val[3]);
                ///计算校验和
                checksum = calculate_checksum(buffer);
                hex_2char(&buffer[41], checksum);
                fseek(fd, current_pos, SEEK_SET); // 将文件指针定位回原来的位置
                fwrite(buffer, strlen(buffer), 1, fd);
                break;
            }
        }
    }
    fclose(fd);
}
