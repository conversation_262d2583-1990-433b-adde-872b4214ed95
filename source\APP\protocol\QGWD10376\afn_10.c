/**
 ******************************************************************************
 * @file    afn_10.c
 * <AUTHOR> @date    2025
 * @brief   电能表协议QGWD10376 AFN 16 数据转发
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "QGDW10376.h"
#include "DLT645_2007.h"
#include "dcu.h"
#include "debug.h"
#include "comm_phy.h"

#define logd(...) DBG_PRINTF(P_GWD376, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_GWD376, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_GWD376, T, __VA_ARGS__)    // 时间戳打印

extern void fnpn_to_DADT(uint8_t fn, uint16_t pn, uint8_t *DADT);
extern void DADT_to_fnpn(uint8_t *DADT, uint16_t *pn, uint8_t *fn);

/// @brief 376 波特率真值表， 不支持7200bps，对应到9600bps
const HAL_UART_BAUDE_TYPE baud_table[] = {BAUDE_300BPS,  BAUDE_600BPS,   BAUDE_1200BPS,  BAUDE_2400BPS,  BAUDE_4800BPS,  BAUDE_9600BPS,
                                          BAUDE_9600BPS, BAUDE_19200BPS, BAUDE_38400BPS, BAUDE_57600BPS, BAUDE_115200BPS};

const HAL_UART_CHAR_TYPE uart_format_table[] = {
    CHAR_8N1,    // 00-无校验
    CHAR_8N1,    // 01-无校验
    CHAR_8E1,    // 10-偶校验
    CHAR_8D1,    // 11-奇校验
};

typedef union com_control_struct
{
    uint8_t u8;
    struct
    {
        uint8_t bit : 2;         ///< 控制位 0-5， 1-6，2-7，3-8bit
        uint8_t check : 2;       ///< 校验类型 0x-无校验，10-偶校验，11-奇校验
        uint8_t stop_bit : 1;    ///< 停止位 0-1, 1-2
        uint8_t baud : 3;        ///< 波特率 0-300, 1-600, 2-1200, 3-2400, 4-4800, 5-7200, 6-9600, 7-19200
    };
} com_control_s;

/// @brief 数据转发处理函数
/// @param req
/// @param rsp
/// @return
rsp_err_t afn10_process(req_obj_s *req, rsp_obj_s *rsp)
{
    rsp_err_t ret = ACK_ERR;    // 默认返回错误
    switch(req->fn)
    {
        case 1:    // F1：数据转发
        {
            if(req->apdu_len < 6) { return ACK_ERR; }    // 数据长度不足
            uint8_t      *ptr = req->req_apdu;
            uint16_t      len;
            uint16_t      timeout;
            com_control_s ctr;
            uint8_t       com;

            com    = *ptr++;    // 通讯端口
            ctr.u8 = *ptr++;    // 通讯参数

            timeout = ((*ptr) & 0x80) ? 1000 * ((*ptr) & 0x7F) : 1000 * ((*ptr) & 0x7F);    // 超时时间，单位ms
            if(timeout == 0) timeout = 1000;
            ptr += 2; // 字节间超时忽略。
            memcpy(&len, ptr, 2), ptr += 2; // 获取转发字节长度
            if(len + 6 > req->apdu_len) { return ACK_ERR; } // 数据长度不足
            req->apdu_len -= (len + 6 + 16); // 减去转发数据长度  mac + password 16字节

            com = PHY_CHN_4;    // 如果com为0，使用默认的COM_RS4851
            dcu.transparent_set(ptr, len, com, uart_format_table[ctr.check], baud_table[ctr.baud], timeout);
            // 数据转发处理
            ret = ACK_NO_RSP;    //
            rsp->err = ACK_NO_RSP;
        }
        break;
        default: {
            ret = ACK_ERR;    //
        }
    }
    logd("AFN 16 Data Forward: fn=%d, ret=%d", req->fn, ret);
    return ret;    // 返回确认结果
}

const gdw376_table_s afn10_table = {
    .afn    = AFN_DATA_FORWARD,    ///< 功能码
    .reset  = NULL,                ///< 复位函数
    .verify = NULL,                ///< 验证函数
    .get    = afn10_process,       ///< 获取函数
    .set    = NULL,                ///< 设置函数
};
