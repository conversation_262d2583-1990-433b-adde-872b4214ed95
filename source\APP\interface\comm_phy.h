/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      comm_phy.h
*    Describe:      
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#ifndef __COMM_PHY_H__
#define __COMM_PHY_H__

#include "typedef.h"
#include "bsp_cfg.h"


typedef uint8_t COMM_STATUS_TYPE;
#define COMM_STATUS_IR_RECV         (1<<1)
#define COMM_STATUS_BLE_RECV        (1<<2)
#define COMM_STATUS_MODULE_RECV     (1<<3)
#define COMM_STATUS_RS4851_RECV     (1<<4)
#define COMM_STATUS_RS4852_RECV     (1<<5)


typedef union
{
    struct
    {
        uint8_t ir_recv         : 1; // 红外接收
        uint8_t module1_recv    : 1; // 模块1接收
        uint8_t module3_recv    : 1; // 模块2接收
        uint8_t rs4851_recv     : 1; // RS485 1接收
        uint8_t rs4852_recv     : 1; // RS485 2接收
        uint8_t module1_reset   : 1; // 模块1复位(默认蓝牙)
        uint8_t module2_reset   : 1; // 模块2复位(默认GPRS/PLC)
        uint8_t ir_lock         : 1; // 红外锁定
    };
    uint8_t value;
}comm_phy_status_s;

typedef enum
{
#if defined(COM_IR)
    PHY_CHN_1,      // 远红外必须在通道1
#endif

#if defined(COM_BLE)
    PHY_CHN_2,      // 蓝牙通道
#endif

#if defined(COM_MODULE)
    PHY_CHN_3,      // 通讯模块通道
#endif

#if defined(COM_RS4851)
    PHY_CHN_4,      // 4851
#endif

#if defined(COM_RS4852)
    PHY_CHN_5,      // 4852
#endif
    PHY_CHN_NUM
} phy_chn_t;


struct comm_phy_s
{
    void (*init)(uint8_t chn, uint8_t* rxbuf, uint16_t bufsize, uint8_t baude, uint8_t check);
    uint16_t (*recv)(uint8_t chn);
    void (*send)(uint8_t chn, uint8_t* msg, uint16_t len);
    void (*random_bytes_get)(uint8_t* val, uint8_t num);
    bool (*state_query)(COMM_STATUS_TYPE status);
};
extern const struct comm_phy_s comm_phy;


#endif /* __COMM_PHY_H__ */
