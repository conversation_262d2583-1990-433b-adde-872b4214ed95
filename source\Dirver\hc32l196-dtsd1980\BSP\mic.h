
/**
 ******************************************************************************
 * @file    mic.h
 * <AUTHOR> @date    2024
 * @brief
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __MIC_H__
#define __MIC_H__

#include "typedef.h"

#if defined(POLYPHASE_METER)
#define PHASE_NUM                   3  ///三相表
#define HARMONIC_WAVE               21 ///@配置谐波测量的谐波数目, 0 不计算谐波
#else
#define PHASE_NUM 1        ///@单相表
#define USE_CHANNEL_2 0    ///@配置是否使能第二通道计量
#define HARMONIC_WAVE               0 ///@配置谐波测量的谐波数目, 0 不计算谐波
#endif

/* 能量/功率计量模式定义，该模式只对三相表有用!!! */
#define MIC_ABSACC_MODE 0    /// 标准计量模式，合相绝对值计量，正反电能寄存器等于各分相正反电能之和
#define MIC_ALGACC_MODE 1    /// 法拉第计量模式，合相代数和计量

/* 接线模式定义，该模式只对三相表有用，用于区分三相三线和三相四线!!! */
#define MIC_WIRE_3P4W_MODE 0    /// 三相四线计量模式
#define MIC_WIRE_3P3W_MODE 1    /// 三相三线计量模式，三相三线表只支持法拉第计量模式

/* 计量通道模式定义，该模式只对单相两线表有用!!! */
#define MIC_CHN_AUTO_MODE 0       /// 单相表双路计量自动切换(根据火零电流大小自动切换)
#define MIC_CHN_PHASE_MODE 1      /// 指定火线路计量
#define MIC_CHN_NEUTRAL_MODE 2    /// 指定零线路计量

#define MIC_SUPPORT_Harmonic_en 0    ///@配置是否支持谐波测量

#define MIC_SUPPORT_SET_CAL_PARA 0    ///@配置是否支持直接修改校表数据
#define MFCCMD_DOPGAIN_ENABLE 0       ///@配置是否支持生产命令单步功率增益校准使能，1.0，0.5L分开校准（一般不开启，除非常规方式不满足要求）

/* 计量基本参数 */
#define INPUT_VOLTAGE               220      ///Un (V)
#define MIN_VOLTAGE                 (float)(40.0) 
#define MIN_WORK_VOLTAGE            (uint16_t)(0.7 * INPUT_VOLTAGE * 10) ///电表工作的最小电压(1V),临界电压
#define BASE_CURRENT                1.5      ///Ib (A)
#define MAXIMUM_CURRENT             6        ///Imax (A)
#define METER_CONST                 6400     ///脉冲常数(imp/kWh)
#define BASE_FREQUENCE              50       ///频率(Hz)
#define START_CURRENT_RADIO         0.002    ///启动电流 (Ib)
#define ACT_ACCURACY_LEVEL          0.5      ///有功精度等级 
#define REACT_ACCURACY_LEVEL        2.0      ///无功精度等级 
#define START_CURRENT               (BASE_CURRENT*START_CURRENT_RADIO) ///单位:1A
#define START_PWR                   (INPUT_VOLTAGE*BASE_CURRENT*START_CURRENT_RADIO)               ///启动功率
#define WATT_MAX_POWER              ((uint32_t)MAXIMUM_CURRENT * INPUT_VOLTAGE * PHASE_NUM)        ///最大电流功率, 单位: 1W
#define MAX_PULSE_PER_SECOND        ((uint32_t)WATT_MAX_POWER * 3 * METER_CONST / 3600000 + 1)     ///每秒最大可能产生脉冲数 * 3
#define ENERGY_PULSE_THRESHOLD_ms   (3600ul * 1000 * 1000 / METER_CONST)                           ///电能脉冲阀值，按w.ms方式做积分
#define ENERGY_PULSE_THRESHOLD_S    (3600ul * 1000 / METER_CONST)                                  ///电能脉冲阀值，按w.s方式做积分
#define MEASURE_RANGE_STR           ASTRINGZ(BASE_CURRENT)"-"ASTRINGZ(MAXIMUM_CURRENT)"A"          ///电流范围
#define INPUT_VOLTAGE_STR           ASTRINGZ(INPUT_VOLTAGE)"V"
#define BASE_CURRENT_STR            ASTRINGZ(BASE_CURRENT)"A"
#define MAXIMUM_CURRENT_STR         ASTRINGZ(MAXIMUM_CURRENT)"A"
#define FREQUENCE_STR               ASTRINGZ(BASE_FREQUENCE)"Hz"
#define ACT_ACCURACY_LEVEL_STR      ASTRINGZ(ACT_ACCURACY_LEVEL)
#define REACT_ACCURACY_LEVEL_STR    ASTRINGZ(REACT_ACCURACY_LEVEL)


/* ADC采样通道定义 */
typedef enum
{
    CHANNEL_1 = 0,    /// 第一路通道, A相(火线)采样
    CHANNEL_2,        /// 第二路通道, B相(零线)采样
#if PHASE_NUM == 3
    CHANNEL_3,          /// 第三路通道, C相采样
    CHANNEL_NETURAL,    /// 第四路通道, N相采样
#endif
    CHANNEL_NUM,
} SAMPLE_CHANNEL;

/* 象限定义 */
typedef enum
{
    QUADRANT_1 = 1,    /// 第一象限  感性Lag
    QUADRANT_2,        /// 第二象限  容性Lead
    QUADRANT_3,        /// 第三象限  感性Lag
    QUADRANT_4         /// 第四象限  容性Lead
} PWR_QUADRANT;

/* 功率类型定义 */
typedef enum
{
    P_POWER = 0,    /// 有功功率
    Q_POWER,        /// 无功功率
    S_POWER         /// 视在功率
} EMU_POWER_TYPE;

/* 分相合相定义 */
typedef enum
{
    T_PHASE = 0,    /// 合相
    A_PHASE,        /// A相(R)
    B_PHASE,        /// B相(Y)
    C_PHASE         /// C相(B)
} EMU_PHASE_TYPE;

/* 线电压类型定义 */
typedef enum
{
    AB_LINE_VOL = 0,    /// AB线电压
    BC_LINE_VOL,        /// BC线电压
    AC_LINE_VOL         /// AC线电压
} EMU_LINE_VOL_TYPE;

/* 矢量类型定义 */
typedef enum
{
    VOL_VECTOR = 0,    /// 电压矢量
    CUR_VECTOR,        /// 电流矢量
} VECTOR_TYPE;

/* 校表步骤定义 */
typedef enum
{
    STEP_INIT = 0,    /// 校表初始化
    STEP_VOL,         /// 校正电压
    STEP_CUR,         /// 校正电流
    STEP_POWER,       /// 校正功率
    STEP_PHASE,       /// 校正相位
    STEP_CUR_N,       /// 校正零线电流: 三路电流增益取平均
    STEP_POFST,       /// 校正功率偏置
    STEP_IOFST,       /// 校正电流偏置
    STEP_QOFST,       /// 无功功率偏置
    STEP_IZERO,       /// 零漂电流校准
    STEP_READREG,     /// 读寄存器值

    STEP_HFCONST = 0x20,    /// 高频脉冲常数调整
    STEP_I2GAIN,            /// 二路电流调整系数计算
    STEP_SAMPLE,            /// 采样

    STEP_PHASE_INIT = 0x30,    /// 相位校准单独初始化
    STEP_POFST_INIT,           /// 功率偏置校准单独初始化
    STEP_QOFST_INIT,           /// 无功偏置校准单独初始化
    STEP_IOFST_INIT,           /// 电流偏置单独初始化

    STEP_SETPARA = 0x50,    /// 设置计量参数
    STEP_GETPARA,           /// 读计量参数
    STEP_SETCAL,            /// 设置校表参数
    STEP_REDCAL,            /// 读取校表参数
    STEP_SAVE    = 0x5A,    /// 保存校表参数
    STEP_CONST   = 0xF0,    /// 刷新计量参数(脉冲常数 -加倍)
    STEP_REFRESH = 0xFF     /// 刷新计量参数，重启计量
} CALIBRATE_STEP_TYPE;

typedef union
{
    struct
    {
        uint32_t pa_rev : 1;    /// A相有功反向
        uint32_t pb_rev : 1;    /// B相有功反向
        uint32_t pc_rev : 1;    /// C相有功反向
        uint32_t pt_rev : 1;    /// 合相有功反向
        uint32_t qa_rev : 1;    /// A相无功反向
        uint32_t qb_rev : 1;    /// B相无功反向
        uint32_t qc_rev : 1;    /// C相无功反向
        uint32_t qt_rev : 1;    /// 合相无功反向

        uint32_t pa_start : 1;     /// A相功率起动
        uint32_t pb_start : 1;     /// B相功率起动
        uint32_t pc_start : 1;     /// C相功率起动
        uint32_t seq_rev : 1;      /// 电压逆相序
        uint32_t i_seq_rev : 1;    /// 电流逆相序

        uint32_t abs : 1;         /// 绝对值出脉冲(适用于三相四线表)
        uint32_t error : 1;       /// 计量错误
        uint32_t main_chn : 1;    /// 单相表计量通道，0-第1通道，1-第2通道
        uint32_t rev : 2;         ///

        uint32_t uncal : 4;    /// 未校表, bitn - 通道n未校准
    };
    uint32_t lword;
} MicStatus_s;

typedef struct
{
    MicStatus_s  stus;
    PWR_QUADRANT quadrant[4];    /// 电能象限
    float        pt_ratio;       /// PT变比
    float        ct_ratio;       /// CT变比

#if defined(POLYPHASE_METER)
    float v_vrms;                ///矢量合电压 1V
    float v_irms;                ///矢量合电流 1A
    float n_irms;                ///零线电流 1A
    float irms[3];               ///电流(A相电流、B相电流、C相电流) 1A
    float vrms[3];               ///电压(A相电压、B相电压、C相电压) 1V
    float line_vrms[3];          ///线电压(AB线电压、BC线电压、AC线电压) 1V
    float freq;                  ///频率 xx.xx
    float pf[4];                 ///功率因数(合相功率因素、A相功率因素、B相功率因素、C相功率因素) 1.000
    float pwr_p[4];              ///有功功率(合相有功功率、A相有功功率、B相有功功率、C相有功功率) 1kW
    float pwr_q[4];              ///无功功率(合相无功功率、A相无功功率、B相无功功率、C相无功功率) 1kvar
    float pwr_s[4];              ///视在功率(合相视在功率、A相视在功率、B相视在功率、C相视在功率) 1kVA
    float pwr_abs[3];            ///绝对值和功率 P_POWER(1kW)、Q_POWER(1kvar)、S_POWER(1kVA)
    float pwr_imp[3];            ///正向功率 P_POWER(1kW)、Q_POWER(1kvar)、S_POWER(1kVA)
    float vi_angle[3];           ///Ia-Ua, Ib-Ub, Ic-Uc xxx.x
    float vv_angle[PHASE_NUM];   ///Ua-Ub, Ua-Uc, Ub-Uc, 0.0~360.0度 xxx.x

#if HARMONIC_WAVE
    float v_harmonic_per[3][HARMONIC_WAVE];    /// 电压谐波百分比
    float i_harmonic_per[3][HARMONIC_WAVE];    /// 电流谐波百分比
    float v_thd[3];                 /// 电压谐波失真度
    float i_thd[3];                 /// 电流谐波失真度
#endif

#else
    float v_irms;         /// 矢量合电流
    float n_irms;         /// 零线电流
    float irms[2];        /// 电流(A相电流、B相电流)
    float vrms[1];        /// 电压
    float freq;           /// 频率
    float pf[3];          /// 功率因数(功率因素、A相功率因素、B相功率因素)
    float pwr_p[3];       /// 有功功率(有功功率、A相有功功率、B相有功功率)
    float pwr_q[3];       /// 无功功率(无功功率、A相无功功率、B相无功功率)
    float pwr_s[3];       /// 视在功率(视在功率、A相视在功率、B相视在功率)
    float vi_angle[3];    /// I-U, Ia-U, Ib-U
#endif
} InstantVal_s;

typedef struct
{
    float urms[3];        /// A,B,C相电压
    float irms[5];        /// A,B,C,N,电流 isum
    float vi_angle[3];    /// A,B,C相电压-电流角度
    float vv_angle[3];    /// AB,BC,CA相电压-电压角度
    float pt_ratio;       /// PT变比
    float ct_ratio;       /// CT变比
} virtrual_meter_data_t;

typedef struct pt_ct_struct
{
    uint32_t pt_numerator;      // CT变比分子, 变压器原边
    uint32_t pt_denominator;    // CT变比分母, 变压器次级
    uint32_t ct_numerator;      // PT变比分子, 变压器原边
    uint32_t ct_denominator;    // PT变比分母, 变压器次级
} pt_ct_s;

/* 计量相关参数 */
typedef union
{
    struct
    {
        uint16_t chk;            /// A相增益
        uint16_t cs;             /// 校验和
        uint8_t  acc_mode;       /// 计量方式，绝对值和/代数和计量
        uint8_t  wiring_mode;    /// 接线方式，三相表 - 3P3W/3P4W， 单相表 - 自动/通道1/通道2
        uint8_t  led_mode;       /// 潜动，启动指示模式(脉冲灯指示潜动状态)
        uint16_t constant;       ///
        pt_ct_s  ratio;          /// PT/CT变比
    };
    uint8_t reserve[32];
} MeasurePara_s;

/// 校表数据结构
typedef struct
{
    float Urms[3];       /// A,B,C相电压
    float Irms[4];       /// A,B,C,N相电流
    float power_p[3];    /// A,B,C相有功功率
    float power_q[3];    /// A,B,C相无功功率
    float power_s[3];    /// A,B,C相视在功率
    float err[3];        /// A,B,C相计量误差
} CalibrateData_s;

/// 校表指令数据结构,协议
typedef struct
{
    ///基本数据
    uint8_t cmd;                 ///指令
    uint8_t const_mul;           ///脉冲常数倍数
    uint8_t Reserved[2];         ///预留
    CalibrateData_s CalData;     ///校表数据
} mfc_cal_data_s;


struct mic_s
{
    /// 瞬时数据
    InstantVal_s *ins;
    /// 初始化
    void (*init)(uint8_t pwr_down);
    /// 关闭计量
    void (*off)(void);
    /// 刷新顺时值
    void (*ins_val_refresh)(void);
    /// @brief 空闲任务
    void (*idle_task)(void);
    /// @brief 低功耗下刷新数据，主要用于全失压
    void (*poweroff_run)(void);
    void (*poweroff_callback)(void func(void));
    /// @brief 获取脉冲
    uint8_t (*pulse)(EMU_POWER_TYPE type, EMU_PHASE_TYPE ph);
    /// @brief 校表
    uint32_t (*cali)(uint8_t chn, uint8_t step, void *pdat_std, void *pdat_samp);
#if defined(POLYPHASE_METER)
    uint8_t (*measure_fast_cnt_add)(EMU_PHASE_TYPE ph, uint32_t *fast_cnt, uint8_t isclac);
#endif
#if (EMU_TYPE == EMU_VIRTUAL)
    void (*set_virtual_mic_data)(uint8_t *buf);
#endif
    /// @brief 获取计量参数
    uint32_t (*measure_para_get)(uint16_t tag);
    /// @brief 设置计量参数
    bool (*measure_para_set)(uint16_t tag, uint32_t val);
};
extern const struct mic_s mic;

#endif /* __MIC_H */